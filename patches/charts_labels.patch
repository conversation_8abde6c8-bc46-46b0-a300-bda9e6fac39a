--- Classes/DataProcessing/Charts/Library/AbstractLibrary.php   2025-02-06 09:52:23
+++ Classes/DataProcessing/Charts/Library/AbstractLibrary_fix.php       2025-02-06 09:59:03
@@ -129,6 +129,12 @@
             return '';
         }

+        if (sizeof($datasets) == 1) {
+           foreach($labels as $id => $label) {
+               $labels[$id] .= ' ' . $datasets[0][$id] .'%';
+           }
+        }
+
         // build datasets for current entity to insert in javascript below
         $datasets = $this->buildEntityDatasetsForJavascript($datasets, $chartEntity);

