name: CI-CD Production

on:
  workflow_dispatch:
  push:
    branches:
      - master

jobs:
  call-grumphp:
    uses: vancado/workflows/.github/workflows/typo3.grumphp.yml@master
    with:
      PHP_VERSION: ${{ vars.PHP_VERSION }}
    secrets: inherit
  call-deployment:
    uses: vancado/workflows/.github/workflows/typo3.deployment.yml@master
    needs: call-grumphp
    with:
      PHP_VERSION: ${{ vars.PHP_VERSION }}
      NODE_VERSION: 20
      BUILD_CMD: "cd frontend && npm run deploy"
      DEPLOY_CMD: "deploy live"
    secrets: inherit
