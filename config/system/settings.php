<?php

return [
    'BE' => [
        'debug' => true,
        'installToolPassword' => '$argon2i$v=19$m=65536,t=16,p=1$YkdFandmWldEMG5YU3dtTA$DqrdOsDEcql5lQvnrcJ3/C7tW3+/Y/JwrW66czsY7v8',
        'passwordHashing' => [
            'className' => 'TYPO3\\CMS\\Core\\Crypto\\PasswordHashing\\Argon2iPasswordHash',
            'options' => [],
        ],
    ],
    'DB' => [
        'Connections' => [
            'Default' => [
                'charset' => 'utf8',
                'driver' => 'mysqli',
            ],
        ],
    ],
    'EXTCONF' => [
        'lang' => [
            'availableLanguages' => [
                'de',
            ],
        ],
    ],
    'EXTENSIONS' => [
        'backend' => [
            'backendFavicon' => '',
            'backendLogo' => '',
            'loginBackgroundImage' => '',
            'loginFootnote' => '',
            'loginHighlightColor' => '',
            'loginLogo' => '',
            'loginLogoAlt' => '',
        ],
        'charts' => [
            'apexcharts_js_assets' => '1',
            'apexcharts_js_javascript' => 'https://cdn.jsdelivr.net/npm/apexcharts@3/dist/apexcharts.min.js',
            'chart_js_assets' => '1',
            'chart_js_javascript' => 'https://cdn.jsdelivr.net/npm/chart.js@3/dist/chart.min.js',
            'library' => 'chart.js',
        ],
        'extensionmanager' => [
            'automaticInstallation' => '1',
            'offlineMode' => '0',
        ],
        'indexed_search' => [
            'catdoc' => '/usr/bin/',
            'debugMode' => '0',
            'deleteFromIndexAfterEditing' => '1',
            'disableFrontendIndexing' => '0',
            'enableMetaphoneSearch' => '1',
            'flagBitMask' => '192',
            'fullTextDataLength' => '0',
            'ignoreExtensions' => '',
            'indexExternalURLs' => '0',
            'maxAge' => '0',
            'maxExternalFiles' => '5',
            'minAge' => '24',
            'pdf_mode' => '20',
            'pdftools' => '/usr/bin/',
            'ppthtml' => '/usr/bin/',
            'unrtf' => '/usr/bin/',
            'unzip' => '/usr/bin/',
            'useCrawlerForExternalFiles' => '0',
            'useMysqlFulltext' => '0',
            'xlhtml' => '/usr/bin/',
        ],
        'news' => [
            'advancedMediaPreview' => '1',
            'archiveDate' => 'date',
            'categoryBeGroupTceFormsRestriction' => '0',
            'categoryRestriction' => '',
            'contentElementRelation' => '1',
            'dateTimeNotRequired' => '0',
            'hidePageTreeForAdministrationModule' => '0',
            'manualSorting' => '0',
            'prependAtCopy' => '1',
            'resourceFolderImporter' => '/news_import',
            'rteForTeaser' => '0',
            'showAdministrationModule' => '1',
            'slugBehaviour' => 'unique',
            'storageUidImporter' => '1',
            'tagPid' => '1',
        ],
        'powermail' => [
            'disableBackendModule' => '0',
            'disableIpLog' => '1',
            'disableMarketingInformation' => '1',
            'disablePluginInformation' => '0',
            'disablePluginInformationMailPreview' => '0',
            'enableCaching' => '0',
            'replaceIrreWithElementBrowser' => '0',
        ],
        'scheduler' => [
            'maxLifetime' => '1440',
            'showSampleTasks' => '1',
        ],
        'schema' => [
            'allowOnlyOneBreadcrumbList' => '0',
            'automaticBreadcrumbExcludeAdditionalDoktypes' => '',
            'automaticBreadcrumbSchemaGeneration' => '0',
            'automaticWebPageSchemaGeneration' => '1',
            'embedMarkupInBodySection' => '0',
            'embedMarkupOnNoindexPages' => '1',
        ],
        'secure_downloads' => [
            'allowPublicAccess' => '1',
            'cachetimeadd' => '3600',
            'checkAccess' => '0',
            'checkDirs' => '0',
            'createFileStorage' => '1',
            'documentRootPath' => '/',
            'enableGroupCheck' => '0',
            'excludeGroups' => '-1,0',
            'forcedownload' => '0',
            'forcedownloadtype' => 'odt|pptx?|docx?|xlsx?|zip|rar|tgz|tar|gz',
            'groupCheckDirs' => '',
            'linkPrefix' => 'securedl',
            'log' => '0',
            'outputFunction' => 'stream',
            'protectedPath' => '',
            'securedDirs' => 'typo3temp|fileadmin/06_extranet',
            'securedFiletypes' => 'pdf|jpe?g|gif|png|odt|pptx?|docx?|xlsx?|zip|rar|tgz|tar|gz',
            'skipCheckConfiguration' => '0',
            'strictGroupCheck' => '0',
            'tokenPrefix' => 'sdl-',
        ],
        'sg_cookie_optin' => [
            'folder' => 'fileadmin/sg_cookie_optin/',
            'hideModuleInProductionContext' => '0',
            'key' => 'MCM4ZI-HFG359-3SQJFY-16KUYX',
        ],
        't3monitoring_client' => [
            'allowedIps' => '*',
            'enableDebugForErrors' => '0',
            'secret' => '',
        ],
        'tt_address' => [
            'backwardsCompatFormat' => '%1$s %3$s',
            'readOnlyNameField' => '1',
            'storeBackwardsCompatName' => '1',
            'telephoneValidationPatternForJs' => '/[^\\d\\+\\s\\-]/g',
            'telephoneValidationPatternForPhp' => '/[^\\d\\+\\s\\-]/',
        ],
        'vnc_warnings' => [
            'pages' => [
                'enabled' => '1',
                'pids' => '',
            ],
        ],
    ],
    'FE' => [
        'cacheHash' => [
            'enforceValidation' => true,
        ],
        'debug' => true,
        'disableNoCacheParameter' => true,
        'passwordHashing' => [
            'className' => 'TYPO3\\CMS\\Core\\Crypto\\PasswordHashing\\Argon2iPasswordHash',
            'options' => [],
        ],
    ],
    'GFX' => [
        'processor' => 'GraphicsMagick',
        'processor_allowTemporaryMasksAsPng' => false,
        'processor_colorspace' => 'RGB',
        'processor_effects' => false,
        'processor_enabled' => true,
        'processor_path' => '/usr/bin/',
    ],
    'LOG' => [
        'TYPO3' => [
            'CMS' => [
                'deprecations' => [
                    'writerConfiguration' => [
                        'notice' => [
                            'TYPO3\CMS\Core\Log\Writer\FileWriter' => [
                                'disabled' => false,
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ],
    'MAIL' => [
        'transport' => 'sendmail',
        'transport_sendmail_command' => '/usr/local/bin/mailpit sendmail -t <EMAIL> --smtp-addr 127.0.0.1:1025',
        'transport_smtp_encrypt' => '',
        'transport_smtp_password' => '',
        'transport_smtp_server' => '',
        'transport_smtp_username' => '',
    ],
    'SYS' => [
        'caching' => [
            'cacheConfigurations' => [
                'hash' => [
                    'backend' => 'TYPO3\\CMS\\Core\\Cache\\Backend\\Typo3DatabaseBackend',
                ],
                'imagesizes' => [
                    'backend' => 'TYPO3\\CMS\\Core\\Cache\\Backend\\Typo3DatabaseBackend',
                    'options' => [
                        'compression' => true,
                    ],
                ],
                'pages' => [
                    'backend' => 'TYPO3\\CMS\\Core\\Cache\\Backend\\Typo3DatabaseBackend',
                    'options' => [
                        'compression' => true,
                    ],
                ],
                'rootline' => [
                    'backend' => 'TYPO3\\CMS\\Core\\Cache\\Backend\\Typo3DatabaseBackend',
                    'options' => [
                        'compression' => true,
                    ],
                ],
            ],
        ],
        'devIPmask' => '*',
        'displayErrors' => 1,
        'encryptionKey' => 'e4fae53c141853df5add62c3a6338522ce0dee96cb94ee66618a777980eb23320b868492a93c219f5e266d3651cc8516',
        'exceptionalErrors' => 12290,
        'features' => [
            'security.usePasswordPolicyForFrontendUsers' => true,
            'yamlImportsFollowDeclarationOrder' => true,
        ],
        'phpTimeZone' => 'UTC',
        'sitename' => 'TYPO3 Project',
        'systemMaintainers' => [
            1,
        ],
    ],
];
