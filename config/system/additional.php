<?php

$GLOBALS['TYPO3_CONF_VARS']['BE']['installToolPassword'] = getenv('TYPO3_BE_INSTALL_TOOL_PASSWORD');

$GLOBALS['TYPO3_CONF_VARS']['DB']['Connections']['Default']['dbname'] = getenv('TYPO3_DB_NAME');
$GLOBALS['TYPO3_CONF_VARS']['DB']['Connections']['Default']['host'] = getenv('TYPO3_DB_HOST');
$GLOBALS['TYPO3_CONF_VARS']['DB']['Connections']['Default']['password'] = getenv('TYPO3_DB_PASSWORD');
$GLOBALS['TYPO3_CONF_VARS']['DB']['Connections']['Default']['user'] = getenv('TYPO3_DB_USER');
$GLOBALS['TYPO3_CONF_VARS']['DB']['Connections']['Default']['port'] = getenv('TYPO3_DB_PORT');

$GLOBALS['TYPO3_CONF_VARS']['BE']['loginRateLimitIpExcludeList'] = getenv('TYPO3_BE_LOGIN_RATE_LIMIT_IP_EXCLUDE_LIST');

$GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['me_backend_security']['maximumValidDays'] = getenv('TYPO3_EXTENSIONS_ME_BACKEND_SECURITY_MAXIMUM_VALID_DAYS');

$GLOBALS['TYPO3_CONF_VARS']['MAIL']['transport'] = getenv('MAIL_TRANSPORT');
$GLOBALS['TYPO3_CONF_VARS']['MAIL']['transport_smtp_server'] = getenv('MAIL_SMTP_SERVER');
$GLOBALS['TYPO3_CONF_VARS']['MAIL']['transport_smtp_password'] = getenv('MAIL_SMTP_PASSWORD');
$GLOBALS['TYPO3_CONF_VARS']['MAIL']['transport_smtp_username'] = getenv('MAIL_SMTP_USERNAME');
$GLOBALS['TYPO3_CONF_VARS']['MAIL']['defaultMailFromAddress'] = getenv('MAIL_DEFAULT_FROM_ADDRESS');
$GLOBALS['TYPO3_CONF_VARS']['MAIL']['defaultMailFromName'] = getenv('MAIL_DEFAULT_FROM_NAME');
$GLOBALS['TYPO3_CONF_VARS']['MAIL']['defaultMailReplyToAddress'] = getenv('MAIL_DEFAULT_REPLY_TO_ADDRESS');
$GLOBALS['TYPO3_CONF_VARS']['MAIL']['defaultMailReplyToName'] = getenv('MAIL_DEFAULT_REPLY_TO_NAME');

$GLOBALS['TYPO3_CONF_VARS']['SYS']['devIPmask'] = getenv('SYS_DEVIPMASK');
$GLOBALS['TYPO3_CONF_VARS']['SYS']['displayErrors'] = getenv('SYS_DISPLAYERRORS');
$GLOBALS['TYPO3_CONF_VARS']['SYS']['errorHandlerErrors'] = getenv('SYS_ERRORHANDLERERRORS'); // E_ALL ^ E_DEPRECATED ^ E_NOTICE ^ E_WARNING (everything except deprecated-msgs and notices and warnings)
$GLOBALS['TYPO3_CONF_VARS']['SYS']['syslogErrorReporting'] =  getenv('SYS_SYSLOGERRORREPORTING'); // E_ALL ^ E_DEPRECATED ^ E_NOTICE ^ E_WARNING (everything except deprecated-msgs and notices and warnings)
$GLOBALS['TYPO3_CONF_VARS']['SYS']['belogErrorReporting'] = getenv('SYS_BELOGERRORREPORTING'); // E_ALL ^ E_DEPRECATED ^ E_NOTICE ^ E_WARNING (everything except deprecated-msgs and notices and warnings)
$GLOBALS['TYPO3_CONF_VARS']['SYS']['trustedHostsPattern'] = getenv('SYS_TRUSTEDHOSTSPATTERN');
$GLOBALS['TYPO3_CONF_VARS']['SYS']['sitename'] = getenv('SITENAME');
$GLOBALS['TYPO3_CONF_VARS']['SYS']['systemMaintainers'] = explode(',', getenv('SYSTEM_MAINTAINER'));

$currentApplicationContext = \TYPO3\CMS\Core\Core\Environment::getContext();
if ($currentApplicationContext == 'Testing') {
    $GLOBALS['TYPO3_CONF_VARS']['EXTCONF']['filefill']['storages'][1] = [
        [
            'identifier' => getenv('EXTCONF_FILEFILL_IDENTIFIER_1'),
            'configuration' => getenv('EXTCONF_FILEFILL_CONFIGURATION_1')
        ]
    ];
}
