base: 'https://extranet.emergy.de/'
baseVariants:
  -
    base: 'https://stage-extranet.emergy.de/'
    condition: 'applicationContext == "Production/Stage"'
  -
    base: 'https://extranetemergy2025.ddev.site/'
    condition: 'applicationContext == "Testing"'
errorHandling:
  -
    errorCode: 404
    errorHandler: Page
    errorContentSource: 't3://page?uid=481'
languages:
  -
    title: Deutsch
    enabled: true
    base: /
    typo3Language: de
    locale: de_DE
    iso-639-1: de
    navigationTitle: DE
    hreflang: de
    direction: ''
    flag: de
    languageId: 0
    websiteTitle: 'Extranet Emergy'
rootPageId: 456
routeEnhancers:
  PageTypeSuffix:
    type: PageType
    map:
      /: 0
      sitemap.xml: 1533906435
routes:
  -
    route: robots.txt
    type: staticText
    content: "User-agent: *\r\nDisallow: /typo3/\r\nDisallow: /typo3_src/\r\n"
settings:
  rootPageId: 456
imports:
  -
    resource: 'EXT:vnc_sitepackage/SubPackages/news/Configuration/YAML/news-config.yaml'
