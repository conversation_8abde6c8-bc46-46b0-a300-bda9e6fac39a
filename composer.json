{"name": "vancado/emergy2025", "description": "Emergy2025 Website", "license": "GPL-2.0-or-later", "config": {"platform": {"php": "8.1"}, "sort-packages": true, "allow-plugins": {"cweagans/composer-patches": true, "ergebnis/composer-normalize": true, "helhum/dotenv-connector": true, "phpro/grumphp": true, "phpstan/extension-installer": true, "pluswerk/grumphp-config": true, "rector/extension-installer": true, "typo3/class-alias-loader": true, "typo3/cms-composer-installers": true}}, "repositories": [{"type": "composer", "url": "https://composer.typo3.org/"}, {"type": "git", "url": "**************:vancado/vnc_powermail.git"}, {"type": "git", "url": "**************:vancado/vnc-content-tabs.git"}, {"type": "git", "url": "**************:vancado/vnc-icon-formelement.git"}, {"type": "git", "url": "**************:vancado/vnc_warnings.git"}, {"type": "git", "url": "**************:vancado/vnc_interactive_image.git"}, {"type": "git", "url": "**************:vancado/vnc_events.git"}, {"type": "path", "url": "packages/*"}], "require": {"b13/container": "^3.1", "brotkrueml/schema": "^3", "cweagans/composer-patches": "^1.7", "deployer/deployer": "^7.0", "friendsoftypo3/content-blocks": "^0.7.19", "friendsoftypo3/tt-address": "^8.0", "georgringer/news": "^11.4", "georgringer/numbered-pagination": "^2.1", "helhum/dotenv-connector": "^3.0", "helhum/typo3-console": "^8.0", "hoogi91/charts": "^3.0", "ichhabrecht/content-defender": "^3.2", "in2code/powermail": "^12.3", "in2code/powermail_cond": "^11.2", "leuchtfeuer/secure-downloads": "^6.1", "sgalinski/sg-cookie-optin": "^6.0", "t3/min": "^3.0", "t3monitor/t3monitoring_client": "dev-master", "typo3/cms-backend": "^12.4", "typo3/cms-belog": "^12.4", "typo3/cms-beuser": "^12.4", "typo3/cms-core": "^12.4", "typo3/cms-dashboard": "^12.4", "typo3/cms-extbase": "^12.4", "typo3/cms-extensionmanager": "^12.4", "typo3/cms-felogin": "^12.4", "typo3/cms-filelist": "^12.4", "typo3/cms-fluid": "^12.4", "typo3/cms-fluid-styled-content": "^12.4", "typo3/cms-frontend": "^12.4", "typo3/cms-impexp": "^12.4", "typo3/cms-indexed-search": "^12.4", "typo3/cms-info": "^12.4", "typo3/cms-install": "^12.4", "typo3/cms-lowlevel": "^12.4", "typo3/cms-recycler": "^12.4", "typo3/cms-redirects": "^12.4", "typo3/cms-rte-ckeditor": "^12.4", "typo3/cms-scheduler": "^12.4", "typo3/cms-seo": "^12.4", "typo3/cms-setup": "^12.4", "typo3/cms-sys-note": "^12.4", "typo3/cms-t3editor": "^12.4", "typo3/cms-tstemplate": "^12.4", "typo3/cms-viewpage": "^12.4", "vancado/vnc-events": "^12", "vancado/vnc-icon-formelement": "^12", "vancado/vnc-interactive-image": "^12", "vancado/vnc-mandant-aquarius": "^1.0", "vancado/vnc-mandant-coebad": "^1.0", "vancado/vnc-mandant-extranet": "^1.0", "vancado/vnc-mandant-swborken": "^1.0", "vancado/vnc-mandant-swcoesfeld": "^1.0", "vancado/vnc-powermail": "^3", "vancado/vnc-warnings": "^12", "yoast-seo-for-typo3/yoast_seo": "^9.0"}, "scripts": {"typo3-cms-scripts": ["typo3 install:fixfolderstructure"], "post-autoload-dump": ["@typo3-cms-scripts"]}, "require-dev": {"ichhabrecht/filefill": "^4.3", "pluswerk/grumphp-config": "^6.5", "saschaegerer/phpstan-typo3": "^1.8.2", "ssch/typo3-rector": "^1.1.3"}, "extra": {"patches": {"hoogi91/charts": ["patches/charts_colors.patch", "patches/charts_color_palette.patch", "patches/charts_labels.patch"]}, "enable-patching": true, "composer-exit-on-patch-failure": true}}