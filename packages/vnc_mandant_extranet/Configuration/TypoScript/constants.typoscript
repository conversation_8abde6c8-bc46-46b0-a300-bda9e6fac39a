plugin.tx_vncsitepackage {
  settings {
    startPagePid = 456
    siteLogo =EXT:vnc_sitepackage/Resources/Public/Images/logo.svg
    imagePath = EXT:vnc_sitepackage/Resources/Public/Images
    svgSpriteFile = Static/symbol-defs.svg
    faviconPath = EXT:vnc_sitepackage/Resources/Public/Favicons/Default/favicon.ico
    metaNavigationPid = 495
    footerNavigationPid = 482
    linkInstagram =
    linkFacebook =
    linkLinkedin =
    linkXing =
    linkYoutube =
    linkX=
    logoutLink = 456
  }
  view {
    # cat=plugin.tx_vncsitepackage/file; type=string; label=Path to template root (FE)
    templateRootPath = EXT:vnc_sitepackage/Resources/Private/Templates/Page/
    # cat=plugin.tx_vncsitepackage/file; type=string; label=Path to template partials (FE)
    partialRootPath = EXT:vnc_mandant_extranet/Resources/Private/Partials/Page/
    # cat=plugin.tx_vncsitepackage/file; type=string; label=Path to template layouts (FE)
    layoutRootPath = EXT:vnc_sitepackage/Resources/Private/Layouts/
  }
}

plugin.tx_indexedsearch {
  settings {
    targetPid = 462
    rootPidList = 456
  }
}


