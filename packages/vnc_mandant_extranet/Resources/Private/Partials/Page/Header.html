<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>

<header class="page-header" data-sticky-header>
    <a class="sr-only sr-only-focusable" href="#page-content">Zum Inhalt springen</a>
    <div class="container-xl">
        <div class="page-header__main d-flex justify-content-between">
            <a class="brand-link" href="{f:uri.page(pageUid: settings.startPagePid)}" aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:home')}">
                <span class="brand-logo">
                     <f:cObject typoscriptObjectPath="lib.inlineSvg" data="{src: '{settings.siteLogo}'}"/>
                </span>
            </a>
            <button
                class="navbar-toggler d-xl-none ms-auto"
                type="button"
                data-bs-toggle="offcanvas"
                data-bs-target="#mainNavigation"
                aria-expanded="false"
                aria-label="Toggle navigation"
            >
                <i class="svg-icon icon nc-menu-8 toggler--expand"></i>
                <i class="svg-icon icon nc-e-remove toggler--collapse"></i>
            </button>
        </div>
    </div>
    <!--div id="navigation" class="page-header__nav offcanvas offcanvas-end"-->
    <div id="navigation">
            <div class="page-header__meta">
                <div class="container-lg h-xl-100">
                    <div class="d-flex justify-content-end h-xl-100">
                    </div>
                </div>
            </div>

            <div class="page-header__nav">
                <f:render partial="Navigation/MainNavigation" arguments="{_all}"/>
            </div>

    </div>
</header>
