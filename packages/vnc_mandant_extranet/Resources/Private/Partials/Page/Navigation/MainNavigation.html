<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:if condition="{mainNavigation}">
        <nav class="nav nav--main offcanvas offcanvas-end" id="mainNavigation" aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:main_navigation')}">
            <div class="container-xl">
                <ul class="nav__list d-xl-flex justify-content-end justify-content-xxl-center">
                    <f:for
                        each="{mainNavigation}"
                        as="page"
                        iteration="mainCounter"
                    >
                        <li class="nav__item nav__lvl-1 {f:if(condition: '{page.children}', then: ' has-children')}{f:if(condition: '{page.active}', then: ' active')}">
                            <f:variable name="currentAttribute" value="{f:if(condition: '{page.active}', then:'aria-current=page')}"></f:variable>
                            <a
                                class="nav__link d-flex justify-content-between py-3 py-xl-2 p-xl-3"
                                href="{page.link}"
                                {currentAttribute}
                                tabindex="0"
                            >
                                {page.title}
                                <span class="svg-icon d-xl-none">
                                    <svg class="icon" preserveAspectRatio="xMaxYMin">
                                        <use xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-triangle-down"></use>
                                    </svg>
                                </span>
                            </a>
                            <f:if condition="{page.children}">
                                <div class="nav__sub">
                                    <div class="nav nav--sub">
                                        <div class="container-xl">
                                            <ul class="nav__list d-xl-flex justify-content-center gap-5">
                                                <f:for
                                                    each="{page.children}"
                                                    as="childPage"
                                                    iteration="childCounter"
                                                >
                                                    <li class="nav__item nav__lvl-2 {f:if(condition: '{childPage.active}', then: ' active')}">
                                                        <f:variable name="currentChildAttribute" value="{f:if(condition: '{childPage.active}', then:'aria-current=page')}"></f:variable>
                                                        <a
                                                            href="#"
                                                            role="button"
                                                            class="nav__link py-2 d-flex gap-2"
                                                            {currentChildAttribute}
                                                        >
                                                            <span class="svg-icon d-xl-none">
                                                                <svg class="icon" preserveAspectRatio="xMaxYMin">
                                                                    <use xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-triangle-down"></use>
                                                                </svg>
                                                            </span>
                                                            {childPage.title}
                                                        </a>
                                                        <f:if condition="{childPage.children}">
                                                            <ul class="nav__list">
                                                            <f:for
                                                                each="{childPage.children}"
                                                                as="subChildPage"
                                                                iteration="subChildCounter"
                                                            >
                                                                <li class="nav__item nav__lvl-3 {f:if(condition: '{subChildPage.active}', then: ' active')}">

                                                                    <span class="svg-icon">
                                                                        <svg class="icon" preserveAspectRatio="xMaxYMin">
                                                                            <use xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-triangle-right"></use>
                                                                        </svg>
                                                                    </span>

                                                                    <f:variable name="currentSubChildAttribute" value="{f:if(condition: '{subChildPage.active}', then:'aria-current=page')}"></f:variable>
                                                                    <a
                                                                        class="nav__link py-3 py-xl-2" href="{subChildPage.link}"
                                                                        {currentSubChildAttribute}
                                                                    >{subChildPage.title}</a>
                                                                </li>
                                                            </f:for>

                                                            </ul>
                                                        </f:if>
                                                    </li>
                                                </f:for>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </f:if>
                        </li>
                    </f:for>
                    <f:comment>Logout link</f:comment>
                    <li class="nav__item nav__lvl-1 {f:if(condition: '{subChildPage.active}', then: ' active')}">
                        <f:cObject typoscriptObjectPath="lib.logoutLink" />
                    </li>
                </ul>
            </div>

            <div class="triangle">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 8 32 32" xml:space="preserve">
                    <g fill="#f1f1ee">
                        <g transform="translate(-24, -170)">
                            <g transform="translate(24, 166)">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="
                                    M17.4,27L32,12.4l-32,0L14.6,27C15.4,27.8,16.6,27.8,17.4,27z"></path>
                            </g>
                        </g>
                    </g>
                </svg>
            </div>

        </nav>
    </f:if>
</html>
