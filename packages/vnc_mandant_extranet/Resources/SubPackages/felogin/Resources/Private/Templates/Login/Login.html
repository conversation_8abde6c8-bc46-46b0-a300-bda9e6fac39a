<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">

<f:flashMessages/>


<div class="container" style="margin-top: 150px">
    <div class="row">
        <f:if condition="{messageKey}">
            <h3>
                <f:render partial="RenderLabelOrMessage" arguments="{key: '{messageKey}_header'}"/>
            </h3>
            <p>
                <f:render partial="RenderLabelOrMessage" arguments="{key: '{messageKey}_message'}"/>
            </p>
        </f:if>
        <div id="felogin-form" class="form bg-gradient-secondary">
            <f:form section="felogin-form" fieldNamePrefix="" action="login" requestToken="{requestToken}" additionalAttributes="{spellcheck: 'false'}">
                <f:render section="content" arguments="{_all}"/>
            </f:form>
        </div>
   </div>
</div>


<f:if condition="{settings.showForgotPassword}">
    <f:link.action action="recovery" controller="PasswordRecovery">
        <f:render partial="RenderLabelOrMessage" arguments="{key: 'forgot_header'}"/>
    </f:link.action>
</f:if>

<f:section name="content">
    <fieldset>
        <legend>
            <f:translate key="login"/>
        </legend>
        <div>
            <label for="tx-felogin-input-username">
                <f:translate key="username"/>
                <f:form.textfield name="user" required="true" class="input input--text" additionalAttributes="{autocomplete: 'username'}" id="tx-felogin-input-username"/>
            </label>
        </div>
        <div>
            <label for="tx-felogin-input-password">
                <f:translate key="password"/>
                <f:form.password name="pass" class="input input--text" additionalAttributes="{required: 'required', autocomplete: 'current-password'}" id="tx-felogin-input-password"/>
            </label>
        </div>

        <f:if condition="{permaloginStatus} > -1">
            <div>
                <label for="permalogin">
                    <f:translate id="permalogin"/>
                    <f:if condition="{permaloginStatus} == 1">
                        <f:then>
                            <f:form.hidden name="permalogin" value="0" additionalAttributes="{disabled: 'disabled'}"/>
                            <f:form.checkbox name="permalogin" id="permalogin" value="1" checked="checked"/>
                        </f:then>
                        <f:else>
                            <f:form.hidden name="permalogin" value="0"/>
                            <f:form.checkbox name="permalogin" id="permalogin" value="1"/>
                        </f:else>
                    </f:if>
                </label>
            </div>
        </f:if>

        <div>
            <f:form.submit value="{f:translate(key: 'login')}" name="submit"/>
        </div>

        <div class="felogin-hidden">
            <f:form.hidden name="logintype" value="login"/>
            <f:if condition="{redirectURL}!=''">
                <f:form.hidden name="redirect_url" value="{redirectURL}" />
            </f:if>
            <f:if condition="{referer}!=''">
                <f:form.hidden name="referer" value="{referer}" />
            </f:if>
            <f:if condition="{redirectReferrer}!=''">
                <f:form.hidden name="redirectReferrer" value="off" />
            </f:if>
            <f:if condition="{noRedirect}!=''">
                <f:form.hidden name="noredirect" value="1" />
            </f:if>
        </div>
    </fieldset>
</f:section>
</html>
