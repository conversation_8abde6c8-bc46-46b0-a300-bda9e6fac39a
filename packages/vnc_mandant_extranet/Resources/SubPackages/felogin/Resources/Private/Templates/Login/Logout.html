<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">

<div class="container" style="margin-top: 150px">
    <div class="row">
        <h3>
            <f:render partial="RenderLabelOrMessage" arguments="{key: 'status_header'}"/>
        </h3>
        <p>
            <f:render partial="RenderLabelOrMessage" arguments="{key: 'status_message'}"/>
        </p>
        <div class="form bg-gradient-secondary" id="felogin-form">
            <f:form action="login" actionUri="{actionUri}" section="felogin-form" fieldNamePrefix="">
                <fieldset>
                    <legend>
                        <f:translate key="logout"/>
                    </legend>
                    <div>
                        <label for="tx-felogin-input-logout">
                            <f:translate key="username"/>
                        </label>
                        {user.username}
                    </div>
                    <div>
                        <f:form.submit value="{f:translate(key: 'logout')}" name="submit" id="tx-felogin-input-logout"/>
                    </div>

                    <div class="felogin-hidden">
                        <f:form.hidden name="logintype" value="logout"/>
                    </div>
                </fieldset>
            </f:form>
        </div>
    </div>
</div>
</html>
