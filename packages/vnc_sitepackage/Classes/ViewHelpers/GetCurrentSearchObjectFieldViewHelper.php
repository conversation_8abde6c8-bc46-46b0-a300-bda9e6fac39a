<?php

namespace Vancado\VncSitepackage\ViewHelpers;

use TYPO3\CMS\Core\Utility\DebugUtility;
use Psr\Http\Message\ServerRequestInterface;
use TYPO3\CMS\Core\Information\Typo3Version;
use TYPO3Fluid\Fluid\Core\Rendering\RenderingContext;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;

class GetCurrentSearchObjectFieldViewHelper extends AbstractViewHelper
{
    public function initializeArguments()
    {
        $this->registerArgument('field', 'string', '', true);
    }

    /**
     * @inheritDoc
     */
    public function render(): mixed
    {
        $field = $this->arguments['field'];
        $request = $this->getRequest();
        $contentObject = $request->getAttribute('currentContentObject')->data;
        if ($field != 'bodytext') {
            return $contentObject[$field];
        }
        return explode(',', $contentObject[$field]);
    }

    private function getRequest(): ?ServerRequestInterface
    {

        if ((new (Typo3Version::class))->getMajorVersion() <= 12) {
            //This is a workaround until the below showed solution is valid for typo3 13
            // @phpstan-ignore-next-line
            return $this->renderingContext->getRequest();
        }

        if ($this->renderingContext->hasAttribute(ServerRequestInterface::class)) {
            return $this->renderingContext->getAttribute(ServerRequestInterface::class);
        }
        return null;
    }
}
