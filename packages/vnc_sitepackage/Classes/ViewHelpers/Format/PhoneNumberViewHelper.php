<?php

namespace <PERSON>ca<PERSON>\VncSitepackage\ViewHelpers\Format;

/*
 * This file is part of the TYPO3 CMS project.
 *
 * It is free software; you can redistribute it and/or modify it under
 * the terms of the GNU General Public License, either version 2
 * of the License, or any later version.
 *
 * For the full copyright and license information, please read the
 * LICENSE.txt file that was distributed with this source code.
 *
 * The TYPO3 project - inspiring people to share!
 */

use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberUtil;
use TYPO3Fluid\Fluid\Core\Rendering\RenderingContextInterface;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;
use TYPO3Fluid\Fluid\Core\ViewHelper\Traits\CompileWithRenderStatic;

class PhoneNumberViewHelper extends AbstractViewHelper
{
    use CompileWithRenderStatic;

    public function initializeArguments(): void
    {
        $this->registerArgument('phoneNumber', 'string', 'The phone number to parse', true);
    }

    /**
     * @inheritDoc
     */
    public static function renderStatic(
        array $arguments,
        \Closure $renderChildrenClosure,
        RenderingContextInterface $renderingContext
    ): string {
        $phoneNumberUtil = PhoneNumberUtil::getInstance();
        $phoneNumber = $arguments['phoneNumber'];

        $phoneNumberObject = $phoneNumberUtil->parse($phoneNumber, 'DE');
        return $phoneNumberUtil->format($phoneNumberObject, PhoneNumberFormat::RFC3966);
    }
}
