<?php

/*
 * This file is part of the TYPO3 CMS project.
 *
 * It is free software; you can redistribute it and/or modify it under
 * the terms of the GNU General Public License, either version 2
 * of the License, or any later version.
 *
 * For the full copyright and license information, please read the
 * LICENSE.txt file that was distributed with this source code.
 *
 * The TYPO3 project - inspiring people to share!
 */

namespace Vancado\VncSitepackage\ViewHelpers;

use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Core\Utility\MathUtility;
use TYPO3\CMS\Core\Utility\PathUtility;
use TYPO3\CMS\Extbase\Utility\LocalizationUtility;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractTagBasedViewHelper;

/**
 * Page browser for indexed search, and only useful here, as the
 * regular pagebrowser
 * so this is a cleaner "pi_browsebox" but not a real page browser
 * functionality
 * @internal
 */
class IndexedSearchPaginatorViewHelper extends AbstractTagBasedViewHelper
{

    /**
     * @var string
     */
    protected static string $prefixId = 'tx_indexedsearch';

    /**
     * @var string
     */
    protected $tagName = 'div';

    /**
     * Initialize arguments
     */
    public function initializeArguments()
    {
        $this->registerArgument('maximumNumberOfResultPages', 'int', '', true);
        $this->registerArgument('numberOfResults', 'int', '', true);
        $this->registerArgument('resultsPerPage', 'int', '', true);
        $this->registerArgument('currentPage', 'int', '', false, 0);
        $this->registerArgument('freeIndexUid', 'int', '');
        $this->registerUniversalTagAttributes();
    }

    /**
     * @inheritDoc
     */
    public function render()
    {
        $maximumNumberOfResultPages = $this->arguments['maximumNumberOfResultPages'];
        $numberOfResults = $this->arguments['numberOfResults'];
        $resultsPerPage = $this->arguments['resultsPerPage'];
        $currentPage = $this->arguments['currentPage'];
        $freeIndexUid = $this->arguments['freeIndexUid'];
        $symboldefsPath = PathUtility::getAbsoluteWebPath(GeneralUtility::getFileAbsFileName('EXT:vnc_sitepackage/Resources/Public/Static/symbol-defs.svg'));

        if ($resultsPerPage <= 0) {
            $resultsPerPage = 10;
        }
        $pageCount = (int)ceil($numberOfResults / $resultsPerPage);
        // only show the result browser if more than one page is needed
        if ($pageCount === 1) {
            return '';
        }

        // Check if $currentPage is in range
        $currentPage = MathUtility::forceIntegerInRange($currentPage, 0, $pageCount - 1);

        $content = '';
        // prev page
        //show on 1st page only
        if ($currentPage === 0) {
            $label = LocalizationUtility::translate('LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:empty_prev');
            $content .= ' <li class="nav__item">
                            <a role="button" class="pagination__link pagination__arrow button--mirrored is-disabled">
                                <i class="svg-icon">
                                    <svg class="icon" preserveAspectRatio="xMaxYMin">
                                        <use xlink:href="'.$symboldefsPath.'#icon-triangle-right" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0"></use>
                                    </svg>
                                </i>
                            </a>
                        </li>';
        }
        // show on all pages after the 1st one
        if ($currentPage > 0) {
            $label = LocalizationUtility::translate('displayResults.previous', 'IndexedSearch') ?? '';
            $content .= $this->makecurrentPageSelector_link($label, $currentPage - 1, $freeIndexUid, 'prev');
        }
        // Check if $maximumNumberOfResultPages is in range
        $maximumNumberOfResultPages = MathUtility::forceIntegerInRange($maximumNumberOfResultPages, 1, $pageCount, 10);
        // Assume $currentPage is in the middle and calculate the index limits of the result page listing
        $minPage = $currentPage - (int)floor($maximumNumberOfResultPages / 2);
        $maxPage = $minPage + $maximumNumberOfResultPages - 1;
        // Check if the indexes are within the page limits
        if ($minPage < 0) {
            $maxPage -= $minPage;
            $minPage = 0;
        } elseif ($maxPage >= $pageCount) {
            $minPage -= $maxPage - $pageCount + 1;
            $maxPage = $pageCount - 1;
        }
        $pageLabel = LocalizationUtility::translate('displayResults.page', 'IndexedSearch');
        for ($a = $minPage; $a <= $maxPage; $a++) {
            $label = trim($pageLabel . ' ' . ($a + 1));
            if ($a === $currentPage) {
                $attr = 'is-active';
            } else {
                $attr = '';
            }
            $label = $this->makecurrentPageSelector_link($label, $a, $freeIndexUid, $attr);
            $content .= $label;
        }
        // next link
        if ($currentPage < $pageCount - 1) {
            $label = LocalizationUtility::translate('displayResults.next', 'IndexedSearch') ?? '';
            $content .= $this->makecurrentPageSelector_link($label, $currentPage + 1, $freeIndexUid, 'next');
        }

        if ($currentPage === $pageCount - 1) {
            $label = LocalizationUtility::translate('LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:empty_next');
            $content .= ' <li class="nav__item">
                            <a role="button" class="pagination__link pagination__arrow is-disabled">
                                <i class="svg-icon">
                                    <svg class="icon" preserveAspectRatio="xMaxYMin">
                                        <use xlink:href="'.$symboldefsPath.'#icon-triangle-right" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0"></use>
                                    </svg>
                                </i>
                            </a>
                        </li>';
        }

        if (!$this->tag->hasAttribute('class')) {
            $this->tag->addAttribute('class', 'pagination');
            $this->tag->addAttribute('aria-label', LocalizationUtility::translate('LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:search_pagination'));
        }

        $this->tag->setContent('<ul class="pagination__list nav__list d-flex gap-4 justify-content-center align-items-center">' . $content . '</ul>');
        return $this->tag->render();
    }

    /**
     * Used to make the link for the result-browser.
     * Notice how the links must resubmit the form after setting the new currentPage-value in a hidden formfield.
     *
     * @param string $str String to wrap in <a> tag
     * @param int $p currentPage value
     * @param string $freeIndexUid List of integers pointing to free indexing configurations to search. -1 represents no filtering, 0 represents TYPO3 pages only, any number above zero is a uid of an indexing configuration!
     * @return string Input string wrapped in <a> tag with onclick event attribute set.
     */
    protected function makecurrentPageSelector_link($str, $p, $freeIndexUid, $attr = '')
    {
        $symboldefsPath = PathUtility::getAbsoluteWebPath(GeneralUtility::getFileAbsFileName('EXT:vnc_sitepackage/Resources/Public/Static/symbol-defs.svg'));
        $onclick = 'document.getElementById(' . GeneralUtility::quoteJSvalue(self::$prefixId . '_pointer') . ').value=' . GeneralUtility::quoteJSvalue((string)$p) . ';';
        if ($freeIndexUid !== null) {
            $onclick .= 'document.getElementById(' . GeneralUtility::quoteJSvalue(self::$prefixId . '_freeIndexUid') . ').value=' . GeneralUtility::quoteJSvalue($freeIndexUid) . ';';
        }
        $onclick .= 'document.getElementById(' . GeneralUtility::quoteJSvalue(self::$prefixId) . ').submit();return false;';
        if ($attr == 'next') {
            return '<li class="nav__item">
                <a class="pagination__link pagination__arrow" role="button" href="#" onclick="' . htmlspecialchars($onclick) . '">
                    <i class="svg-icon">
                        <svg class="icon" preserveAspectRatio="xMaxYMin">
                            <use xlink:href="'.$symboldefsPath.'#icon-triangle-right" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0"></use>
                        </svg>
                    </i>
                </a></li>';
        } elseif ($attr == 'prev') {
            return '<li class="nav__item">
                <a class="pagination__link pagination__arrow button--mirrored" role="button" href="#"  onclick="' . htmlspecialchars($onclick) . '">
                    <i class="svg-icon">
                        <svg class="icon" preserveAspectRatio="xMaxYMin">
                            <use xlink:href="'.$symboldefsPath.'#icon-triangle-right" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0"></use>
                        </svg>
                    </i>
                </a></li>';
        }
        return '<li class="nav__item"><a href="#" class="pagination__link nav___link rounded-circle ' . $attr . '" onclick="' . htmlspecialchars($onclick) . '">' . htmlspecialchars($str) . '</a></li>';
    }
}
