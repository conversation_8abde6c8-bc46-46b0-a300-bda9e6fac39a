<html
    data-namespace-typo3-fluid="true"
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:v="http://typo3.org/ns/Vancado/VncSitepackage/ViewHelpers"
>
<f:if condition="{metaNavigation}">
    <nav
        aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:meta_navigation')}"
        class="nav nav--meta">
        <div class="container-lg h-100 p-0">
            <ul class="nav__list d-flex justify-content-evenly h-100">
                <f:for as="metaItem" each="{metaNavigation}" iteration="i">
                    <li class="nav__item nav__item{f:if(condition: '{metaItem.active}', then: ' active')}">
                        <f:variable name="currentAttribute"
                                    value="{f:if(condition: '{metaItem.active}', then:'aria-current=page')}"></f:variable>
                        <a class="nav__link py-1 px-lg-3 small" href="{metaItem.link}"
                           target="{f:if(condition: metaItem.target, then: metaItem.target, else: '_self')}"
                           {currentAttribute}>
                           <span class="svg-icon">
                                <f:if condition="{metaItem.files.0}">
                                    <v:svg file="{metaItem.files.0}" />
                                </f:if>
                            </span>
                            <f:format.raw>{metaItem.title}</f:format.raw>
                        </a>
                    </li>
                </f:for>
            </ul>
        </div>
    </nav>
</f:if>
</html>
