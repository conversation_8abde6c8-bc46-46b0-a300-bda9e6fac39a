<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <footer class="page-footer mt-auto">
        <div class="page-footer__container container py-5 d-flex flex-column">
            <div class="row mb-4">
                <div class="col-12 mb-3">
                    <nav class="nav nav--social" aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:socialmedia_links')}">
                        <ul class="nav__list d-flex gap-3 justify-content-center">
                            <f:if condition="{settings.linkFacebook}">
                            <li class="nav__item">
                                <a
                                    class="nav__item button button--secondary"
                                    href="{settings.linkFacebook}"
                                    target="_blank"
                                    aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:facebook')}"
                                >
                                    <i class="svg-icon text-white rounded-pill">
                                        <svg class="icon" preserveAspectRatio="xMaxYMin">
                                            <use xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-logo-fb-simple"></use>
                                        </svg>
                                    </i>
                                </a>
                            </li>
                            </f:if>

                            <f:if condition="{settings.linkInstagram}">
                                <li class="nav__item">
                                    <a
                                        class="nav__item button button--secondary"
                                        href="{settings.linkInstagram}"
                                        target="_blank"
                                        aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:instagram')}"
                                    >
                                        <i class="svg-icon text-white rounded-pill">
                                            <svg class="icon" preserveAspectRatio="xMaxYMin">
                                                <use xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-logo-instagram"></use>
                                            </svg>
                                        </i>
                                    </a>
                                </li>
                            </f:if>

                            <f:if condition="{settings.linkLinkedin}">
                                <li class="nav__item">
                                    <a
                                        class="nav__item button button--secondary"
                                        href="{settings.linkLinkedin}"
                                        target="_blank"
                                        aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:linkedin')}"
                                    >
                                        <i class="svg-icon text-white rounded-pill">
                                            <svg class="icon" preserveAspectRatio="xMaxYMin">
                                                <use xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-logo-linkedin"></use>
                                            </svg>
                                        </i>
                                    </a>
                                </li>
                            </f:if>
                            <f:comment><!--
                                                    <f:if condition="{settings.linkXing}">
                                                        <li class="nav__item">
                                                            <a
                                                                class="nav__item"
                                                                href="{settings.linkXing}"
                                                                target="_blank"
                                                                aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:xing')}"
                                                            >
                                                                <i class="svg-icon text-white bg-gradient-secondary rounded-pill">
                                                                    <svg class="icon" preserveAspectRatio="xMaxYMin">
                                                                        <use xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-logo-xing"></use>
                                                                    </svg>
                                                                </i>
                                                            </a>
                                                        </li>
                                                    </f:if>
                        -->
                            </f:comment>
                            <f:if condition="{settings.linkYoutube}">
                                <li class="nav__item">
                                    <a
                                        class="nav__item button button--secondary"
                                        href="{settings.linkYoutube}"
                                        target="_blank"
                                        aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:youtube')}"
                                    >
                                        <i class="svg-icon text-white rounded-pill">
                                            <svg class="icon" preserveAspectRatio="xMaxYMin">
                                                <use xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-logo-youtube"></use>
                                            </svg>
                                        </i>
                                    </a>
                                </li>
                            </f:if>
                            <f:if condition="{settings.linkX}">
                                <li class="nav__item">
                                    <a
                                        class="nav__item button button--secondary"
                                        href="{settings.linkX}"
                                        target="_blank"
                                        aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:x')}"
                                    >
                                        <i class="svg-icon text-white rounded-pill">
                                            <svg class="icon" preserveAspectRatio="xMaxYMin">
                                                <use xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-logo-twitter"></use>
                                            </svg>
                                        </i>
                                    </a>
                                </li>
                            </f:if>

                        </ul>
                    </nav>
                </div>
            </div>
            <div class="row mb-4 text-center text-lg-start">
                <div class="col-lg-3 mb-4 mb-lg-0">
                    <a href="{f:uri.page(pageUid: settings.startPagePid)}" class="brand-logo" style="display: block;max-width: 100%;" aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:home')}">
                        <f:cObject typoscriptObjectPath="lib.inlineSvg" data="{src: '{settings.siteLogo}'}"/>
                    </a>
                </div>
                <div class="col-lg-3 mb-4 mb-lg-0">
                    <article>
                        <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{pageUid: '{settings.footerNavigationPid}', colPos: '2'}"/>
                    </article>
                </div>
                <div class="col-lg-3 mb-4 mb-lg-0">
                    <article>
                        <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{pageUid: '{settings.footerNavigationPid}', colPos: '3'}"/>
                    </article>
                </div>
                <div class="col-lg-3 mb-4 mb-lg-0">
                    <article>
                        <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{pageUid: '{settings.footerNavigationPid}', colPos: '4'}"/>
                    </article>
                </div>
            </div>
        </div>

        <div class="page-footer__bottom py-2">
            <div class="page-footer_bottom-container container">
                <div class="row">
                    <div class="col-12 text-center">
                        <nav class="nav nav--footer" aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:footer_navigation')}">
                            <ul class="nav__list d-flex justify-content-center">
                                <f:for
                                    each="{footerNavigation}"
                                    as="page"
                                    iteration="i"
                                    >
                                    <li class="nav__item">
                                        <f:variable name="currentAttribute" value="{f:if(condition: '{page.active}', then:'aria-current=page')}"></f:variable>
                                        <a href="{page.link}" target="{page.target}" class="nav__link" {currentAttribute}>{page.title}</a>
                                    </li>
                                </f:for>
                                <li class="nav__item">
                                    <f:link.external uri="javascript:SgCookieOptin.openCookieOptin();" class="nav__link">Cookie-Zentrale</f:link.external>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>

            </div>
        </div>
    </footer>
</html>
