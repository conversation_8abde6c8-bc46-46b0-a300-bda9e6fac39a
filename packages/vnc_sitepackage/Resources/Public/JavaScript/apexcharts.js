! function() {
    "use strict";
    var t = {};
    t.default = {
        chartsData: null,
        chartsContainer: null,
        init: function() {
            if ("object" == typeof window["Hoogi91.chartsData"] && (this.chartsData = window["Hoogi91.chartsData"]), this.chartsContainer = document.querySelectorAll(".chart-container > .chart"), this.chartsContainer.length > 0 && null !== this.chartsData && Object.keys(this.chartsData).length > 0)
                for (var t = 0; t < this.chartsContainer.length; ++t) {
                    var e = this.chartsContainer[t],
                        a = e.getAttribute("data-chart-type") || "",
                        r = e.getAttribute("data-chart-data") || "";
                    if (!(a.length <= 0 || r.length <= 0)) {
                        var n = this.getChartData(r);
                        if (!(n.length <= 0)) {
                            var o = n.labels || [],
                                i = n.datasets || [];
                            if (o.length > 0 && i.length > 0) switch (a) {
                                case "chart_bar":
                                    this.createBarChart(e, o, i);
                                    break;
                                case "chart_line":
                                    this.createLineChart(e, o, i);
                                    break;
                                case "chart_pie":
                                    this.createPieChart(e, o, i);
                                    break;
                                case "chart_doughnut":
                                    this.createDoughnutChart(e, o, i)
                            }
                        }
                    }
                }
        },
        getChartData: function(t) {
            return void 0 === this.chartsData[t] ? {} : this.chartsData[t]
        },
        createBarChart: function(t, e, a) {
            var r = this,
                n = r.getChartOptions(t),
                o = new ApexCharts(t, {
                    chart: {
                        type: "bar",
                        stacked: "1" === r.getKeyOfObject(n, "bar.stacked", 0)
                    },
                    series: r.createDatasets(a, (function(t) {
                        var n = r.createDatasets(t.data, (function(r, n) {
                            var o = {
                                x: e[n] || "",
                                y: r
                            };
                            return 1 === a.length && t.background.length > 1 && (o.fillColor = t.background[n] || null), 1 === a.length && t.border.length > 1 && (o.strokeColor = t.border[n] || null), o
                        }));
                        return {
                            name: t.label,
                            data: n
                        }
                    })),
                    labels: e,
                    colors: a[0].border || a[0].background || void 0,
                    fill: {
                        colors: a[0].background || a[0].border || void 0
                    },
                    dataLabels: {
                        enabled: !1
                    },
                    stroke: {
                        width: 1
                    },
                    plotOptions: {
                        bar: {
                            horizontal: "1" === r.getKeyOfObject(n, "bar.horizontal", 0)
                        }
                    },
                    xaxis: r.getTicksConfig(n, "x"),
                    yaxis: r.getTicksConfig(n, "y"),
                    legend: {
                        show: "1" === r.getKeyOfObject(n, "legend.active", 0),
                        position: r.getKeyOfObject(n, "legend.position", "top")
                    }
                });
            return o.render(), o
        },
        createLineChart: function(t, e, a) {
            var r = this,
                n = r.getChartOptions(t),
                o = new ApexCharts(t, {
                    chart: {
                        type: "1" === r.getKeyOfObject(n, "line.fill", 0) ? "area" : "line",
                        stacked: "1" === r.getKeyOfObject(n, "line.stacked", 0)
                    },
                    series: r.createDatasets(a, (function(t) {
                        return {
                            name: t.label,
                            data: t.data
                        }
                    })),
                    labels: e,
                    colors: a[0].border || a[0].background || void 0,
                    fill: {
                        colors: a[0].background || a[0].border || void 0
                    },
                    dataLabels: {
                        enabled: !1
                    },
                    stroke: {
                        colors: a[0].background || void 0,
                        curve: r.getKeyOfObject(n, "line.stepped", "smooth")
                    },
                    markers: {
                        size: [5, 7],
                        colors: a[0].border || void 0
                    },
                    xaxis: r.getTicksConfig(n, "x"),
                    yaxis: r.getTicksConfig(n, "y"),
                    legend: {
                        show: "1" === r.getKeyOfObject(n, "legend.active", 0),
                        position: r.getKeyOfObject(n, "legend.position", "top")
                    }
                });
            return o.render(), o
        },
        createPieChart: function(t, e, a) {
            var r = this,
                n = r.getChartOptions(t),
                o = new ApexCharts(t, {
                    chart: {
                        type: "pie"
                    },

                    colors: a[0].background || a[0].border || [],
                    series: a[0].data || [],
                    labels: e,
                    dataLabels: {
                        enabled: !1
                    },
                    legend: {
                        show: "1" === r.getKeyOfObject(n, "legend.active", 0),
                        position: r.getKeyOfObject(n, "legend.position", "top"),
                        horizontalAlign: "left",
                        width: 50
                    },
                    responsive: [{
                        breakpoint: 992,
                        options: {
                            legend: {
                                position: "bottom"
                            }
                        }
                    }]
                });
            return o.render(), o
        },
        createDoughnutChart: function(t, e, a) {
            var r = this,
                n = r.getChartOptions(t),
                o = parseInt(r.getKeyOfObject(n, "doughnut.cutoutPercentage", 75), 10),
                i = new ApexCharts(t, {
                    chart: {
                        type: "donut",
                         height: 400
                    },
                    colors: a[0].background || a[0].border || [],
                    series: a[0].data || [],
                    labels: e,
                    dataLabels: {
                        enabled: !1
                    },
                    plotOptions: {
                        pie: {
                            donut: {
                                size: o <= 0 ? 100 : o
                            }
                        }
                    },
                    legend: {
                        show: "1" === r.getKeyOfObject(n, "legend.active", 0),
                        position: r.getKeyOfObject(n, "legend.position", "top"),
                        horizontalAlign: "left",
                        width: 250,
                        // height: 100
                    },
                    responsive: [{
                        breakpoint: 1025,
                        options: {
                            legend: {
                                position: "left",
                                width: 400
                            },
                            chart: {
                                height: 300
                            },
                            plotOptions: {
                                pie: {
                                    donut: {
                                        size: '60%'
                                    }
                                }
                            }
                        }
                    },
                    {
                        breakpoint: 992,
                        options: {
                            legend: {
                                position: "left",
                                width: 300
                            },
                            chart: {
                                height: 200
                            },
                            plotOptions: {
                                pie: {
                                    donut: {
                                        size: '50%'
                                    }
                                }
                            }
                        }
                    },
                    {
                        breakpoint: 767,
                        options: {
                            legend: {
                                position: 'bottom',
                                width: 0
                            },
                            chart: {
                                height: 300
                            },
                            plotOptions: {
                                pie: {
                                    donut: {
                                        size: '40%'
                                    }
                                }
                            }
                        }
                    }]
                });
            return i.render(), i
        },
        createDatasets: function(t, e) {
            var a = e || null,
                r = [];
            if ("function" != typeof a) return r;
            if ("object" == typeof t && t.length > 0)
                for (var n = 0; n < t.length; ++n) {
                    var o = a(t[n], n);
                    r.push(o)
                }
            return r
        },
        getKeyOfObject: function(t, e, a) {
            var r = e.split(".").reduce((function(t, e) {
                return null == t ? t : t[e]
            }), t);
            return void 0 !== r ? r : a
        },
        getTicksConfig: function(t, e) {
            var a = {
                title: {
                    text: this.getKeyOfObject(t, "axis." + e + ".label", void 0)
                }
            };
            if (!0 === ("1" === this.getKeyOfObject(t, "axis." + e + ".auto", 0))) return a;
            var r = this.getKeyOfObject(t, "axis." + e + ".min", null);
            null !== r && (a.min = parseInt(r, 10));
            var n = this.getKeyOfObject(t, "axis." + e + ".max", null);
            return null !== n && n > r && (a.max = parseInt(n, 10)), a
        },
        getChartOptions: function(t) {
            var e = {};
            try {
                e = JSON.parse(t.getAttribute("data-chart-config")) || {}
            } catch (t) {}
            return Object.keys(e).length <= 0 ? {} : e
        }
    }, window["Hoogi91.Charts"] = t.default
}();
