require(["TYPO3/CMS/Core/DocumentService","TYPO3/CMS/Backend/Modal"],(function(e,t){return function(){var n={874:function(e){"use strict";e.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},818:function(e,t,n){var r=n(874),o=n(851),a=Object.hasOwnProperty,i={};for(var l in r)a.call(r,l)&&(i[r[l]]=l);var s=e.exports={to:{},get:{}};function c(e,t,n){return Math.min(Math.max(t,e),n)}function u(e){var t=Math.round(e).toString(16).toUpperCase();return t.length<2?"0"+t:t}s.get=function(e){var t,n;switch(e.substring(0,3).toLowerCase()){case"hsl":t=s.get.hsl(e),n="hsl";break;case"hwb":t=s.get.hwb(e),n="hwb";break;default:t=s.get.rgb(e),n="rgb"}return t?{model:n,value:t}:null},s.get.rgb=function(e){if(!e)return null;var t,n,o,i=[0,0,0,1];if(t=e.match(/^#([a-f0-9]{6})([a-f0-9]{2})?$/i)){for(o=t[2],t=t[1],n=0;n<3;n++){var l=2*n;i[n]=parseInt(t.slice(l,l+2),16)}o&&(i[3]=parseInt(o,16)/255)}else if(t=e.match(/^#([a-f0-9]{3,4})$/i)){for(o=(t=t[1])[3],n=0;n<3;n++)i[n]=parseInt(t[n]+t[n],16);o&&(i[3]=parseInt(o+o,16)/255)}else if(t=e.match(/^rgba?\(\s*([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/)){for(n=0;n<3;n++)i[n]=parseInt(t[n+1],0);t[4]&&(t[5]?i[3]=.01*parseFloat(t[4]):i[3]=parseFloat(t[4]))}else{if(!(t=e.match(/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/)))return(t=e.match(/^(\w+)$/))?"transparent"===t[1]?[0,0,0,0]:a.call(r,t[1])?((i=r[t[1]])[3]=1,i):null:null;for(n=0;n<3;n++)i[n]=Math.round(2.55*parseFloat(t[n+1]));t[4]&&(t[5]?i[3]=.01*parseFloat(t[4]):i[3]=parseFloat(t[4]))}for(n=0;n<3;n++)i[n]=c(i[n],0,255);return i[3]=c(i[3],0,1),i},s.get.hsl=function(e){if(!e)return null;var t=e.match(/^hsla?\(\s*([+-]?(?:\d{0,3}\.)?\d+)(?:deg)?\s*,?\s*([+-]?[\d\.]+)%\s*,?\s*([+-]?[\d\.]+)%\s*(?:[,|\/]\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/);if(t){var n=parseFloat(t[4]);return[(parseFloat(t[1])%360+360)%360,c(parseFloat(t[2]),0,100),c(parseFloat(t[3]),0,100),c(isNaN(n)?1:n,0,1)]}return null},s.get.hwb=function(e){if(!e)return null;var t=e.match(/^hwb\(\s*([+-]?\d{0,3}(?:\.\d+)?)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/);if(t){var n=parseFloat(t[4]);return[(parseFloat(t[1])%360+360)%360,c(parseFloat(t[2]),0,100),c(parseFloat(t[3]),0,100),c(isNaN(n)?1:n,0,1)]}return null},s.to.hex=function(){var e=o(arguments);return"#"+u(e[0])+u(e[1])+u(e[2])+(e[3]<1?u(Math.round(255*e[3])):"")},s.to.rgb=function(){var e=o(arguments);return e.length<4||1===e[3]?"rgb("+Math.round(e[0])+", "+Math.round(e[1])+", "+Math.round(e[2])+")":"rgba("+Math.round(e[0])+", "+Math.round(e[1])+", "+Math.round(e[2])+", "+e[3]+")"},s.to.rgb.percent=function(){var e=o(arguments),t=Math.round(e[0]/255*100),n=Math.round(e[1]/255*100),r=Math.round(e[2]/255*100);return e.length<4||1===e[3]?"rgb("+t+"%, "+n+"%, "+r+"%)":"rgba("+t+"%, "+n+"%, "+r+"%, "+e[3]+")"},s.to.hsl=function(){var e=o(arguments);return e.length<4||1===e[3]?"hsl("+e[0]+", "+e[1]+"%, "+e[2]+"%)":"hsla("+e[0]+", "+e[1]+"%, "+e[2]+"%, "+e[3]+")"},s.to.hwb=function(){var e=o(arguments),t="";return e.length>=4&&1!==e[3]&&(t=", "+e[3]),"hwb("+e[0]+", "+e[1]+"%, "+e[2]+"%"+t+")"},s.to.keyword=function(e){return i[e.slice(0,3)]}},767:function(e,t,n){const r=n(818),o=n(978),a=[].slice,i=["keyword","gray","hex"],l={};for(const e of Object.keys(o))l[a.call(o[e].labels).sort().join("")]=e;const s={};function c(e,t){if(!(this instanceof c))return new c(e,t);if(t&&t in i&&(t=null),t&&!(t in o))throw new Error("Unknown model: "+t);let n,u;if(null==e)this.model="rgb",this.color=[0,0,0],this.valpha=1;else if(e instanceof c)this.model=e.model,this.color=e.color.slice(),this.valpha=e.valpha;else if("string"==typeof e){const t=r.get(e);if(null===t)throw new Error("Unable to parse color from string: "+e);this.model=t.model,u=o[this.model].channels,this.color=t.value.slice(0,u),this.valpha="number"==typeof t.value[u]?t.value[u]:1}else if(e.length>0){this.model=t||"rgb",u=o[this.model].channels;const n=a.call(e,0,u);this.color=p(n,u),this.valpha="number"==typeof e[u]?e[u]:1}else if("number"==typeof e)this.model="rgb",this.color=[e>>16&255,e>>8&255,255&e],this.valpha=1;else{this.valpha=1;const t=Object.keys(e);"alpha"in e&&(t.splice(t.indexOf("alpha"),1),this.valpha="number"==typeof e.alpha?e.alpha:0);const r=t.sort().join("");if(!(r in l))throw new Error("Unable to parse color from object: "+JSON.stringify(e));this.model=l[r];const a=o[this.model].labels,i=[];for(n=0;n<a.length;n++)i.push(e[a[n]]);this.color=p(i)}if(s[this.model])for(u=o[this.model].channels,n=0;n<u;n++){const e=s[this.model][n];e&&(this.color[n]=e(this.color[n]))}this.valpha=Math.max(0,Math.min(1,this.valpha)),Object.freeze&&Object.freeze(this)}c.prototype={toString(){return this.string()},toJSON(){return this[this.model]()},string(e){let t=this.model in r.to?this:this.rgb();t=t.round("number"==typeof e?e:1);const n=1===t.valpha?t.color:t.color.concat(this.valpha);return r.to[t.model](n)},percentString(e){const t=this.rgb().round("number"==typeof e?e:1),n=1===t.valpha?t.color:t.color.concat(this.valpha);return r.to.rgb.percent(n)},array(){return 1===this.valpha?this.color.slice():this.color.concat(this.valpha)},object(){const e={},t=o[this.model].channels,n=o[this.model].labels;for(let r=0;r<t;r++)e[n[r]]=this.color[r];return 1!==this.valpha&&(e.alpha=this.valpha),e},unitArray(){const e=this.rgb().color;return e[0]/=255,e[1]/=255,e[2]/=255,1!==this.valpha&&e.push(this.valpha),e},unitObject(){const e=this.rgb().object();return e.r/=255,e.g/=255,e.b/=255,1!==this.valpha&&(e.alpha=this.valpha),e},round(e){return e=Math.max(e||0,0),new c(this.color.map(function(e){return function(t){return function(e,t){return Number(e.toFixed(t))}(t,e)}}(e)).concat(this.valpha),this.model)},alpha(e){return arguments.length>0?new c(this.color.concat(Math.max(0,Math.min(1,e))),this.model):this.valpha},red:u("rgb",0,h(255)),green:u("rgb",1,h(255)),blue:u("rgb",2,h(255)),hue:u(["hsl","hsv","hsl","hwb","hcg"],0,(e=>(e%360+360)%360)),saturationl:u("hsl",1,h(100)),lightness:u("hsl",2,h(100)),saturationv:u("hsv",1,h(100)),value:u("hsv",2,h(100)),chroma:u("hcg",1,h(100)),gray:u("hcg",2,h(100)),white:u("hwb",1,h(100)),wblack:u("hwb",2,h(100)),cyan:u("cmyk",0,h(100)),magenta:u("cmyk",1,h(100)),yellow:u("cmyk",2,h(100)),black:u("cmyk",3,h(100)),x:u("xyz",0,h(100)),y:u("xyz",1,h(100)),z:u("xyz",2,h(100)),l:u("lab",0,h(100)),a:u("lab",1),b:u("lab",2),keyword(e){return arguments.length>0?new c(e):o[this.model].keyword(this.color)},hex(e){return arguments.length>0?new c(e):r.to.hex(this.rgb().round().color)},hexa(e){if(arguments.length>0)return new c(e);const t=this.rgb().round().color;let n=Math.round(255*this.valpha).toString(16).toUpperCase();return 1===n.length&&(n="0"+n),r.to.hex(t)+n},rgbNumber(){const e=this.rgb().color;return(255&e[0])<<16|(255&e[1])<<8|255&e[2]},luminosity(){const e=this.rgb().color,t=[];for(const[n,r]of e.entries()){const e=r/255;t[n]=e<=.03928?e/12.92:((e+.055)/1.055)**2.4}return.2126*t[0]+.7152*t[1]+.0722*t[2]},contrast(e){const t=this.luminosity(),n=e.luminosity();return t>n?(t+.05)/(n+.05):(n+.05)/(t+.05)},level(e){const t=this.contrast(e);return t>=7.1?"AAA":t>=4.5?"AA":""},isDark(){const e=this.rgb().color;return(299*e[0]+587*e[1]+114*e[2])/1e3<128},isLight(){return!this.isDark()},negate(){const e=this.rgb();for(let t=0;t<3;t++)e.color[t]=255-e.color[t];return e},lighten(e){const t=this.hsl();return t.color[2]+=t.color[2]*e,t},darken(e){const t=this.hsl();return t.color[2]-=t.color[2]*e,t},saturate(e){const t=this.hsl();return t.color[1]+=t.color[1]*e,t},desaturate(e){const t=this.hsl();return t.color[1]-=t.color[1]*e,t},whiten(e){const t=this.hwb();return t.color[1]+=t.color[1]*e,t},blacken(e){const t=this.hwb();return t.color[2]+=t.color[2]*e,t},grayscale(){const e=this.rgb().color,t=.3*e[0]+.59*e[1]+.11*e[2];return c.rgb(t,t,t)},fade(e){return this.alpha(this.valpha-this.valpha*e)},opaquer(e){return this.alpha(this.valpha+this.valpha*e)},rotate(e){const t=this.hsl();let n=t.color[0];return n=(n+e)%360,n=n<0?360+n:n,t.color[0]=n,t},mix(e,t){if(!e||!e.rgb)throw new Error('Argument to "mix" was not a Color instance, but rather an instance of '+typeof e);const n=e.rgb(),r=this.rgb(),o=void 0===t?.5:t,a=2*o-1,i=n.alpha()-r.alpha(),l=((a*i==-1?a:(a+i)/(1+a*i))+1)/2,s=1-l;return c.rgb(l*n.red()+s*r.red(),l*n.green()+s*r.green(),l*n.blue()+s*r.blue(),n.alpha()*o+r.alpha()*(1-o))}};for(const e of Object.keys(o)){if(i.includes(e))continue;const t=o[e].channels;c.prototype[e]=function(){if(this.model===e)return new c(this);if(arguments.length>0)return new c(arguments,e);const n="number"==typeof arguments[t]?t:this.valpha;return new c(d(o[this.model][e].raw(this.color)).concat(n),e)},c[e]=function(n){return"number"==typeof n&&(n=p(a.call(arguments),t)),new c(n,e)}}function u(e,t,n){e=Array.isArray(e)?e:[e];for(const r of e)(s[r]||(s[r]=[]))[t]=n;return e=e[0],function(r){let o;return arguments.length>0?(n&&(r=n(r)),o=this[e](),o.color[t]=r,o):(o=this[e]().color[t],n&&(o=n(o)),o)}}function h(e){return function(t){return Math.max(0,Math.min(e,t))}}function d(e){return Array.isArray(e)?e:[e]}function p(e,t){for(let n=0;n<t;n++)"number"!=typeof e[n]&&(e[n]=0);return e}e.exports=c},956:function(e,t,n){const r=n(900),o={};for(const e of Object.keys(r))o[r[e]]=e;const a={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};e.exports=a;for(const e of Object.keys(a)){if(!("channels"in a[e]))throw new Error("missing channels property: "+e);if(!("labels"in a[e]))throw new Error("missing channel labels property: "+e);if(a[e].labels.length!==a[e].channels)throw new Error("channel and label counts mismatch: "+e);const{channels:t,labels:n}=a[e];delete a[e].channels,delete a[e].labels,Object.defineProperty(a[e],"channels",{value:t}),Object.defineProperty(a[e],"labels",{value:n})}a.rgb.hsl=function(e){const t=e[0]/255,n=e[1]/255,r=e[2]/255,o=Math.min(t,n,r),a=Math.max(t,n,r),i=a-o;let l,s;a===o?l=0:t===a?l=(n-r)/i:n===a?l=2+(r-t)/i:r===a&&(l=4+(t-n)/i),l=Math.min(60*l,360),l<0&&(l+=360);const c=(o+a)/2;return s=a===o?0:c<=.5?i/(a+o):i/(2-a-o),[l,100*s,100*c]},a.rgb.hsv=function(e){let t,n,r,o,a;const i=e[0]/255,l=e[1]/255,s=e[2]/255,c=Math.max(i,l,s),u=c-Math.min(i,l,s),h=function(e){return(c-e)/6/u+.5};return 0===u?(o=0,a=0):(a=u/c,t=h(i),n=h(l),r=h(s),i===c?o=r-n:l===c?o=1/3+t-r:s===c&&(o=2/3+n-t),o<0?o+=1:o>1&&(o-=1)),[360*o,100*a,100*c]},a.rgb.hwb=function(e){const t=e[0],n=e[1];let r=e[2];const o=a.rgb.hsl(e)[0],i=1/255*Math.min(t,Math.min(n,r));return r=1-1/255*Math.max(t,Math.max(n,r)),[o,100*i,100*r]},a.rgb.cmyk=function(e){const t=e[0]/255,n=e[1]/255,r=e[2]/255,o=Math.min(1-t,1-n,1-r);return[100*((1-t-o)/(1-o)||0),100*((1-n-o)/(1-o)||0),100*((1-r-o)/(1-o)||0),100*o]},a.rgb.keyword=function(e){const t=o[e];if(t)return t;let n,a=1/0;for(const t of Object.keys(r)){const o=r[t],s=(l=o,((i=e)[0]-l[0])**2+(i[1]-l[1])**2+(i[2]-l[2])**2);s<a&&(a=s,n=t)}var i,l;return n},a.keyword.rgb=function(e){return r[e]},a.rgb.xyz=function(e){let t=e[0]/255,n=e[1]/255,r=e[2]/255;t=t>.04045?((t+.055)/1.055)**2.4:t/12.92,n=n>.04045?((n+.055)/1.055)**2.4:n/12.92,r=r>.04045?((r+.055)/1.055)**2.4:r/12.92;return[100*(.4124*t+.3576*n+.1805*r),100*(.2126*t+.7152*n+.0722*r),100*(.0193*t+.1192*n+.9505*r)]},a.rgb.lab=function(e){const t=a.rgb.xyz(e);let n=t[0],r=t[1],o=t[2];n/=95.047,r/=100,o/=108.883,n=n>.008856?n**(1/3):7.787*n+16/116,r=r>.008856?r**(1/3):7.787*r+16/116,o=o>.008856?o**(1/3):7.787*o+16/116;return[116*r-16,500*(n-r),200*(r-o)]},a.hsl.rgb=function(e){const t=e[0]/360,n=e[1]/100,r=e[2]/100;let o,a,i;if(0===n)return i=255*r,[i,i,i];o=r<.5?r*(1+n):r+n-r*n;const l=2*r-o,s=[0,0,0];for(let e=0;e<3;e++)a=t+1/3*-(e-1),a<0&&a++,a>1&&a--,i=6*a<1?l+6*(o-l)*a:2*a<1?o:3*a<2?l+(o-l)*(2/3-a)*6:l,s[e]=255*i;return s},a.hsl.hsv=function(e){const t=e[0];let n=e[1]/100,r=e[2]/100,o=n;const a=Math.max(r,.01);r*=2,n*=r<=1?r:2-r,o*=a<=1?a:2-a;return[t,100*(0===r?2*o/(a+o):2*n/(r+n)),100*((r+n)/2)]},a.hsv.rgb=function(e){const t=e[0]/60,n=e[1]/100;let r=e[2]/100;const o=Math.floor(t)%6,a=t-Math.floor(t),i=255*r*(1-n),l=255*r*(1-n*a),s=255*r*(1-n*(1-a));switch(r*=255,o){case 0:return[r,s,i];case 1:return[l,r,i];case 2:return[i,r,s];case 3:return[i,l,r];case 4:return[s,i,r];case 5:return[r,i,l]}},a.hsv.hsl=function(e){const t=e[0],n=e[1]/100,r=e[2]/100,o=Math.max(r,.01);let a,i;i=(2-n)*r;const l=(2-n)*o;return a=n*o,a/=l<=1?l:2-l,a=a||0,i/=2,[t,100*a,100*i]},a.hwb.rgb=function(e){const t=e[0]/360;let n=e[1]/100,r=e[2]/100;const o=n+r;let a;o>1&&(n/=o,r/=o);const i=Math.floor(6*t),l=1-r;a=6*t-i,0!=(1&i)&&(a=1-a);const s=n+a*(l-n);let c,u,h;switch(i){default:case 6:case 0:c=l,u=s,h=n;break;case 1:c=s,u=l,h=n;break;case 2:c=n,u=l,h=s;break;case 3:c=n,u=s,h=l;break;case 4:c=s,u=n,h=l;break;case 5:c=l,u=n,h=s}return[255*c,255*u,255*h]},a.cmyk.rgb=function(e){const t=e[0]/100,n=e[1]/100,r=e[2]/100,o=e[3]/100;return[255*(1-Math.min(1,t*(1-o)+o)),255*(1-Math.min(1,n*(1-o)+o)),255*(1-Math.min(1,r*(1-o)+o))]},a.xyz.rgb=function(e){const t=e[0]/100,n=e[1]/100,r=e[2]/100;let o,a,i;return o=3.2406*t+-1.5372*n+-.4986*r,a=-.9689*t+1.8758*n+.0415*r,i=.0557*t+-.204*n+1.057*r,o=o>.0031308?1.055*o**(1/2.4)-.055:12.92*o,a=a>.0031308?1.055*a**(1/2.4)-.055:12.92*a,i=i>.0031308?1.055*i**(1/2.4)-.055:12.92*i,o=Math.min(Math.max(0,o),1),a=Math.min(Math.max(0,a),1),i=Math.min(Math.max(0,i),1),[255*o,255*a,255*i]},a.xyz.lab=function(e){let t=e[0],n=e[1],r=e[2];t/=95.047,n/=100,r/=108.883,t=t>.008856?t**(1/3):7.787*t+16/116,n=n>.008856?n**(1/3):7.787*n+16/116,r=r>.008856?r**(1/3):7.787*r+16/116;return[116*n-16,500*(t-n),200*(n-r)]},a.lab.xyz=function(e){let t,n,r;n=(e[0]+16)/116,t=e[1]/500+n,r=n-e[2]/200;const o=n**3,a=t**3,i=r**3;return n=o>.008856?o:(n-16/116)/7.787,t=a>.008856?a:(t-16/116)/7.787,r=i>.008856?i:(r-16/116)/7.787,t*=95.047,n*=100,r*=108.883,[t,n,r]},a.lab.lch=function(e){const t=e[0],n=e[1],r=e[2];let o;o=360*Math.atan2(r,n)/2/Math.PI,o<0&&(o+=360);return[t,Math.sqrt(n*n+r*r),o]},a.lch.lab=function(e){const t=e[0],n=e[1],r=e[2]/360*2*Math.PI;return[t,n*Math.cos(r),n*Math.sin(r)]},a.rgb.ansi16=function(e,t=null){const[n,r,o]=e;let i=null===t?a.rgb.hsv(e)[2]:t;if(i=Math.round(i/50),0===i)return 30;let l=30+(Math.round(o/255)<<2|Math.round(r/255)<<1|Math.round(n/255));return 2===i&&(l+=60),l},a.hsv.ansi16=function(e){return a.rgb.ansi16(a.hsv.rgb(e),e[2])},a.rgb.ansi256=function(e){const t=e[0],n=e[1],r=e[2];if(t===n&&n===r)return t<8?16:t>248?231:Math.round((t-8)/247*24)+232;return 16+36*Math.round(t/255*5)+6*Math.round(n/255*5)+Math.round(r/255*5)},a.ansi16.rgb=function(e){let t=e%10;if(0===t||7===t)return e>50&&(t+=3.5),t=t/10.5*255,[t,t,t];const n=.5*(1+~~(e>50));return[(1&t)*n*255,(t>>1&1)*n*255,(t>>2&1)*n*255]},a.ansi256.rgb=function(e){if(e>=232){const t=10*(e-232)+8;return[t,t,t]}let t;e-=16;return[Math.floor(e/36)/5*255,Math.floor((t=e%36)/6)/5*255,t%6/5*255]},a.rgb.hex=function(e){const t=(((255&Math.round(e[0]))<<16)+((255&Math.round(e[1]))<<8)+(255&Math.round(e[2]))).toString(16).toUpperCase();return"000000".substring(t.length)+t},a.hex.rgb=function(e){const t=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!t)return[0,0,0];let n=t[0];3===t[0].length&&(n=n.split("").map((e=>e+e)).join(""));const r=parseInt(n,16);return[r>>16&255,r>>8&255,255&r]},a.rgb.hcg=function(e){const t=e[0]/255,n=e[1]/255,r=e[2]/255,o=Math.max(Math.max(t,n),r),a=Math.min(Math.min(t,n),r),i=o-a;let l,s;return l=i<1?a/(1-i):0,s=i<=0?0:o===t?(n-r)/i%6:o===n?2+(r-t)/i:4+(t-n)/i,s/=6,s%=1,[360*s,100*i,100*l]},a.hsl.hcg=function(e){const t=e[1]/100,n=e[2]/100,r=n<.5?2*t*n:2*t*(1-n);let o=0;return r<1&&(o=(n-.5*r)/(1-r)),[e[0],100*r,100*o]},a.hsv.hcg=function(e){const t=e[1]/100,n=e[2]/100,r=t*n;let o=0;return r<1&&(o=(n-r)/(1-r)),[e[0],100*r,100*o]},a.hcg.rgb=function(e){const t=e[0]/360,n=e[1]/100,r=e[2]/100;if(0===n)return[255*r,255*r,255*r];const o=[0,0,0],a=t%1*6,i=a%1,l=1-i;let s=0;switch(Math.floor(a)){case 0:o[0]=1,o[1]=i,o[2]=0;break;case 1:o[0]=l,o[1]=1,o[2]=0;break;case 2:o[0]=0,o[1]=1,o[2]=i;break;case 3:o[0]=0,o[1]=l,o[2]=1;break;case 4:o[0]=i,o[1]=0,o[2]=1;break;default:o[0]=1,o[1]=0,o[2]=l}return s=(1-n)*r,[255*(n*o[0]+s),255*(n*o[1]+s),255*(n*o[2]+s)]},a.hcg.hsv=function(e){const t=e[1]/100,n=t+e[2]/100*(1-t);let r=0;return n>0&&(r=t/n),[e[0],100*r,100*n]},a.hcg.hsl=function(e){const t=e[1]/100,n=e[2]/100*(1-t)+.5*t;let r=0;return n>0&&n<.5?r=t/(2*n):n>=.5&&n<1&&(r=t/(2*(1-n))),[e[0],100*r,100*n]},a.hcg.hwb=function(e){const t=e[1]/100,n=t+e[2]/100*(1-t);return[e[0],100*(n-t),100*(1-n)]},a.hwb.hcg=function(e){const t=e[1]/100,n=1-e[2]/100,r=n-t;let o=0;return r<1&&(o=(n-r)/(1-r)),[e[0],100*r,100*o]},a.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]},a.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]},a.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]},a.gray.hsl=function(e){return[0,0,e[0]]},a.gray.hsv=a.gray.hsl,a.gray.hwb=function(e){return[0,100,e[0]]},a.gray.cmyk=function(e){return[0,0,0,e[0]]},a.gray.lab=function(e){return[e[0],0,0]},a.gray.hex=function(e){const t=255&Math.round(e[0]/100*255),n=((t<<16)+(t<<8)+t).toString(16).toUpperCase();return"000000".substring(n.length)+n},a.rgb.gray=function(e){return[(e[0]+e[1]+e[2])/3/255*100]}},978:function(e,t,n){const r=n(956),o=n(774),a={};Object.keys(r).forEach((e=>{a[e]={},Object.defineProperty(a[e],"channels",{value:r[e].channels}),Object.defineProperty(a[e],"labels",{value:r[e].labels});const t=o(e);Object.keys(t).forEach((n=>{const r=t[n];a[e][n]=function(e){const t=function(...t){const n=t[0];if(null==n)return n;n.length>1&&(t=n);const r=e(t);if("object"==typeof r)for(let e=r.length,t=0;t<e;t++)r[t]=Math.round(r[t]);return r};return"conversion"in e&&(t.conversion=e.conversion),t}(r),a[e][n].raw=function(e){const t=function(...t){const n=t[0];return null==n?n:(n.length>1&&(t=n),e(t))};return"conversion"in e&&(t.conversion=e.conversion),t}(r)}))})),e.exports=a},774:function(e,t,n){const r=n(956);function o(e){const t=function(){const e={},t=Object.keys(r);for(let n=t.length,r=0;r<n;r++)e[t[r]]={distance:-1,parent:null};return e}(),n=[e];for(t[e].distance=0;n.length;){const e=n.pop(),o=Object.keys(r[e]);for(let r=o.length,a=0;a<r;a++){const r=o[a],i=t[r];-1===i.distance&&(i.distance=t[e].distance+1,i.parent=e,n.unshift(r))}}return t}function a(e,t){return function(n){return t(e(n))}}function i(e,t){const n=[t[e].parent,e];let o=r[t[e].parent][e],i=t[e].parent;for(;t[i].parent;)n.unshift(t[i].parent),o=a(r[t[i].parent][i],o),i=t[i].parent;return o.conversion=n,o}e.exports=function(e){const t=o(e),n={},r=Object.keys(t);for(let e=r.length,o=0;o<e;o++){const e=r[o];null!==t[e].parent&&(n[e]=i(e,t))}return n}},900:function(e){"use strict";e.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},955:function(e,t,n){"use strict";var r=n(81),o=n.n(r),a=n(645),i=n.n(a)()(o());i.push([e.id,".color-palette {\n    display: flex;\n    flex-direction: row;\n    flex-wrap: nowrap;\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n}\n\n.new-palette {\n    flex: 1 0 auto;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 4px;\n    color: #000;\n    background: #eee;\n    border: 1px solid #bbb;\n    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out;\n}\n\n.new-palette:hover {\n    color: #333;\n    background: #d7d7d7;\n    border-color: #9c9c9c;\n}\n\n.color {\n    position: relative;\n    flex: 1 0 auto;\n    gap: 20px;\n    display: flex;\n    flex-direction: column-reverse;\n    align-items: center;\n    justify-content: center;\n}\n\n.action {\n    opacity: 0;\n    transition: opacity 0.5s ease-in-out;\n    fill: white;\n    color: white;\n}\n\n.action.move,\n.action.delete {\n    flex: 0 0 auto;\n    width: 24px;\n    height: 24px;\n    cursor: pointer;\n}\n\n.action.edit {\n    display: block;\n    position: absolute;\n    bottom: 120px;\n    left: 0;\n    width: 100%;\n    font-size: 18px;\n    font-weight: bold;\n    text-align: center;\n    cursor: pointer;\n}\n\n.color:hover > .action {\n    opacity: 0.9;\n}\n\n.color:hover > .action {\n    opacity: 0.9;\n}\n\n.color.light .action {\n    fill: black;\n    color: black;\n}\n\n.color > .action.add {\n    display: inline-block;\n    position: absolute;\n    right: -20px;\n    width: 40px;\n    height: 100%;\n    opacity: 0;\n    z-index: 100;\n}\n\n.color > .action.add:hover {\n    opacity: 1;\n}\n\n.color > .action.add > svg,\n.color > .action.initial-add > svg {\n    fill: black;\n    cursor: pointer;\n}\n\n.color > .action.add > svg {\n    position: relative;\n    top: calc(50% - 20px);\n    width: 40px;\n    height: 40px;\n    background: white;\n    border-radius: 50%;\n    box-shadow: 0 0 10px rgb(0 0 0 / 50%);\n}\n\n.color:last-child .action.add {\n    right: 1rem;\n}\n\ncolor-picker {\n    position: absolute;\n    top: 0;\n    left: 0;\n}\n\n.start-button {\n    position: absolute;\n    top: calc(50% - 100px);\n    left: calc(50% - 100px);\n    width: 200px;\n    height: 200px;\n    background: transparent;\n    border: 0;\n    cursor: pointer;\n}\n\n.start-button > slot[name=newButtonIcon] {\n    fill: #ff8700;\n    color: #ff8700;\n}\n\n.start-button > slot[name=newButtonText] {\n    font-size: 20px;\n    text-transform: uppercase;\n    font-family: monospace;\n}\n\n.color-palette.editor > .color {\n    min-width: 140px;\n}\n\n.color-palette.preview {\n    cursor: pointer;\n    border-radius: 4px;\n    box-shadow: 0 0.375rem 1rem #d8d8d8;\n}\n\n.color-palette.preview > .color:first-child {\n    border-top-left-radius: 4px;\n    border-bottom-left-radius: 4px;\n}\n\n.color-palette.preview > .color:last-child {\n    border-top-right-radius: 4px;\n    border-bottom-right-radius: 4px;\n}\n",""]),t.Z=i},344:function(e,t,n){"use strict";var r=n(81),o=n.n(r),a=n(645),i=n.n(a),l=n(667),s=n.n(l),c=new URL(n(3),n.b),u=i()(o()),h=s()(c);u.push([e.id,':host {\n    --wrapper-gap: 40px;\n    --wrapper-width: 410px;\n    --wrapper-height: 175px;\n\n    --color-picker-size: 10px;\n    --color-picker-border: 1px;\n\n    --color-area-bg-color: #F00;\n    --alpha-bg-color: rgb(193, 66, 66);\n}\n\n:host, .backdrop {\n    position: relative;\n    display: block;\n    width: 100%;\n    height: 100%;\n    z-index: 200; /* must be higher than z-index of add button in color-palette */\n}\n\n.backdrop {\n    background-color: rgba(0, 0, 0, 0.5);\n}\n\n.picker-wrapper {\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    background: white;\n    border-radius: 2px;\n    padding: var(--wrapper-gap);\n    width: calc(var(--wrapper-width) + var(--wrapper-gap) * 2);\n    height: calc(var(--wrapper-height) + var(--wrapper-gap) * 2);\n    margin-left: calc((var(--wrapper-width) / 2 * -1) - var(--wrapper-gap) * 2);\n    margin-top: calc((var(--wrapper-height) / 2 * -1) - var(--wrapper-gap) * 2);\n    box-shadow: 0 0.375rem 1rem rgb(0 0 0 / 50%);\n    display: flex;\n    gap: 10px;\n    flex-direction: row;\n    user-select: none;\n}\n\n.picker-wrapper .picking-area {\n    width: 253px;\n    height: 253px;\n    border: 1px solid #ddd;\n    position: relative;\n}\n\n.picker-wrapper .control-area {\n    flex: 1 0 auto;\n    display: flex;\n    gap: 10px;\n    flex-direction: column;\n    align-self: center;\n}\n\n.picker-wrapper .control-area > div {\n    position: relative;\n    height: 30px;\n}\n\n/* HSV format - Hue-Saturation-Value(Brightness) */\n.picker-wrapper .picking-area {\n    background: -moz-linear-gradient(bottom, #000 0%, rgba(0, 0, 0, 0) 100%),\n    -moz-linear-gradient(left, #FFF 0%, rgba(255, 255, 255, 0) 100%);\n    background: -webkit-linear-gradient(bottom, #000 0%, rgba(0, 0, 0, 0) 100%),\n    -webkit-linear-gradient(left, #FFF 0%, rgba(255, 255, 255, 0) 100%);\n    background: -ms-linear-gradient(bottom, #000 0%, rgba(0, 0, 0, 0) 100%),\n    -ms-linear-gradient(left, #FFF 0%, rgba(255, 255, 255, 0) 100%);\n    background: -o-linear-gradient(bottom, #000 0%, rgba(0, 0, 0, 0) 100%),\n    -o-linear-gradient(left, #FFF 0%, rgba(255, 255, 255, 0) 100%);\n    background-color: var(--color-area-bg-color);\n}\n\n.picker-wrapper .picker {\n    position: absolute;\n    top: calc(50% - var(--color-picker-size) + (var(--color-picker-border) * 2));\n    left: calc(50% - var(--color-picker-size) + (var(--color-picker-border) * 2));\n    width: var(--color-picker-size);\n    height: var(--color-picker-size);\n    border-radius: 50%;\n    border: var(--color-picker-border) solid #fff;\n}\n\n.picker-wrapper .picker:before {\n    width: calc(var(--color-picker-size) - (var(--color-picker-border) * 2));\n    height: calc(var(--color-picker-size) - (var(--color-picker-border) * 2));\n    content: "";\n    position: absolute;\n    border: var(--color-picker-border) solid #999;\n    border-radius: 50%;\n}\n\n.picker-wrapper .hue {\n    border: 1px solid #ccc;\n    background: -moz-linear-gradient(left, #F00 0%, #FF0 16.66%, #0F0 33.33%, #0FF 50%,\n    #00F 66.66%, #F0F 83.33%, #F00 100%);\n    background: -webkit-linear-gradient(left, #F00 0%, #FF0 16.66%, #0F0 33.33%, #0FF 50%,\n    #00F 66.66%, #F0F 83.33%, #F00 100%);\n    background: linear-gradient(to right, #F00 0%, #FF0 16.66%, #0F0 33.33%, #0FF 50%,\n    #00F 66.66%, #F0F 83.33%, #F00 100%);\n}\n\n.picker-wrapper .alpha {\n    border: 1px solid #ccc;\n    background: url('+h+");\n}\n\n.picker-wrapper .alpha-mask {\n    width: 100%;\n    height: 100%;\n    background: -moz-linear-gradient(90deg, transparent, var(--alpha-bg-color));\n    background: -webkit-linear-gradient(90deg, transparent, var(--alpha-bg-color));\n    background: linear-gradient(90deg, transparent, var(--alpha-bg-color));\n}\n\n.picker-wrapper .slider-picker {\n    position: absolute;\n    top: -1px;\n    width: 2px;\n    height: 100%;\n    border: 1px solid #777;\n    background-color: #fff;\n}\n\n.picker-wrapper .hue-input,\n.picker-wrapper .rgb-input {\n    display: flex;\n    flex-direction: row;\n    gap: 5px;\n}\n\n.picker-wrapper input {\n    width: 30px;\n    -moz-appearance: textfield;\n}\n\n.picker-wrapper input::-webkit-outer-spin-button,\n.picker-wrapper input::-webkit-inner-spin-button {\n    -webkit-appearance: none;\n    margin: 0;\n}\n",""]),t.Z=u},645:function(e){"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n="",r=void 0!==t[5];return t[4]&&(n+="@supports (".concat(t[4],") {")),t[2]&&(n+="@media ".concat(t[2]," {")),r&&(n+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),n+=e(t),r&&(n+="}"),t[2]&&(n+="}"),t[4]&&(n+="}"),n})).join("")},t.i=function(e,n,r,o,a){"string"==typeof e&&(e=[[null,e,void 0]]);var i={};if(r)for(var l=0;l<this.length;l++){var s=this[l][0];null!=s&&(i[s]=!0)}for(var c=0;c<e.length;c++){var u=[].concat(e[c]);r&&i[u[0]]||(void 0!==a&&(void 0===u[5]||(u[1]="@layer".concat(u[5].length>0?" ".concat(u[5]):""," {").concat(u[1],"}")),u[5]=a),n&&(u[2]?(u[1]="@media ".concat(u[2]," {").concat(u[1],"}"),u[2]=n):u[2]=n),o&&(u[4]?(u[1]="@supports (".concat(u[4],") {").concat(u[1],"}"),u[4]=o):u[4]="".concat(o)),t.push(u))}},t}},667:function(e){"use strict";e.exports=function(e,t){return t||(t={}),e?(e=String(e.__esModule?e.default:e),/^['"].*['"]$/.test(e)&&(e=e.slice(1,-1)),t.hash&&(e+=t.hash),/["'() \t\n]|(%20)/.test(e)||t.needQuotes?'"'.concat(e.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):e):e}},81:function(e){"use strict";e.exports=function(e){return e[1]}},171:function(e){e.exports=function(e){return!(!e||"string"==typeof e)&&(e instanceof Array||Array.isArray(e)||e.length>=0&&(e.splice instanceof Function||Object.getOwnPropertyDescriptor(e,e.length-1)&&"String"!==e.constructor.name))}},851:function(e,t,n){"use strict";var r=n(171),o=Array.prototype.concat,a=Array.prototype.slice,i=e.exports=function(e){for(var t=[],n=0,i=e.length;n<i;n++){var l=e[n];r(l)?t=o.call(t,a.call(l)):t.push(l)}return t};i.wrap=function(e){return function(){return e(i(arguments))}}},3:function(e){"use strict";e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAG0lEQVR42mM4c+bMf2SMDhjooABdAF0DHRQAAJ4P6+G77p9NAAAAAElFTkSuQmCC"},140:function(e){"use strict";e.exports=t},384:function(t){"use strict";t.exports=e}},r={};function o(e){var t=r[e];if(void 0!==t)return t.exports;var a=r[e]={id:e,exports:{}};return n[e](a,a.exports,o),a.exports}o.m=n,o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,{a:t}),t},o.d=function(e,t){for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.b=document.baseURI||self.location.href;var a={};return function(){"use strict";o.r(a);var e=o(384),t=o.n(e),n=o(140),r=o.n(n),i=o(767),l=o.n(i);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){h(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function h(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function d(){return d=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},d.apply(this,arguments)}function p(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function g(e){if("undefined"!=typeof window&&window.navigator)return!!navigator.userAgent.match(e)}var f=g(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),v=g(/Edge/i),b=g(/firefox/i),m=g(/safari/i)&&!g(/chrome/i)&&!g(/android/i),w=g(/iP(ad|od|hone)/i),y=g(/chrome/i)&&g(/android/i),k={capture:!1,passive:!1};function x(e,t,n){e.addEventListener(t,n,!f&&k)}function E(e,t,n){e.removeEventListener(t,n,!f&&k)}function M(e,t){if(t){if(">"===t[0]&&(t=t.substring(1)),e)try{if(e.matches)return e.matches(t);if(e.msMatchesSelector)return e.msMatchesSelector(t);if(e.webkitMatchesSelector)return e.webkitMatchesSelector(t)}catch(e){return!1}return!1}}function C(e){return e.host&&e!==document&&e.host.nodeType?e.host:e.parentNode}function S(e,t,n,r){if(e){n=n||document;do{if(null!=t&&(">"===t[0]?e.parentNode===n&&M(e,t):M(e,t))||r&&e===n)return e;if(e===n)break}while(e=C(e))}return null}var _,D=/\s+/g;function A(e,t,n){if(e&&t)if(e.classList)e.classList[n?"add":"remove"](t);else{var r=(" "+e.className+" ").replace(D," ").replace(" "+t+" "," ");e.className=(r+(n?" "+t:"")).replace(D," ")}}function T(e,t,n){var r=e&&e.style;if(r){if(void 0===n)return document.defaultView&&document.defaultView.getComputedStyle?n=document.defaultView.getComputedStyle(e,""):e.currentStyle&&(n=e.currentStyle),void 0===t?n:n[t];t in r||-1!==t.indexOf("webkit")||(t="-webkit-"+t),r[t]=n+("string"==typeof n?"":"px")}}function P(e,t){var n="";if("string"==typeof e)n=e;else do{var r=T(e,"transform");r&&"none"!==r&&(n=r+" "+n)}while(!t&&(e=e.parentNode));var o=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return o&&new o(n)}function O(e,t,n){if(e){var r=e.getElementsByTagName(t),o=0,a=r.length;if(n)for(;o<a;o++)n(r[o],o);return r}return[]}function I(){var e=document.scrollingElement;return e||document.documentElement}function F(e,t,n,r,o){if(e.getBoundingClientRect||e===window){var a,i,l,s,c,u,h;if(e!==window&&e.parentNode&&e!==I()?(i=(a=e.getBoundingClientRect()).top,l=a.left,s=a.bottom,c=a.right,u=a.height,h=a.width):(i=0,l=0,s=window.innerHeight,c=window.innerWidth,u=window.innerHeight,h=window.innerWidth),(t||n)&&e!==window&&(o=o||e.parentNode,!f))do{if(o&&o.getBoundingClientRect&&("none"!==T(o,"transform")||n&&"static"!==T(o,"position"))){var d=o.getBoundingClientRect();i-=d.top+parseInt(T(o,"border-top-width")),l-=d.left+parseInt(T(o,"border-left-width")),s=i+a.height,c=l+a.width;break}}while(o=o.parentNode);if(r&&e!==window){var p=P(o||e),g=p&&p.a,v=p&&p.d;p&&(s=(i/=v)+(u/=v),c=(l/=g)+(h/=g))}return{top:i,left:l,bottom:s,right:c,width:h,height:u}}}function N(e,t,n){for(var r=B(e,!0),o=F(e)[t];r;){var a=F(r)[n];if(!("top"===n||"left"===n?o>=a:o<=a))return r;if(r===I())break;r=B(r,!1)}return!1}function j(e,t,n,r){for(var o=0,a=0,i=e.children;a<i.length;){if("none"!==i[a].style.display&&i[a]!==qe.ghost&&(r||i[a]!==qe.dragged)&&S(i[a],n.draggable,e,!1)){if(o===t)return i[a];o++}a++}return null}function R(e,t){for(var n=e.lastElementChild;n&&(n===qe.ghost||"none"===T(n,"display")||t&&!M(n,t));)n=n.previousElementSibling;return n||null}function L(e,t){var n=0;if(!e||!e.parentNode)return-1;for(;e=e.previousElementSibling;)"TEMPLATE"===e.nodeName.toUpperCase()||e===qe.clone||t&&!M(e,t)||n++;return n}function z(e){var t=0,n=0,r=I();if(e)do{var o=P(e),a=o.a,i=o.d;t+=e.scrollLeft*a,n+=e.scrollTop*i}while(e!==r&&(e=e.parentNode));return[t,n]}function B(e,t){if(!e||!e.getBoundingClientRect)return I();var n=e,r=!1;do{if(n.clientWidth<n.scrollWidth||n.clientHeight<n.scrollHeight){var o=T(n);if(n.clientWidth<n.scrollWidth&&("auto"==o.overflowX||"scroll"==o.overflowX)||n.clientHeight<n.scrollHeight&&("auto"==o.overflowY||"scroll"==o.overflowY)){if(!n.getBoundingClientRect||n===document.body)return I();if(r||t)return n;r=!0}}}while(n=n.parentNode);return I()}function X(e,t){return Math.round(e.top)===Math.round(t.top)&&Math.round(e.left)===Math.round(t.left)&&Math.round(e.height)===Math.round(t.height)&&Math.round(e.width)===Math.round(t.width)}function Y(e,t){return function(){if(!_){var n=arguments,r=this;1===n.length?e.call(r,n[0]):e.apply(r,n),_=setTimeout((function(){_=void 0}),t)}}}function H(e,t,n){e.scrollLeft+=t,e.scrollTop+=n}function q(e){var t=window.Polymer,n=window.jQuery||window.Zepto;return t&&t.dom?t.dom(e).cloneNode(!0):n?n(e).clone(!0)[0]:e.cloneNode(!0)}var W="Sortable"+(new Date).getTime();function V(){var e,t=[];return{captureAnimationState:function(){(t=[],this.options.animation)&&[].slice.call(this.el.children).forEach((function(e){if("none"!==T(e,"display")&&e!==qe.ghost){t.push({target:e,rect:F(e)});var n=c({},t[t.length-1].rect);if(e.thisAnimationDuration){var r=P(e,!0);r&&(n.top-=r.f,n.left-=r.e)}e.fromRect=n}}))},addAnimationState:function(e){t.push(e)},removeAnimationState:function(e){t.splice(function(e,t){for(var n in e)if(e.hasOwnProperty(n))for(var r in t)if(t.hasOwnProperty(r)&&t[r]===e[n][r])return Number(n);return-1}(t,{target:e}),1)},animateAll:function(n){var r=this;if(!this.options.animation)return clearTimeout(e),void("function"==typeof n&&n());var o=!1,a=0;t.forEach((function(e){var t=0,n=e.target,i=n.fromRect,l=F(n),s=n.prevFromRect,c=n.prevToRect,u=e.rect,h=P(n,!0);h&&(l.top-=h.f,l.left-=h.e),n.toRect=l,n.thisAnimationDuration&&X(s,l)&&!X(i,l)&&(u.top-l.top)/(u.left-l.left)==(i.top-l.top)/(i.left-l.left)&&(t=function(e,t,n,r){return Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))/Math.sqrt(Math.pow(t.top-n.top,2)+Math.pow(t.left-n.left,2))*r.animation}(u,s,c,r.options)),X(l,i)||(n.prevFromRect=i,n.prevToRect=l,t||(t=r.options.animation),r.animate(n,u,l,t)),t&&(o=!0,a=Math.max(a,t),clearTimeout(n.animationResetTimer),n.animationResetTimer=setTimeout((function(){n.animationTime=0,n.prevFromRect=null,n.fromRect=null,n.prevToRect=null,n.thisAnimationDuration=null}),t),n.thisAnimationDuration=t)})),clearTimeout(e),o?e=setTimeout((function(){"function"==typeof n&&n()}),a):"function"==typeof n&&n(),t=[]},animate:function(e,t,n,r){if(r){T(e,"transition",""),T(e,"transform","");var o=P(this.el),a=o&&o.a,i=o&&o.d,l=(t.left-n.left)/(a||1),s=(t.top-n.top)/(i||1);e.animatingX=!!l,e.animatingY=!!s,T(e,"transform","translate3d("+l+"px,"+s+"px,0)"),this.forRepaintDummy=function(e){return e.offsetWidth}(e),T(e,"transition","transform "+r+"ms"+(this.options.easing?" "+this.options.easing:"")),T(e,"transform","translate3d(0,0,0)"),"number"==typeof e.animated&&clearTimeout(e.animated),e.animated=setTimeout((function(){T(e,"transition",""),T(e,"transform",""),e.animated=!1,e.animatingX=!1,e.animatingY=!1}),r)}}}}var U=[],G={initializeByDefault:!0},$={mount:function(e){for(var t in G)G.hasOwnProperty(t)&&!(t in e)&&(e[t]=G[t]);U.forEach((function(t){if(t.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")})),U.push(e)},pluginEvent:function(e,t,n){var r=this;this.eventCanceled=!1,n.cancel=function(){r.eventCanceled=!0};var o=e+"Global";U.forEach((function(r){t[r.pluginName]&&(t[r.pluginName][o]&&t[r.pluginName][o](c({sortable:t},n)),t.options[r.pluginName]&&t[r.pluginName][e]&&t[r.pluginName][e](c({sortable:t},n)))}))},initializePlugins:function(e,t,n,r){for(var o in U.forEach((function(r){var o=r.pluginName;if(e.options[o]||r.initializeByDefault){var a=new r(e,t,e.options);a.sortable=e,a.options=e.options,e[o]=a,d(n,a.defaults)}})),e.options)if(e.options.hasOwnProperty(o)){var a=this.modifyOption(e,o,e.options[o]);void 0!==a&&(e.options[o]=a)}},getEventProperties:function(e,t){var n={};return U.forEach((function(r){"function"==typeof r.eventProperties&&d(n,r.eventProperties.call(t[r.pluginName],e))})),n},modifyOption:function(e,t,n){var r;return U.forEach((function(o){e[o.pluginName]&&o.optionListeners&&"function"==typeof o.optionListeners[t]&&(r=o.optionListeners[t].call(e[o.pluginName],n))})),r}};function Z(e){var t=e.sortable,n=e.rootEl,r=e.name,o=e.targetEl,a=e.cloneEl,i=e.toEl,l=e.fromEl,s=e.oldIndex,u=e.newIndex,h=e.oldDraggableIndex,d=e.newDraggableIndex,p=e.originalEvent,g=e.putSortable,b=e.extraEventProperties;if(t=t||n&&n[W]){var m,w=t.options,y="on"+r.charAt(0).toUpperCase()+r.substr(1);!window.CustomEvent||f||v?(m=document.createEvent("Event")).initEvent(r,!0,!0):m=new CustomEvent(r,{bubbles:!0,cancelable:!0}),m.to=i||n,m.from=l||n,m.item=o||n,m.clone=a,m.oldIndex=s,m.newIndex=u,m.oldDraggableIndex=h,m.newDraggableIndex=d,m.originalEvent=p,m.pullMode=g?g.lastPutMode:void 0;var k=c(c({},b),$.getEventProperties(r,t));for(var x in k)m[x]=k[x];n&&n.dispatchEvent(m),w[y]&&w[y].call(t,m)}}var Q=["evt"],J=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.evt,o=p(n,Q);$.pluginEvent.bind(qe)(e,t,c({dragEl:ee,parentEl:te,ghostEl:ne,rootEl:re,nextEl:oe,lastDownEl:ae,cloneEl:ie,cloneHidden:le,dragStarted:ye,putSortable:pe,activeSortable:qe.active,originalEvent:r,oldIndex:se,oldDraggableIndex:ue,newIndex:ce,newDraggableIndex:he,hideGhostForTarget:Be,unhideGhostForTarget:Xe,cloneNowHidden:function(){le=!0},cloneNowShown:function(){le=!1},dispatchSortableEvent:function(e){K({sortable:t,name:e,originalEvent:r})}},o))};function K(e){Z(c({putSortable:pe,cloneEl:ie,targetEl:ee,rootEl:re,oldIndex:se,oldDraggableIndex:ue,newIndex:ce,newDraggableIndex:he},e))}var ee,te,ne,re,oe,ae,ie,le,se,ce,ue,he,de,pe,ge,fe,ve,be,me,we,ye,ke,xe,Ee,Me,Ce=!1,Se=!1,_e=[],De=!1,Ae=!1,Te=[],Pe=!1,Oe=[],Ie="undefined"!=typeof document,Fe=w,Ne=v||f?"cssFloat":"float",je=Ie&&!y&&!w&&"draggable"in document.createElement("div"),Re=function(){if(Ie){if(f)return!1;var e=document.createElement("x");return e.style.cssText="pointer-events:auto","auto"===e.style.pointerEvents}}(),Le=function(e,t){var n=T(e),r=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),o=j(e,0,t),a=j(e,1,t),i=o&&T(o),l=a&&T(a),s=i&&parseInt(i.marginLeft)+parseInt(i.marginRight)+F(o).width,c=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+F(a).width;if("flex"===n.display)return"column"===n.flexDirection||"column-reverse"===n.flexDirection?"vertical":"horizontal";if("grid"===n.display)return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(o&&i.float&&"none"!==i.float){var u="left"===i.float?"left":"right";return!a||"both"!==l.clear&&l.clear!==u?"horizontal":"vertical"}return o&&("block"===i.display||"flex"===i.display||"table"===i.display||"grid"===i.display||s>=r&&"none"===n[Ne]||a&&"none"===n[Ne]&&s+c>r)?"vertical":"horizontal"},ze=function(e){function t(e,n){return function(r,o,a,i){var l=r.options.group.name&&o.options.group.name&&r.options.group.name===o.options.group.name;if(null==e&&(n||l))return!0;if(null==e||!1===e)return!1;if(n&&"clone"===e)return e;if("function"==typeof e)return t(e(r,o,a,i),n)(r,o,a,i);var s=(n?r:o).options.group.name;return!0===e||"string"==typeof e&&e===s||e.join&&e.indexOf(s)>-1}}var n={},r=e.group;r&&"object"==u(r)||(r={name:r}),n.name=r.name,n.checkPull=t(r.pull,!0),n.checkPut=t(r.put),n.revertClone=r.revertClone,e.group=n},Be=function(){!Re&&ne&&T(ne,"display","none")},Xe=function(){!Re&&ne&&T(ne,"display","")};Ie&&document.addEventListener("click",(function(e){if(Se)return e.preventDefault(),e.stopPropagation&&e.stopPropagation(),e.stopImmediatePropagation&&e.stopImmediatePropagation(),Se=!1,!1}),!0);var Ye=function(e){if(ee){e=e.touches?e.touches[0]:e;var t=(o=e.clientX,a=e.clientY,_e.some((function(e){var t=e[W].options.emptyInsertThreshold;if(t&&!R(e)){var n=F(e),r=o>=n.left-t&&o<=n.right+t,l=a>=n.top-t&&a<=n.bottom+t;return r&&l?i=e:void 0}})),i);if(t){var n={};for(var r in e)e.hasOwnProperty(r)&&(n[r]=e[r]);n.target=n.rootEl=t,n.preventDefault=void 0,n.stopPropagation=void 0,t[W]._onDragOver(n)}}var o,a,i},He=function(e){ee&&ee.parentNode[W]._isOutsideThisEl(e.target)};function qe(e,t){if(!e||!e.nodeType||1!==e.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(e));this.el=e,this.options=t=d({},t),e[W]=this;var n={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(e.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Le(e,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(e,t){e.setData("Text",t.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==qe.supportPointer&&"PointerEvent"in window&&!m,emptyInsertThreshold:5};for(var r in $.initializePlugins(this,e,n),n)!(r in t)&&(t[r]=n[r]);for(var o in ze(t),this)"_"===o.charAt(0)&&"function"==typeof this[o]&&(this[o]=this[o].bind(this));this.nativeDraggable=!t.forceFallback&&je,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?x(e,"pointerdown",this._onTapStart):(x(e,"mousedown",this._onTapStart),x(e,"touchstart",this._onTapStart)),this.nativeDraggable&&(x(e,"dragover",this),x(e,"dragenter",this)),_e.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),d(this,V())}function We(e,t,n,r,o,a,i,l){var s,c,u=e[W],h=u.options.onMove;return!window.CustomEvent||f||v?(s=document.createEvent("Event")).initEvent("move",!0,!0):s=new CustomEvent("move",{bubbles:!0,cancelable:!0}),s.to=t,s.from=e,s.dragged=n,s.draggedRect=r,s.related=o||t,s.relatedRect=a||F(t),s.willInsertAfter=l,s.originalEvent=i,e.dispatchEvent(s),h&&(c=h.call(u,s,i)),c}function Ve(e){e.draggable=!1}function Ue(){Pe=!1}function Ge(e){for(var t=e.tagName+e.className+e.src+e.href+e.textContent,n=t.length,r=0;n--;)r+=t.charCodeAt(n);return r.toString(36)}function $e(e){return setTimeout(e,0)}function Ze(e){return clearTimeout(e)}qe.prototype={constructor:qe,_isOutsideThisEl:function(e){this.el.contains(e)||e===this.el||(ke=null)},_getDirection:function(e,t){return"function"==typeof this.options.direction?this.options.direction.call(this,e,t,ee):this.options.direction},_onTapStart:function(e){if(e.cancelable){var t=this,n=this.el,r=this.options,o=r.preventOnFilter,a=e.type,i=e.touches&&e.touches[0]||e.pointerType&&"touch"===e.pointerType&&e,l=(i||e).target,s=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||l,c=r.filter;if(function(e){Oe.length=0;var t=e.getElementsByTagName("input"),n=t.length;for(;n--;){var r=t[n];r.checked&&Oe.push(r)}}(n),!ee&&!(/mousedown|pointerdown/.test(a)&&0!==e.button||r.disabled)&&!s.isContentEditable&&(this.nativeDraggable||!m||!l||"SELECT"!==l.tagName.toUpperCase())&&!((l=S(l,r.draggable,n,!1))&&l.animated||ae===l)){if(se=L(l),ue=L(l,r.draggable),"function"==typeof c){if(c.call(this,e,l,this))return K({sortable:t,rootEl:s,name:"filter",targetEl:l,toEl:n,fromEl:n}),J("filter",t,{evt:e}),void(o&&e.cancelable&&e.preventDefault())}else if(c&&(c=c.split(",").some((function(r){if(r=S(s,r.trim(),n,!1))return K({sortable:t,rootEl:r,name:"filter",targetEl:l,fromEl:n,toEl:n}),J("filter",t,{evt:e}),!0}))))return void(o&&e.cancelable&&e.preventDefault());r.handle&&!S(s,r.handle,n,!1)||this._prepareDragStart(e,i,l)}}},_prepareDragStart:function(e,t,n){var r,o=this,a=o.el,i=o.options,l=a.ownerDocument;if(n&&!ee&&n.parentNode===a){var s=F(n);if(re=a,te=(ee=n).parentNode,oe=ee.nextSibling,ae=n,de=i.group,qe.dragged=ee,ge={target:ee,clientX:(t||e).clientX,clientY:(t||e).clientY},me=ge.clientX-s.left,we=ge.clientY-s.top,this._lastX=(t||e).clientX,this._lastY=(t||e).clientY,ee.style["will-change"]="all",r=function(){J("delayEnded",o,{evt:e}),qe.eventCanceled?o._onDrop():(o._disableDelayedDragEvents(),!b&&o.nativeDraggable&&(ee.draggable=!0),o._triggerDragStart(e,t),K({sortable:o,name:"choose",originalEvent:e}),A(ee,i.chosenClass,!0))},i.ignore.split(",").forEach((function(e){O(ee,e.trim(),Ve)})),x(l,"dragover",Ye),x(l,"mousemove",Ye),x(l,"touchmove",Ye),x(l,"mouseup",o._onDrop),x(l,"touchend",o._onDrop),x(l,"touchcancel",o._onDrop),b&&this.nativeDraggable&&(this.options.touchStartThreshold=4,ee.draggable=!0),J("delayStart",this,{evt:e}),!i.delay||i.delayOnTouchOnly&&!t||this.nativeDraggable&&(v||f))r();else{if(qe.eventCanceled)return void this._onDrop();x(l,"mouseup",o._disableDelayedDrag),x(l,"touchend",o._disableDelayedDrag),x(l,"touchcancel",o._disableDelayedDrag),x(l,"mousemove",o._delayedDragTouchMoveHandler),x(l,"touchmove",o._delayedDragTouchMoveHandler),i.supportPointer&&x(l,"pointermove",o._delayedDragTouchMoveHandler),o._dragStartTimer=setTimeout(r,i.delay)}}},_delayedDragTouchMoveHandler:function(e){var t=e.touches?e.touches[0]:e;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){ee&&Ve(ee),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;E(e,"mouseup",this._disableDelayedDrag),E(e,"touchend",this._disableDelayedDrag),E(e,"touchcancel",this._disableDelayedDrag),E(e,"mousemove",this._delayedDragTouchMoveHandler),E(e,"touchmove",this._delayedDragTouchMoveHandler),E(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,t){t=t||"touch"==e.pointerType&&e,!this.nativeDraggable||t?this.options.supportPointer?x(document,"pointermove",this._onTouchMove):x(document,t?"touchmove":"mousemove",this._onTouchMove):(x(ee,"dragend",this),x(re,"dragstart",this._onDragStart));try{document.selection?$e((function(){document.selection.empty()})):window.getSelection().removeAllRanges()}catch(e){}},_dragStarted:function(e,t){if(Ce=!1,re&&ee){J("dragStarted",this,{evt:t}),this.nativeDraggable&&x(document,"dragover",He);var n=this.options;!e&&A(ee,n.dragClass,!1),A(ee,n.ghostClass,!0),qe.active=this,e&&this._appendGhost(),K({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(fe){this._lastX=fe.clientX,this._lastY=fe.clientY,Be();for(var e=document.elementFromPoint(fe.clientX,fe.clientY),t=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(fe.clientX,fe.clientY))!==t;)t=e;if(ee.parentNode[W]._isOutsideThisEl(e),t)do{if(t[W]){if(t[W]._onDragOver({clientX:fe.clientX,clientY:fe.clientY,target:e,rootEl:t})&&!this.options.dragoverBubble)break}e=t}while(t=t.parentNode);Xe()}},_onTouchMove:function(e){if(ge){var t=this.options,n=t.fallbackTolerance,r=t.fallbackOffset,o=e.touches?e.touches[0]:e,a=ne&&P(ne,!0),i=ne&&a&&a.a,l=ne&&a&&a.d,s=Fe&&Me&&z(Me),c=(o.clientX-ge.clientX+r.x)/(i||1)+(s?s[0]-Te[0]:0)/(i||1),u=(o.clientY-ge.clientY+r.y)/(l||1)+(s?s[1]-Te[1]:0)/(l||1);if(!qe.active&&!Ce){if(n&&Math.max(Math.abs(o.clientX-this._lastX),Math.abs(o.clientY-this._lastY))<n)return;this._onDragStart(e,!0)}if(ne){a?(a.e+=c-(ve||0),a.f+=u-(be||0)):a={a:1,b:0,c:0,d:1,e:c,f:u};var h="matrix(".concat(a.a,",").concat(a.b,",").concat(a.c,",").concat(a.d,",").concat(a.e,",").concat(a.f,")");T(ne,"webkitTransform",h),T(ne,"mozTransform",h),T(ne,"msTransform",h),T(ne,"transform",h),ve=c,be=u,fe=o}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!ne){var e=this.options.fallbackOnBody?document.body:re,t=F(ee,!0,Fe,!0,e),n=this.options;if(Fe){for(Me=e;"static"===T(Me,"position")&&"none"===T(Me,"transform")&&Me!==document;)Me=Me.parentNode;Me!==document.body&&Me!==document.documentElement?(Me===document&&(Me=I()),t.top+=Me.scrollTop,t.left+=Me.scrollLeft):Me=I(),Te=z(Me)}A(ne=ee.cloneNode(!0),n.ghostClass,!1),A(ne,n.fallbackClass,!0),A(ne,n.dragClass,!0),T(ne,"transition",""),T(ne,"transform",""),T(ne,"box-sizing","border-box"),T(ne,"margin",0),T(ne,"top",t.top),T(ne,"left",t.left),T(ne,"width",t.width),T(ne,"height",t.height),T(ne,"opacity","0.8"),T(ne,"position",Fe?"absolute":"fixed"),T(ne,"zIndex","100000"),T(ne,"pointerEvents","none"),qe.ghost=ne,e.appendChild(ne),T(ne,"transform-origin",me/parseInt(ne.style.width)*100+"% "+we/parseInt(ne.style.height)*100+"%")}},_onDragStart:function(e,t){var n=this,r=e.dataTransfer,o=n.options;J("dragStart",this,{evt:e}),qe.eventCanceled?this._onDrop():(J("setupClone",this),qe.eventCanceled||((ie=q(ee)).draggable=!1,ie.style["will-change"]="",this._hideClone(),A(ie,this.options.chosenClass,!1),qe.clone=ie),n.cloneId=$e((function(){J("clone",n),qe.eventCanceled||(n.options.removeCloneOnHide||re.insertBefore(ie,ee),n._hideClone(),K({sortable:n,name:"clone"}))})),!t&&A(ee,o.dragClass,!0),t?(Se=!0,n._loopId=setInterval(n._emulateDragOver,50)):(E(document,"mouseup",n._onDrop),E(document,"touchend",n._onDrop),E(document,"touchcancel",n._onDrop),r&&(r.effectAllowed="move",o.setData&&o.setData.call(n,r,ee)),x(document,"drop",n),T(ee,"transform","translateZ(0)")),Ce=!0,n._dragStartId=$e(n._dragStarted.bind(n,t,e)),x(document,"selectstart",n),ye=!0,m&&T(document.body,"user-select","none"))},_onDragOver:function(e){var t,n,r,o,a=this.el,i=e.target,l=this.options,s=l.group,u=qe.active,h=de===s,d=l.sort,p=pe||u,g=this,f=!1;if(!Pe){if(void 0!==e.preventDefault&&e.cancelable&&e.preventDefault(),i=S(i,l.draggable,a,!0),I("dragOver"),qe.eventCanceled)return f;if(ee.contains(e.target)||i.animated&&i.animatingX&&i.animatingY||g._ignoreWhileAnimating===i)return B(!1);if(Se=!1,u&&!l.disabled&&(h?d||(r=te!==re):pe===this||(this.lastPutMode=de.checkPull(this,u,ee,e))&&s.checkPut(this,u,ee,e))){if(o="vertical"===this._getDirection(e,i),t=F(ee),I("dragOverValid"),qe.eventCanceled)return f;if(r)return te=re,z(),this._hideClone(),I("revert"),qe.eventCanceled||(oe?re.insertBefore(ee,oe):re.appendChild(ee)),B(!0);var v=R(a,l.draggable);if(!v||function(e,t,n){var r=F(R(n.el,n.options.draggable)),o=10;return t?e.clientX>r.right+o||e.clientX<=r.right&&e.clientY>r.bottom&&e.clientX>=r.left:e.clientX>r.right&&e.clientY>r.top||e.clientX<=r.right&&e.clientY>r.bottom+o}(e,o,this)&&!v.animated){if(v===ee)return B(!1);if(v&&a===e.target&&(i=v),i&&(n=F(i)),!1!==We(re,a,ee,t,i,n,e,!!i))return z(),a.appendChild(ee),te=a,X(),B(!0)}else if(v&&function(e,t,n){var r=F(j(n.el,0,n.options,!0)),o=10;return t?e.clientX<r.left-o||e.clientY<r.top&&e.clientX<r.right:e.clientY<r.top-o||e.clientY<r.bottom&&e.clientX<r.left}(e,o,this)){var b=j(a,0,l,!0);if(b===ee)return B(!1);if(n=F(i=b),!1!==We(re,a,ee,t,i,n,e,!1))return z(),a.insertBefore(ee,b),te=a,X(),B(!0)}else if(i.parentNode===a){n=F(i);var m,w,y,k=ee.parentNode!==a,x=!function(e,t,n){var r=n?e.left:e.top,o=n?e.right:e.bottom,a=n?e.width:e.height,i=n?t.left:t.top,l=n?t.right:t.bottom,s=n?t.width:t.height;return r===i||o===l||r+a/2===i+s/2}(ee.animated&&ee.toRect||t,i.animated&&i.toRect||n,o),E=o?"top":"left",M=N(i,"top","top")||N(ee,"top","top"),C=M?M.scrollTop:void 0;if(ke!==i&&(w=n[E],De=!1,Ae=!x&&l.invertSwap||k),m=function(e,t,n,r,o,a,i,l){var s=r?e.clientY:e.clientX,c=r?n.height:n.width,u=r?n.top:n.left,h=r?n.bottom:n.right,d=!1;if(!i)if(l&&Ee<c*o){if(!De&&(1===xe?s>u+c*a/2:s<h-c*a/2)&&(De=!0),De)d=!0;else if(1===xe?s<u+Ee:s>h-Ee)return-xe}else if(s>u+c*(1-o)/2&&s<h-c*(1-o)/2)return function(e){return L(ee)<L(e)?1:-1}(t);if((d=d||i)&&(s<u+c*a/2||s>h-c*a/2))return s>u+c/2?1:-1;return 0}(e,i,n,o,x?1:l.swapThreshold,null==l.invertedSwapThreshold?l.swapThreshold:l.invertedSwapThreshold,Ae,ke===i),0!==m){var _=L(ee);do{_-=m,y=te.children[_]}while(y&&("none"===T(y,"display")||y===ne))}if(0===m||y===i)return B(!1);ke=i,xe=m;var D=i.nextElementSibling,P=!1,O=We(re,a,ee,t,i,n,e,P=1===m);if(!1!==O)return 1!==O&&-1!==O||(P=1===O),Pe=!0,setTimeout(Ue,30),z(),P&&!D?a.appendChild(ee):i.parentNode.insertBefore(ee,P?D:i),M&&H(M,0,C-M.scrollTop),te=ee.parentNode,void 0===w||Ae||(Ee=Math.abs(w-F(i)[E])),X(),B(!0)}if(a.contains(ee))return B(!1)}return!1}function I(l,s){J(l,g,c({evt:e,isOwner:h,axis:o?"vertical":"horizontal",revert:r,dragRect:t,targetRect:n,canSort:d,fromSortable:p,target:i,completed:B,onMove:function(n,r){return We(re,a,ee,t,n,F(n),e,r)},changed:X},s))}function z(){I("dragOverAnimationCapture"),g.captureAnimationState(),g!==p&&p.captureAnimationState()}function B(t){return I("dragOverCompleted",{insertion:t}),t&&(h?u._hideClone():u._showClone(g),g!==p&&(A(ee,pe?pe.options.ghostClass:u.options.ghostClass,!1),A(ee,l.ghostClass,!0)),pe!==g&&g!==qe.active?pe=g:g===qe.active&&pe&&(pe=null),p===g&&(g._ignoreWhileAnimating=i),g.animateAll((function(){I("dragOverAnimationComplete"),g._ignoreWhileAnimating=null})),g!==p&&(p.animateAll(),p._ignoreWhileAnimating=null)),(i===ee&&!ee.animated||i===a&&!i.animated)&&(ke=null),l.dragoverBubble||e.rootEl||i===document||(ee.parentNode[W]._isOutsideThisEl(e.target),!t&&Ye(e)),!l.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),f=!0}function X(){ce=L(ee),he=L(ee,l.draggable),K({sortable:g,name:"change",toEl:a,newIndex:ce,newDraggableIndex:he,originalEvent:e})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){E(document,"mousemove",this._onTouchMove),E(document,"touchmove",this._onTouchMove),E(document,"pointermove",this._onTouchMove),E(document,"dragover",Ye),E(document,"mousemove",Ye),E(document,"touchmove",Ye)},_offUpEvents:function(){var e=this.el.ownerDocument;E(e,"mouseup",this._onDrop),E(e,"touchend",this._onDrop),E(e,"pointerup",this._onDrop),E(e,"touchcancel",this._onDrop),E(document,"selectstart",this)},_onDrop:function(e){var t=this.el,n=this.options;ce=L(ee),he=L(ee,n.draggable),J("drop",this,{evt:e}),te=ee&&ee.parentNode,ce=L(ee),he=L(ee,n.draggable),qe.eventCanceled||(Ce=!1,Ae=!1,De=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),Ze(this.cloneId),Ze(this._dragStartId),this.nativeDraggable&&(E(document,"drop",this),E(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),m&&T(document.body,"user-select",""),T(ee,"transform",""),e&&(ye&&(e.cancelable&&e.preventDefault(),!n.dropBubble&&e.stopPropagation()),ne&&ne.parentNode&&ne.parentNode.removeChild(ne),(re===te||pe&&"clone"!==pe.lastPutMode)&&ie&&ie.parentNode&&ie.parentNode.removeChild(ie),ee&&(this.nativeDraggable&&E(ee,"dragend",this),Ve(ee),ee.style["will-change"]="",ye&&!Ce&&A(ee,pe?pe.options.ghostClass:this.options.ghostClass,!1),A(ee,this.options.chosenClass,!1),K({sortable:this,name:"unchoose",toEl:te,newIndex:null,newDraggableIndex:null,originalEvent:e}),re!==te?(ce>=0&&(K({rootEl:te,name:"add",toEl:te,fromEl:re,originalEvent:e}),K({sortable:this,name:"remove",toEl:te,originalEvent:e}),K({rootEl:te,name:"sort",toEl:te,fromEl:re,originalEvent:e}),K({sortable:this,name:"sort",toEl:te,originalEvent:e})),pe&&pe.save()):ce!==se&&ce>=0&&(K({sortable:this,name:"update",toEl:te,originalEvent:e}),K({sortable:this,name:"sort",toEl:te,originalEvent:e})),qe.active&&(null!=ce&&-1!==ce||(ce=se,he=ue),K({sortable:this,name:"end",toEl:te,originalEvent:e}),this.save())))),this._nulling()},_nulling:function(){J("nulling",this),re=ee=te=ne=oe=ie=ae=le=ge=fe=ye=ce=he=se=ue=ke=xe=pe=de=qe.dragged=qe.ghost=qe.clone=qe.active=null,Oe.forEach((function(e){e.checked=!0})),Oe.length=ve=be=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":ee&&(this._onDragOver(e),function(e){e.dataTransfer&&(e.dataTransfer.dropEffect="move");e.cancelable&&e.preventDefault()}(e));break;case"selectstart":e.preventDefault()}},toArray:function(){for(var e,t=[],n=this.el.children,r=0,o=n.length,a=this.options;r<o;r++)S(e=n[r],a.draggable,this.el,!1)&&t.push(e.getAttribute(a.dataIdAttr)||Ge(e));return t},sort:function(e,t){var n={},r=this.el;this.toArray().forEach((function(e,t){var o=r.children[t];S(o,this.options.draggable,r,!1)&&(n[e]=o)}),this),t&&this.captureAnimationState(),e.forEach((function(e){n[e]&&(r.removeChild(n[e]),r.appendChild(n[e]))})),t&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,t){return S(e,t||this.options.draggable,this.el,!1)},option:function(e,t){var n=this.options;if(void 0===t)return n[e];var r=$.modifyOption(this,e,t);n[e]=void 0!==r?r:t,"group"===e&&ze(n)},destroy:function(){J("destroy",this);var e=this.el;e[W]=null,E(e,"mousedown",this._onTapStart),E(e,"touchstart",this._onTapStart),E(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(E(e,"dragover",this),E(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),(function(e){e.removeAttribute("draggable")})),this._onDrop(),this._disableDelayedDragEvents(),_e.splice(_e.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!le){if(J("hideClone",this),qe.eventCanceled)return;T(ie,"display","none"),this.options.removeCloneOnHide&&ie.parentNode&&ie.parentNode.removeChild(ie),le=!0}},_showClone:function(e){if("clone"===e.lastPutMode){if(le){if(J("showClone",this),qe.eventCanceled)return;ee.parentNode!=re||this.options.group.revertClone?oe?re.insertBefore(ie,oe):re.appendChild(ie):re.insertBefore(ie,ee),this.options.group.revertClone&&this.animate(ee,ie),T(ie,"display",""),le=!1}}else this._hideClone()}},Ie&&x(document,"touchmove",(function(e){(qe.active||Ce)&&e.cancelable&&e.preventDefault()})),qe.utils={on:x,off:E,css:T,find:O,is:function(e,t){return!!S(e,t,e,!1)},extend:function(e,t){if(e&&t)for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e},throttle:Y,closest:S,toggleClass:A,clone:q,index:L,nextTick:$e,cancelNextTick:Ze,detectDirection:Le,getChild:j},qe.get=function(e){return e[W]},qe.mount=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];t[0].constructor===Array&&(t=t[0]),t.forEach((function(e){if(!e.prototype||!e.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(e));e.utils&&(qe.utils=c(c({},qe.utils),e.utils)),$.mount(e)}))},qe.create=function(e,t){return new qe(e,t)},qe.version="1.14.0";var Qe,Je,Ke,et=[];function tt(){et.forEach((function(e){clearInterval(e.pid)})),et=[]}Y((function(e,t,n,r){if(t.scroll){var o,a=(e.touches?e.touches[0]:e).clientX,i=(e.touches?e.touches[0]:e).clientY,l=t.scrollSensitivity,s=t.scrollSpeed,c=I(),u=!1;Je!==n&&(Je=n,tt(),Qe=t.scroll,o=t.scrollFn,!0===Qe&&(Qe=B(n,!0)));var h=0,d=Qe;do{var p=d,g=F(p),f=g.top,v=g.bottom,b=g.left,m=g.right,w=g.width,y=g.height,k=void 0,x=void 0,E=p.scrollWidth,M=p.scrollHeight,C=T(p),S=p.scrollLeft,_=p.scrollTop;p===c?(k=w<E&&("auto"===C.overflowX||"scroll"===C.overflowX||"visible"===C.overflowX),x=y<M&&("auto"===C.overflowY||"scroll"===C.overflowY||"visible"===C.overflowY)):(k=w<E&&("auto"===C.overflowX||"scroll"===C.overflowX),x=y<M&&("auto"===C.overflowY||"scroll"===C.overflowY));var D=k&&(Math.abs(m-a)<=l&&S+w<E)-(Math.abs(b-a)<=l&&!!S),A=x&&(Math.abs(v-i)<=l&&_+y<M)-(Math.abs(f-i)<=l&&!!_);if(!et[h])for(var P=0;P<=h;P++)et[P]||(et[P]={});et[h].vx==D&&et[h].vy==A&&et[h].el===p||(et[h].el=p,et[h].vx=D,et[h].vy=A,clearInterval(et[h].pid),0==D&&0==A||(u=!0,et[h].pid=setInterval(function(){r&&0===this.layer&&qe.active._onTouchMove(Ke);var t=et[this.layer].vy?et[this.layer].vy*s:0,n=et[this.layer].vx?et[this.layer].vx*s:0;"function"==typeof o&&"continue"!==o.call(qe.dragged.parentNode[W],n,t,e,Ke,et[this.layer].el)||H(et[this.layer].el,n,t)}.bind({layer:h}),24))),h++}while(t.bubbleScroll&&d!==c&&(d=B(d,!1)));u}}),30);var nt=function(e){var t=e.originalEvent,n=e.putSortable,r=e.dragEl,o=e.activeSortable,a=e.dispatchSortableEvent,i=e.hideGhostForTarget,l=e.unhideGhostForTarget;if(t){var s=n||o;i();var c=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,u=document.elementFromPoint(c.clientX,c.clientY);l(),s&&!s.el.contains(u)&&(a("spill"),this.onSpill({dragEl:r,putSortable:n}))}};function rt(){}function ot(){}rt.prototype={startIndex:null,dragStart:function(e){var t=e.oldDraggableIndex;this.startIndex=t},onSpill:function(e){var t=e.dragEl,n=e.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var r=j(this.sortable.el,this.startIndex,this.options);r?this.sortable.el.insertBefore(t,r):this.sortable.el.appendChild(t),this.sortable.animateAll(),n&&n.animateAll()},drop:nt},d(rt,{pluginName:"revertOnSpill"}),ot.prototype={onSpill:function(e){var t=e.dragEl,n=e.putSortable||this.sortable;n.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),n.animateAll()},drop:nt},d(ot,{pluginName:"removeOnSpill"});var at=qe;function it(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=document.createElement(e);return Object.entries(t).forEach((e=>{let[t,r]=e;if("textContent"===t)n.textContent=r;else if("innerHTML"===t)n.innerHTML=r;else if("append"===t){if(r instanceof NodeList){const e=[];r.forEach((function(t){e.push(t.cloneNode(!0))})),r=e}n.append(...r)}else n.setAttribute(t,r)})),n}function lt(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};n.class="color"+(ct(e)?" light":""),n.style="background-color:"+e;const r=it("div",n);return!0===t&&(r.innerHTML=`\n            <span class="action edit">\n                ${st(e)}\n            </span>\n            <span class="action move">\n                <svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><g class="icon-color" transform="matrix(1.05, 0, 0, 1.05, -0.5, -0.5)"><path d="M14.823 7.823l-2.396-2.396a.25.25 0 0 0-.427.177V7H9V4h1.396a.25.25 0 0 0 .177-.427L8.177 1.177a.25.25 0 0 0-.354 0L5.427 3.573A.25.25 0 0 0 5.604 4H7v3H4V5.604a.25.25 0 0 0-.427-.177L1.177 7.823a.25.25 0 0 0 0 .354l2.396 2.396A.25.25 0 0 0 4 10.396V9h3v3H5.604a.25.25 0 0 0-.177.427l2.396 2.396a.25.25 0 0 0 .354 0l2.396-2.396a.25.25 0 0 0-.177-.427H9V9h3v1.396a.25.25 0 0 0 .427.177l2.396-2.396a.25.25 0 0 0 0-.354z"></path></g></svg>\n            </span>\n            <span class="action delete">\n                <svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><g class="icon-color" transform="matrix(1.6, 0, 0, 1.6, -4.9, -4.9)"><path d="M11.9 5.5L9.4 8l2.5 2.5c.2.2.2.5 0 .7l-.7.7c-.2.2-.5.2-.7 0L8 9.4l-2.5 2.5c-.2.2-.5.2-.7 0l-.7-.7c-.2-.2-.2-.5 0-.7L6.6 8 4.1 5.5c-.2-.2-.2-.5 0-.7l.7-.7c.2-.2.5-.2.7 0L8 6.6l2.5-2.5c.2-.2.5-.2.7 0l.7.7c.2.2.2.5 0 .7z"></path></g></svg>\n            </span>\n            <span class="action add">\n                <svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><g class="icon-color"><path d="M12.5 9H9v3.5c0 .3-.2.5-.5.5h-1c-.3 0-.5-.2-.5-.5V9H3.5c-.3 0-.5-.2-.5-.5v-1c0-.3.2-.5.5-.5H7V3.5c0-.3.2-.5.5-.5h1c.3 0 .5.2.5.5V7h3.5c.3 0 .5.2.5.5v1c0 .3-.2.5-.5.5z"/></g></svg>\n            </span>\n        `),r}function st(e){return e.alpha()<1?e.hexa():e.hex()}function ct(e){return e.luminosity()>.6}var ut=o(344);class ht extends HTMLElement{static get observedAttributes(){return["value"]}constructor(){var e,t,n;super(),n='\n<div class="picking-area">\n    <div class="picker"></div>\n</div>\n<div class="control-area">\n    <div class="hue">\n        <div class="slider-picker" style="left: -1px;"></div>\n    </div>\n    <div class="hue-input">\n        <label>H <input type="number" min="0" max="359" name="h"/></label>\n        <label>S <input type="number" min="0" max="100" name="s"/></label>\n        <label>V <input type="number" min="0" max="100" name="v"/></label>\n    </div>\n    <div class="alpha">\n        <div class="alpha-mask">\n            <div class="slider-picker" style="left: calc(100% + 1px);"></div>\n        </div>\n    </div>\n    <div class="rgb-input">\n        <label>R <input type="number" min="0" max="255" name="r"/></label>\n        <label>G <input type="number" min="0" max="255" name="g"/></label>\n        <label>B <input type="number" min="0" max="255" name="b"/></label>\n        <label>A <input type="number" min="0" max="1" step="0.01" name="a"/></label>\n    </div>\n</div>\n    ',(t="template")in(e=this)?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,this.pickerWrapper=it("div",{class:"picker-wrapper",innerHTML:this.template});const r=it("style",{textContent:ut.Z}),o=it("div",{class:"backdrop",append:[this.pickerWrapper]});this.attachShadow({mode:"open"}).append(r,o),this.colorArea=this.shadowRoot.querySelector(".picking-area"),this.colorPicker=this.shadowRoot.querySelector(".picking-area > .picker"),this.hueArea=this.shadowRoot.querySelector(".hue"),this.huePicker=this.shadowRoot.querySelector(".hue .slider-picker"),this.alphaArea=this.shadowRoot.querySelector(".alpha"),this.alphaPicker=this.shadowRoot.querySelector(".alpha .slider-picker"),this.createPickerArea(this.colorArea,((e,t,n)=>{const r=100-100*n/e.height,o=100*t/e.width;this.color=this.color.saturationv(o).value(r)})),this.createPickerArea(this.hueArea,((e,t)=>{this.color=this.color.hue(359*t/e.width)})),this.createPickerArea(this.alphaArea,((e,t)=>{this.color=this.color.alpha((t/e.width).toFixed(2))})),this.shadowRoot.querySelectorAll("input").forEach((e=>{e.addEventListener("change",(t=>{const n=e.getAttribute("name"),r=parseInt(t.target.value);let o=this.color;"h"===n?o=o.hue(r):"s"===n?o=o.saturationv(r):"v"===n?o=o.value(r):"r"===n?o=o.red(r):"g"===n?o=o.green(r):"b"===n?o=o.blue(r):"a"===n&&(o=o.alpha(parseFloat(t.target.value).toFixed(2))),this.color=o}))})),o.addEventListener("click",(e=>{!0!==this.isMoving&&!1===this.pickerWrapper.contains(e.target)&&(this.style.display="none",this.triggerColorChanged())}))}connectedCallback(){this.__proto__=customElements.get("color-picker").prototype}attributeChangedCallback(e,t,n){"value"===e&&(this.color=l()(n))}get color(){return this._value||l()("#fff")}set color(e){this._value=e,this.shadowRoot.querySelectorAll("input").forEach((t=>{const n=t.getAttribute("name");"h"===n?t.value=parseInt(e.hue(),10).toString():"s"===n?t.value=parseInt(e.saturationv(),10).toString():"v"===n?t.value=parseInt(e.value(),10).toString():"r"===n?t.value=parseInt(e.red(),10).toString():"g"===n?t.value=parseInt(e.green(),10).toString():"b"===n?t.value=parseInt(e.blue(),10).toString():"a"===n&&(t.value=parseFloat(e.alpha()).toFixed(2).toString())})),this.shadowRoot.host.style.setProperty("--color-area-bg-color",l()(e).saturationv(100).value(100).rgb()),this.shadowRoot.host.style.setProperty("--alpha-bg-color",e.rgb());const t=this.colorPicker.getBoundingClientRect().width/2;this.colorPicker.style.top=(100-e.value())/100*this.colorArea.getBoundingClientRect().height-t+"px",this.colorPicker.style.left=e.saturationv()/100*this.colorArea.getBoundingClientRect().width-t+"px",this.huePicker.style.left=Math.max(e.hue()/359*this.hueArea.getBoundingClientRect().width-2,-1)+"px",this.alphaPicker.style.left=Math.max(e.alpha()*this.alphaArea.getBoundingClientRect().width-2,-1)+"px",this.triggerColorChanged()}createPickerArea(e,t){this.setMouseTracking(e,(n=>{let r=e.getBoundingClientRect(),o=n.clientX-r.left,a=n.clientY-r.top;o>r.width&&(o=r.width),a>r.width&&(a=r.height),o<0&&(o=0),a<0&&(a=0),t(r,o,a)}))}setMouseTracking(e,t){e.addEventListener("mousedown",(e=>{this.isMoving=!0,t(e),this.shadowRoot.addEventListener("mousemove",t)})),this.pickerWrapper.addEventListener("mouseup",(()=>{this.shadowRoot.removeEventListener("mousemove",t),this.isMoving=!1}))}get triggerColorChanged(){return void 0===this._colorChangedDispatcher&&(this._colorChangedDispatcher=function(e,t,n){let r;return function(){const o=this,a=arguments,i=function(){r=null,n||t.apply(o,a)},l=n&&!r;clearTimeout(r),r=setTimeout(i,e),l&&t.apply(o,a)}}(250,(()=>{this.dispatchEvent(new CustomEvent("colorChanged",{detail:{color:this.color}}))}))),this._colorChangedDispatcher}}var dt=o(955);class pt extends HTMLElement{static get observedAttributes(){return["ref"]}constructor(){super(),this.colorPalette=it("div",{class:"color-palette"});const e=it("style",{textContent:dt.Z});this.attachShadow({mode:"open"}).append(e,this.colorPalette),this.mode=this.getAttribute("mode")||null,"preview"===this.mode?(console.debug("renderPreview"),this.renderPreview()):(console.debug("renderPalette (mode: "+this.mode+")"),this.renderPalette())}get ref(){return this._ref}set ref(e){null!==e&&(this._ref=e,this._input=document.getElementById(this.ref),console.debug("reference: "+this._ref,this._input),this.renderPaletteContent())}get value(){return this._input.value.split("|").filter((e=>!!e)).map((e=>l()(e)))}set value(e){console.debug("old-value:",this._input),this._input.value=[...e.querySelectorAll(".color")].map((e=>l()(e.style.backgroundColor))).join("|"),console.debug("new-value:",this._input)}connectedCallback(){this.__proto__=customElements.get("color-palette").prototype}attributeChangedCallback(e,t,n){"ref"===e&&(this.ref=n)}renderPreview(){this.colorPalette.classList.add("preview"),this.colorPalette.addEventListener("click",(()=>{const e=it("color-palette",{ref:this.ref,append:this.childNodes}),t=it("div",{class:"color-palette-modal",append:[e]}),n=r().advanced({title:"Color Palette",content:t,size:r().sizes.large,additionalCssClasses:["color-palette-modal-wrapper"]});console.debug("paletteModalOpened",this.colorPalette),n.on("hide.bs.modal",(()=>{this.renderPaletteContent(),console.debug("paletteModalClosed",this.colorPalette)}))}))}renderPalette(){const e=it("color-picker",{style:"display:none"});e.addEventListener("colorChanged",(e=>{this.currentColorItem.classList.toggle("light",ct(e.detail.color)),this.currentColorItem.style.backgroundColor=e.detail.color,this.currentColorItem.querySelector(".edit").textContent=st(e.detail.color),this.value=this.colorPalette,console.debug("colorChanged: "+e.detail.color,this.currentColorItem)})),this.shadowRoot.appendChild(e),this.colorPalette.classList.add("editor"),this.colorPalette.addEventListener("click",(t=>{const n=t.target.closest(".color"),r=t.target.closest(".action");if(null!==r&&null!==n)if(!0===r.classList.contains("add")&&null!==t.target.closest("svg")){var o;let e=l()(n.style.backgroundColor);e=void 0!==(null===(o=n.nextSibling)||void 0===o?void 0:o.style.backgroundColor)?e.mix(l()(n.nextSibling.style.backgroundColor)):e.darken(.1),n.parentNode.insertBefore(lt(e,!0),n.nextSibling),this.value=this.colorPalette}else!0===r.classList.contains("edit")?(this.currentColorItem=n,e.style.display="",e.setAttribute("value",n.style.backgroundColor)):!0===r.classList.contains("delete")&&(n.remove(),this.value=this.colorPalette,0===this.value.length&&this.renderPaletteContent())})),this.colorPalette.addEventListener("wheel",(e=>{e.preventDefault(),this.colorPalette.scrollLeft+=e.deltaY})),at.create(this.colorPalette,{handle:".action.move",onEnd:()=>this.value=this.colorPalette})}renderPaletteContent(){if(0!==this.value.length||"preview"!==this.mode){if(0===this.value.length){const e=it("button",{class:"start-button"});return e.appendChild(it("slot",{name:"newButtonIcon"})),e.appendChild(it("slot",{name:"newButtonText"})),e.addEventListener("click",(e=>{e.preventDefault(),this._input.value=["#829d1e","#b0b108","#769943","#575756","#6e706e","#8a8572","#b9baab"].join("|"),this.renderPaletteContent()})),void this.colorPalette.replaceChildren(e)}this.colorPalette.innerHTML="",this.value.forEach((e=>{this.colorPalette.appendChild(lt(e,"preview"!==this.mode))}))}else this.colorPalette.replaceChildren(it("slot",{name:"empty",class:"new-palette"}))}}t().ready().then((()=>{customElements.define("color-picker",ht),customElements.define("color-palette",pt)})).catch((()=>{console.error("Failed to load DOM for processing color palette inputs!")}))}(),a}()}));
