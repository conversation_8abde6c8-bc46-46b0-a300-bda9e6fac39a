/*--------------------------------

Nucleo Web Font
Generated using nucleoapp.com

-------------------------------- */
@font-face {
  font-family: 'Nucleo';
  src: url('../../fonts/Nucleo.eot');
  src: url('../../fonts/Nucleo.eot') format('embedded-opentype'), url('../../fonts/Nucleo.woff2') format('woff2'), url('../../fonts/Nucleo.woff') format('woff'), url('../../fonts/Nucleo.ttf') format('truetype'), url('../../fonts/Nucleo.svg') format('svg');
  font-weight: normal;
  font-style: normal;
}

/* --------------------------------

Reset

-------------------------------- */

*, *::after, *::before {
  box-sizing: inherit;
}

* {
  font: inherit;
}

html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, 
figure, figcaption, footer, header, hgroup, 
menu, nav, output, ruby, section, summary,
time, mark, audio, video, hr {
  margin: 0;
  padding: 0;
  border: 0;
}

html {
  box-sizing: border-box;
}

body {
  background-color: white;
  font-family: system-ui, sans-serif;
  color: hsl(240, 4%, 20%);
  padding: 1em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

article, aside, details, figcaption, figure, 
footer, header, hgroup, menu, nav, section, main, form legend {
  display: block;
}

ol, ul {
  list-style: none;
}

button, input, textarea, select {
  margin: 0;
}

a {
  color: hsl(230, 93%, 66%);
}

/* --------------------------------

Main components

-------------------------------- */
header {
  text-align: center;
  margin: 3em auto;
}

header h1 {
  font-size: 2.6rem;
  font-weight: 600;
}

header p {
  font-size: 1rem;
  margin-top: 1em;
  color: hsla(0, 0%, 0%, 0.5);
}

ul {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

ul li {
  border-radius: .4em;
  transition: background-color .2s;
  user-select: none;
  overflow: hidden;
  text-align: center;
  padding: 1em;
}

ul li:hover {
  background: hsla(0, 0%, 0%, 0.05);
}

ul p, ul em, ul input {
  display: block;
  font-size: 0.75rem;
  color: hsla(0, 0%, 0%, 0.5);
  user-select: auto;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  max-width: 6rem;
}

ul p {
  padding: 8px 0 4px;
}

ul p::selection, ul em::selection {
  background: hsl(230, 93%, 66%);
  color: #fff;
}

ul p::-moz-selection, ul em::-moz-selection {
  background: hsl(230, 93%, 66%);
  color: #fff;
}

ul em {
  margin-bottom: 4px;
}

ul em::before {
  content: '[';
}
ul em::after {
  content: ']';
}

ul input {
  text-align: center;
  background: transparent;
  border: none;
  box-shadow: none;
  outline: none;
  font-family: auto;
}

/* --------------------------------

icons

-------------------------------- */
.icon {
  display: inline-block;
  font: normal normal normal 32px/1 'Nucleo';
  speak: none;
  text-transform: none;
  /* Better Font Rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/*------------------------
  font icons
-------------------------*/

.nc-2x-drag-down::before {
  content: "\ea01";
}

.nc-2x-drag-up::before {
  content: "\ea02";
}

.nc-2x-swipe-down::before {
  content: "\ea03";
}

.nc-2x-swipe-left::before {
  content: "\ea04";
}

.nc-2x-swipe-right::before {
  content: "\ea05";
}

.nc-2x-swipe-up::before {
  content: "\ea06";
}

.nc-2x-tap::before {
  content: "\ea07";
}

.nc-3d-29::before {
  content: "\ea08";
}

.nc-3d-glasses::before {
  content: "\ea09";
}

.nc-3d-model::before {
  content: "\ea0a";
}

.nc-3d-printing::before {
  content: "\ea0b";
}

.nc-3x-swipe-left::before {
  content: "\ea0c";
}

.nc-3x-swipe-right::before {
  content: "\ea0d";
}

.nc-3x-swipe-up::before {
  content: "\ea0e";
}

.nc-3x-tap::before {
  content: "\ea0f";
}

.nc-4x-swipe-left::before {
  content: "\ea10";
}

.nc-4x-swipe-right::before {
  content: "\ea11";
}

.nc-4x-swipe-up::before {
  content: "\ea12";
}

.nc-a-chart::before {
  content: "\ea13";
}

.nc-a-chat::before {
  content: "\ea14";
}

.nc-a-tag::before {
  content: "\ea15";
}

.nc-a-tag-add::before {
  content: "\ea16";
}

.nc-a-tag-remove::before {
  content: "\ea17";
}

.nc-abc::before {
  content: "\ea18";
}

.nc-access-key::before {
  content: "\ea19";
}

.nc-accessibility::before {
  content: "\ea1a";
}

.nc-accessibility-lift::before {
  content: "\ea1b";
}

.nc-account::before {
  content: "\ea1c";
}

.nc-acorn::before {
  content: "\ea1d";
}

.nc-active-38::before {
  content: "\ea1e";
}

.nc-active-40::before {
  content: "\ea1f";
}

.nc-adaptive-bike::before {
  content: "\ea20";
}

.nc-add::before {
  content: "\ea21";
}

.nc-add-fav::before {
  content: "\ea22";
}

.nc-add-favorite::before {
  content: "\ea23";
}

.nc-add-like::before {
  content: "\ea24";
}

.nc-add-notification::before {
  content: "\ea25";
}

.nc-add-to-cart::before {
  content: "\ea26";
}

.nc-add-to-cart-2::before {
  content: "\ea27";
}

.nc-address-book::before {
  content: "\ea28";
}

.nc-adult-content::before {
  content: "\ea29";
}

.nc-agenda::before {
  content: "\ea2a";
}

.nc-agenda-bookmark::before {
  content: "\ea2b";
}

.nc-ai::before {
  content: "\ea2c";
}

.nc-ai-generated-img::before {
  content: "\ea2d";
}

.nc-air-baloon::before {
  content: "\ea2e";
}

.nc-air-bomb::before {
  content: "\ea2f";
}

.nc-air-conditioner::before {
  content: "\ea30";
}

.nc-airbag::before {
  content: "\ea31";
}

.nc-airplane::before {
  content: "\ea32";
}

.nc-airport::before {
  content: "\ea33";
}

.nc-airport-trolley::before {
  content: "\ea34";
}

.nc-alarm::before {
  content: "\ea35";
}

.nc-alarm-add::before {
  content: "\ea36";
}

.nc-alarm-disable::before {
  content: "\ea37";
}

.nc-album::before {
  content: "\ea38";
}

.nc-alcohol::before {
  content: "\ea39";
}

.nc-algorithm::before {
  content: "\ea3a";
}

.nc-alien-29::before {
  content: "\ea3b";
}

.nc-alien-33::before {
  content: "\ea3c";
}

.nc-align-bottom::before {
  content: "\ea3d";
}

.nc-align-center-horizontal::before {
  content: "\ea3e";
}

.nc-align-center-vertical::before {
  content: "\ea3f";
}

.nc-align-left::before {
  content: "\ea40";
}

.nc-align-right::before {
  content: "\ea41";
}

.nc-align-top::before {
  content: "\ea42";
}

.nc-all-directions::before {
  content: "\ea43";
}

.nc-alpha-order::before {
  content: "\ea44";
}

.nc-ambulance::before {
  content: "\ea45";
}

.nc-ampersand::before {
  content: "\ea46";
}

.nc-analytics::before {
  content: "\ea47";
}

.nc-anchor::before {
  content: "\ea48";
}

.nc-android::before {
  content: "\ea49";
}

.nc-angle::before {
  content: "\ea4a";
}

.nc-angry-10::before {
  content: "\ea4b";
}

.nc-angry-44::before {
  content: "\ea4c";
}

.nc-animation-14::before {
  content: "\ea4d";
}

.nc-animation-31::before {
  content: "\ea4e";
}

.nc-animation-32::before {
  content: "\ea4f";
}

.nc-antenna::before {
  content: "\ea50";
}

.nc-anti-shake::before {
  content: "\ea51";
}

.nc-apartment::before {
  content: "\ea52";
}

.nc-aperture::before {
  content: "\ea53";
}

.nc-api::before {
  content: "\ea54";
}

.nc-app::before {
  content: "\ea55";
}

.nc-app-services::before {
  content: "\ea56";
}

.nc-app-store::before {
  content: "\ea57";
}

.nc-apple::before {
  content: "\ea58";
}

.nc-apple-2::before {
  content: "\ea59";
}

.nc-appointment::before {
  content: "\ea5a";
}

.nc-apps::before {
  content: "\ea5b";
}

.nc-apron::before {
  content: "\ea5c";
}

.nc-arcade::before {
  content: "\ea5d";
}

.nc-archer::before {
  content: "\ea5e";
}

.nc-archery::before {
  content: "\ea5f";
}

.nc-archery-target::before {
  content: "\ea60";
}

.nc-archive::before {
  content: "\ea61";
}

.nc-archive-check::before {
  content: "\ea62";
}

.nc-archive-content::before {
  content: "\ea63";
}

.nc-archive-doc::before {
  content: "\ea64";
}

.nc-archive-doc-check::before {
  content: "\ea65";
}

.nc-archive-drawer::before {
  content: "\ea66";
}

.nc-archive-file::before {
  content: "\ea67";
}

.nc-archive-file-check::before {
  content: "\ea68";
}

.nc-armchair::before {
  content: "\ea69";
}

.nc-armor::before {
  content: "\ea6a";
}

.nc-army::before {
  content: "\ea6b";
}

.nc-arrow-bottom-left::before {
  content: "\ea6c";
}

.nc-arrow-bottom-right::before {
  content: "\ea6d";
}

.nc-arrow-down::before {
  content: "\ea6e";
}

.nc-arrow-down-2::before {
  content: "\ea6f";
}

.nc-arrow-down-3::before {
  content: "\ea70";
}

.nc-arrow-e::before {
  content: "\ea71";
}

.nc-arrow-left::before {
  content: "\ea72";
}

.nc-arrow-left-2::before {
  content: "\ea73";
}

.nc-arrow-left-3::before {
  content: "\ea74";
}

.nc-arrow-n::before {
  content: "\ea75";
}

.nc-arrow-right::before {
  content: "\ea76";
}

.nc-arrow-right-2::before {
  content: "\ea77";
}

.nc-arrow-right-3::before {
  content: "\ea78";
}

.nc-arrow-s::before {
  content: "\ea79";
}

.nc-arrow-sm-down::before {
  content: "\ea7a";
}

.nc-arrow-sm-left::before {
  content: "\ea7b";
}

.nc-arrow-sm-right::before {
  content: "\ea7c";
}

.nc-arrow-tool::before {
  content: "\ea7d";
}

.nc-arrow-top-left::before {
  content: "\ea7e";
}

.nc-arrow-top-right::before {
  content: "\ea7f";
}

.nc-arrow-up::before {
  content: "\ea80";
}

.nc-arrow-up-2::before {
  content: "\ea81";
}

.nc-arrow-up-3::before {
  content: "\ea82";
}

.nc-arrow-w::before {
  content: "\ea83";
}

.nc-arrows-expand::before {
  content: "\ea84";
}

.nc-arrows-expand-2::before {
  content: "\ea85";
}

.nc-arrows-fullscreen::before {
  content: "\ea86";
}

.nc-arrows-fullscreen-2::before {
  content: "\ea87";
}

.nc-arrows-maximize::before {
  content: "\ea88";
}

.nc-arrows-maximize-2::before {
  content: "\ea89";
}

.nc-arrows-opposite-directions::before {
  content: "\ea8a";
}

.nc-arrows-same-direction::before {
  content: "\ea8b";
}

.nc-artboard::before {
  content: "\ea8c";
}

.nc-artificial-brain::before {
  content: "\ea8d";
}

.nc-artificial-intelligence::before {
  content: "\ea8e";
}

.nc-assault-rifle::before {
  content: "\ea8f";
}

.nc-astronaut::before {
  content: "\ea90";
}

.nc-astronomy::before {
  content: "\ea91";
}

.nc-at-sign::before {
  content: "\ea92";
}

.nc-at-sign-2::before {
  content: "\ea93";
}

.nc-athletics::before {
  content: "\ea94";
}

.nc-atm::before {
  content: "\ea95";
}

.nc-atom::before {
  content: "\ea96";
}

.nc-attach::before {
  content: "\ea97";
}

.nc-attachment::before {
  content: "\ea98";
}

.nc-aubergine::before {
  content: "\ea99";
}

.nc-audio-description::before {
  content: "\ea9a";
}

.nc-audio-jack::before {
  content: "\ea9b";
}

.nc-audio-mixer::before {
  content: "\ea9c";
}

.nc-augmented-reality::before {
  content: "\ea9d";
}

.nc-auto-flash::before {
  content: "\ea9e";
}

.nc-auto-flash-2::before {
  content: "\ea9f";
}

.nc-auto-focus::before {
  content: "\eaa0";
}

.nc-automated-logistics::before {
  content: "\eaa1";
}

.nc-avocado::before {
  content: "\eaa2";
}

.nc-award::before {
  content: "\eaa3";
}

.nc-award-49::before {
  content: "\eaa4";
}

.nc-axe::before {
  content: "\eaa5";
}

.nc-b-chart::before {
  content: "\eaa6";
}

.nc-b-comment::before {
  content: "\eaa7";
}

.nc-b-eye::before {
  content: "\eaa8";
}

.nc-baby::before {
  content: "\eaa9";
}

.nc-baby-bottle::before {
  content: "\eaaa";
}

.nc-baby-car-seat::before {
  content: "\eaab";
}

.nc-baby-clothes::before {
  content: "\eaac";
}

.nc-baby-monitor::before {
  content: "\eaad";
}

.nc-baby-stroller::before {
  content: "\eaae";
}

.nc-back-arrow::before {
  content: "\eaaf";
}

.nc-backpack::before {
  content: "\eab0";
}

.nc-backpack-2::before {
  content: "\eab1";
}

.nc-backpack-57::before {
  content: "\eab2";
}

.nc-backpack-58::before {
  content: "\eab3";
}

.nc-backup::before {
  content: "\eab4";
}

.nc-backward::before {
  content: "\eab5";
}

.nc-bacon::before {
  content: "\eab6";
}

.nc-badge::before {
  content: "\eab7";
}

.nc-bag::before {
  content: "\eab8";
}

.nc-bag-16::before {
  content: "\eab9";
}

.nc-bag-17::before {
  content: "\eaba";
}

.nc-bag-20::before {
  content: "\eabb";
}

.nc-bag-21::before {
  content: "\eabc";
}

.nc-bag-22::before {
  content: "\eabd";
}

.nc-bag-49::before {
  content: "\eabe";
}

.nc-bag-50::before {
  content: "\eabf";
}

.nc-bag-add-18::before {
  content: "\eac0";
}

.nc-bag-add-21::before {
  content: "\eac1";
}

.nc-bag-delivery::before {
  content: "\eac2";
}

.nc-bag-edit::before {
  content: "\eac3";
}

.nc-bag-remove-19::before {
  content: "\eac4";
}

.nc-bag-remove-22::before {
  content: "\eac5";
}

.nc-bag-time::before {
  content: "\eac6";
}

.nc-baggage-collection::before {
  content: "\eac7";
}

.nc-baggage-scale::before {
  content: "\eac8";
}

.nc-baguette::before {
  content: "\eac9";
}

.nc-bahai::before {
  content: "\eaca";
}

.nc-bakery::before {
  content: "\eacb";
}

.nc-balance::before {
  content: "\eacc";
}

.nc-baloon::before {
  content: "\eacd";
}

.nc-bamboo::before {
  content: "\eace";
}

.nc-ban::before {
  content: "\eacf";
}

.nc-banana::before {
  content: "\ead0";
}

.nc-bank-statement::before {
  content: "\ead1";
}

.nc-barbecue::before {
  content: "\ead2";
}

.nc-barbecue-15::before {
  content: "\ead3";
}

.nc-barbecue-tools::before {
  content: "\ead4";
}

.nc-barbell::before {
  content: "\ead5";
}

.nc-barbershop::before {
  content: "\ead6";
}

.nc-barcode::before {
  content: "\ead7";
}

.nc-barcode-qr::before {
  content: "\ead8";
}

.nc-barcode-scan::before {
  content: "\ead9";
}

.nc-bars-anim::before {
  content: "\eada";
}

.nc-bars-anim-2::before {
  content: "\eadb";
}

.nc-bars-anim-3::before {
  content: "\eadc";
}

.nc-baseball::before {
  content: "\eadd";
}

.nc-baseball-bat::before {
  content: "\eade";
}

.nc-baseball-pitch::before {
  content: "\eadf";
}

.nc-baseball-player::before {
  content: "\eae0";
}

.nc-basket::before {
  content: "\eae1";
}

.nc-basket-add::before {
  content: "\eae2";
}

.nc-basket-edit::before {
  content: "\eae3";
}

.nc-basket-favorite::before {
  content: "\eae4";
}

.nc-basket-remove::before {
  content: "\eae5";
}

.nc-basket-search::before {
  content: "\eae6";
}

.nc-basket-share::before {
  content: "\eae7";
}

.nc-basket-simple::before {
  content: "\eae8";
}

.nc-basket-simple-add::before {
  content: "\eae9";
}

.nc-basket-simple-remove::before {
  content: "\eaea";
}

.nc-basket-update::before {
  content: "\eaeb";
}

.nc-basketball::before {
  content: "\eaec";
}

.nc-basketball-board::before {
  content: "\eaed";
}

.nc-basketball-player::before {
  content: "\eaee";
}

.nc-basketball-ring::before {
  content: "\eaef";
}

.nc-bat::before {
  content: "\eaf0";
}

.nc-bath-faucet::before {
  content: "\eaf1";
}

.nc-bathroom-cabinet::before {
  content: "\eaf2";
}

.nc-bathtub::before {
  content: "\eaf3";
}

.nc-battery::before {
  content: "\eaf4";
}

.nc-battery-charging::before {
  content: "\eaf5";
}

.nc-battery-level::before {
  content: "\eaf6";
}

.nc-battery-low::before {
  content: "\eaf7";
}

.nc-battery-power::before {
  content: "\eaf8";
}

.nc-battery-status::before {
  content: "\eaf9";
}

.nc-beach-bat::before {
  content: "\eafa";
}

.nc-beach-umbrella::before {
  content: "\eafb";
}

.nc-bear::before {
  content: "\eafc";
}

.nc-bear-2::before {
  content: "\eafd";
}

.nc-beard::before {
  content: "\eafe";
}

.nc-bed::before {
  content: "\eaff";
}

.nc-bedroom::before {
  content: "\eb00";
}

.nc-bee::before {
  content: "\eb01";
}

.nc-beer-95::before {
  content: "\eb02";
}

.nc-beer-96::before {
  content: "\eb03";
}

.nc-bell::before {
  content: "\eb04";
}

.nc-belt::before {
  content: "\eb05";
}

.nc-beverage::before {
  content: "\eb06";
}

.nc-bicep::before {
  content: "\eb07";
}

.nc-big-ben::before {
  content: "\eb08";
}

.nc-big-eyes::before {
  content: "\eb09";
}

.nc-big-smile::before {
  content: "\eb0a";
}

.nc-bigmouth::before {
  content: "\eb0b";
}

.nc-bike::before {
  content: "\eb0c";
}

.nc-bike-bmx::before {
  content: "\eb0d";
}

.nc-bikini::before {
  content: "\eb0e";
}

.nc-bill::before {
  content: "\eb0f";
}

.nc-billboard::before {
  content: "\eb10";
}

.nc-billiard-ball::before {
  content: "\eb11";
}

.nc-bin::before {
  content: "\eb12";
}

.nc-binary-code::before {
  content: "\eb13";
}

.nc-binoculars::before {
  content: "\eb14";
}

.nc-biochemistry::before {
  content: "\eb15";
}

.nc-biology::before {
  content: "\eb16";
}

.nc-biscuit::before {
  content: "\eb17";
}

.nc-bitcoin::before {
  content: "\eb18";
}

.nc-bleah::before {
  content: "\eb19";
}

.nc-blend::before {
  content: "\eb1a";
}

.nc-blender::before {
  content: "\eb1b";
}

.nc-blindness::before {
  content: "\eb1c";
}

.nc-block::before {
  content: "\eb1d";
}

.nc-block-down::before {
  content: "\eb1e";
}

.nc-block-left::before {
  content: "\eb1f";
}

.nc-block-right::before {
  content: "\eb20";
}

.nc-block-up::before {
  content: "\eb21";
}

.nc-blockchain::before {
  content: "\eb22";
}

.nc-blockquote::before {
  content: "\eb23";
}

.nc-blog::before {
  content: "\eb24";
}

.nc-blue-mosque::before {
  content: "\eb25";
}

.nc-blueberries::before {
  content: "\eb26";
}

.nc-blueprint::before {
  content: "\eb27";
}

.nc-bluetooth::before {
  content: "\eb28";
}

.nc-board::before {
  content: "\eb29";
}

.nc-board-2::before {
  content: "\eb2a";
}

.nc-board-27::before {
  content: "\eb2b";
}

.nc-board-28::before {
  content: "\eb2c";
}

.nc-board-29::before {
  content: "\eb2d";
}

.nc-board-30::before {
  content: "\eb2e";
}

.nc-board-51::before {
  content: "\eb2f";
}

.nc-board-game::before {
  content: "\eb30";
}

.nc-boat::before {
  content: "\eb31";
}

.nc-boat-front::before {
  content: "\eb32";
}

.nc-boat-small-02::before {
  content: "\eb33";
}

.nc-boat-small-03::before {
  content: "\eb34";
}

.nc-body-back::before {
  content: "\eb35";
}

.nc-body-butt::before {
  content: "\eb36";
}

.nc-body-cream::before {
  content: "\eb37";
}

.nc-bodybuilder::before {
  content: "\eb38";
}

.nc-boiling-water::before {
  content: "\eb39";
}

.nc-bold::before {
  content: "\eb3a";
}

.nc-bolt::before {
  content: "\eb3b";
}

.nc-bomb::before {
  content: "\eb3c";
}

.nc-bones::before {
  content: "\eb3d";
}

.nc-book::before {
  content: "\eb3e";
}

.nc-book-39::before {
  content: "\eb3f";
}

.nc-book-bookmark::before {
  content: "\eb40";
}

.nc-book-bookmark-2::before {
  content: "\eb41";
}

.nc-book-open::before {
  content: "\eb42";
}

.nc-book-open-2::before {
  content: "\eb43";
}

.nc-bookmark::before {
  content: "\eb44";
}

.nc-bookmark-add::before {
  content: "\eb45";
}

.nc-bookmark-add-2::before {
  content: "\eb46";
}

.nc-bookmark-delete::before {
  content: "\eb47";
}

.nc-bookmark-delete-2::before {
  content: "\eb48";
}

.nc-bookmarks::before {
  content: "\eb49";
}

.nc-books::before {
  content: "\eb4a";
}

.nc-books-46::before {
  content: "\eb4b";
}

.nc-boot::before {
  content: "\eb4c";
}

.nc-boot-2::before {
  content: "\eb4d";
}

.nc-boot-woman::before {
  content: "\eb4e";
}

.nc-boots::before {
  content: "\eb4f";
}

.nc-border::before {
  content: "\eb50";
}

.nc-border-collie::before {
  content: "\eb51";
}

.nc-border-radius::before {
  content: "\eb52";
}

.nc-border-width::before {
  content: "\eb53";
}

.nc-bored::before {
  content: "\eb54";
}

.nc-botany::before {
  content: "\eb55";
}

.nc-bottle::before {
  content: "\eb56";
}

.nc-bottle-wine::before {
  content: "\eb57";
}

.nc-bouquet::before {
  content: "\eb58";
}

.nc-bow::before {
  content: "\eb59";
}

.nc-bowl::before {
  content: "\eb5a";
}

.nc-bowling-ball::before {
  content: "\eb5b";
}

.nc-bowling-pins::before {
  content: "\eb5c";
}

.nc-box::before {
  content: "\eb5d";
}

.nc-box-2::before {
  content: "\eb5e";
}

.nc-box-3d-50::before {
  content: "\eb5f";
}

.nc-box-arrow-bottom-left::before {
  content: "\eb60";
}

.nc-box-arrow-bottom-right::before {
  content: "\eb61";
}

.nc-box-arrow-down::before {
  content: "\eb62";
}

.nc-box-arrow-left::before {
  content: "\eb63";
}

.nc-box-arrow-right::before {
  content: "\eb64";
}

.nc-box-arrow-top-left::before {
  content: "\eb65";
}

.nc-box-arrow-top-right::before {
  content: "\eb66";
}

.nc-box-arrow-up::before {
  content: "\eb67";
}

.nc-box-caret-down::before {
  content: "\eb68";
}

.nc-box-caret-left::before {
  content: "\eb69";
}

.nc-box-caret-right::before {
  content: "\eb6a";
}

.nc-box-caret-up::before {
  content: "\eb6b";
}

.nc-box-ctrl-down::before {
  content: "\eb6c";
}

.nc-box-ctrl-left::before {
  content: "\eb6d";
}

.nc-box-ctrl-right::before {
  content: "\eb6e";
}

.nc-box-ctrl-up::before {
  content: "\eb6f";
}

.nc-box-ribbon::before {
  content: "\eb70";
}

.nc-boxing::before {
  content: "\eb71";
}

.nc-boxing-bag::before {
  content: "\eb72";
}

.nc-boxing-glove::before {
  content: "\eb73";
}

.nc-bra::before {
  content: "\eb74";
}

.nc-braille::before {
  content: "\eb75";
}

.nc-brain::before {
  content: "\eb76";
}

.nc-brakes::before {
  content: "\eb77";
}

.nc-brandenburg-gate::before {
  content: "\eb78";
}

.nc-bread::before {
  content: "\eb79";
}

.nc-bride::before {
  content: "\eb7a";
}

.nc-briefcase-24::before {
  content: "\eb7b";
}

.nc-briefcase-25::before {
  content: "\eb7c";
}

.nc-briefcase-26::before {
  content: "\eb7d";
}

.nc-brightness::before {
  content: "\eb7e";
}

.nc-brioche::before {
  content: "\eb7f";
}

.nc-broccoli::before {
  content: "\eb80";
}

.nc-broken-heart::before {
  content: "\eb81";
}

.nc-broom::before {
  content: "\eb82";
}

.nc-browse::before {
  content: "\eb83";
}

.nc-browser-chrome::before {
  content: "\eb84";
}

.nc-browser-edge::before {
  content: "\eb85";
}

.nc-browser-edge-legacy::before {
  content: "\eb86";
}

.nc-browser-firefox::before {
  content: "\eb87";
}

.nc-browser-ie::before {
  content: "\eb88";
}

.nc-browser-opera::before {
  content: "\eb89";
}

.nc-browser-safari::before {
  content: "\eb8a";
}

.nc-brush::before {
  content: "\eb8b";
}

.nc-btn-play::before {
  content: "\eb8c";
}

.nc-btn-play-2::before {
  content: "\eb8d";
}

.nc-btn-stop::before {
  content: "\eb8e";
}

.nc-bucket::before {
  content: "\eb8f";
}

.nc-bucket-2::before {
  content: "\eb90";
}

.nc-buddhism::before {
  content: "\eb91";
}

.nc-buddhist-temple::before {
  content: "\eb92";
}

.nc-bug::before {
  content: "\eb93";
}

.nc-bulb::before {
  content: "\eb94";
}

.nc-bulb-61::before {
  content: "\eb95";
}

.nc-bulb-62::before {
  content: "\eb96";
}

.nc-bulb-63::before {
  content: "\eb97";
}

.nc-bulb-saver::before {
  content: "\eb98";
}

.nc-bullet-list::before {
  content: "\eb99";
}

.nc-bullet-list-67::before {
  content: "\eb9a";
}

.nc-bullet-list-68::before {
  content: "\eb9b";
}

.nc-bullet-list-69::before {
  content: "\eb9c";
}

.nc-bullet-list-70::before {
  content: "\eb9d";
}

.nc-bullets::before {
  content: "\eb9e";
}

.nc-bureau-dresser::before {
  content: "\eb9f";
}

.nc-burj-al-arab::before {
  content: "\eba0";
}

.nc-bus::before {
  content: "\eba1";
}

.nc-bus-front-10::before {
  content: "\eba2";
}

.nc-bus-front-12::before {
  content: "\eba3";
}

.nc-business-agent::before {
  content: "\eba4";
}

.nc-business-contact-85::before {
  content: "\eba5";
}

.nc-business-contact-86::before {
  content: "\eba6";
}

.nc-business-contact-87::before {
  content: "\eba7";
}

.nc-business-contact-88::before {
  content: "\eba8";
}

.nc-businessman-03::before {
  content: "\eba9";
}

.nc-businessman-04::before {
  content: "\ebaa";
}

.nc-butter::before {
  content: "\ebab";
}

.nc-butterfly::before {
  content: "\ebac";
}

.nc-button::before {
  content: "\ebad";
}

.nc-button-2::before {
  content: "\ebae";
}

.nc-button-eject::before {
  content: "\ebaf";
}

.nc-button-next::before {
  content: "\ebb0";
}

.nc-button-pause::before {
  content: "\ebb1";
}

.nc-button-play::before {
  content: "\ebb2";
}

.nc-button-power::before {
  content: "\ebb3";
}

.nc-button-previous::before {
  content: "\ebb4";
}

.nc-button-record::before {
  content: "\ebb5";
}

.nc-button-rewind::before {
  content: "\ebb6";
}

.nc-button-skip::before {
  content: "\ebb7";
}

.nc-button-stop::before {
  content: "\ebb8";
}

.nc-buzz::before {
  content: "\ebb9";
}

.nc-c-add::before {
  content: "\ebba";
}

.nc-c-check::before {
  content: "\ebbb";
}

.nc-c-delete::before {
  content: "\ebbc";
}

.nc-c-edit::before {
  content: "\ebbd";
}

.nc-c-info::before {
  content: "\ebbe";
}

.nc-c-pulse::before {
  content: "\ebbf";
}

.nc-c-question::before {
  content: "\ebc0";
}

.nc-c-remove::before {
  content: "\ebc1";
}

.nc-c-warning::before {
  content: "\ebc2";
}

.nc-cabinet::before {
  content: "\ebc3";
}

.nc-cable::before {
  content: "\ebc4";
}

.nc-cactus::before {
  content: "\ebc5";
}

.nc-cake::before {
  content: "\ebc6";
}

.nc-cake-13::before {
  content: "\ebc7";
}

.nc-cake-2::before {
  content: "\ebc8";
}

.nc-cake-slice::before {
  content: "\ebc9";
}

.nc-calculator::before {
  content: "\ebca";
}

.nc-calendar::before {
  content: "\ebcb";
}

.nc-calendar-2::before {
  content: "\ebcc";
}

.nc-calendar-date::before {
  content: "\ebcd";
}

.nc-calendar-date-2::before {
  content: "\ebce";
}

.nc-calendar-day-view::before {
  content: "\ebcf";
}

.nc-calendar-event::before {
  content: "\ebd0";
}

.nc-calendar-event-2::before {
  content: "\ebd1";
}

.nc-calendar-event-create::before {
  content: "\ebd2";
}

.nc-call-doctor::before {
  content: "\ebd3";
}

.nc-camcorder::before {
  content: "\ebd4";
}

.nc-camera::before {
  content: "\ebd5";
}

.nc-camera-2::before {
  content: "\ebd6";
}

.nc-camera-3::before {
  content: "\ebd7";
}

.nc-camera-button::before {
  content: "\ebd8";
}

.nc-camera-flash::before {
  content: "\ebd9";
}

.nc-camera-flashlight::before {
  content: "\ebda";
}

.nc-camera-focus::before {
  content: "\ebdb";
}

.nc-camera-focus-2::before {
  content: "\ebdc";
}

.nc-camera-lens::before {
  content: "\ebdd";
}

.nc-camera-roll::before {
  content: "\ebde";
}

.nc-camera-screen::before {
  content: "\ebdf";
}

.nc-camera-shooting::before {
  content: "\ebe0";
}

.nc-camera-timer::before {
  content: "\ebe1";
}

.nc-camper::before {
  content: "\ebe2";
}

.nc-camping::before {
  content: "\ebe3";
}

.nc-camping-lantern::before {
  content: "\ebe4";
}

.nc-can::before {
  content: "\ebe5";
}

.nc-candle::before {
  content: "\ebe6";
}

.nc-candlestick-chart::before {
  content: "\ebe7";
}

.nc-candy::before {
  content: "\ebe8";
}

.nc-candy-2::before {
  content: "\ebe9";
}

.nc-canvas::before {
  content: "\ebea";
}

.nc-cap::before {
  content: "\ebeb";
}

.nc-car::before {
  content: "\ebec";
}

.nc-car-2::before {
  content: "\ebed";
}

.nc-car-accident::before {
  content: "\ebee";
}

.nc-car-connect::before {
  content: "\ebef";
}

.nc-car-door::before {
  content: "\ebf0";
}

.nc-car-front::before {
  content: "\ebf1";
}

.nc-car-lights::before {
  content: "\ebf2";
}

.nc-car-parking::before {
  content: "\ebf3";
}

.nc-car-simple::before {
  content: "\ebf4";
}

.nc-car-sport::before {
  content: "\ebf5";
}

.nc-car-ventilation::before {
  content: "\ebf6";
}

.nc-car-wash::before {
  content: "\ebf7";
}

.nc-card-edit::before {
  content: "\ebf8";
}

.nc-card-favorite::before {
  content: "\ebf9";
}

.nc-card-remove::before {
  content: "\ebfa";
}

.nc-card-update::before {
  content: "\ebfb";
}

.nc-cards::before {
  content: "\ebfc";
}

.nc-caret-sm-up::before {
  content: "\ebfd";
}

.nc-carrot::before {
  content: "\ebfe";
}

.nc-cart::before {
  content: "\ebff";
}

.nc-cart-add::before {
  content: "\ec00";
}

.nc-cart-add-9::before {
  content: "\ec01";
}

.nc-cart-favorite::before {
  content: "\ec02";
}

.nc-cart-full::before {
  content: "\ec03";
}

.nc-cart-refresh::before {
  content: "\ec04";
}

.nc-cart-remove::before {
  content: "\ec05";
}

.nc-cart-remove-9::before {
  content: "\ec06";
}

.nc-cart-return::before {
  content: "\ec07";
}

.nc-cart-simple-add::before {
  content: "\ec08";
}

.nc-cart-simple-remove::before {
  content: "\ec09";
}

.nc-cart-speed::before {
  content: "\ec0a";
}

.nc-cash-register::before {
  content: "\ec0b";
}

.nc-casino::before {
  content: "\ec0c";
}

.nc-casino-chip::before {
  content: "\ec0d";
}

.nc-castle::before {
  content: "\ec0e";
}

.nc-cat::before {
  content: "\ec0f";
}

.nc-catalog::before {
  content: "\ec10";
}

.nc-cauldron::before {
  content: "\ec11";
}

.nc-cctv::before {
  content: "\ec12";
}

.nc-cd-reader::before {
  content: "\ec13";
}

.nc-celsius::before {
  content: "\ec14";
}

.nc-centralize::before {
  content: "\ec15";
}

.nc-certificate::before {
  content: "\ec16";
}

.nc-chain::before {
  content: "\ec17";
}

.nc-chair::before {
  content: "\ec18";
}

.nc-chalkboard::before {
  content: "\ec19";
}

.nc-champagne::before {
  content: "\ec1a";
}

.nc-chandelier::before {
  content: "\ec1b";
}

.nc-change-direction::before {
  content: "\ec1c";
}

.nc-charger-cable::before {
  content: "\ec1d";
}

.nc-chart::before {
  content: "\ec1e";
}

.nc-chart-bar-32::before {
  content: "\ec1f";
}

.nc-chart-bar-33::before {
  content: "\ec20";
}

.nc-chart-growth::before {
  content: "\ec21";
}

.nc-chart-pie-35::before {
  content: "\ec22";
}

.nc-chart-pie-36::before {
  content: "\ec23";
}

.nc-chat::before {
  content: "\ec24";
}

.nc-chat-bot::before {
  content: "\ec25";
}

.nc-check::before {
  content: "\ec26";
}

.nc-check-all::before {
  content: "\ec27";
}

.nc-check-double::before {
  content: "\ec28";
}

.nc-check-in::before {
  content: "\ec29";
}

.nc-check-list::before {
  content: "\ec2a";
}

.nc-check-out::before {
  content: "\ec2b";
}

.nc-check-single::before {
  content: "\ec2c";
}

.nc-checkbox-btn::before {
  content: "\ec2d";
}

.nc-checkbox-btn-checked::before {
  content: "\ec2e";
}

.nc-cheese::before {
  content: "\ec2f";
}

.nc-cheese-87::before {
  content: "\ec30";
}

.nc-cheeseburger::before {
  content: "\ec31";
}

.nc-chef::before {
  content: "\ec32";
}

.nc-chef-hat::before {
  content: "\ec33";
}

.nc-chemistry::before {
  content: "\ec34";
}

.nc-cheque::before {
  content: "\ec35";
}

.nc-cheque-2::before {
  content: "\ec36";
}

.nc-cheque-3::before {
  content: "\ec37";
}

.nc-chequered-flag::before {
  content: "\ec38";
}

.nc-cherry::before {
  content: "\ec39";
}

.nc-chess-bishop::before {
  content: "\ec3a";
}

.nc-chess-king::before {
  content: "\ec3b";
}

.nc-chess-knight::before {
  content: "\ec3c";
}

.nc-chess-pawn::before {
  content: "\ec3d";
}

.nc-chess-queen::before {
  content: "\ec3e";
}

.nc-chess-tower::before {
  content: "\ec3f";
}

.nc-chicken::before {
  content: "\ec40";
}

.nc-chicken-2::before {
  content: "\ec41";
}

.nc-chicken-front::before {
  content: "\ec42";
}

.nc-child::before {
  content: "\ec43";
}

.nc-chili::before {
  content: "\ec44";
}

.nc-chimney::before {
  content: "\ec45";
}

.nc-chips::before {
  content: "\ec46";
}

.nc-choco-cream::before {
  content: "\ec47";
}

.nc-chocolate::before {
  content: "\ec48";
}

.nc-chocolate-mousse::before {
  content: "\ec49";
}

.nc-christianity::before {
  content: "\ec4a";
}

.nc-church::before {
  content: "\ec4b";
}

.nc-churros::before {
  content: "\ec4c";
}

.nc-cinema::before {
  content: "\ec4d";
}

.nc-circle::before {
  content: "\ec4e";
}

.nc-circle-anim::before {
  content: "\ec4f";
}

.nc-circle-anim-2::before {
  content: "\ec50";
}

.nc-circle-anim-3::before {
  content: "\ec51";
}

.nc-circle-arrow-down::before {
  content: "\ec52";
}

.nc-circle-arrow-left::before {
  content: "\ec53";
}

.nc-circle-arrow-right::before {
  content: "\ec54";
}

.nc-circle-arrow-up::before {
  content: "\ec55";
}

.nc-circle-caret-down::before {
  content: "\ec56";
}

.nc-circle-caret-left::before {
  content: "\ec57";
}

.nc-circle-caret-right::before {
  content: "\ec58";
}

.nc-circle-caret-up::before {
  content: "\ec59";
}

.nc-circle-ctrl-down::before {
  content: "\ec5a";
}

.nc-circle-ctrl-left::before {
  content: "\ec5b";
}

.nc-circle-ctrl-right::before {
  content: "\ec5c";
}

.nc-circle-ctrl-up::before {
  content: "\ec5d";
}

.nc-circle-in::before {
  content: "\ec5e";
}

.nc-circle-out::before {
  content: "\ec5f";
}

.nc-circuit::before {
  content: "\ec60";
}

.nc-circuit-round::before {
  content: "\ec61";
}

.nc-clapperboard::before {
  content: "\ec62";
}

.nc-clapperboard-2::before {
  content: "\ec63";
}

.nc-clarinet::before {
  content: "\ec64";
}

.nc-clear-data::before {
  content: "\ec65";
}

.nc-climbing::before {
  content: "\ec66";
}

.nc-clock::before {
  content: "\ec67";
}

.nc-clock-anim::before {
  content: "\ec68";
}

.nc-clone::before {
  content: "\ec69";
}

.nc-closed-captioning::before {
  content: "\ec6a";
}

.nc-clothes-hanger::before {
  content: "\ec6b";
}

.nc-clothing-hanger::before {
  content: "\ec6c";
}

.nc-cloud::before {
  content: "\ec6d";
}

.nc-cloud-data-download::before {
  content: "\ec6e";
}

.nc-cloud-download::before {
  content: "\ec6f";
}

.nc-cloud-drop::before {
  content: "\ec70";
}

.nc-cloud-fog-31::before {
  content: "\ec71";
}

.nc-cloud-fog-32::before {
  content: "\ec72";
}

.nc-cloud-forecast::before {
  content: "\ec73";
}

.nc-cloud-hail::before {
  content: "\ec74";
}

.nc-cloud-light::before {
  content: "\ec75";
}

.nc-cloud-mining::before {
  content: "\ec76";
}

.nc-cloud-moon::before {
  content: "\ec77";
}

.nc-cloud-rain::before {
  content: "\ec78";
}

.nc-cloud-rainbow::before {
  content: "\ec79";
}

.nc-cloud-snow-34::before {
  content: "\ec7a";
}

.nc-cloud-snow-42::before {
  content: "\ec7b";
}

.nc-cloud-sun-17::before {
  content: "\ec7c";
}

.nc-cloud-sun-19::before {
  content: "\ec7d";
}

.nc-cloud-upload::before {
  content: "\ec7e";
}

.nc-clover::before {
  content: "\ec7f";
}

.nc-clubs-suit::before {
  content: "\ec80";
}

.nc-cn-tower::before {
  content: "\ec81";
}

.nc-coat::before {
  content: "\ec82";
}

.nc-coat-hanger::before {
  content: "\ec83";
}

.nc-cockade::before {
  content: "\ec84";
}

.nc-cocktail::before {
  content: "\ec85";
}

.nc-code::before {
  content: "\ec86";
}

.nc-code-editor::before {
  content: "\ec87";
}

.nc-coffe-long::before {
  content: "\ec88";
}

.nc-coffee::before {
  content: "\ec89";
}

.nc-coffee-bean::before {
  content: "\ec8a";
}

.nc-coffee-long::before {
  content: "\ec8b";
}

.nc-coffee-maker::before {
  content: "\ec8c";
}

.nc-coffin::before {
  content: "\ec8d";
}

.nc-cogwheel::before {
  content: "\ec8e";
}

.nc-coins::before {
  content: "\ec8f";
}

.nc-collar::before {
  content: "\ec90";
}

.nc-collection::before {
  content: "\ec91";
}

.nc-colosseum::before {
  content: "\ec92";
}

.nc-comb::before {
  content: "\ec93";
}

.nc-command::before {
  content: "\ec94";
}

.nc-comment::before {
  content: "\ec95";
}

.nc-comment-add::before {
  content: "\ec96";
}

.nc-comments::before {
  content: "\ec97";
}

.nc-compact-camera::before {
  content: "\ec98";
}

.nc-compare::before {
  content: "\ec99";
}

.nc-compare-items::before {
  content: "\ec9a";
}

.nc-compass::before {
  content: "\ec9b";
}

.nc-compass-04::before {
  content: "\ec9c";
}

.nc-compass-05::before {
  content: "\ec9d";
}

.nc-compass-06::before {
  content: "\ec9e";
}

.nc-compass-2::before {
  content: "\ec9f";
}

.nc-compass-3::before {
  content: "\eca0";
}

.nc-components::before {
  content: "\eca1";
}

.nc-compressed-file::before {
  content: "\eca2";
}

.nc-computer::before {
  content: "\eca3";
}

.nc-computer-monitor::before {
  content: "\eca4";
}

.nc-computer-upload::before {
  content: "\eca5";
}

.nc-concierge::before {
  content: "\eca6";
}

.nc-condom::before {
  content: "\eca7";
}

.nc-cone::before {
  content: "\eca8";
}

.nc-conference-room::before {
  content: "\eca9";
}

.nc-configuration-tools::before {
  content: "\ecaa";
}

.nc-connect::before {
  content: "\ecab";
}

.nc-connection::before {
  content: "\ecac";
}

.nc-construction-sign::before {
  content: "\ecad";
}

.nc-contact::before {
  content: "\ecae";
}

.nc-contact-list::before {
  content: "\ecaf";
}

.nc-contactless::before {
  content: "\ecb0";
}

.nc-contactless-card::before {
  content: "\ecb1";
}

.nc-contacts::before {
  content: "\ecb2";
}

.nc-content-360deg::before {
  content: "\ecb3";
}

.nc-content-delivery::before {
  content: "\ecb4";
}

.nc-contrast::before {
  content: "\ecb5";
}

.nc-contrast-2::before {
  content: "\ecb6";
}

.nc-control-panel::before {
  content: "\ecb7";
}

.nc-controller::before {
  content: "\ecb8";
}

.nc-controller-2::before {
  content: "\ecb9";
}

.nc-conversion::before {
  content: "\ecba";
}

.nc-cookies::before {
  content: "\ecbb";
}

.nc-copy::before {
  content: "\ecbc";
}

.nc-copy-2::before {
  content: "\ecbd";
}

.nc-copyright::before {
  content: "\ecbe";
}

.nc-corn::before {
  content: "\ecbf";
}

.nc-corner-bottom-left::before {
  content: "\ecc0";
}

.nc-corner-bottom-right::before {
  content: "\ecc1";
}

.nc-corner-down-round::before {
  content: "\ecc2";
}

.nc-corner-left-down::before {
  content: "\ecc3";
}

.nc-corner-left-round::before {
  content: "\ecc4";
}

.nc-corner-right-down::before {
  content: "\ecc5";
}

.nc-corner-right-round::before {
  content: "\ecc6";
}

.nc-corner-top-left::before {
  content: "\ecc7";
}

.nc-corner-top-right::before {
  content: "\ecc8";
}

.nc-corner-up-left::before {
  content: "\ecc9";
}

.nc-corner-up-right::before {
  content: "\ecca";
}

.nc-corner-up-round::before {
  content: "\eccb";
}

.nc-cornucopia::before {
  content: "\eccc";
}

.nc-corset::before {
  content: "\eccd";
}

.nc-coughing::before {
  content: "\ecce";
}

.nc-countdown::before {
  content: "\eccf";
}

.nc-countdown-2::before {
  content: "\ecd0";
}

.nc-couple-gay::before {
  content: "\ecd1";
}

.nc-couple-lesbian::before {
  content: "\ecd2";
}

.nc-coupon::before {
  content: "\ecd3";
}

.nc-cow::before {
  content: "\ecd4";
}

.nc-cpu::before {
  content: "\ecd5";
}

.nc-crab::before {
  content: "\ecd6";
}

.nc-cradle::before {
  content: "\ecd7";
}

.nc-crane::before {
  content: "\ecd8";
}

.nc-creative-commons::before {
  content: "\ecd9";
}

.nc-credit-card::before {
  content: "\ecda";
}

.nc-credit-card-in::before {
  content: "\ecdb";
}

.nc-credit-locked::before {
  content: "\ecdc";
}

.nc-crepe::before {
  content: "\ecdd";
}

.nc-cricket-bat::before {
  content: "\ecde";
}

.nc-croissant::before {
  content: "\ecdf";
}

.nc-crop::before {
  content: "\ece0";
}

.nc-cross::before {
  content: "\ece1";
}

.nc-cross-down::before {
  content: "\ece2";
}

.nc-cross-horizontal::before {
  content: "\ece3";
}

.nc-cross-left::before {
  content: "\ece4";
}

.nc-cross-right::before {
  content: "\ece5";
}

.nc-cross-up::before {
  content: "\ece6";
}

.nc-cross-vertical::before {
  content: "\ece7";
}

.nc-crosshair::before {
  content: "\ece8";
}

.nc-crossing-directions::before {
  content: "\ece9";
}

.nc-crossroad::before {
  content: "\ecea";
}

.nc-croupier::before {
  content: "\eceb";
}

.nc-crown::before {
  content: "\ecec";
}

.nc-crumpet::before {
  content: "\eced";
}

.nc-crunches::before {
  content: "\ecee";
}

.nc-cry-15::before {
  content: "\ecef";
}

.nc-cry-57::before {
  content: "\ecf0";
}

.nc-crying-baby::before {
  content: "\ecf1";
}

.nc-crypto-wallet::before {
  content: "\ecf2";
}

.nc-cryptography::before {
  content: "\ecf3";
}

.nc-css3::before {
  content: "\ecf4";
}

.nc-ctrl-backward::before {
  content: "\ecf5";
}

.nc-ctrl-down::before {
  content: "\ecf6";
}

.nc-ctrl-forward::before {
  content: "\ecf7";
}

.nc-ctrl-left::before {
  content: "\ecf8";
}

.nc-ctrl-right::before {
  content: "\ecf9";
}

.nc-ctrl-up::before {
  content: "\ecfa";
}

.nc-cupcake::before {
  content: "\ecfb";
}

.nc-cure::before {
  content: "\ecfc";
}

.nc-curling::before {
  content: "\ecfd";
}

.nc-curling-stone::before {
  content: "\ecfe";
}

.nc-currency-dollar::before {
  content: "\ecff";
}

.nc-currency-euro::before {
  content: "\ed00";
}

.nc-currency-exchange::before {
  content: "\ed01";
}

.nc-currency-exchange-2::before {
  content: "\ed02";
}

.nc-currency-pound::before {
  content: "\ed03";
}

.nc-currency-yen::before {
  content: "\ed04";
}

.nc-cursor-48::before {
  content: "\ed05";
}

.nc-cursor-49::before {
  content: "\ed06";
}

.nc-cursor-add::before {
  content: "\ed07";
}

.nc-cursor-grab::before {
  content: "\ed08";
}

.nc-cursor-load::before {
  content: "\ed09";
}

.nc-cursor-menu::before {
  content: "\ed0a";
}

.nc-cursor-not-allowed::before {
  content: "\ed0b";
}

.nc-cursor-pointer::before {
  content: "\ed0c";
}

.nc-cursor-text::before {
  content: "\ed0d";
}

.nc-curtains::before {
  content: "\ed0e";
}

.nc-curved-arrow-down::before {
  content: "\ed0f";
}

.nc-curved-arrow-left::before {
  content: "\ed10";
}

.nc-curved-arrow-right::before {
  content: "\ed11";
}

.nc-curved-circuit::before {
  content: "\ed12";
}

.nc-customer-support::before {
  content: "\ed13";
}

.nc-cut::before {
  content: "\ed14";
}

.nc-cute::before {
  content: "\ed15";
}

.nc-cutlery::before {
  content: "\ed16";
}

.nc-cutlery-75::before {
  content: "\ed17";
}

.nc-cutlery-76::before {
  content: "\ed18";
}

.nc-cutlery-77::before {
  content: "\ed19";
}

.nc-cyborg::before {
  content: "\ed1a";
}

.nc-cycle::before {
  content: "\ed1b";
}

.nc-cycling::before {
  content: "\ed1c";
}

.nc-cycling-track::before {
  content: "\ed1d";
}

.nc-d-add::before {
  content: "\ed1e";
}

.nc-d-chart::before {
  content: "\ed1f";
}

.nc-d-check::before {
  content: "\ed20";
}

.nc-d-delete::before {
  content: "\ed21";
}

.nc-d-edit::before {
  content: "\ed22";
}

.nc-d-remove::before {
  content: "\ed23";
}

.nc-dancer::before {
  content: "\ed24";
}

.nc-dart::before {
  content: "\ed25";
}

.nc-dashboard::before {
  content: "\ed26";
}

.nc-data-download::before {
  content: "\ed27";
}

.nc-data-settings::before {
  content: "\ed28";
}

.nc-data-table::before {
  content: "\ed29";
}

.nc-data-upload::before {
  content: "\ed2a";
}

.nc-database::before {
  content: "\ed2b";
}

.nc-dead-hand::before {
  content: "\ed2c";
}

.nc-deadlift::before {
  content: "\ed2d";
}

.nc-deaf::before {
  content: "\ed2e";
}

.nc-debt::before {
  content: "\ed2f";
}

.nc-decentralize::before {
  content: "\ed30";
}

.nc-decision-process::before {
  content: "\ed31";
}

.nc-decoration::before {
  content: "\ed32";
}

.nc-decrease-font-size::before {
  content: "\ed33";
}

.nc-decrease-indent::before {
  content: "\ed34";
}

.nc-deer::before {
  content: "\ed35";
}

.nc-delete::before {
  content: "\ed36";
}

.nc-delete-forever::before {
  content: "\ed37";
}

.nc-delete-key::before {
  content: "\ed38";
}

.nc-delete-x::before {
  content: "\ed39";
}

.nc-delivery::before {
  content: "\ed3a";
}

.nc-delivery-2::before {
  content: "\ed3b";
}

.nc-delivery-3::before {
  content: "\ed3c";
}

.nc-delivery-fast::before {
  content: "\ed3d";
}

.nc-delivery-time::before {
  content: "\ed3e";
}

.nc-delivery-track::before {
  content: "\ed3f";
}

.nc-depth::before {
  content: "\ed40";
}

.nc-design::before {
  content: "\ed41";
}

.nc-design-system::before {
  content: "\ed42";
}

.nc-desk::before {
  content: "\ed43";
}

.nc-desk-drawer::before {
  content: "\ed44";
}

.nc-desk-lamp::before {
  content: "\ed45";
}

.nc-detached-property::before {
  content: "\ed46";
}

.nc-detox::before {
  content: "\ed47";
}

.nc-device-connection::before {
  content: "\ed48";
}

.nc-devil::before {
  content: "\ed49";
}

.nc-devto::before {
  content: "\ed4a";
}

.nc-diamond::before {
  content: "\ed4b";
}

.nc-diamonds-suits::before {
  content: "\ed4c";
}

.nc-diaper::before {
  content: "\ed4d";
}

.nc-diaper-changing-area::before {
  content: "\ed4e";
}

.nc-dice::before {
  content: "\ed4f";
}

.nc-dice-2::before {
  content: "\ed50";
}

.nc-diet::before {
  content: "\ed51";
}

.nc-diet-food::before {
  content: "\ed52";
}

.nc-diet-plan::before {
  content: "\ed53";
}

.nc-digital-image::before {
  content: "\ed54";
}

.nc-digital-key::before {
  content: "\ed55";
}

.nc-digital-piano::before {
  content: "\ed56";
}

.nc-direction::before {
  content: "\ed57";
}

.nc-direction-down::before {
  content: "\ed58";
}

.nc-direction-left::before {
  content: "\ed59";
}

.nc-direction-right::before {
  content: "\ed5a";
}

.nc-direction-up::before {
  content: "\ed5b";
}

.nc-directions::before {
  content: "\ed5c";
}

.nc-discord::before {
  content: "\ed5d";
}

.nc-discount-2::before {
  content: "\ed5e";
}

.nc-disgusted::before {
  content: "\ed5f";
}

.nc-dish::before {
  content: "\ed60";
}

.nc-dishwasher::before {
  content: "\ed61";
}

.nc-disinfectant::before {
  content: "\ed62";
}

.nc-disk::before {
  content: "\ed63";
}

.nc-disk-reader::before {
  content: "\ed64";
}

.nc-disperse::before {
  content: "\ed65";
}

.nc-distance::before {
  content: "\ed66";
}

.nc-distribute-horizontal::before {
  content: "\ed67";
}

.nc-distribute-vertical::before {
  content: "\ed68";
}

.nc-divider::before {
  content: "\ed69";
}

.nc-dizzy-face::before {
  content: "\ed6a";
}

.nc-dna-27::before {
  content: "\ed6b";
}

.nc-dna-38::before {
  content: "\ed6c";
}

.nc-doc-folder::before {
  content: "\ed6d";
}

.nc-dock-bottom::before {
  content: "\ed6e";
}

.nc-dock-left::before {
  content: "\ed6f";
}

.nc-dock-right::before {
  content: "\ed70";
}

.nc-dock-top::before {
  content: "\ed71";
}

.nc-doctor::before {
  content: "\ed72";
}

.nc-document::before {
  content: "\ed73";
}

.nc-document-2::before {
  content: "\ed74";
}

.nc-document-copy::before {
  content: "\ed75";
}

.nc-dog::before {
  content: "\ed76";
}

.nc-dog-house::before {
  content: "\ed77";
}

.nc-dog-leash::before {
  content: "\ed78";
}

.nc-dont-touch-eyes::before {
  content: "\ed79";
}

.nc-dont-touch-mouth::before {
  content: "\ed7a";
}

.nc-donut::before {
  content: "\ed7b";
}

.nc-door::before {
  content: "\ed7c";
}

.nc-door-2::before {
  content: "\ed7d";
}

.nc-door-3::before {
  content: "\ed7e";
}

.nc-door-handle::before {
  content: "\ed7f";
}

.nc-doorphone::before {
  content: "\ed80";
}

.nc-dots::before {
  content: "\ed81";
}

.nc-dots-anim::before {
  content: "\ed82";
}

.nc-dots-anim-2::before {
  content: "\ed83";
}

.nc-dots-anim-3::before {
  content: "\ed84";
}

.nc-dots-anim-4::before {
  content: "\ed85";
}

.nc-dots-anim-5::before {
  content: "\ed86";
}

.nc-dots-anim-6::before {
  content: "\ed87";
}

.nc-dots-anim-7::before {
  content: "\ed88";
}

.nc-double-arrow-left::before {
  content: "\ed89";
}

.nc-double-arrow-right::before {
  content: "\ed8a";
}

.nc-double-bed::before {
  content: "\ed8b";
}

.nc-double-tap::before {
  content: "\ed8c";
}

.nc-down-arrow::before {
  content: "\ed8d";
}

.nc-download::before {
  content: "\ed8e";
}

.nc-download-data::before {
  content: "\ed8f";
}

.nc-download-file::before {
  content: "\ed90";
}

.nc-drag::before {
  content: "\ed91";
}

.nc-drag-21::before {
  content: "\ed92";
}

.nc-drag-31::before {
  content: "\ed93";
}

.nc-drag-down::before {
  content: "\ed94";
}

.nc-drag-left::before {
  content: "\ed95";
}

.nc-drag-right::before {
  content: "\ed96";
}

.nc-drag-up::before {
  content: "\ed97";
}

.nc-drawer::before {
  content: "\ed98";
}

.nc-drawer-2::before {
  content: "\ed99";
}

.nc-dress-man::before {
  content: "\ed9a";
}

.nc-dress-woman::before {
  content: "\ed9b";
}

.nc-dresser::before {
  content: "\ed9c";
}

.nc-dresser-2::before {
  content: "\ed9d";
}

.nc-dresser-3::before {
  content: "\ed9e";
}

.nc-drill::before {
  content: "\ed9f";
}

.nc-drink::before {
  content: "\eda0";
}

.nc-drink-2::before {
  content: "\eda1";
}

.nc-drink-list::before {
  content: "\eda2";
}

.nc-drinking-bottle::before {
  content: "\eda3";
}

.nc-drone::before {
  content: "\eda4";
}

.nc-drone-2::before {
  content: "\eda5";
}

.nc-drop::before {
  content: "\eda6";
}

.nc-drop-15::before {
  content: "\eda7";
}

.nc-drop-cap::before {
  content: "\eda8";
}

.nc-drops::before {
  content: "\eda9";
}

.nc-druidism::before {
  content: "\edaa";
}

.nc-drums::before {
  content: "\edab";
}

.nc-duck::before {
  content: "\edac";
}

.nc-dumbbell::before {
  content: "\edad";
}

.nc-duplicate::before {
  content: "\edae";
}

.nc-e-add::before {
  content: "\edaf";
}

.nc-e-delete::before {
  content: "\edb0";
}

.nc-e-reader::before {
  content: "\edb1";
}

.nc-e-remove::before {
  content: "\edb2";
}

.nc-earbuds::before {
  content: "\edb3";
}

.nc-earth-science::before {
  content: "\edb4";
}

.nc-eclipse::before {
  content: "\edb5";
}

.nc-eco-home::before {
  content: "\edb6";
}

.nc-ecology::before {
  content: "\edb7";
}

.nc-edge-razor::before {
  content: "\edb8";
}

.nc-edit::before {
  content: "\edb9";
}

.nc-edit-color::before {
  content: "\edba";
}

.nc-edit-contrast::before {
  content: "\edbb";
}

.nc-edit-curves::before {
  content: "\edbc";
}

.nc-edit-levels::before {
  content: "\edbd";
}

.nc-edit-note::before {
  content: "\edbe";
}

.nc-edit-saturation::before {
  content: "\edbf";
}

.nc-egg::before {
  content: "\edc0";
}

.nc-egg-38::before {
  content: "\edc1";
}

.nc-egg-39::before {
  content: "\edc2";
}

.nc-eggs::before {
  content: "\edc3";
}

.nc-eight::before {
  content: "\edc4";
}

.nc-eject::before {
  content: "\edc5";
}

.nc-electronic-circuit::before {
  content: "\edc6";
}

.nc-elephant::before {
  content: "\edc7";
}

.nc-elliptical-cross-trainer::before {
  content: "\edc8";
}

.nc-email::before {
  content: "\edc9";
}

.nc-email-open::before {
  content: "\edca";
}

.nc-embryo::before {
  content: "\edcb";
}

.nc-empty::before {
  content: "\edcc";
}

.nc-energy::before {
  content: "\edcd";
}

.nc-energy-drink::before {
  content: "\edce";
}

.nc-energy-shaker::before {
  content: "\edcf";
}

.nc-energy-supplement::before {
  content: "\edd0";
}

.nc-engine::before {
  content: "\edd1";
}

.nc-engine-start::before {
  content: "\edd2";
}

.nc-enlarge::before {
  content: "\edd3";
}

.nc-enlarge-diagonal::before {
  content: "\edd4";
}

.nc-enlarge-diagonal-2::before {
  content: "\edd5";
}

.nc-enlarge-h::before {
  content: "\edd6";
}

.nc-enlarge-horizontal::before {
  content: "\edd7";
}

.nc-enlarge-vertical::before {
  content: "\edd8";
}

.nc-enter::before {
  content: "\edd9";
}

.nc-equation::before {
  content: "\edda";
}

.nc-equestrian-helmet::before {
  content: "\eddb";
}

.nc-eraser-32::before {
  content: "\eddc";
}

.nc-eraser-33::before {
  content: "\eddd";
}

.nc-eraser-46::before {
  content: "\edde";
}

.nc-escalator::before {
  content: "\eddf";
}

.nc-event-confirm::before {
  content: "\ede0";
}

.nc-event-create::before {
  content: "\ede1";
}

.nc-event-ticket::before {
  content: "\ede2";
}

.nc-exchange::before {
  content: "\ede3";
}

.nc-exclamation-mark::before {
  content: "\ede4";
}

.nc-exercise-bike::before {
  content: "\ede5";
}

.nc-exhibition::before {
  content: "\ede6";
}

.nc-exit-right::before {
  content: "\ede7";
}

.nc-expand::before {
  content: "\ede8";
}

.nc-expand-2::before {
  content: "\ede9";
}

.nc-expand-h::before {
  content: "\edea";
}

.nc-expand-window::before {
  content: "\edeb";
}

.nc-explore::before {
  content: "\edec";
}

.nc-explore-2::before {
  content: "\eded";
}

.nc-explore-user::before {
  content: "\edee";
}

.nc-export::before {
  content: "\edef";
}

.nc-eye::before {
  content: "\edf0";
}

.nc-eye-recognition::before {
  content: "\edf1";
}

.nc-eyelash::before {
  content: "\edf2";
}

.nc-eyeliner::before {
  content: "\edf3";
}

.nc-eyeshadow::before {
  content: "\edf4";
}

.nc-ez-bar::before {
  content: "\edf5";
}

.nc-f-add::before {
  content: "\edf6";
}

.nc-f-chat::before {
  content: "\edf7";
}

.nc-f-check::before {
  content: "\edf8";
}

.nc-f-comment::before {
  content: "\edf9";
}

.nc-f-dashboard::before {
  content: "\edfa";
}

.nc-f-delete::before {
  content: "\edfb";
}

.nc-f-remove::before {
  content: "\edfc";
}

.nc-face-man::before {
  content: "\edfd";
}

.nc-face-powder::before {
  content: "\edfe";
}

.nc-face-recognition::before {
  content: "\edff";
}

.nc-face-woman::before {
  content: "\ee00";
}

.nc-factory::before {
  content: "\ee01";
}

.nc-fahrenheit::before {
  content: "\ee02";
}

.nc-fairy-wand::before {
  content: "\ee03";
}

.nc-family::before {
  content: "\ee04";
}

.nc-family-roof::before {
  content: "\ee05";
}

.nc-fan::before {
  content: "\ee06";
}

.nc-farm-2::before {
  content: "\ee07";
}

.nc-farm-rows::before {
  content: "\ee08";
}

.nc-farmer::before {
  content: "\ee09";
}

.nc-farmer-market::before {
  content: "\ee0a";
}

.nc-fav-list::before {
  content: "\ee0b";
}

.nc-fav-property::before {
  content: "\ee0c";
}

.nc-fav-remove::before {
  content: "\ee0d";
}

.nc-favorite::before {
  content: "\ee0e";
}

.nc-feedback::before {
  content: "\ee0f";
}

.nc-feeding-bottle::before {
  content: "\ee10";
}

.nc-female-figure::before {
  content: "\ee11";
}

.nc-female-sign::before {
  content: "\ee12";
}

.nc-fence::before {
  content: "\ee13";
}

.nc-fencing::before {
  content: "\ee14";
}

.nc-fencing-swords::before {
  content: "\ee15";
}

.nc-fertilizer::before {
  content: "\ee16";
}

.nc-file::before {
  content: "\ee17";
}

.nc-file-2::before {
  content: "\ee18";
}

.nc-file-add::before {
  content: "\ee19";
}

.nc-file-alert::before {
  content: "\ee1a";
}

.nc-file-archive::before {
  content: "\ee1b";
}

.nc-file-article::before {
  content: "\ee1c";
}

.nc-file-audio::before {
  content: "\ee1d";
}

.nc-file-audio-2::before {
  content: "\ee1e";
}

.nc-file-bookmark::before {
  content: "\ee1f";
}

.nc-file-chart-bar::before {
  content: "\ee20";
}

.nc-file-chart-pie::before {
  content: "\ee21";
}

.nc-file-check::before {
  content: "\ee22";
}

.nc-file-cloud::before {
  content: "\ee23";
}

.nc-file-copies::before {
  content: "\ee24";
}

.nc-file-copy::before {
  content: "\ee25";
}

.nc-file-delete::before {
  content: "\ee26";
}

.nc-file-dev::before {
  content: "\ee27";
}

.nc-file-download::before {
  content: "\ee28";
}

.nc-file-download-3::before {
  content: "\ee29";
}

.nc-file-edit::before {
  content: "\ee2a";
}

.nc-file-export::before {
  content: "\ee2b";
}

.nc-file-favorite::before {
  content: "\ee2c";
}

.nc-file-folder::before {
  content: "\ee2d";
}

.nc-file-gallery::before {
  content: "\ee2e";
}

.nc-file-history::before {
  content: "\ee2f";
}

.nc-file-image::before {
  content: "\ee30";
}

.nc-file-import::before {
  content: "\ee31";
}

.nc-file-info::before {
  content: "\ee32";
}

.nc-file-link::before {
  content: "\ee33";
}

.nc-file-locked::before {
  content: "\ee34";
}

.nc-file-money::before {
  content: "\ee35";
}

.nc-file-new::before {
  content: "\ee36";
}

.nc-file-no-access::before {
  content: "\ee37";
}

.nc-file-play::before {
  content: "\ee38";
}

.nc-file-preferences::before {
  content: "\ee39";
}

.nc-file-question::before {
  content: "\ee3a";
}

.nc-file-remove::before {
  content: "\ee3b";
}

.nc-file-replace::before {
  content: "\ee3c";
}

.nc-file-search::before {
  content: "\ee3d";
}

.nc-file-settings::before {
  content: "\ee3e";
}

.nc-file-shared::before {
  content: "\ee3f";
}

.nc-file-starred::before {
  content: "\ee40";
}

.nc-file-sync::before {
  content: "\ee41";
}

.nc-file-text::before {
  content: "\ee42";
}

.nc-file-upload::before {
  content: "\ee43";
}

.nc-file-upload-2::before {
  content: "\ee44";
}

.nc-file-upload-3::before {
  content: "\ee45";
}

.nc-file-user::before {
  content: "\ee46";
}

.nc-file-vector::before {
  content: "\ee47";
}

.nc-film::before {
  content: "\ee48";
}

.nc-filter::before {
  content: "\ee49";
}

.nc-filter-check::before {
  content: "\ee4a";
}

.nc-filter-organization::before {
  content: "\ee4b";
}

.nc-filter-remove::before {
  content: "\ee4c";
}

.nc-filter-tool::before {
  content: "\ee4d";
}

.nc-final-score::before {
  content: "\ee4e";
}

.nc-find-baggage::before {
  content: "\ee4f";
}

.nc-find-replace::before {
  content: "\ee50";
}

.nc-finger-snap::before {
  content: "\ee51";
}

.nc-fire::before {
  content: "\ee52";
}

.nc-firearm::before {
  content: "\ee53";
}

.nc-fireplace::before {
  content: "\ee54";
}

.nc-firewall::before {
  content: "\ee55";
}

.nc-fireworks::before {
  content: "\ee56";
}

.nc-fish::before {
  content: "\ee57";
}

.nc-fishbone::before {
  content: "\ee58";
}

.nc-fist::before {
  content: "\ee59";
}

.nc-fit-horizontal::before {
  content: "\ee5a";
}

.nc-fit-vertical::before {
  content: "\ee5b";
}

.nc-five::before {
  content: "\ee5c";
}

.nc-flag::before {
  content: "\ee5d";
}

.nc-flag-complex::before {
  content: "\ee5e";
}

.nc-flag-diagonal-33::before {
  content: "\ee5f";
}

.nc-flag-diagonal-34::before {
  content: "\ee60";
}

.nc-flag-points-31::before {
  content: "\ee61";
}

.nc-flag-points-32::before {
  content: "\ee62";
}

.nc-flag-simple::before {
  content: "\ee63";
}

.nc-flame::before {
  content: "\ee64";
}

.nc-flash-off::before {
  content: "\ee65";
}

.nc-flash-off-2::before {
  content: "\ee66";
}

.nc-flashlight::before {
  content: "\ee67";
}

.nc-flask::before {
  content: "\ee68";
}

.nc-flask-2::before {
  content: "\ee69";
}

.nc-flick-down::before {
  content: "\ee6a";
}

.nc-flick-left::before {
  content: "\ee6b";
}

.nc-flick-right::before {
  content: "\ee6c";
}

.nc-flick-up::before {
  content: "\ee6d";
}

.nc-flight::before {
  content: "\ee6e";
}

.nc-flight-connection::before {
  content: "\ee6f";
}

.nc-flip::before {
  content: "\ee70";
}

.nc-flip-horizontal::before {
  content: "\ee71";
}

.nc-flip-up::before {
  content: "\ee72";
}

.nc-flip-vertical::before {
  content: "\ee73";
}

.nc-floor::before {
  content: "\ee74";
}

.nc-floor-lamp::before {
  content: "\ee75";
}

.nc-floors::before {
  content: "\ee76";
}

.nc-floppy-disk::before {
  content: "\ee77";
}

.nc-flower::before {
  content: "\ee78";
}

.nc-flower-07::before {
  content: "\ee79";
}

.nc-flower-2::before {
  content: "\ee7a";
}

.nc-flower-rose::before {
  content: "\ee7b";
}

.nc-fog::before {
  content: "\ee7c";
}

.nc-folder::before {
  content: "\ee7d";
}

.nc-folder-2::before {
  content: "\ee7e";
}

.nc-folder-3::before {
  content: "\ee7f";
}

.nc-folder-add::before {
  content: "\ee80";
}

.nc-folder-alert::before {
  content: "\ee81";
}

.nc-folder-audio::before {
  content: "\ee82";
}

.nc-folder-bookmark::before {
  content: "\ee83";
}

.nc-folder-chart-bar::before {
  content: "\ee84";
}

.nc-folder-chart-pie::before {
  content: "\ee85";
}

.nc-folder-check::before {
  content: "\ee86";
}

.nc-folder-cloud::before {
  content: "\ee87";
}

.nc-folder-dev::before {
  content: "\ee88";
}

.nc-folder-download::before {
  content: "\ee89";
}

.nc-folder-edit::before {
  content: "\ee8a";
}

.nc-folder-favorite::before {
  content: "\ee8b";
}

.nc-folder-gallery::before {
  content: "\ee8c";
}

.nc-folder-history::before {
  content: "\ee8d";
}

.nc-folder-image::before {
  content: "\ee8e";
}

.nc-folder-info::before {
  content: "\ee8f";
}

.nc-folder-link::before {
  content: "\ee90";
}

.nc-folder-locked::before {
  content: "\ee91";
}

.nc-folder-money::before {
  content: "\ee92";
}

.nc-folder-music::before {
  content: "\ee93";
}

.nc-folder-no-access::before {
  content: "\ee94";
}

.nc-folder-play::before {
  content: "\ee95";
}

.nc-folder-preferences::before {
  content: "\ee96";
}

.nc-folder-question::before {
  content: "\ee97";
}

.nc-folder-remove::before {
  content: "\ee98";
}

.nc-folder-replace::before {
  content: "\ee99";
}

.nc-folder-search::before {
  content: "\ee9a";
}

.nc-folder-settings::before {
  content: "\ee9b";
}

.nc-folder-shared::before {
  content: "\ee9c";
}

.nc-folder-starred::before {
  content: "\ee9d";
}

.nc-folder-sync::before {
  content: "\ee9e";
}

.nc-folder-upload::before {
  content: "\ee9f";
}

.nc-folder-user::before {
  content: "\eea0";
}

.nc-folder-vector::before {
  content: "\eea1";
}

.nc-food-course::before {
  content: "\eea2";
}

.nc-food-dog::before {
  content: "\eea3";
}

.nc-food-scale::before {
  content: "\eea4";
}

.nc-food-supplement::before {
  content: "\eea5";
}

.nc-football-headguard::before {
  content: "\eea6";
}

.nc-forecast::before {
  content: "\eea7";
}

.nc-forest::before {
  content: "\eea8";
}

.nc-fork::before {
  content: "\eea9";
}

.nc-fork-2::before {
  content: "\eeaa";
}

.nc-forklift::before {
  content: "\eeab";
}

.nc-form::before {
  content: "\eeac";
}

.nc-format-left::before {
  content: "\eead";
}

.nc-format-right::before {
  content: "\eeae";
}

.nc-forward::before {
  content: "\eeaf";
}

.nc-four::before {
  content: "\eeb0";
}

.nc-frame::before {
  content: "\eeb1";
}

.nc-frame-effect::before {
  content: "\eeb2";
}

.nc-frankenstein::before {
  content: "\eeb3";
}

.nc-fridge::before {
  content: "\eeb4";
}

.nc-fruit-crate::before {
  content: "\eeb5";
}

.nc-fruit-picking::before {
  content: "\eeb6";
}

.nc-fuel::before {
  content: "\eeb7";
}

.nc-fuel-2::before {
  content: "\eeb8";
}

.nc-fuel-electric::before {
  content: "\eeb9";
}

.nc-full-screen::before {
  content: "\eeba";
}

.nc-fullscreen::before {
  content: "\eebb";
}

.nc-fullscreen-2::before {
  content: "\eebc";
}

.nc-fullsize::before {
  content: "\eebd";
}

.nc-function::before {
  content: "\eebe";
}

.nc-funnel::before {
  content: "\eebf";
}

.nc-furnished-property::before {
  content: "\eec0";
}

.nc-g-chart::before {
  content: "\eec1";
}

.nc-g-check::before {
  content: "\eec2";
}

.nc-gallery-layout::before {
  content: "\eec3";
}

.nc-gallery-view::before {
  content: "\eec4";
}

.nc-gaming-console::before {
  content: "\eec5";
}

.nc-gaming-controller::before {
  content: "\eec6";
}

.nc-gantt::before {
  content: "\eec7";
}

.nc-garden-fork::before {
  content: "\eec8";
}

.nc-garden-shears::before {
  content: "\eec9";
}

.nc-gardening-tools::before {
  content: "\eeca";
}

.nc-garlic::before {
  content: "\eecb";
}

.nc-gas-mask::before {
  content: "\eecc";
}

.nc-gathering-restrictions::before {
  content: "\eecd";
}

.nc-gear::before {
  content: "\eece";
}

.nc-geometry::before {
  content: "\eecf";
}

.nc-ghost::before {
  content: "\eed0";
}

.nc-ghost-2::before {
  content: "\eed1";
}

.nc-gift::before {
  content: "\eed2";
}

.nc-gift-exchange::before {
  content: "\eed3";
}

.nc-git-commit::before {
  content: "\eed4";
}

.nc-git-merge::before {
  content: "\eed5";
}

.nc-glass::before {
  content: "\eed6";
}

.nc-glass-water::before {
  content: "\eed7";
}

.nc-glasses::before {
  content: "\eed8";
}

.nc-glasses-2::before {
  content: "\eed9";
}

.nc-globe::before {
  content: "\eeda";
}

.nc-globe-2::before {
  content: "\eedb";
}

.nc-glove::before {
  content: "\eedc";
}

.nc-gloves::before {
  content: "\eedd";
}

.nc-goal-65::before {
  content: "\eede";
}

.nc-gold::before {
  content: "\eedf";
}

.nc-gold-coin::before {
  content: "\eee0";
}

.nc-golden-gate-bridge::before {
  content: "\eee1";
}

.nc-golf-ball::before {
  content: "\eee2";
}

.nc-golf-club::before {
  content: "\eee3";
}

.nc-golf-course::before {
  content: "\eee4";
}

.nc-golf-player::before {
  content: "\eee5";
}

.nc-golf-strike::before {
  content: "\eee6";
}

.nc-gooey-anim::before {
  content: "\eee7";
}

.nc-google::before {
  content: "\eee8";
}

.nc-goose::before {
  content: "\eee9";
}

.nc-gps::before {
  content: "\eeea";
}

.nc-grab::before {
  content: "\eeeb";
}

.nc-gradient::before {
  content: "\eeec";
}

.nc-grain::before {
  content: "\eeed";
}

.nc-grain-2::before {
  content: "\eeee";
}

.nc-grain-effect::before {
  content: "\eeef";
}

.nc-grammar-check::before {
  content: "\eef0";
}

.nc-grammar-check-2::before {
  content: "\eef1";
}

.nc-grandparent::before {
  content: "\eef2";
}

.nc-grape::before {
  content: "\eef3";
}

.nc-graphics-tablet::before {
  content: "\eef4";
}

.nc-grave::before {
  content: "\eef5";
}

.nc-great-wall::before {
  content: "\eef6";
}

.nc-greenhouse::before {
  content: "\eef7";
}

.nc-grenade::before {
  content: "\eef8";
}

.nc-grid::before {
  content: "\eef9";
}

.nc-grid-interface::before {
  content: "\eefa";
}

.nc-grid-layout::before {
  content: "\eefb";
}

.nc-grid-system::before {
  content: "\eefc";
}

.nc-grid-view::before {
  content: "\eefd";
}

.nc-groom::before {
  content: "\eefe";
}

.nc-group::before {
  content: "\eeff";
}

.nc-guitar::before {
  content: "\ef00";
}

.nc-gym::before {
  content: "\ef01";
}

.nc-gym-class::before {
  content: "\ef02";
}

.nc-gym-shoes::before {
  content: "\ef03";
}

.nc-gymnastics::before {
  content: "\ef04";
}

.nc-hacker::before {
  content: "\ef05";
}

.nc-hair-clipper::before {
  content: "\ef06";
}

.nc-hair-dryer::before {
  content: "\ef07";
}

.nc-hair-gel::before {
  content: "\ef08";
}

.nc-hair-man::before {
  content: "\ef09";
}

.nc-hair-straightener::before {
  content: "\ef0a";
}

.nc-hair-towel::before {
  content: "\ef0b";
}

.nc-hair-woman::before {
  content: "\ef0c";
}

.nc-hairdresser::before {
  content: "\ef0d";
}

.nc-halloween-pumpkin::before {
  content: "\ef0e";
}

.nc-hammer::before {
  content: "\ef0f";
}

.nc-hand-card::before {
  content: "\ef10";
}

.nc-hand-heart::before {
  content: "\ef11";
}

.nc-hand-mixer::before {
  content: "\ef12";
}

.nc-handball::before {
  content: "\ef13";
}

.nc-handheld-console::before {
  content: "\ef14";
}

.nc-handout::before {
  content: "\ef15";
}

.nc-hands-heart::before {
  content: "\ef16";
}

.nc-handshake::before {
  content: "\ef17";
}

.nc-hanging-toys::before {
  content: "\ef18";
}

.nc-happy-baby::before {
  content: "\ef19";
}

.nc-happy-sun::before {
  content: "\ef1a";
}

.nc-hash-mark::before {
  content: "\ef1b";
}

.nc-hat::before {
  content: "\ef1c";
}

.nc-hat-2::before {
  content: "\ef1d";
}

.nc-hat-3::before {
  content: "\ef1e";
}

.nc-hat-top::before {
  content: "\ef1f";
}

.nc-hazelnut::before {
  content: "\ef20";
}

.nc-hdmi::before {
  content: "\ef21";
}

.nc-heading-1::before {
  content: "\ef22";
}

.nc-heading-2::before {
  content: "\ef23";
}

.nc-heading-3::before {
  content: "\ef24";
}

.nc-heading-4::before {
  content: "\ef25";
}

.nc-heading-5::before {
  content: "\ef26";
}

.nc-heading-6::before {
  content: "\ef27";
}

.nc-headphones::before {
  content: "\ef28";
}

.nc-headphones-2::before {
  content: "\ef29";
}

.nc-headphones-3::before {
  content: "\ef2a";
}

.nc-headphones-mic::before {
  content: "\ef2b";
}

.nc-headset::before {
  content: "\ef2c";
}

.nc-heart::before {
  content: "\ef2d";
}

.nc-heart-anim::before {
  content: "\ef2e";
}

.nc-heart-balloons::before {
  content: "\ef2f";
}

.nc-heart-lock::before {
  content: "\ef30";
}

.nc-heartbeat::before {
  content: "\ef31";
}

.nc-hearts-suit::before {
  content: "\ef32";
}

.nc-heater::before {
  content: "\ef33";
}

.nc-height::before {
  content: "\ef34";
}

.nc-height-2::before {
  content: "\ef35";
}

.nc-helicopter::before {
  content: "\ef36";
}

.nc-helmet::before {
  content: "\ef37";
}

.nc-hide::before {
  content: "\ef38";
}

.nc-hierarchy-53::before {
  content: "\ef39";
}

.nc-hierarchy-54::before {
  content: "\ef3a";
}

.nc-hierarchy-55::before {
  content: "\ef3b";
}

.nc-hierarchy-56::before {
  content: "\ef3c";
}

.nc-high-priority::before {
  content: "\ef3d";
}

.nc-hinduism::before {
  content: "\ef3e";
}

.nc-hob::before {
  content: "\ef3f";
}

.nc-hockey::before {
  content: "\ef40";
}

.nc-hockey-stick::before {
  content: "\ef41";
}

.nc-hold::before {
  content: "\ef42";
}

.nc-home::before {
  content: "\ef43";
}

.nc-home-2::before {
  content: "\ef44";
}

.nc-home-3::before {
  content: "\ef45";
}

.nc-home-search::before {
  content: "\ef46";
}

.nc-honey::before {
  content: "\ef47";
}

.nc-honeymoon::before {
  content: "\ef48";
}

.nc-hoodie::before {
  content: "\ef49";
}

.nc-hook::before {
  content: "\ef4a";
}

.nc-horizontal-divider::before {
  content: "\ef4b";
}

.nc-horse::before {
  content: "\ef4c";
}

.nc-horse-2::before {
  content: "\ef4d";
}

.nc-horse-hopper::before {
  content: "\ef4e";
}

.nc-horseshoe::before {
  content: "\ef4f";
}

.nc-hose::before {
  content: "\ef50";
}

.nc-hospital-32::before {
  content: "\ef51";
}

.nc-hospital-33::before {
  content: "\ef52";
}

.nc-hospital-34::before {
  content: "\ef53";
}

.nc-hospital-bed::before {
  content: "\ef54";
}

.nc-hot-dog::before {
  content: "\ef55";
}

.nc-hot-key::before {
  content: "\ef56";
}

.nc-hotel::before {
  content: "\ef57";
}

.nc-hotel-bell::before {
  content: "\ef58";
}

.nc-hotel-symbol::before {
  content: "\ef59";
}

.nc-hotspot::before {
  content: "\ef5a";
}

.nc-hourglass::before {
  content: "\ef5b";
}

.nc-house::before {
  content: "\ef5c";
}

.nc-house-pricing::before {
  content: "\ef5d";
}

.nc-house-property::before {
  content: "\ef5e";
}

.nc-house-search-engine::before {
  content: "\ef5f";
}

.nc-html5::before {
  content: "\ef60";
}

.nc-humanoid::before {
  content: "\ef61";
}

.nc-humidity-26::before {
  content: "\ef62";
}

.nc-humidity-52::before {
  content: "\ef63";
}

.nc-hurricane-44::before {
  content: "\ef64";
}

.nc-hurricane-45::before {
  content: "\ef65";
}

.nc-hut::before {
  content: "\ef66";
}

.nc-hybrid-car::before {
  content: "\ef67";
}

.nc-hyperlink::before {
  content: "\ef68";
}

.nc-hyperlink-broken::before {
  content: "\ef69";
}

.nc-i-add::before {
  content: "\ef6a";
}

.nc-i-check::before {
  content: "\ef6b";
}

.nc-i-delete::before {
  content: "\ef6c";
}

.nc-i-edit::before {
  content: "\ef6d";
}

.nc-i-remove::before {
  content: "\ef6e";
}

.nc-ice-cream::before {
  content: "\ef6f";
}

.nc-ice-cream-22::before {
  content: "\ef70";
}

.nc-ice-cream-72::before {
  content: "\ef71";
}

.nc-ice-skates::before {
  content: "\ef72";
}

.nc-igloo::before {
  content: "\ef73";
}

.nc-image::before {
  content: "\ef74";
}

.nc-image-2::before {
  content: "\ef75";
}

.nc-image-add::before {
  content: "\ef76";
}

.nc-image-delete::before {
  content: "\ef77";
}

.nc-image-location::before {
  content: "\ef78";
}

.nc-img::before {
  content: "\ef79";
}

.nc-img-rotate-left::before {
  content: "\ef7a";
}

.nc-img-rotate-right::before {
  content: "\ef7b";
}

.nc-img-stack::before {
  content: "\ef7c";
}

.nc-incense::before {
  content: "\ef7d";
}

.nc-incognito::before {
  content: "\ef7e";
}

.nc-increase::before {
  content: "\ef7f";
}

.nc-increase-font-size::before {
  content: "\ef80";
}

.nc-increase-indent::before {
  content: "\ef81";
}

.nc-infinite::before {
  content: "\ef82";
}

.nc-infinite-loop::before {
  content: "\ef83";
}

.nc-info::before {
  content: "\ef84";
}

.nc-info-point::before {
  content: "\ef85";
}

.nc-infrared-thermometer::before {
  content: "\ef86";
}

.nc-input::before {
  content: "\ef87";
}

.nc-instant-camera::before {
  content: "\ef88";
}

.nc-instant-camera-2::before {
  content: "\ef89";
}

.nc-interview::before {
  content: "\ef8a";
}

.nc-intestine::before {
  content: "\ef8b";
}

.nc-invert-direction::before {
  content: "\ef8c";
}

.nc-invert-process::before {
  content: "\ef8d";
}

.nc-iron::before {
  content: "\ef8e";
}

.nc-iron-2::before {
  content: "\ef8f";
}

.nc-iron-dont::before {
  content: "\ef90";
}

.nc-islam::before {
  content: "\ef91";
}

.nc-jacuzzi::before {
  content: "\ef92";
}

.nc-jam::before {
  content: "\ef93";
}

.nc-jeans-41::before {
  content: "\ef94";
}

.nc-jeans-43::before {
  content: "\ef95";
}

.nc-jeans-pocket::before {
  content: "\ef96";
}

.nc-jelly::before {
  content: "\ef97";
}

.nc-jellyfish::before {
  content: "\ef98";
}

.nc-jewel::before {
  content: "\ef99";
}

.nc-joint-account::before {
  content: "\ef9a";
}

.nc-journey::before {
  content: "\ef9b";
}

.nc-journey-06::before {
  content: "\ef9c";
}

.nc-journey-07::before {
  content: "\ef9d";
}

.nc-journey-08::before {
  content: "\ef9e";
}

.nc-js-console::before {
  content: "\ef9f";
}

.nc-json-logo::before {
  content: "\efa0";
}

.nc-judaism::before {
  content: "\efa1";
}

.nc-juice::before {
  content: "\efa2";
}

.nc-jump-rope::before {
  content: "\efa3";
}

.nc-karate::before {
  content: "\efa4";
}

.nc-ketchup::before {
  content: "\efa5";
}

.nc-kettle::before {
  content: "\efa6";
}

.nc-kettlebell::before {
  content: "\efa7";
}

.nc-key::before {
  content: "\efa8";
}

.nc-keyboard::before {
  content: "\efa9";
}

.nc-keyboard-hide::before {
  content: "\efaa";
}

.nc-keyboard-mouse::before {
  content: "\efab";
}

.nc-keyboard-wired::before {
  content: "\efac";
}

.nc-keyboard-wireless::before {
  content: "\efad";
}

.nc-kid::before {
  content: "\efae";
}

.nc-kid-2::before {
  content: "\efaf";
}

.nc-kiss::before {
  content: "\efb0";
}

.nc-kitchen-fan::before {
  content: "\efb1";
}

.nc-kiwi::before {
  content: "\efb2";
}

.nc-knife::before {
  content: "\efb3";
}

.nc-knob::before {
  content: "\efb4";
}

.nc-l-add::before {
  content: "\efb5";
}

.nc-l-check::before {
  content: "\efb6";
}

.nc-l-circle::before {
  content: "\efb7";
}

.nc-l-circles::before {
  content: "\efb8";
}

.nc-l-location::before {
  content: "\efb9";
}

.nc-l-remove::before {
  content: "\efba";
}

.nc-l-search::before {
  content: "\efbb";
}

.nc-l-security::before {
  content: "\efbc";
}

.nc-l-settings::before {
  content: "\efbd";
}

.nc-l-sync::before {
  content: "\efbe";
}

.nc-l-system-update::before {
  content: "\efbf";
}

.nc-label::before {
  content: "\efc0";
}

.nc-ladder::before {
  content: "\efc1";
}

.nc-ladybug::before {
  content: "\efc2";
}

.nc-land::before {
  content: "\efc3";
}

.nc-landing::before {
  content: "\efc4";
}

.nc-landscape-orientation::before {
  content: "\efc5";
}

.nc-language::before {
  content: "\efc6";
}

.nc-laptop::before {
  content: "\efc7";
}

.nc-laptop-1::before {
  content: "\efc8";
}

.nc-laptop-2::before {
  content: "\efc9";
}

.nc-laptop-71::before {
  content: "\efca";
}

.nc-laptop-72::before {
  content: "\efcb";
}

.nc-lat-station::before {
  content: "\efcc";
}

.nc-laugh-17::before {
  content: "\efcd";
}

.nc-laugh-35::before {
  content: "\efce";
}

.nc-launch::before {
  content: "\efcf";
}

.nc-launch-app::before {
  content: "\efd0";
}

.nc-laundry::before {
  content: "\efd1";
}

.nc-law::before {
  content: "\efd2";
}

.nc-layers::before {
  content: "\efd3";
}

.nc-layers-2::before {
  content: "\efd4";
}

.nc-layout-11::before {
  content: "\efd5";
}

.nc-layout-25::before {
  content: "\efd6";
}

.nc-layout-grid::before {
  content: "\efd7";
}

.nc-leaf::before {
  content: "\efd8";
}

.nc-leaf-36::before {
  content: "\efd9";
}

.nc-leaf-38::before {
  content: "\efda";
}

.nc-leave::before {
  content: "\efdb";
}

.nc-left-arrow::before {
  content: "\efdc";
}

.nc-leggins::before {
  content: "\efdd";
}

.nc-lemon::before {
  content: "\efde";
}

.nc-lemon-slice::before {
  content: "\efdf";
}

.nc-letter::before {
  content: "\efe0";
}

.nc-letter-a::before {
  content: "\efe1";
}

.nc-letter-b::before {
  content: "\efe2";
}

.nc-letter-c::before {
  content: "\efe3";
}

.nc-letter-d::before {
  content: "\efe4";
}

.nc-letter-e::before {
  content: "\efe5";
}

.nc-letter-f::before {
  content: "\efe6";
}

.nc-letter-g::before {
  content: "\efe7";
}

.nc-letter-h::before {
  content: "\efe8";
}

.nc-letter-i::before {
  content: "\efe9";
}

.nc-letter-j::before {
  content: "\efea";
}

.nc-letter-k::before {
  content: "\efeb";
}

.nc-letter-l::before {
  content: "\efec";
}

.nc-letter-m::before {
  content: "\efed";
}

.nc-letter-n::before {
  content: "\efee";
}

.nc-letter-o::before {
  content: "\efef";
}

.nc-letter-p::before {
  content: "\eff0";
}

.nc-letter-q::before {
  content: "\eff1";
}

.nc-letter-r::before {
  content: "\eff2";
}

.nc-letter-s::before {
  content: "\eff3";
}

.nc-letter-t::before {
  content: "\eff4";
}

.nc-letter-u::before {
  content: "\eff5";
}

.nc-letter-v::before {
  content: "\eff6";
}

.nc-letter-w::before {
  content: "\eff7";
}

.nc-letter-x::before {
  content: "\eff8";
}

.nc-letter-y::before {
  content: "\eff9";
}

.nc-letter-z::before {
  content: "\effa";
}

.nc-library::before {
  content: "\effb";
}

.nc-license-key::before {
  content: "\effc";
}

.nc-lifering::before {
  content: "\effd";
}

.nc-lift::before {
  content: "\effe";
}

.nc-light-2::before {
  content: "\efff";
}

.nc-light-control::before {
  content: "\f000";
}

.nc-light-switch::before {
  content: "\f001";
}

.nc-light-traffic::before {
  content: "\f002";
}

.nc-lighter::before {
  content: "\f003";
}

.nc-lighthouse::before {
  content: "\f004";
}

.nc-lightning::before {
  content: "\f005";
}

.nc-like::before {
  content: "\f006";
}

.nc-line-chart::before {
  content: "\f007";
}

.nc-line-height::before {
  content: "\f008";
}

.nc-link::before {
  content: "\f009";
}

.nc-linux::before {
  content: "\f00a";
}

.nc-lip-gloss::before {
  content: "\f00b";
}

.nc-lips::before {
  content: "\f00c";
}

.nc-lipstick::before {
  content: "\f00d";
}

.nc-lipstick-2::before {
  content: "\f00e";
}

.nc-liquid-soap-container::before {
  content: "\f00f";
}

.nc-list::before {
  content: "\f010";
}

.nc-live-streaming::before {
  content: "\f011";
}

.nc-loader-bars::before {
  content: "\f012";
}

.nc-loan::before {
  content: "\f013";
}

.nc-lobster::before {
  content: "\f014";
}

.nc-lock::before {
  content: "\f015";
}

.nc-lock-landscape::before {
  content: "\f016";
}

.nc-lock-orientation::before {
  content: "\f017";
}

.nc-lock-portrait::before {
  content: "\f018";
}

.nc-log-in::before {
  content: "\f019";
}

.nc-log-out::before {
  content: "\f01a";
}

.nc-logic::before {
  content: "\f01b";
}

.nc-logo-500px::before {
  content: "\f01c";
}

.nc-logo-angellist::before {
  content: "\f01d";
}

.nc-logo-behance::before {
  content: "\f01e";
}

.nc-logo-blogger::before {
  content: "\f01f";
}

.nc-logo-buffer::before {
  content: "\f020";
}

.nc-logo-buysellads::before {
  content: "\f021";
}

.nc-logo-codepen::before {
  content: "\f022";
}

.nc-logo-creative-market::before {
  content: "\f023";
}

.nc-logo-crunchbase::before {
  content: "\f024";
}

.nc-logo-deviantart::before {
  content: "\f025";
}

.nc-logo-dribbble::before {
  content: "\f026";
}

.nc-logo-dropbox::before {
  content: "\f027";
}

.nc-logo-envato::before {
  content: "\f028";
}

.nc-logo-evernote::before {
  content: "\f029";
}

.nc-logo-facebook::before {
  content: "\f02a";
}

.nc-logo-fb-simple::before {
  content: "\f02b";
}

.nc-logo-feedly::before {
  content: "\f02c";
}

.nc-logo-flickr::before {
  content: "\f02d";
}

.nc-logo-github::before {
  content: "\f02e";
}

.nc-logo-google-plus::before {
  content: "\f02f";
}

.nc-logo-instagram::before {
  content: "\f030";
}

.nc-logo-lastfm::before {
  content: "\f031";
}

.nc-logo-linkedin::before {
  content: "\f032";
}

.nc-logo-medium::before {
  content: "\f033";
}

.nc-logo-meetup::before {
  content: "\f034";
}

.nc-logo-messenger::before {
  content: "\f035";
}

.nc-logo-mixer::before {
  content: "\f036";
}

.nc-logo-myspace::before {
  content: "\f037";
}

.nc-logo-paypal::before {
  content: "\f038";
}

.nc-logo-pinterest::before {
  content: "\f039";
}

.nc-logo-product-hunt::before {
  content: "\f03a";
}

.nc-logo-qq::before {
  content: "\f03b";
}

.nc-logo-reddit::before {
  content: "\f03c";
}

.nc-logo-rss::before {
  content: "\f03d";
}

.nc-logo-shopify::before {
  content: "\f03e";
}

.nc-logo-skype::before {
  content: "\f03f";
}

.nc-logo-slack::before {
  content: "\f040";
}

.nc-logo-snapchat::before {
  content: "\f041";
}

.nc-logo-soundcloud::before {
  content: "\f042";
}

.nc-logo-spotify::before {
  content: "\f043";
}

.nc-logo-squarespace::before {
  content: "\f044";
}

.nc-logo-trello::before {
  content: "\f045";
}

.nc-logo-tumblr::before {
  content: "\f046";
}

.nc-logo-twitter::before {
  content: "\f047";
}

.nc-logo-unsplash::before {
  content: "\f048";
}

.nc-logo-vimeo::before {
  content: "\f049";
}

.nc-logo-vine::before {
  content: "\f04a";
}

.nc-logo-vk::before {
  content: "\f04b";
}

.nc-logo-wechat::before {
  content: "\f04c";
}

.nc-logo-weibo::before {
  content: "\f04d";
}

.nc-logo-whatsapp::before {
  content: "\f04e";
}

.nc-logo-wikipedia::before {
  content: "\f04f";
}

.nc-logo-wordpress::before {
  content: "\f050";
}

.nc-logo-yelp::before {
  content: "\f051";
}

.nc-logo-youtube::before {
  content: "\f052";
}

.nc-logout::before {
  content: "\f053";
}

.nc-lollipop::before {
  content: "\f054";
}

.nc-long-sleeve::before {
  content: "\f055";
}

.nc-loop::before {
  content: "\f056";
}

.nc-loop-2::before {
  content: "\f057";
}

.nc-lotus-flower::before {
  content: "\f058";
}

.nc-loudspeaker::before {
  content: "\f059";
}

.nc-love::before {
  content: "\f05a";
}

.nc-love-camera::before {
  content: "\f05b";
}

.nc-love-car::before {
  content: "\f05c";
}

.nc-love-card::before {
  content: "\f05d";
}

.nc-love-heart-pin::before {
  content: "\f05e";
}

.nc-love-letter::before {
  content: "\f05f";
}

.nc-love-message::before {
  content: "\f060";
}

.nc-love-movie::before {
  content: "\f061";
}

.nc-love-song::before {
  content: "\f062";
}

.nc-low-priority::before {
  content: "\f063";
}

.nc-low-vision::before {
  content: "\f064";
}

.nc-lowercase::before {
  content: "\f065";
}

.nc-lucky-seven::before {
  content: "\f066";
}

.nc-luggage::before {
  content: "\f067";
}

.nc-lungs::before {
  content: "\f068";
}

.nc-lungs-infection::before {
  content: "\f069";
}

.nc-m-add::before {
  content: "\f06a";
}

.nc-m-check::before {
  content: "\f06b";
}

.nc-m-delete::before {
  content: "\f06c";
}

.nc-m-edit::before {
  content: "\f06d";
}

.nc-m-heart::before {
  content: "\f06e";
}

.nc-m-location::before {
  content: "\f06f";
}

.nc-m-remove::before {
  content: "\f070";
}

.nc-m-search::before {
  content: "\f071";
}

.nc-m-security::before {
  content: "\f072";
}

.nc-m-settings::before {
  content: "\f073";
}

.nc-m-share::before {
  content: "\f074";
}

.nc-m-star::before {
  content: "\f075";
}

.nc-m-sync::before {
  content: "\f076";
}

.nc-m-time::before {
  content: "\f077";
}

.nc-m-update::before {
  content: "\f078";
}

.nc-machine-learning::before {
  content: "\f079";
}

.nc-macro::before {
  content: "\f07a";
}

.nc-mad-12::before {
  content: "\f07b";
}

.nc-mad-58::before {
  content: "\f07c";
}

.nc-magic-wand::before {
  content: "\f07d";
}

.nc-magnet::before {
  content: "\f07e";
}

.nc-magnifier::before {
  content: "\f07f";
}

.nc-magnifier-zoom-in::before {
  content: "\f080";
}

.nc-magnifier-zoom-out::before {
  content: "\f081";
}

.nc-mail::before {
  content: "\f082";
}

.nc-makeup::before {
  content: "\f083";
}

.nc-makeup-blush::before {
  content: "\f084";
}

.nc-makeup-brush::before {
  content: "\f085";
}

.nc-makeup-cream::before {
  content: "\f086";
}

.nc-makeup-foundation::before {
  content: "\f087";
}

.nc-makeup-mirror::before {
  content: "\f088";
}

.nc-makeup-palette::before {
  content: "\f089";
}

.nc-male-sign::before {
  content: "\f08a";
}

.nc-malicious::before {
  content: "\f08b";
}

.nc-man::before {
  content: "\f08c";
}

.nc-man-arrow-down::before {
  content: "\f08d";
}

.nc-man-arrow-up::before {
  content: "\f08e";
}

.nc-man-profile::before {
  content: "\f08f";
}

.nc-man-up-front::before {
  content: "\f090";
}

.nc-manga-62::before {
  content: "\f091";
}

.nc-manga-63::before {
  content: "\f092";
}

.nc-map::before {
  content: "\f093";
}

.nc-map-big::before {
  content: "\f094";
}

.nc-map-compass::before {
  content: "\f095";
}

.nc-map-gps::before {
  content: "\f096";
}

.nc-map-marker::before {
  content: "\f097";
}

.nc-map-pin::before {
  content: "\f098";
}

.nc-maple-leaf::before {
  content: "\f099";
}

.nc-mario-mushroom::before {
  content: "\f09a";
}

.nc-markdown::before {
  content: "\f09b";
}

.nc-marker::before {
  content: "\f09c";
}

.nc-marker-2::before {
  content: "\f09d";
}

.nc-marker-3::before {
  content: "\f09e";
}

.nc-market-music::before {
  content: "\f09f";
}

.nc-market-play::before {
  content: "\f0a0";
}

.nc-mascara::before {
  content: "\f0a1";
}

.nc-mask-face::before {
  content: "\f0a2";
}

.nc-mask-oval::before {
  content: "\f0a3";
}

.nc-mask-rect::before {
  content: "\f0a4";
}

.nc-massage::before {
  content: "\f0a5";
}

.nc-mat::before {
  content: "\f0a6";
}

.nc-matches::before {
  content: "\f0a7";
}

.nc-math::before {
  content: "\f0a8";
}

.nc-maximize::before {
  content: "\f0a9";
}

.nc-maximize-area::before {
  content: "\f0aa";
}

.nc-mayo::before {
  content: "\f0ab";
}

.nc-measure-02::before {
  content: "\f0ac";
}

.nc-measure-17::before {
  content: "\f0ad";
}

.nc-measure-big::before {
  content: "\f0ae";
}

.nc-measurement::before {
  content: "\f0af";
}

.nc-measuring-cup::before {
  content: "\f0b0";
}

.nc-meat-spit::before {
  content: "\f0b1";
}

.nc-medal::before {
  content: "\f0b2";
}

.nc-media-player::before {
  content: "\f0b3";
}

.nc-media-stream::before {
  content: "\f0b4";
}

.nc-medical-clipboard::before {
  content: "\f0b5";
}

.nc-medical-mask::before {
  content: "\f0b6";
}

.nc-medication::before {
  content: "\f0b7";
}

.nc-medicine::before {
  content: "\f0b8";
}

.nc-medicine-ball::before {
  content: "\f0b9";
}

.nc-meeting::before {
  content: "\f0ba";
}

.nc-megaphone::before {
  content: "\f0bb";
}

.nc-menu::before {
  content: "\f0bc";
}

.nc-menu-2::before {
  content: "\f0bd";
}

.nc-menu-3::before {
  content: "\f0be";
}

.nc-menu-4::before {
  content: "\f0bf";
}

.nc-menu-6::before {
  content: "\f0c0";
}

.nc-menu-7::before {
  content: "\f0c1";
}

.nc-menu-8::before {
  content: "\f0c2";
}

.nc-menu-dots::before {
  content: "\f0c3";
}

.nc-merge::before {
  content: "\f0c4";
}

.nc-merge-2::before {
  content: "\f0c5";
}

.nc-messaging::before {
  content: "\f0c6";
}

.nc-metrics::before {
  content: "\f0c7";
}

.nc-mic::before {
  content: "\f0c8";
}

.nc-mic-2::before {
  content: "\f0c9";
}

.nc-mickey-mouse::before {
  content: "\f0ca";
}

.nc-microbiology::before {
  content: "\f0cb";
}

.nc-microphone::before {
  content: "\f0cc";
}

.nc-microphone-2::before {
  content: "\f0cd";
}

.nc-microphone-off::before {
  content: "\f0ce";
}

.nc-microscope::before {
  content: "\f0cf";
}

.nc-microsoft::before {
  content: "\f0d0";
}

.nc-microsoft-onenote::before {
  content: "\f0d1";
}

.nc-microwave::before {
  content: "\f0d2";
}

.nc-migration::before {
  content: "\f0d3";
}

.nc-military-camp::before {
  content: "\f0d4";
}

.nc-military-knife::before {
  content: "\f0d5";
}

.nc-military-medal::before {
  content: "\f0d6";
}

.nc-military-tag::before {
  content: "\f0d7";
}

.nc-military-tank::before {
  content: "\f0d8";
}

.nc-military-vest::before {
  content: "\f0d9";
}

.nc-milk::before {
  content: "\f0da";
}

.nc-miner::before {
  content: "\f0db";
}

.nc-mirror::before {
  content: "\f0dc";
}

.nc-mirror-2::before {
  content: "\f0dd";
}

.nc-mirror-display::before {
  content: "\f0de";
}

.nc-mirror-tablet-phone::before {
  content: "\f0df";
}

.nc-missile::before {
  content: "\f0e0";
}

.nc-mistletoe::before {
  content: "\f0e1";
}

.nc-mobile-banking::before {
  content: "\f0e2";
}

.nc-mobile-card::before {
  content: "\f0e3";
}

.nc-mobile-chat::before {
  content: "\f0e4";
}

.nc-mobile-contact::before {
  content: "\f0e5";
}

.nc-mobile-design::before {
  content: "\f0e6";
}

.nc-mobile-dev::before {
  content: "\f0e7";
}

.nc-mobile-phone::before {
  content: "\f0e8";
}

.nc-moka::before {
  content: "\f0e9";
}

.nc-molecule::before {
  content: "\f0ea";
}

.nc-molecule-39::before {
  content: "\f0eb";
}

.nc-molecule-40::before {
  content: "\f0ec";
}

.nc-money-11::before {
  content: "\f0ed";
}

.nc-money-12::before {
  content: "\f0ee";
}

.nc-money-13::before {
  content: "\f0ef";
}

.nc-money-bag::before {
  content: "\f0f0";
}

.nc-money-coins::before {
  content: "\f0f1";
}

.nc-money-growth::before {
  content: "\f0f2";
}

.nc-money-time::before {
  content: "\f0f3";
}

.nc-money-transfer::before {
  content: "\f0f4";
}

.nc-monster::before {
  content: "\f0f5";
}

.nc-moon::before {
  content: "\f0f6";
}

.nc-moon-cloud-drop::before {
  content: "\f0f7";
}

.nc-moon-cloud-fog::before {
  content: "\f0f8";
}

.nc-moon-cloud-hail::before {
  content: "\f0f9";
}

.nc-moon-cloud-light::before {
  content: "\f0fa";
}

.nc-moon-cloud-rain::before {
  content: "\f0fb";
}

.nc-moon-cloud-snow-61::before {
  content: "\f0fc";
}

.nc-moon-cloud-snow-62::before {
  content: "\f0fd";
}

.nc-moon-fog::before {
  content: "\f0fe";
}

.nc-moon-full::before {
  content: "\f0ff";
}

.nc-moon-stars::before {
  content: "\f100";
}

.nc-mortar::before {
  content: "\f101";
}

.nc-mortgage::before {
  content: "\f102";
}

.nc-mosque::before {
  content: "\f103";
}

.nc-moto::before {
  content: "\f104";
}

.nc-mountain::before {
  content: "\f105";
}

.nc-mouse::before {
  content: "\f106";
}

.nc-mouse-2::before {
  content: "\f107";
}

.nc-mouse-anim::before {
  content: "\f108";
}

.nc-move::before {
  content: "\f109";
}

.nc-move-2::before {
  content: "\f10a";
}

.nc-move-3::before {
  content: "\f10b";
}

.nc-move-down::before {
  content: "\f10c";
}

.nc-move-down-2::before {
  content: "\f10d";
}

.nc-move-down-right::before {
  content: "\f10e";
}

.nc-move-layer-down::before {
  content: "\f10f";
}

.nc-move-layer-left::before {
  content: "\f110";
}

.nc-move-layer-right::before {
  content: "\f111";
}

.nc-move-layer-up::before {
  content: "\f112";
}

.nc-move-left::before {
  content: "\f113";
}

.nc-move-right::before {
  content: "\f114";
}

.nc-move-up::before {
  content: "\f115";
}

.nc-move-up-2::before {
  content: "\f116";
}

.nc-move-up-left::before {
  content: "\f117";
}

.nc-movie::before {
  content: "\f118";
}

.nc-movie-2::before {
  content: "\f119";
}

.nc-movie-3::before {
  content: "\f11a";
}

.nc-movie-reel::before {
  content: "\f11b";
}

.nc-mower::before {
  content: "\f11c";
}

.nc-muffin::before {
  content: "\f11d";
}

.nc-mug::before {
  content: "\f11e";
}

.nc-mushroom::before {
  content: "\f11f";
}

.nc-music::before {
  content: "\f120";
}

.nc-music-album::before {
  content: "\f121";
}

.nc-music-cloud::before {
  content: "\f122";
}

.nc-music-note::before {
  content: "\f123";
}

.nc-music-player::before {
  content: "\f124";
}

.nc-music-playlist::before {
  content: "\f125";
}

.nc-mustache::before {
  content: "\f126";
}

.nc-n-check::before {
  content: "\f127";
}

.nc-n-edit::before {
  content: "\f128";
}

.nc-nail-file::before {
  content: "\f129";
}

.nc-nail-polish::before {
  content: "\f12a";
}

.nc-nail-polish-2::before {
  content: "\f12b";
}

.nc-name-card::before {
  content: "\f12c";
}

.nc-nav-down::before {
  content: "\f12d";
}

.nc-nav-left::before {
  content: "\f12e";
}

.nc-nav-right::before {
  content: "\f12f";
}

.nc-nav-up::before {
  content: "\f130";
}

.nc-navigation::before {
  content: "\f131";
}

.nc-neck-duster::before {
  content: "\f132";
}

.nc-needle::before {
  content: "\f133";
}

.nc-negative-judgement::before {
  content: "\f134";
}

.nc-nerd::before {
  content: "\f135";
}

.nc-net::before {
  content: "\f136";
}

.nc-network-communication::before {
  content: "\f137";
}

.nc-network-connection::before {
  content: "\f138";
}

.nc-networking::before {
  content: "\f139";
}

.nc-new::before {
  content: "\f13a";
}

.nc-new-construction::before {
  content: "\f13b";
}

.nc-new-notification::before {
  content: "\f13c";
}

.nc-news::before {
  content: "\f13d";
}

.nc-newsletter::before {
  content: "\f13e";
}

.nc-newsletter-dev::before {
  content: "\f13f";
}

.nc-night::before {
  content: "\f140";
}

.nc-night-table::before {
  content: "\f141";
}

.nc-nine::before {
  content: "\f142";
}

.nc-ninja::before {
  content: "\f143";
}

.nc-no-contact::before {
  content: "\f144";
}

.nc-no-guns::before {
  content: "\f145";
}

.nc-no-photo::before {
  content: "\f146";
}

.nc-no-results::before {
  content: "\f147";
}

.nc-no-smoking::before {
  content: "\f148";
}

.nc-no-words::before {
  content: "\f149";
}

.nc-nodes::before {
  content: "\f14a";
}

.nc-noodles::before {
  content: "\f14b";
}

.nc-note::before {
  content: "\f14c";
}

.nc-note-code::before {
  content: "\f14d";
}

.nc-notebook::before {
  content: "\f14e";
}

.nc-notepad::before {
  content: "\f14f";
}

.nc-notes::before {
  content: "\f150";
}

.nc-notification::before {
  content: "\f151";
}

.nc-notification-2::before {
  content: "\f152";
}

.nc-nurse::before {
  content: "\f153";
}

.nc-nutrition::before {
  content: "\f154";
}

.nc-ny-building::before {
  content: "\f155";
}

.nc-o-check::before {
  content: "\f156";
}

.nc-o-warning::before {
  content: "\f157";
}

.nc-octagon::before {
  content: "\f158";
}

.nc-octagon-m::before {
  content: "\f159";
}

.nc-octopus::before {
  content: "\f15a";
}

.nc-office::before {
  content: "\f15b";
}

.nc-office-badge::before {
  content: "\f15c";
}

.nc-office-chair::before {
  content: "\f15d";
}

.nc-office-pass::before {
  content: "\f15e";
}

.nc-offline::before {
  content: "\f15f";
}

.nc-oil::before {
  content: "\f160";
}

.nc-oil-2::before {
  content: "\f161";
}

.nc-olympic-flame::before {
  content: "\f162";
}

.nc-one::before {
  content: "\f163";
}

.nc-onion::before {
  content: "\f164";
}

.nc-online-banking::before {
  content: "\f165";
}

.nc-open-ai::before {
  content: "\f166";
}

.nc-open-book::before {
  content: "\f167";
}

.nc-open-folder::before {
  content: "\f168";
}

.nc-open-in-browser::before {
  content: "\f169";
}

.nc-opening-times::before {
  content: "\f16a";
}

.nc-opposite-directions::before {
  content: "\f16b";
}

.nc-opposite-directions-2::before {
  content: "\f16c";
}

.nc-options::before {
  content: "\f16d";
}

.nc-orange::before {
  content: "\f16e";
}

.nc-ordered-list::before {
  content: "\f16f";
}

.nc-organic::before {
  content: "\f170";
}

.nc-organic-2::before {
  content: "\f171";
}

.nc-orientation::before {
  content: "\f172";
}

.nc-oven::before {
  content: "\f173";
}

.nc-ovum-sperm::before {
  content: "\f174";
}

.nc-owl::before {
  content: "\f175";
}

.nc-p-add::before {
  content: "\f176";
}

.nc-p-chart::before {
  content: "\f177";
}

.nc-p-check::before {
  content: "\f178";
}

.nc-p-edit::before {
  content: "\f179";
}

.nc-p-heart::before {
  content: "\f17a";
}

.nc-p-location::before {
  content: "\f17b";
}

.nc-p-remove::before {
  content: "\f17c";
}

.nc-p-search::before {
  content: "\f17d";
}

.nc-p-settings::before {
  content: "\f17e";
}

.nc-p-share::before {
  content: "\f17f";
}

.nc-p-sync::before {
  content: "\f180";
}

.nc-p-system-update::before {
  content: "\f181";
}

.nc-p-time::before {
  content: "\f182";
}

.nc-pacifier::before {
  content: "\f183";
}

.nc-pacman::before {
  content: "\f184";
}

.nc-padlock::before {
  content: "\f185";
}

.nc-padlock-unlocked::before {
  content: "\f186";
}

.nc-pagoda::before {
  content: "\f187";
}

.nc-paint-16::before {
  content: "\f188";
}

.nc-paint-37::before {
  content: "\f189";
}

.nc-paint-38::before {
  content: "\f18a";
}

.nc-paint-brush::before {
  content: "\f18b";
}

.nc-paint-bucket-39::before {
  content: "\f18c";
}

.nc-paint-bucket-40::before {
  content: "\f18d";
}

.nc-pajamas::before {
  content: "\f18e";
}

.nc-palette::before {
  content: "\f18f";
}

.nc-pallet::before {
  content: "\f190";
}

.nc-pallet-stacked-boxes::before {
  content: "\f191";
}

.nc-palm-tree::before {
  content: "\f192";
}

.nc-pan::before {
  content: "\f193";
}

.nc-pancake::before {
  content: "\f194";
}

.nc-panda::before {
  content: "\f195";
}

.nc-panel::before {
  content: "\f196";
}

.nc-pantone::before {
  content: "\f197";
}

.nc-paper::before {
  content: "\f198";
}

.nc-paper-design::before {
  content: "\f199";
}

.nc-paper-dev::before {
  content: "\f19a";
}

.nc-paper-diploma::before {
  content: "\f19b";
}

.nc-parachute::before {
  content: "\f19c";
}

.nc-paragraph::before {
  content: "\f19d";
}

.nc-paragraph-2::before {
  content: "\f19e";
}

.nc-paralympic-games::before {
  content: "\f19f";
}

.nc-parenting::before {
  content: "\f1a0";
}

.nc-paris-tower::before {
  content: "\f1a1";
}

.nc-park::before {
  content: "\f1a2";
}

.nc-parking::before {
  content: "\f1a3";
}

.nc-parking-sensors::before {
  content: "\f1a4";
}

.nc-parrot::before {
  content: "\f1a5";
}

.nc-party::before {
  content: "\f1a6";
}

.nc-passenger::before {
  content: "\f1a7";
}

.nc-passport::before {
  content: "\f1a8";
}

.nc-password::before {
  content: "\f1a9";
}

.nc-pasta::before {
  content: "\f1aa";
}

.nc-patch::before {
  content: "\f1ab";
}

.nc-patch-19::before {
  content: "\f1ac";
}

.nc-patch-34::before {
  content: "\f1ad";
}

.nc-path-exclude::before {
  content: "\f1ae";
}

.nc-path-intersect::before {
  content: "\f1af";
}

.nc-path-minus::before {
  content: "\f1b0";
}

.nc-path-unite::before {
  content: "\f1b1";
}

.nc-pattern-recognition::before {
  content: "\f1b2";
}

.nc-paw::before {
  content: "\f1b3";
}

.nc-payee::before {
  content: "\f1b4";
}

.nc-payment::before {
  content: "\f1b5";
}

.nc-payment-method::before {
  content: "\f1b6";
}

.nc-payor::before {
  content: "\f1b7";
}

.nc-pc::before {
  content: "\f1b8";
}

.nc-pc-monitor::before {
  content: "\f1b9";
}

.nc-pc-mouse::before {
  content: "\f1ba";
}

.nc-pc-play-media::before {
  content: "\f1bb";
}

.nc-pci-card::before {
  content: "\f1bc";
}

.nc-peanut::before {
  content: "\f1bd";
}

.nc-pear::before {
  content: "\f1be";
}

.nc-peas::before {
  content: "\f1bf";
}

.nc-pectoral-machine::before {
  content: "\f1c0";
}

.nc-pen::before {
  content: "\f1c1";
}

.nc-pen-01::before {
  content: "\f1c2";
}

.nc-pen-2::before {
  content: "\f1c3";
}

.nc-pen-23::before {
  content: "\f1c4";
}

.nc-pen-tool::before {
  content: "\f1c5";
}

.nc-pencil::before {
  content: "\f1c6";
}

.nc-pencil-47::before {
  content: "\f1c7";
}

.nc-pendant-lighting::before {
  content: "\f1c8";
}

.nc-pendulum::before {
  content: "\f1c9";
}

.nc-penguin::before {
  content: "\f1ca";
}

.nc-pennant::before {
  content: "\f1cb";
}

.nc-people-network::before {
  content: "\f1cc";
}

.nc-pepper::before {
  content: "\f1cd";
}

.nc-percent-sign::before {
  content: "\f1ce";
}

.nc-percentage-38::before {
  content: "\f1cf";
}

.nc-percentage-39::before {
  content: "\f1d0";
}

.nc-perfume::before {
  content: "\f1d1";
}

.nc-personal-trainer::before {
  content: "\f1d2";
}

.nc-pesticide::before {
  content: "\f1d3";
}

.nc-pet-food::before {
  content: "\f1d4";
}

.nc-pharmacy::before {
  content: "\f1d5";
}

.nc-phone::before {
  content: "\f1d6";
}

.nc-phone-button::before {
  content: "\f1d7";
}

.nc-phone-call::before {
  content: "\f1d8";
}

.nc-phone-call-end::before {
  content: "\f1d9";
}

.nc-phone-camera-back::before {
  content: "\f1da";
}

.nc-phone-camera-front::before {
  content: "\f1db";
}

.nc-phone-charging::before {
  content: "\f1dc";
}

.nc-phone-charging-2::before {
  content: "\f1dd";
}

.nc-phone-charging-3::before {
  content: "\f1de";
}

.nc-phone-dock::before {
  content: "\f1df";
}

.nc-phone-heart::before {
  content: "\f1e0";
}

.nc-phone-heartbeat::before {
  content: "\f1e1";
}

.nc-phone-music::before {
  content: "\f1e2";
}

.nc-phone-toolbar::before {
  content: "\f1e3";
}

.nc-photo::before {
  content: "\f1e4";
}

.nc-photo-album::before {
  content: "\f1e5";
}

.nc-photo-editor::before {
  content: "\f1e6";
}

.nc-photo-frame::before {
  content: "\f1e7";
}

.nc-photo-not-allowed::before {
  content: "\f1e8";
}

.nc-piano::before {
  content: "\f1e9";
}

.nc-piano-2::before {
  content: "\f1ea";
}

.nc-pickaxe::before {
  content: "\f1eb";
}

.nc-pickle::before {
  content: "\f1ec";
}

.nc-picnic-basket::before {
  content: "\f1ed";
}

.nc-picture::before {
  content: "\f1ee";
}

.nc-pie::before {
  content: "\f1ef";
}

.nc-pig::before {
  content: "\f1f0";
}

.nc-pig-2::before {
  content: "\f1f1";
}

.nc-pilcrow::before {
  content: "\f1f2";
}

.nc-pilgrim-hat::before {
  content: "\f1f3";
}

.nc-pill-42::before {
  content: "\f1f4";
}

.nc-pill-43::before {
  content: "\f1f5";
}

.nc-pill-bottle::before {
  content: "\f1f6";
}

.nc-pin::before {
  content: "\f1f7";
}

.nc-pin-2::before {
  content: "\f1f8";
}

.nc-pin-3::before {
  content: "\f1f9";
}

.nc-pin-add::before {
  content: "\f1fa";
}

.nc-pin-add-2::before {
  content: "\f1fb";
}

.nc-pin-check::before {
  content: "\f1fc";
}

.nc-pin-copy::before {
  content: "\f1fd";
}

.nc-pin-delete::before {
  content: "\f1fe";
}

.nc-pin-edit::before {
  content: "\f1ff";
}

.nc-pin-heart::before {
  content: "\f200";
}

.nc-pin-remove::before {
  content: "\f201";
}

.nc-pin-remove-2::before {
  content: "\f202";
}

.nc-pin-search::before {
  content: "\f203";
}

.nc-pin-security::before {
  content: "\f204";
}

.nc-pin-settings::before {
  content: "\f205";
}

.nc-pin-share::before {
  content: "\f206";
}

.nc-pin-star::before {
  content: "\f207";
}

.nc-pin-sync::before {
  content: "\f208";
}

.nc-pin-time::before {
  content: "\f209";
}

.nc-pin-user::before {
  content: "\f20a";
}

.nc-pinch::before {
  content: "\f20b";
}

.nc-pineapple::before {
  content: "\f20c";
}

.nc-pins::before {
  content: "\f20d";
}

.nc-pipe::before {
  content: "\f20e";
}

.nc-pirate::before {
  content: "\f20f";
}

.nc-pisa-tower::before {
  content: "\f210";
}

.nc-pizza::before {
  content: "\f211";
}

.nc-pizza-slice::before {
  content: "\f212";
}

.nc-plane::before {
  content: "\f213";
}

.nc-planet::before {
  content: "\f214";
}

.nc-plant-leaf::before {
  content: "\f215";
}

.nc-plant-soil::before {
  content: "\f216";
}

.nc-plant-vase::before {
  content: "\f217";
}

.nc-plate::before {
  content: "\f218";
}

.nc-play-media::before {
  content: "\f219";
}

.nc-play-movie::before {
  content: "\f21a";
}

.nc-player::before {
  content: "\f21b";
}

.nc-playground::before {
  content: "\f21c";
}

.nc-playing-cards::before {
  content: "\f21d";
}

.nc-playlist::before {
  content: "\f21e";
}

.nc-plug::before {
  content: "\f21f";
}

.nc-plug-2::before {
  content: "\f220";
}

.nc-podcast::before {
  content: "\f221";
}

.nc-podcast-mic::before {
  content: "\f222";
}

.nc-podium::before {
  content: "\f223";
}

.nc-podium-trophy::before {
  content: "\f224";
}

.nc-point-a::before {
  content: "\f225";
}

.nc-point-b::before {
  content: "\f226";
}

.nc-pointing-down::before {
  content: "\f227";
}

.nc-pointing-left::before {
  content: "\f228";
}

.nc-pointing-right::before {
  content: "\f229";
}

.nc-pointing-up::before {
  content: "\f22a";
}

.nc-polaroid::before {
  content: "\f22b";
}

.nc-polaroid-photo::before {
  content: "\f22c";
}

.nc-polaroid-portrait::before {
  content: "\f22d";
}

.nc-polaroid-shot-delete::before {
  content: "\f22e";
}

.nc-polaroid-shot-new::before {
  content: "\f22f";
}

.nc-polaroid-shots::before {
  content: "\f230";
}

.nc-police-officer::before {
  content: "\f231";
}

.nc-poop::before {
  content: "\f232";
}

.nc-popcorn::before {
  content: "\f233";
}

.nc-pos::before {
  content: "\f234";
}

.nc-position::before {
  content: "\f235";
}

.nc-position-marker::before {
  content: "\f236";
}

.nc-position-pin::before {
  content: "\f237";
}

.nc-position-user::before {
  content: "\f238";
}

.nc-positive-judgement::before {
  content: "\f239";
}

.nc-pot::before {
  content: "\f23a";
}

.nc-potato::before {
  content: "\f23b";
}

.nc-potion::before {
  content: "\f23c";
}

.nc-power-level::before {
  content: "\f23d";
}

.nc-power-lifting::before {
  content: "\f23e";
}

.nc-power-rack::before {
  content: "\f23f";
}

.nc-pram::before {
  content: "\f240";
}

.nc-preferences::before {
  content: "\f241";
}

.nc-pregnancy-test::before {
  content: "\f242";
}

.nc-pregnant-woman::before {
  content: "\f243";
}

.nc-present::before {
  content: "\f244";
}

.nc-presentation::before {
  content: "\f245";
}

.nc-print::before {
  content: "\f246";
}

.nc-printer::before {
  content: "\f247";
}

.nc-priority-high::before {
  content: "\f248";
}

.nc-priority-highest::before {
  content: "\f249";
}

.nc-priority-low::before {
  content: "\f24a";
}

.nc-priority-lowest::before {
  content: "\f24b";
}

.nc-priority-normal::before {
  content: "\f24c";
}

.nc-privacy::before {
  content: "\f24d";
}

.nc-privacy-policy::before {
  content: "\f24e";
}

.nc-privacy-settings::before {
  content: "\f24f";
}

.nc-profile::before {
  content: "\f250";
}

.nc-progress::before {
  content: "\f251";
}

.nc-progress-2::before {
  content: "\f252";
}

.nc-progress-indicator::before {
  content: "\f253";
}

.nc-projector::before {
  content: "\f254";
}

.nc-property::before {
  content: "\f255";
}

.nc-property-agreement::before {
  content: "\f256";
}

.nc-property-app::before {
  content: "\f257";
}

.nc-property-for-sale::before {
  content: "\f258";
}

.nc-property-location::before {
  content: "\f259";
}

.nc-property-sold::before {
  content: "\f25a";
}

.nc-property-to-rent::before {
  content: "\f25b";
}

.nc-prosciutto::before {
  content: "\f25c";
}

.nc-prototype::before {
  content: "\f25d";
}

.nc-pruning-saws::before {
  content: "\f25e";
}

.nc-pulse::before {
  content: "\f25f";
}

.nc-pulse-chart::before {
  content: "\f260";
}

.nc-pulse-sleep::before {
  content: "\f261";
}

.nc-pumpkin::before {
  content: "\f262";
}

.nc-puzzle-09::before {
  content: "\f263";
}

.nc-puzzle-10::before {
  content: "\f264";
}

.nc-puzzle-toy::before {
  content: "\f265";
}

.nc-puzzled::before {
  content: "\f266";
}

.nc-pyramid::before {
  content: "\f267";
}

.nc-question-mark::before {
  content: "\f268";
}

.nc-questionnaire::before {
  content: "\f269";
}

.nc-quite-happy::before {
  content: "\f26a";
}

.nc-quote::before {
  content: "\f26b";
}

.nc-r-chat::before {
  content: "\f26c";
}

.nc-r-down-left-arrows::before {
  content: "\f26d";
}

.nc-r-down-right-arrows::before {
  content: "\f26e";
}

.nc-r-up-left-arrows::before {
  content: "\f26f";
}

.nc-r-up-right-arrows::before {
  content: "\f270";
}

.nc-rabbit::before {
  content: "\f271";
}

.nc-radar::before {
  content: "\f272";
}

.nc-radiation::before {
  content: "\f273";
}

.nc-radio::before {
  content: "\f274";
}

.nc-radio-btn::before {
  content: "\f275";
}

.nc-radio-btn-checked::before {
  content: "\f276";
}

.nc-rain::before {
  content: "\f277";
}

.nc-rain-hail::before {
  content: "\f278";
}

.nc-rainbow::before {
  content: "\f279";
}

.nc-rake::before {
  content: "\f27a";
}

.nc-ram::before {
  content: "\f27b";
}

.nc-ram-2::before {
  content: "\f27c";
}

.nc-random::before {
  content: "\f27d";
}

.nc-ranking::before {
  content: "\f27e";
}

.nc-rat::before {
  content: "\f27f";
}

.nc-rat-head::before {
  content: "\f280";
}

.nc-rate-down::before {
  content: "\f281";
}

.nc-rate-up::before {
  content: "\f282";
}

.nc-raw-image::before {
  content: "\f283";
}

.nc-razor::before {
  content: "\f284";
}

.nc-read::before {
  content: "\f285";
}

.nc-reading::before {
  content: "\f286";
}

.nc-reading-tablet::before {
  content: "\f287";
}

.nc-real-estate::before {
  content: "\f288";
}

.nc-receipt::before {
  content: "\f289";
}

.nc-receipt-list-42::before {
  content: "\f28a";
}

.nc-receipt-list-43::before {
  content: "\f28b";
}

.nc-recipe::before {
  content: "\f28c";
}

.nc-recipe-book-46::before {
  content: "\f28d";
}

.nc-recipe-book-47::before {
  content: "\f28e";
}

.nc-recipe-create::before {
  content: "\f28f";
}

.nc-record-player::before {
  content: "\f290";
}

.nc-recycling::before {
  content: "\f291";
}

.nc-redo::before {
  content: "\f292";
}

.nc-referee::before {
  content: "\f293";
}

.nc-refresh::before {
  content: "\f294";
}

.nc-refresh-01::before {
  content: "\f295";
}

.nc-refresh-02::before {
  content: "\f296";
}

.nc-refund::before {
  content: "\f297";
}

.nc-reload::before {
  content: "\f298";
}

.nc-remote-control::before {
  content: "\f299";
}

.nc-remove::before {
  content: "\f29a";
}

.nc-remove-fav::before {
  content: "\f29b";
}

.nc-remove-favorite::before {
  content: "\f29c";
}

.nc-remove-like::before {
  content: "\f29d";
}

.nc-repeat::before {
  content: "\f29e";
}

.nc-repeat-cycle::before {
  content: "\f29f";
}

.nc-replay::before {
  content: "\f2a0";
}

.nc-reply::before {
  content: "\f2a1";
}

.nc-reply-all::before {
  content: "\f2a2";
}

.nc-reply-arrow::before {
  content: "\f2a3";
}

.nc-research::before {
  content: "\f2a4";
}

.nc-reservation::before {
  content: "\f2a5";
}

.nc-resistance-band::before {
  content: "\f2a6";
}

.nc-resize-x::before {
  content: "\f2a7";
}

.nc-resize-y::before {
  content: "\f2a8";
}

.nc-respond-arrow::before {
  content: "\f2a9";
}

.nc-restaurant-menu::before {
  content: "\f2aa";
}

.nc-restore::before {
  content: "\f2ab";
}

.nc-rice::before {
  content: "\f2ac";
}

.nc-right-arrow::before {
  content: "\f2ad";
}

.nc-rim::before {
  content: "\f2ae";
}

.nc-ring::before {
  content: "\f2af";
}

.nc-rings::before {
  content: "\f2b0";
}

.nc-rio-statue::before {
  content: "\f2b1";
}

.nc-ripple-anim::before {
  content: "\f2b2";
}

.nc-road::before {
  content: "\f2b3";
}

.nc-road-2::before {
  content: "\f2b4";
}

.nc-road-sign-left::before {
  content: "\f2b5";
}

.nc-road-sign-right::before {
  content: "\f2b6";
}

.nc-roadmap::before {
  content: "\f2b7";
}

.nc-roast-chicken::before {
  content: "\f2b8";
}

.nc-roast-turkey::before {
  content: "\f2b9";
}

.nc-robot::before {
  content: "\f2ba";
}

.nc-robot-cleaner::before {
  content: "\f2bb";
}

.nc-robotic-arm::before {
  content: "\f2bc";
}

.nc-rock::before {
  content: "\f2bd";
}

.nc-rolling-pin::before {
  content: "\f2be";
}

.nc-romantic-dinner::before {
  content: "\f2bf";
}

.nc-romantic-restaurant::before {
  content: "\f2c0";
}

.nc-rotate-22::before {
  content: "\f2c1";
}

.nc-rotate-23::before {
  content: "\f2c2";
}

.nc-rotate-camera::before {
  content: "\f2c3";
}

.nc-rotate-left::before {
  content: "\f2c4";
}

.nc-rotate-right::before {
  content: "\f2c5";
}

.nc-rotating-bars-anim::before {
  content: "\f2c6";
}

.nc-roulette::before {
  content: "\f2c7";
}

.nc-round-dollar::before {
  content: "\f2c8";
}

.nc-round-euro::before {
  content: "\f2c9";
}

.nc-round-pound::before {
  content: "\f2ca";
}

.nc-round-yen::before {
  content: "\f2cb";
}

.nc-route::before {
  content: "\f2cc";
}

.nc-route-alert::before {
  content: "\f2cd";
}

.nc-route-close::before {
  content: "\f2ce";
}

.nc-route-open::before {
  content: "\f2cf";
}

.nc-router::before {
  content: "\f2d0";
}

.nc-row-machine::before {
  content: "\f2d1";
}

.nc-row-table::before {
  content: "\f2d2";
}

.nc-rowing::before {
  content: "\f2d3";
}

.nc-rowing-oars::before {
  content: "\f2d4";
}

.nc-rugby::before {
  content: "\f2d5";
}

.nc-rugby-ball::before {
  content: "\f2d6";
}

.nc-ruler-pencil::before {
  content: "\f2d7";
}

.nc-run-shoes::before {
  content: "\f2d8";
}

.nc-runny-nose::before {
  content: "\f2d9";
}

.nc-s-add::before {
  content: "\f2da";
}

.nc-s-ban::before {
  content: "\f2db";
}

.nc-s-check::before {
  content: "\f2dc";
}

.nc-s-delete::before {
  content: "\f2dd";
}

.nc-s-edit::before {
  content: "\f2de";
}

.nc-s-info::before {
  content: "\f2df";
}

.nc-s-pulse::before {
  content: "\f2e0";
}

.nc-s-question::before {
  content: "\f2e1";
}

.nc-s-remove::before {
  content: "\f2e2";
}

.nc-s-warning::before {
  content: "\f2e3";
}

.nc-sad::before {
  content: "\f2e4";
}

.nc-safe::before {
  content: "\f2e5";
}

.nc-sagrada-familia::before {
  content: "\f2e6";
}

.nc-saint-basil-cathedral::before {
  content: "\f2e7";
}

.nc-salad::before {
  content: "\f2e8";
}

.nc-sale::before {
  content: "\f2e9";
}

.nc-salt::before {
  content: "\f2ea";
}

.nc-santa-hat::before {
  content: "\f2eb";
}

.nc-satellite::before {
  content: "\f2ec";
}

.nc-satellite-dish::before {
  content: "\f2ed";
}

.nc-satisfied::before {
  content: "\f2ee";
}

.nc-sauna::before {
  content: "\f2ef";
}

.nc-sausage::before {
  content: "\f2f0";
}

.nc-save-for-later::before {
  content: "\f2f1";
}

.nc-save-planet::before {
  content: "\f2f2";
}

.nc-save-the-date::before {
  content: "\f2f3";
}

.nc-save-to-list::before {
  content: "\f2f4";
}

.nc-saved-items::before {
  content: "\f2f5";
}

.nc-savings::before {
  content: "\f2f6";
}

.nc-saxophone::before {
  content: "\f2f7";
}

.nc-scale::before {
  content: "\f2f8";
}

.nc-scale-2::before {
  content: "\f2f9";
}

.nc-scale-3::before {
  content: "\f2fa";
}

.nc-scale-4::before {
  content: "\f2fb";
}

.nc-scale-down::before {
  content: "\f2fc";
}

.nc-scale-up::before {
  content: "\f2fd";
}

.nc-scan::before {
  content: "\f2fe";
}

.nc-scarecrow::before {
  content: "\f2ff";
}

.nc-scarf::before {
  content: "\f300";
}

.nc-scented-candle::before {
  content: "\f301";
}

.nc-school::before {
  content: "\f302";
}

.nc-scissors::before {
  content: "\f303";
}

.nc-scissors-2::before {
  content: "\f304";
}

.nc-scissors-dashed::before {
  content: "\f305";
}

.nc-scooter::before {
  content: "\f306";
}

.nc-scotch::before {
  content: "\f307";
}

.nc-screen-enlarge::before {
  content: "\f308";
}

.nc-screen-expand::before {
  content: "\f309";
}

.nc-screen-maximize::before {
  content: "\f30a";
}

.nc-screen-reader::before {
  content: "\f30b";
}

.nc-screen-rotation::before {
  content: "\f30c";
}

.nc-screen-sharing-2::before {
  content: "\f30d";
}

.nc-screen-sharing-off-2::before {
  content: "\f30e";
}

.nc-screen-touch::before {
  content: "\f30f";
}

.nc-scroll-horizontal::before {
  content: "\f310";
}

.nc-scroll-vertical::before {
  content: "\f311";
}

.nc-sd-card::before {
  content: "\f312";
}

.nc-search::before {
  content: "\f313";
}

.nc-search-content::before {
  content: "\f314";
}

.nc-search-property::before {
  content: "\f315";
}

.nc-search-user::before {
  content: "\f316";
}

.nc-search-zoom-in::before {
  content: "\f317";
}

.nc-search-zoom-out::before {
  content: "\f318";
}

.nc-seat::before {
  content: "\f319";
}

.nc-seatbelt::before {
  content: "\f31a";
}

.nc-security::before {
  content: "\f31b";
}

.nc-security-gate::before {
  content: "\f31c";
}

.nc-security-officer::before {
  content: "\f31d";
}

.nc-segmentation::before {
  content: "\f31e";
}

.nc-select::before {
  content: "\f31f";
}

.nc-selection::before {
  content: "\f320";
}

.nc-selfie::before {
  content: "\f321";
}

.nc-selfie-2::before {
  content: "\f322";
}

.nc-send::before {
  content: "\f323";
}

.nc-send-message::before {
  content: "\f324";
}

.nc-send-to-phone::before {
  content: "\f325";
}

.nc-sensor::before {
  content: "\f326";
}

.nc-separate::before {
  content: "\f327";
}

.nc-separate-branch::before {
  content: "\f328";
}

.nc-separate-directions::before {
  content: "\f329";
}

.nc-server::before {
  content: "\f32a";
}

.nc-server-rack::before {
  content: "\f32b";
}

.nc-settings::before {
  content: "\f32c";
}

.nc-settings-gear::before {
  content: "\f32d";
}

.nc-settings-wheel::before {
  content: "\f32e";
}

.nc-setup-options::before {
  content: "\f32f";
}

.nc-setup-preferences::before {
  content: "\f330";
}

.nc-setup-tools::before {
  content: "\f331";
}

.nc-seven::before {
  content: "\f332";
}

.nc-shaker::before {
  content: "\f333";
}

.nc-shape-adjust::before {
  content: "\f334";
}

.nc-shape-arrow::before {
  content: "\f335";
}

.nc-shape-circle::before {
  content: "\f336";
}

.nc-shape-custom::before {
  content: "\f337";
}

.nc-shape-line::before {
  content: "\f338";
}

.nc-shape-oval::before {
  content: "\f339";
}

.nc-shape-polygon::before {
  content: "\f33a";
}

.nc-shape-polygon-2::before {
  content: "\f33b";
}

.nc-shape-rectangle::before {
  content: "\f33c";
}

.nc-shape-square::before {
  content: "\f33d";
}

.nc-shape-star::before {
  content: "\f33e";
}

.nc-shape-triangle::before {
  content: "\f33f";
}

.nc-shape-triangle-2::before {
  content: "\f340";
}

.nc-shapes::before {
  content: "\f341";
}

.nc-share::before {
  content: "\f342";
}

.nc-share-2::before {
  content: "\f343";
}

.nc-share-3::before {
  content: "\f344";
}

.nc-sharing::before {
  content: "\f345";
}

.nc-shark::before {
  content: "\f346";
}

.nc-shark-2::before {
  content: "\f347";
}

.nc-sharpen::before {
  content: "\f348";
}

.nc-sharpener::before {
  content: "\f349";
}

.nc-sheep::before {
  content: "\f34a";
}

.nc-shell::before {
  content: "\f34b";
}

.nc-shield::before {
  content: "\f34c";
}

.nc-shinto::before {
  content: "\f34d";
}

.nc-shipping-container::before {
  content: "\f34e";
}

.nc-shirt::before {
  content: "\f34f";
}

.nc-shirt-business::before {
  content: "\f350";
}

.nc-shirt-buttons::before {
  content: "\f351";
}

.nc-shirt-neck::before {
  content: "\f352";
}

.nc-shoe-man::before {
  content: "\f353";
}

.nc-shoe-woman::before {
  content: "\f354";
}

.nc-shop::before {
  content: "\f355";
}

.nc-shop-location::before {
  content: "\f356";
}

.nc-shopping-bag::before {
  content: "\f357";
}

.nc-shopping-cart::before {
  content: "\f358";
}

.nc-shopping-cart-2::before {
  content: "\f359";
}

.nc-shopping-label::before {
  content: "\f35a";
}

.nc-shopping-tag::before {
  content: "\f35b";
}

.nc-shorts::before {
  content: "\f35c";
}

.nc-shotgun::before {
  content: "\f35d";
}

.nc-shovel::before {
  content: "\f35e";
}

.nc-show::before {
  content: "\f35f";
}

.nc-shower::before {
  content: "\f360";
}

.nc-shrimp::before {
  content: "\f361";
}

.nc-shuffle::before {
  content: "\f362";
}

.nc-shuffle-2::before {
  content: "\f363";
}

.nc-shuttle::before {
  content: "\f364";
}

.nc-shuttlecock::before {
  content: "\f365";
}

.nc-shy::before {
  content: "\f366";
}

.nc-sick::before {
  content: "\f367";
}

.nc-sickle::before {
  content: "\f368";
}

.nc-sickle-2::before {
  content: "\f369";
}

.nc-sidebar::before {
  content: "\f36a";
}

.nc-sign::before {
  content: "\f36b";
}

.nc-sign-board::before {
  content: "\f36c";
}

.nc-sign-down::before {
  content: "\f36d";
}

.nc-sign-left::before {
  content: "\f36e";
}

.nc-sign-right::before {
  content: "\f36f";
}

.nc-sign-up::before {
  content: "\f370";
}

.nc-signal::before {
  content: "\f371";
}

.nc-signature::before {
  content: "\f372";
}

.nc-silly::before {
  content: "\f373";
}

.nc-sim-card::before {
  content: "\f374";
}

.nc-single-bed::before {
  content: "\f375";
}

.nc-sink::before {
  content: "\f376";
}

.nc-sink-faucet::before {
  content: "\f377";
}

.nc-six::before {
  content: "\f378";
}

.nc-size-large::before {
  content: "\f379";
}

.nc-size-medium::before {
  content: "\f37a";
}

.nc-size-small::before {
  content: "\f37b";
}

.nc-skateboard::before {
  content: "\f37c";
}

.nc-skateboard-2::before {
  content: "\f37d";
}

.nc-skateboarding::before {
  content: "\f37e";
}

.nc-skating::before {
  content: "\f37f";
}

.nc-skiing::before {
  content: "\f380";
}

.nc-skipping-rope::before {
  content: "\f381";
}

.nc-skirt::before {
  content: "\f382";
}

.nc-skull::before {
  content: "\f383";
}

.nc-skull-2::before {
  content: "\f384";
}

.nc-slacks-12::before {
  content: "\f385";
}

.nc-slacks-13::before {
  content: "\f386";
}

.nc-sleep::before {
  content: "\f387";
}

.nc-sleep-2::before {
  content: "\f388";
}

.nc-sleeping-baby::before {
  content: "\f389";
}

.nc-slice::before {
  content: "\f38a";
}

.nc-slide-left::before {
  content: "\f38b";
}

.nc-slide-right::before {
  content: "\f38c";
}

.nc-slider::before {
  content: "\f38d";
}

.nc-slideshow-anim::before {
  content: "\f38e";
}

.nc-slippers::before {
  content: "\f38f";
}

.nc-slot-machine::before {
  content: "\f390";
}

.nc-sloth::before {
  content: "\f391";
}

.nc-smart::before {
  content: "\f392";
}

.nc-smart-house::before {
  content: "\f393";
}

.nc-smartphone::before {
  content: "\f394";
}

.nc-smartwatch::before {
  content: "\f395";
}

.nc-smile::before {
  content: "\f396";
}

.nc-smiling-face-glasses::before {
  content: "\f397";
}

.nc-smiling-face-sunglasses::before {
  content: "\f398";
}

.nc-smoking::before {
  content: "\f399";
}

.nc-smoothie::before {
  content: "\f39a";
}

.nc-snack::before {
  content: "\f39b";
}

.nc-snake::before {
  content: "\f39c";
}

.nc-sneeze::before {
  content: "\f39d";
}

.nc-sniper-rifle::before {
  content: "\f39e";
}

.nc-snorkel-mask::before {
  content: "\f39f";
}

.nc-snow::before {
  content: "\f3a0";
}

.nc-snow-ball::before {
  content: "\f3a1";
}

.nc-snowboard::before {
  content: "\f3a2";
}

.nc-snowboarding::before {
  content: "\f3a3";
}

.nc-snowman::before {
  content: "\f3a4";
}

.nc-snowman-head::before {
  content: "\f3a5";
}

.nc-soap::before {
  content: "\f3a6";
}

.nc-soccer::before {
  content: "\f3a7";
}

.nc-soccer-ball::before {
  content: "\f3a8";
}

.nc-soccer-field::before {
  content: "\f3a9";
}

.nc-social-distancing::before {
  content: "\f3aa";
}

.nc-social-sharing::before {
  content: "\f3ab";
}

.nc-sock::before {
  content: "\f3ac";
}

.nc-socket::before {
  content: "\f3ad";
}

.nc-socket-europe-1::before {
  content: "\f3ae";
}

.nc-socket-europe-2::before {
  content: "\f3af";
}

.nc-socket-uk::before {
  content: "\f3b0";
}

.nc-sofa::before {
  content: "\f3b1";
}

.nc-soft-drink::before {
  content: "\f3b2";
}

.nc-soldier::before {
  content: "\f3b3";
}

.nc-solider-helmet::before {
  content: "\f3b4";
}

.nc-sort-tool::before {
  content: "\f3b5";
}

.nc-sound::before {
  content: "\f3b6";
}

.nc-sound-wave::before {
  content: "\f3b7";
}

.nc-soundwave::before {
  content: "\f3b8";
}

.nc-soup::before {
  content: "\f3b9";
}

.nc-soy-sauce::before {
  content: "\f3ba";
}

.nc-spa::before {
  content: "\f3bb";
}

.nc-spa-rocks::before {
  content: "\f3bc";
}

.nc-space-divider::before {
  content: "\f3bd";
}

.nc-spaceship::before {
  content: "\f3be";
}

.nc-spade::before {
  content: "\f3bf";
}

.nc-spades-suit::before {
  content: "\f3c0";
}

.nc-sparks::before {
  content: "\f3c1";
}

.nc-speaker::before {
  content: "\f3c2";
}

.nc-speaker-2::before {
  content: "\f3c3";
}

.nc-spectrum::before {
  content: "\f3c4";
}

.nc-speechless::before {
  content: "\f3c5";
}

.nc-speedometer::before {
  content: "\f3c6";
}

.nc-sperm::before {
  content: "\f3c7";
}

.nc-sphinx::before {
  content: "\f3c8";
}

.nc-spider::before {
  content: "\f3c9";
}

.nc-spinning-bike::before {
  content: "\f3ca";
}

.nc-spiteful::before {
  content: "\f3cb";
}

.nc-split::before {
  content: "\f3cc";
}

.nc-split-branch::before {
  content: "\f3cd";
}

.nc-sport-bag::before {
  content: "\f3ce";
}

.nc-sport-mode::before {
  content: "\f3cf";
}

.nc-sports-bra::before {
  content: "\f3d0";
}

.nc-sports-fan::before {
  content: "\f3d1";
}

.nc-sports-tank::before {
  content: "\f3d2";
}

.nc-spray-bottle::before {
  content: "\f3d3";
}

.nc-spray-can::before {
  content: "\f3d4";
}

.nc-square-marker::before {
  content: "\f3d5";
}

.nc-square-pin::before {
  content: "\f3d6";
}

.nc-squares-anim::before {
  content: "\f3d7";
}

.nc-squares-anim-2::before {
  content: "\f3d8";
}

.nc-ssd::before {
  content: "\f3d9";
}

.nc-stack::before {
  content: "\f3da";
}

.nc-stadium::before {
  content: "\f3db";
}

.nc-stair-climber::before {
  content: "\f3dc";
}

.nc-stairs::before {
  content: "\f3dd";
}

.nc-stamp::before {
  content: "\f3de";
}

.nc-standing-man::before {
  content: "\f3df";
}

.nc-standing-woman::before {
  content: "\f3e0";
}

.nc-star::before {
  content: "\f3e1";
}

.nc-star-rate::before {
  content: "\f3e2";
}

.nc-statistics::before {
  content: "\f3e3";
}

.nc-statue-of-liberty::before {
  content: "\f3e4";
}

.nc-stay-home::before {
  content: "\f3e5";
}

.nc-steak::before {
  content: "\f3e6";
}

.nc-steak-2::before {
  content: "\f3e7";
}

.nc-steam-iron::before {
  content: "\f3e8";
}

.nc-steering-wheel::before {
  content: "\f3e9";
}

.nc-steps::before {
  content: "\f3ea";
}

.nc-stethoscope::before {
  content: "\f3eb";
}

.nc-sticker::before {
  content: "\f3ec";
}

.nc-stock-2::before {
  content: "\f3ed";
}

.nc-stock-market::before {
  content: "\f3ee";
}

.nc-stop-sign::before {
  content: "\f3ef";
}

.nc-stopwatch::before {
  content: "\f3f0";
}

.nc-storage-hanger::before {
  content: "\f3f1";
}

.nc-storage-shelves::before {
  content: "\f3f2";
}

.nc-storage-unit::before {
  content: "\f3f3";
}

.nc-store::before {
  content: "\f3f4";
}

.nc-strawberry::before {
  content: "\f3f5";
}

.nc-stretch::before {
  content: "\f3f6";
}

.nc-stretching::before {
  content: "\f3f7";
}

.nc-strikethrough::before {
  content: "\f3f8";
}

.nc-style::before {
  content: "\f3f9";
}

.nc-submachine-gun::before {
  content: "\f3fa";
}

.nc-submarine::before {
  content: "\f3fb";
}

.nc-subscript::before {
  content: "\f3fc";
}

.nc-subtitles::before {
  content: "\f3fd";
}

.nc-sugar::before {
  content: "\f3fe";
}

.nc-sun::before {
  content: "\f3ff";
}

.nc-sun-cloud::before {
  content: "\f400";
}

.nc-sun-cloud-drop::before {
  content: "\f401";
}

.nc-sun-cloud-fog::before {
  content: "\f402";
}

.nc-sun-cloud-hail::before {
  content: "\f403";
}

.nc-sun-cloud-light::before {
  content: "\f404";
}

.nc-sun-cloud-rain::before {
  content: "\f405";
}

.nc-sun-cloud-snow-54::before {
  content: "\f406";
}

.nc-sun-cloud-snow-55::before {
  content: "\f407";
}

.nc-sun-fog-29::before {
  content: "\f408";
}

.nc-sun-fog-30::before {
  content: "\f409";
}

.nc-sun-fog-43::before {
  content: "\f40a";
}

.nc-sunglasses::before {
  content: "\f40b";
}

.nc-sunglasses-48::before {
  content: "\f40c";
}

.nc-superscript::before {
  content: "\f40d";
}

.nc-support::before {
  content: "\f40e";
}

.nc-surfboard::before {
  content: "\f40f";
}

.nc-surprise::before {
  content: "\f410";
}

.nc-survey::before {
  content: "\f411";
}

.nc-sushi::before {
  content: "\f412";
}

.nc-swap-horizontal::before {
  content: "\f413";
}

.nc-swap-vertical::before {
  content: "\f414";
}

.nc-swimming::before {
  content: "\f415";
}

.nc-swimming-pool::before {
  content: "\f416";
}

.nc-swimsuit::before {
  content: "\f417";
}

.nc-swipe-bottom::before {
  content: "\f418";
}

.nc-swipe-left::before {
  content: "\f419";
}

.nc-swipe-right::before {
  content: "\f41a";
}

.nc-swipe-up::before {
  content: "\f41b";
}

.nc-swiss-knife::before {
  content: "\f41c";
}

.nc-switches::before {
  content: "\f41d";
}

.nc-sword::before {
  content: "\f41e";
}

.nc-sydney-opera-house::before {
  content: "\f41f";
}

.nc-sync-devices::before {
  content: "\f420";
}

.nc-syringe::before {
  content: "\f421";
}

.nc-system-configuration::before {
  content: "\f422";
}

.nc-system-preferences::before {
  content: "\f423";
}

.nc-system-update::before {
  content: "\f424";
}

.nc-t-add::before {
  content: "\f425";
}

.nc-t-delete::before {
  content: "\f426";
}

.nc-t-remove::before {
  content: "\f427";
}

.nc-table::before {
  content: "\f428";
}

.nc-table-lamp::before {
  content: "\f429";
}

.nc-table-layout::before {
  content: "\f42a";
}

.nc-table-move::before {
  content: "\f42b";
}

.nc-table-slide::before {
  content: "\f42c";
}

.nc-table-tennis-bat::before {
  content: "\f42d";
}

.nc-tablet::before {
  content: "\f42e";
}

.nc-tablet-2::before {
  content: "\f42f";
}

.nc-tablet-charging::before {
  content: "\f430";
}

.nc-tablet-mobile::before {
  content: "\f431";
}

.nc-tablet-toolbar::before {
  content: "\f432";
}

.nc-tacos::before {
  content: "\f433";
}

.nc-tactic::before {
  content: "\f434";
}

.nc-tag::before {
  content: "\f435";
}

.nc-tag-add::before {
  content: "\f436";
}

.nc-tag-check::before {
  content: "\f437";
}

.nc-tag-cut::before {
  content: "\f438";
}

.nc-tag-loyalty::before {
  content: "\f439";
}

.nc-tag-remove::before {
  content: "\f43a";
}

.nc-tag-sale::before {
  content: "\f43b";
}

.nc-tags-stack::before {
  content: "\f43c";
}

.nc-take-off::before {
  content: "\f43d";
}

.nc-takeaway::before {
  content: "\f43e";
}

.nc-taoism::before {
  content: "\f43f";
}

.nc-tap-01::before {
  content: "\f440";
}

.nc-tap-02::before {
  content: "\f441";
}

.nc-tape::before {
  content: "\f442";
}

.nc-target::before {
  content: "\f443";
}

.nc-taxi::before {
  content: "\f444";
}

.nc-tea::before {
  content: "\f445";
}

.nc-tea-bag::before {
  content: "\f446";
}

.nc-team::before {
  content: "\f447";
}

.nc-teddy-bear::before {
  content: "\f448";
}

.nc-telegram::before {
  content: "\f449";
}

.nc-telephone::before {
  content: "\f44a";
}

.nc-telescope::before {
  content: "\f44b";
}

.nc-temperature::before {
  content: "\f44c";
}

.nc-temperature-2::before {
  content: "\f44d";
}

.nc-temple::before {
  content: "\f44e";
}

.nc-temple-2::before {
  content: "\f44f";
}

.nc-tennis::before {
  content: "\f450";
}

.nc-tennis-ball::before {
  content: "\f451";
}

.nc-tennis-racket::before {
  content: "\f452";
}

.nc-terrace::before {
  content: "\f453";
}

.nc-text::before {
  content: "\f454";
}

.nc-text-align-center::before {
  content: "\f455";
}

.nc-text-align-justify::before {
  content: "\f456";
}

.nc-text-align-left::before {
  content: "\f457";
}

.nc-text-align-right::before {
  content: "\f458";
}

.nc-text-bg-color::before {
  content: "\f459";
}

.nc-text-color::before {
  content: "\f45a";
}

.nc-text-horizontal-scale::before {
  content: "\f45b";
}

.nc-text-italic::before {
  content: "\f45c";
}

.nc-text-size::before {
  content: "\f45d";
}

.nc-text-tracking::before {
  content: "\f45e";
}

.nc-text-vertical-scale::before {
  content: "\f45f";
}

.nc-texture::before {
  content: "\f460";
}

.nc-theater::before {
  content: "\f461";
}

.nc-theater-curtains::before {
  content: "\f462";
}

.nc-thermometer::before {
  content: "\f463";
}

.nc-three::before {
  content: "\f464";
}

.nc-three-dimensional-object::before {
  content: "\f465";
}

.nc-three-dimensional-world::before {
  content: "\f466";
}

.nc-three-way-direction::before {
  content: "\f467";
}

.nc-thumb-down::before {
  content: "\f468";
}

.nc-thumb-up::before {
  content: "\f469";
}

.nc-ticket::before {
  content: "\f46a";
}

.nc-tie-01::before {
  content: "\f46b";
}

.nc-tie-02::before {
  content: "\f46c";
}

.nc-tie-bow::before {
  content: "\f46d";
}

.nc-tiktok::before {
  content: "\f46e";
}

.nc-time-alarm::before {
  content: "\f46f";
}

.nc-time-clock::before {
  content: "\f470";
}

.nc-time-machine::before {
  content: "\f471";
}

.nc-timeline::before {
  content: "\f472";
}

.nc-timer::before {
  content: "\f473";
}

.nc-title-case::before {
  content: "\f474";
}

.nc-tnt-explosives::before {
  content: "\f475";
}

.nc-toast::before {
  content: "\f476";
}

.nc-toaster::before {
  content: "\f477";
}

.nc-todo::before {
  content: "\f478";
}

.nc-toggle::before {
  content: "\f479";
}

.nc-toilet::before {
  content: "\f47a";
}

.nc-toilet-paper::before {
  content: "\f47b";
}

.nc-toilette::before {
  content: "\f47c";
}

.nc-tomato::before {
  content: "\f47d";
}

.nc-tool-blur::before {
  content: "\f47e";
}

.nc-tool-hand::before {
  content: "\f47f";
}

.nc-tool-select::before {
  content: "\f480";
}

.nc-tooth::before {
  content: "\f481";
}

.nc-towel::before {
  content: "\f482";
}

.nc-towel-hanger::before {
  content: "\f483";
}

.nc-track-delivery::before {
  content: "\f484";
}

.nc-tractor::before {
  content: "\f485";
}

.nc-traffic::before {
  content: "\f486";
}

.nc-train::before {
  content: "\f487";
}

.nc-train-speed::before {
  content: "\f488";
}

.nc-tram::before {
  content: "\f489";
}

.nc-transaction::before {
  content: "\f48a";
}

.nc-transactions::before {
  content: "\f48b";
}

.nc-transform::before {
  content: "\f48c";
}

.nc-transform-2d::before {
  content: "\f48d";
}

.nc-transform-origin::before {
  content: "\f48e";
}

.nc-translation::before {
  content: "\f48f";
}

.nc-transparent::before {
  content: "\f490";
}

.nc-trash::before {
  content: "\f491";
}

.nc-trash-can::before {
  content: "\f492";
}

.nc-travel-makeup-mirror::before {
  content: "\f493";
}

.nc-treadmill::before {
  content: "\f494";
}

.nc-treasure-map-21::before {
  content: "\f495";
}

.nc-treasure-map-40::before {
  content: "\f496";
}

.nc-tree::before {
  content: "\f497";
}

.nc-tree-01::before {
  content: "\f498";
}

.nc-tree-03::before {
  content: "\f499";
}

.nc-tree-2::before {
  content: "\f49a";
}

.nc-tree-ball::before {
  content: "\f49b";
}

.nc-trend-down::before {
  content: "\f49c";
}

.nc-trend-up::before {
  content: "\f49d";
}

.nc-triangle-down::before {
  content: "\f49e";
}

.nc-triangle-left::before {
  content: "\f49f";
}

.nc-triangle-line-down::before {
  content: "\f4a0";
}

.nc-triangle-line-left::before {
  content: "\f4a1";
}

.nc-triangle-line-right::before {
  content: "\f4a2";
}

.nc-triangle-line-up::before {
  content: "\f4a3";
}

.nc-triangle-right::before {
  content: "\f4a4";
}

.nc-triangle-sm-down::before {
  content: "\f4a5";
}

.nc-triangle-sm-left::before {
  content: "\f4a6";
}

.nc-triangle-sm-right::before {
  content: "\f4a7";
}

.nc-triangle-sm-up::before {
  content: "\f4a8";
}

.nc-triangle-up::before {
  content: "\f4a9";
}

.nc-tripod::before {
  content: "\f4aa";
}

.nc-trophy::before {
  content: "\f4ab";
}

.nc-trowel::before {
  content: "\f4ac";
}

.nc-truck-front::before {
  content: "\f4ad";
}

.nc-trumpet::before {
  content: "\f4ae";
}

.nc-trunk::before {
  content: "\f4af";
}

.nc-tshirt-53::before {
  content: "\f4b0";
}

.nc-tshirt-54::before {
  content: "\f4b1";
}

.nc-tshirt-sport::before {
  content: "\f4b2";
}

.nc-tty::before {
  content: "\f4b3";
}

.nc-turkey-head::before {
  content: "\f4b4";
}

.nc-turn-e::before {
  content: "\f4b5";
}

.nc-turn-n::before {
  content: "\f4b6";
}

.nc-turn-s::before {
  content: "\f4b7";
}

.nc-turn-w::before {
  content: "\f4b8";
}

.nc-turtle::before {
  content: "\f4b9";
}

.nc-tv::before {
  content: "\f4ba";
}

.nc-tv-stand::before {
  content: "\f4bb";
}

.nc-twitch::before {
  content: "\f4bc";
}

.nc-two::before {
  content: "\f4bd";
}

.nc-two-way-direction::before {
  content: "\f4be";
}

.nc-type-tool::before {
  content: "\f4bf";
}

.nc-typography::before {
  content: "\f4c0";
}

.nc-umbrella-13::before {
  content: "\f4c1";
}

.nc-underline::before {
  content: "\f4c2";
}

.nc-underwear::before {
  content: "\f4c3";
}

.nc-underwear-man::before {
  content: "\f4c4";
}

.nc-undo::before {
  content: "\f4c5";
}

.nc-ungroup::before {
  content: "\f4c6";
}

.nc-unite::before {
  content: "\f4c7";
}

.nc-unite-2::before {
  content: "\f4c8";
}

.nc-unlink::before {
  content: "\f4c9";
}

.nc-unlocked::before {
  content: "\f4ca";
}

.nc-unordered-list::before {
  content: "\f4cb";
}

.nc-up-arrow::before {
  content: "\f4cc";
}

.nc-upload::before {
  content: "\f4cd";
}

.nc-upload-data::before {
  content: "\f4ce";
}

.nc-upload-file::before {
  content: "\f4cf";
}

.nc-uppercase::before {
  content: "\f4d0";
}

.nc-upset-13::before {
  content: "\f4d1";
}

.nc-upset-14::before {
  content: "\f4d2";
}

.nc-url::before {
  content: "\f4d3";
}

.nc-usb::before {
  content: "\f4d4";
}

.nc-user::before {
  content: "\f4d5";
}

.nc-user-2::before {
  content: "\f4d6";
}

.nc-user-add::before {
  content: "\f4d7";
}

.nc-user-bubble::before {
  content: "\f4d8";
}

.nc-user-c-frame::before {
  content: "\f4d9";
}

.nc-user-c-frame-2::before {
  content: "\f4da";
}

.nc-user-c-frame-3::before {
  content: "\f4db";
}

.nc-user-check::before {
  content: "\f4dc";
}

.nc-user-create::before {
  content: "\f4dd";
}

.nc-user-delete::before {
  content: "\f4de";
}

.nc-user-delete-cross::before {
  content: "\f4df";
}

.nc-user-delete-line::before {
  content: "\f4e0";
}

.nc-user-edit::before {
  content: "\f4e1";
}

.nc-user-focus::before {
  content: "\f4e2";
}

.nc-user-group::before {
  content: "\f4e3";
}

.nc-user-heart::before {
  content: "\f4e4";
}

.nc-user-list::before {
  content: "\f4e5";
}

.nc-user-location::before {
  content: "\f4e6";
}

.nc-user-location-2::before {
  content: "\f4e7";
}

.nc-user-lock::before {
  content: "\f4e8";
}

.nc-user-new::before {
  content: "\f4e9";
}

.nc-user-remove::before {
  content: "\f4ea";
}

.nc-user-s-frame::before {
  content: "\f4eb";
}

.nc-user-s-frame-2::before {
  content: "\f4ec";
}

.nc-user-s-frame-3::before {
  content: "\f4ed";
}

.nc-user-search::before {
  content: "\f4ee";
}

.nc-user-share::before {
  content: "\f4ef";
}

.nc-user-star::before {
  content: "\f4f0";
}

.nc-user-sync::before {
  content: "\f4f1";
}

.nc-user-time::before {
  content: "\f4f2";
}

.nc-user-wearing-glasses::before {
  content: "\f4f3";
}

.nc-users::before {
  content: "\f4f4";
}

.nc-users-add::before {
  content: "\f4f5";
}

.nc-users-check::before {
  content: "\f4f6";
}

.nc-users-heart::before {
  content: "\f4f7";
}

.nc-users-location::before {
  content: "\f4f8";
}

.nc-users-lock::before {
  content: "\f4f9";
}

.nc-users-meeting::before {
  content: "\f4fa";
}

.nc-users-mm::before {
  content: "\f4fb";
}

.nc-users-remove::before {
  content: "\f4fc";
}

.nc-users-wm::before {
  content: "\f4fd";
}

.nc-users-ww::before {
  content: "\f4fe";
}

.nc-utility-bench::before {
  content: "\f4ff";
}

.nc-vacuum-cleaner::before {
  content: "\f500";
}

.nc-vampire::before {
  content: "\f501";
}

.nc-vector::before {
  content: "\f502";
}

.nc-vegan::before {
  content: "\f503";
}

.nc-ventilation::before {
  content: "\f504";
}

.nc-verified::before {
  content: "\f505";
}

.nc-vertical-divider::before {
  content: "\f506";
}

.nc-vespa::before {
  content: "\f507";
}

.nc-vespa-front::before {
  content: "\f508";
}

.nc-vest::before {
  content: "\f509";
}

.nc-vest-31::before {
  content: "\f50a";
}

.nc-vibrance::before {
  content: "\f50b";
}

.nc-video::before {
  content: "\f50c";
}

.nc-video-camera::before {
  content: "\f50d";
}

.nc-video-gallery::before {
  content: "\f50e";
}

.nc-video-gallery-2::before {
  content: "\f50f";
}

.nc-video-off::before {
  content: "\f510";
}

.nc-video-player::before {
  content: "\f511";
}

.nc-video-playlist::before {
  content: "\f512";
}

.nc-view::before {
  content: "\f513";
}

.nc-vignette::before {
  content: "\f514";
}

.nc-vintage-computer::before {
  content: "\f515";
}

.nc-vintage-tv::before {
  content: "\f516";
}

.nc-violin::before {
  content: "\f517";
}

.nc-virtual-assistant::before {
  content: "\f518";
}

.nc-virtual-assistant-2::before {
  content: "\f519";
}

.nc-virtual-environment::before {
  content: "\f51a";
}

.nc-virtual-reality::before {
  content: "\f51b";
}

.nc-virus::before {
  content: "\f51c";
}

.nc-voice-recognition::before {
  content: "\f51d";
}

.nc-voice-record::before {
  content: "\f51e";
}

.nc-volleyball::before {
  content: "\f51f";
}

.nc-volleyball-player::before {
  content: "\f520";
}

.nc-volume::before {
  content: "\f521";
}

.nc-volume-2::before {
  content: "\f522";
}

.nc-volume-down::before {
  content: "\f523";
}

.nc-volume-mute::before {
  content: "\f524";
}

.nc-volume-off::before {
  content: "\f525";
}

.nc-volume-up::before {
  content: "\f526";
}

.nc-vpn::before {
  content: "\f527";
}

.nc-vr-controller::before {
  content: "\f528";
}

.nc-vr-headset::before {
  content: "\f529";
}

.nc-waffle::before {
  content: "\f52a";
}

.nc-walk::before {
  content: "\f52b";
}

.nc-walking-aid::before {
  content: "\f52c";
}

.nc-walking-support::before {
  content: "\f52d";
}

.nc-wallet::before {
  content: "\f52e";
}

.nc-wallet-43::before {
  content: "\f52f";
}

.nc-wallet-44::before {
  content: "\f530";
}

.nc-wallet-90::before {
  content: "\f531";
}

.nc-wand::before {
  content: "\f532";
}

.nc-wardrobe::before {
  content: "\f533";
}

.nc-wardrobe-2::before {
  content: "\f534";
}

.nc-wardrobe-3::before {
  content: "\f535";
}

.nc-wardrobe-4::before {
  content: "\f536";
}

.nc-warning-sign::before {
  content: "\f537";
}

.nc-wash-30::before {
  content: "\f538";
}

.nc-wash-60::before {
  content: "\f539";
}

.nc-wash-90::before {
  content: "\f53a";
}

.nc-wash-hand::before {
  content: "\f53b";
}

.nc-wash-hands::before {
  content: "\f53c";
}

.nc-washing-fluid::before {
  content: "\f53d";
}

.nc-washing-machine::before {
  content: "\f53e";
}

.nc-waste::before {
  content: "\f53f";
}

.nc-waste-danger::before {
  content: "\f540";
}

.nc-waste-recycling::before {
  content: "\f541";
}

.nc-watch::before {
  content: "\f542";
}

.nc-watch-2::before {
  content: "\f543";
}

.nc-watch-dev::before {
  content: "\f544";
}

.nc-watch-heart::before {
  content: "\f545";
}

.nc-watch-heartbeat::before {
  content: "\f546";
}

.nc-water::before {
  content: "\f547";
}

.nc-water-aerobics::before {
  content: "\f548";
}

.nc-water-hand::before {
  content: "\f549";
}

.nc-water-polo::before {
  content: "\f54a";
}

.nc-water-polo-ball::before {
  content: "\f54b";
}

.nc-water-sink::before {
  content: "\f54c";
}

.nc-water-surface::before {
  content: "\f54d";
}

.nc-water-wave::before {
  content: "\f54e";
}

.nc-watering-can::before {
  content: "\f54f";
}

.nc-watering-plants::before {
  content: "\f550";
}

.nc-watermelon::before {
  content: "\f551";
}

.nc-wc-sign::before {
  content: "\f552";
}

.nc-web-design::before {
  content: "\f553";
}

.nc-web-hyperlink::before {
  content: "\f554";
}

.nc-web-link::before {
  content: "\f555";
}

.nc-web-url::before {
  content: "\f556";
}

.nc-webcam::before {
  content: "\f557";
}

.nc-webcam-2::before {
  content: "\f558";
}

.nc-webpage::before {
  content: "\f559";
}

.nc-wedding-arch::before {
  content: "\f55a";
}

.nc-wedding-cake::before {
  content: "\f55b";
}

.nc-wedding-ring::before {
  content: "\f55c";
}

.nc-wedding-rings::before {
  content: "\f55d";
}

.nc-weed::before {
  content: "\f55e";
}

.nc-weight-bench::before {
  content: "\f55f";
}

.nc-weight-gain::before {
  content: "\f560";
}

.nc-weight-loss::before {
  content: "\f561";
}

.nc-weight-plate::before {
  content: "\f562";
}

.nc-weight-scale::before {
  content: "\f563";
}

.nc-what::before {
  content: "\f564";
}

.nc-wheel::before {
  content: "\f565";
}

.nc-wheel-2::before {
  content: "\f566";
}

.nc-wheelbarrow::before {
  content: "\f567";
}

.nc-wheelchair::before {
  content: "\f568";
}

.nc-wheelchair-2::before {
  content: "\f569";
}

.nc-wheelchair-ramp::before {
  content: "\f56a";
}

.nc-whisk::before {
  content: "\f56b";
}

.nc-whiskers::before {
  content: "\f56c";
}

.nc-whistle::before {
  content: "\f56d";
}

.nc-white-balance::before {
  content: "\f56e";
}

.nc-white-house::before {
  content: "\f56f";
}

.nc-widget::before {
  content: "\f570";
}

.nc-width::before {
  content: "\f571";
}

.nc-wifi::before {
  content: "\f572";
}

.nc-wifi-2::before {
  content: "\f573";
}

.nc-wifi-off::before {
  content: "\f574";
}

.nc-wifi-protected::before {
  content: "\f575";
}

.nc-wifi-router::before {
  content: "\f576";
}

.nc-wind::before {
  content: "\f577";
}

.nc-wind-2::before {
  content: "\f578";
}

.nc-windmill::before {
  content: "\f579";
}

.nc-windmill-2::before {
  content: "\f57a";
}

.nc-window::before {
  content: "\f57b";
}

.nc-window-add::before {
  content: "\f57c";
}

.nc-window-code::before {
  content: "\f57d";
}

.nc-window-delete::before {
  content: "\f57e";
}

.nc-window-dev::before {
  content: "\f57f";
}

.nc-window-maximize::before {
  content: "\f580";
}

.nc-window-minimize::before {
  content: "\f581";
}

.nc-window-paragraph::before {
  content: "\f582";
}

.nc-window-responsive::before {
  content: "\f583";
}

.nc-windsurfing::before {
  content: "\f584";
}

.nc-wine-list::before {
  content: "\f585";
}

.nc-wink-06::before {
  content: "\f586";
}

.nc-wink-11::before {
  content: "\f587";
}

.nc-wink-69::before {
  content: "\f588";
}

.nc-winner::before {
  content: "\f589";
}

.nc-wireframe::before {
  content: "\f58a";
}

.nc-wireless-charging::before {
  content: "\f58b";
}

.nc-witch-hat::before {
  content: "\f58c";
}

.nc-wolf::before {
  content: "\f58d";
}

.nc-woman::before {
  content: "\f58e";
}

.nc-woman-arrow-down::before {
  content: "\f58f";
}

.nc-woman-arrow-up::before {
  content: "\f590";
}

.nc-woman-man::before {
  content: "\f591";
}

.nc-woman-profile::before {
  content: "\f592";
}

.nc-woman-up-front::before {
  content: "\f593";
}

.nc-wood::before {
  content: "\f594";
}

.nc-wool-ball::before {
  content: "\f595";
}

.nc-work-badge::before {
  content: "\f596";
}

.nc-workout-plan::before {
  content: "\f597";
}

.nc-world::before {
  content: "\f598";
}

.nc-world-2::before {
  content: "\f599";
}

.nc-world-marker::before {
  content: "\f59a";
}

.nc-world-pin::before {
  content: "\f59b";
}

.nc-wrench::before {
  content: "\f59c";
}

.nc-wrench-tool::before {
  content: "\f59d";
}

.nc-xmas-sock::before {
  content: "\f59e";
}

.nc-yoga::before {
  content: "\f59f";
}

.nc-yogurt::before {
  content: "\f5a0";
}

.nc-zero::before {
  content: "\f5a1";
}

.nc-zipped-file::before {
  content: "\f5a2";
}

.nc-zombie::before {
  content: "\f5a3";
}

.nc-zoom::before {
  content: "\f5a4";
}

.nc-zoom-in::before {
  content: "\f5a5";
}

.nc-zoom-out::before {
  content: "\f5a6";
}

