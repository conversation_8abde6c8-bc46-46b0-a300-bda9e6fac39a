/*--------------------------------

Nucleo icon font
Generated using nucleoapp.com

-------------------------------- */

@nc-font-path: "../fonts";

@font-face {
  font-family: 'Nucleo';
  src: url('@{nc-font-path}/Nucleo.eot');
  src: url('@{nc-font-path}/Nucleo.eot') format('embedded-opentype'),
       url('@{nc-font-path}/Nucleo.woff2') format('woff2'),
       url('@{nc-font-path}/Nucleo.woff') format('woff'),
       url('@{nc-font-path}/Nucleo.ttf') format('truetype'),
       url('@{nc-font-path}/Nucleo.svg') format('svg');
}

/* base class */
.icon {
  display: inline-block;
  font: normal normal normal 1em/1 'Nucleo';
  color: inherit;
  flex-shrink: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* size examples - relative units */
.icon-sm {
	font-size: 0.8em;
}

.icon-lg {
  font-size: 1.2em;
}

/* size examples - absolute units */
.icon-16 {
  font-size: 16px;
}

.icon-32 {
  font-size: 32px;
}

/* rotate the icon infinitely */
.icon-is-spinning {
  animation: icon-spin 1s infinite linear;
}

@keyframes icon-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* transform */
.icon-rotate-90  {
	transform: rotate(90deg);
}

.icon-rotate-180 {
	transform: rotate(180deg);
}

.icon-rotate-270 {
	transform: rotate(270deg);
}

.icon-flip-y {
	transform: scaleY(-1);
}

.icon-flip-x {
	transform: scaleX(-1);
}

/* icons */
@nc-2x-drag-down: "\ea01";
@nc-2x-drag-up: "\ea02";
@nc-2x-swipe-down: "\ea03";
@nc-2x-swipe-left: "\ea04";
@nc-2x-swipe-right: "\ea05";
@nc-2x-swipe-up: "\ea06";
@nc-2x-tap: "\ea07";
@nc-3d-29: "\ea08";
@nc-3d-glasses: "\ea09";
@nc-3d-model: "\ea0a";
@nc-3d-printing: "\ea0b";
@nc-3x-swipe-left: "\ea0c";
@nc-3x-swipe-right: "\ea0d";
@nc-3x-swipe-up: "\ea0e";
@nc-3x-tap: "\ea0f";
@nc-4x-swipe-left: "\ea10";
@nc-4x-swipe-right: "\ea11";
@nc-4x-swipe-up: "\ea12";
@nc-a-chart: "\ea13";
@nc-a-chat: "\ea14";
@nc-a-tag: "\ea15";
@nc-a-tag-add: "\ea16";
@nc-a-tag-remove: "\ea17";
@nc-abc: "\ea18";
@nc-access-key: "\ea19";
@nc-accessibility: "\ea1a";
@nc-accessibility-lift: "\ea1b";
@nc-account: "\ea1c";
@nc-acorn: "\ea1d";
@nc-active-38: "\ea1e";
@nc-active-40: "\ea1f";
@nc-adaptive-bike: "\ea20";
@nc-add: "\ea21";
@nc-add-fav: "\ea22";
@nc-add-favorite: "\ea23";
@nc-add-like: "\ea24";
@nc-add-notification: "\ea25";
@nc-add-to-cart: "\ea26";
@nc-add-to-cart-2: "\ea27";
@nc-address-book: "\ea28";
@nc-adult-content: "\ea29";
@nc-agenda: "\ea2a";
@nc-agenda-bookmark: "\ea2b";
@nc-ai: "\ea2c";
@nc-ai-generated-img: "\ea2d";
@nc-air-baloon: "\ea2e";
@nc-air-bomb: "\ea2f";
@nc-air-conditioner: "\ea30";
@nc-airbag: "\ea31";
@nc-airplane: "\ea32";
@nc-airport: "\ea33";
@nc-airport-trolley: "\ea34";
@nc-alarm: "\ea35";
@nc-alarm-add: "\ea36";
@nc-alarm-disable: "\ea37";
@nc-album: "\ea38";
@nc-alcohol: "\ea39";
@nc-algorithm: "\ea3a";
@nc-alien-29: "\ea3b";
@nc-alien-33: "\ea3c";
@nc-align-bottom: "\ea3d";
@nc-align-center-horizontal: "\ea3e";
@nc-align-center-vertical: "\ea3f";
@nc-align-left: "\ea40";
@nc-align-right: "\ea41";
@nc-align-top: "\ea42";
@nc-all-directions: "\ea43";
@nc-alpha-order: "\ea44";
@nc-ambulance: "\ea45";
@nc-ampersand: "\ea46";
@nc-analytics: "\ea47";
@nc-anchor: "\ea48";
@nc-android: "\ea49";
@nc-angle: "\ea4a";
@nc-angry-10: "\ea4b";
@nc-angry-44: "\ea4c";
@nc-animation-14: "\ea4d";
@nc-animation-31: "\ea4e";
@nc-animation-32: "\ea4f";
@nc-antenna: "\ea50";
@nc-anti-shake: "\ea51";
@nc-apartment: "\ea52";
@nc-aperture: "\ea53";
@nc-api: "\ea54";
@nc-app: "\ea55";
@nc-app-services: "\ea56";
@nc-app-store: "\ea57";
@nc-apple: "\ea58";
@nc-apple-2: "\ea59";
@nc-appointment: "\ea5a";
@nc-apps: "\ea5b";
@nc-apron: "\ea5c";
@nc-arcade: "\ea5d";
@nc-archer: "\ea5e";
@nc-archery: "\ea5f";
@nc-archery-target: "\ea60";
@nc-archive: "\ea61";
@nc-archive-check: "\ea62";
@nc-archive-content: "\ea63";
@nc-archive-doc: "\ea64";
@nc-archive-doc-check: "\ea65";
@nc-archive-drawer: "\ea66";
@nc-archive-file: "\ea67";
@nc-archive-file-check: "\ea68";
@nc-armchair: "\ea69";
@nc-armor: "\ea6a";
@nc-army: "\ea6b";
@nc-arrow-bottom-left: "\ea6c";
@nc-arrow-bottom-right: "\ea6d";
@nc-arrow-down: "\ea6e";
@nc-arrow-down-2: "\ea6f";
@nc-arrow-down-3: "\ea70";
@nc-arrow-e: "\ea71";
@nc-arrow-left: "\ea72";
@nc-arrow-left-2: "\ea73";
@nc-arrow-left-3: "\ea74";
@nc-arrow-n: "\ea75";
@nc-arrow-right: "\ea76";
@nc-arrow-right-2: "\ea77";
@nc-arrow-right-3: "\ea78";
@nc-arrow-s: "\ea79";
@nc-arrow-sm-down: "\ea7a";
@nc-arrow-sm-left: "\ea7b";
@nc-arrow-sm-right: "\ea7c";
@nc-arrow-tool: "\ea7d";
@nc-arrow-top-left: "\ea7e";
@nc-arrow-top-right: "\ea7f";
@nc-arrow-up: "\ea80";
@nc-arrow-up-2: "\ea81";
@nc-arrow-up-3: "\ea82";
@nc-arrow-w: "\ea83";
@nc-arrows-expand: "\ea84";
@nc-arrows-expand-2: "\ea85";
@nc-arrows-fullscreen: "\ea86";
@nc-arrows-fullscreen-2: "\ea87";
@nc-arrows-maximize: "\ea88";
@nc-arrows-maximize-2: "\ea89";
@nc-arrows-opposite-directions: "\ea8a";
@nc-arrows-same-direction: "\ea8b";
@nc-artboard: "\ea8c";
@nc-artificial-brain: "\ea8d";
@nc-artificial-intelligence: "\ea8e";
@nc-assault-rifle: "\ea8f";
@nc-astronaut: "\ea90";
@nc-astronomy: "\ea91";
@nc-at-sign: "\ea92";
@nc-at-sign-2: "\ea93";
@nc-athletics: "\ea94";
@nc-atm: "\ea95";
@nc-atom: "\ea96";
@nc-attach: "\ea97";
@nc-attachment: "\ea98";
@nc-aubergine: "\ea99";
@nc-audio-description: "\ea9a";
@nc-audio-jack: "\ea9b";
@nc-audio-mixer: "\ea9c";
@nc-augmented-reality: "\ea9d";
@nc-auto-flash: "\ea9e";
@nc-auto-flash-2: "\ea9f";
@nc-auto-focus: "\eaa0";
@nc-automated-logistics: "\eaa1";
@nc-avocado: "\eaa2";
@nc-award: "\eaa3";
@nc-award-49: "\eaa4";
@nc-axe: "\eaa5";
@nc-b-chart: "\eaa6";
@nc-b-comment: "\eaa7";
@nc-b-eye: "\eaa8";
@nc-baby: "\eaa9";
@nc-baby-bottle: "\eaaa";
@nc-baby-car-seat: "\eaab";
@nc-baby-clothes: "\eaac";
@nc-baby-monitor: "\eaad";
@nc-baby-stroller: "\eaae";
@nc-back-arrow: "\eaaf";
@nc-backpack: "\eab0";
@nc-backpack-2: "\eab1";
@nc-backpack-57: "\eab2";
@nc-backpack-58: "\eab3";
@nc-backup: "\eab4";
@nc-backward: "\eab5";
@nc-bacon: "\eab6";
@nc-badge: "\eab7";
@nc-bag: "\eab8";
@nc-bag-16: "\eab9";
@nc-bag-17: "\eaba";
@nc-bag-20: "\eabb";
@nc-bag-21: "\eabc";
@nc-bag-22: "\eabd";
@nc-bag-49: "\eabe";
@nc-bag-50: "\eabf";
@nc-bag-add-18: "\eac0";
@nc-bag-add-21: "\eac1";
@nc-bag-delivery: "\eac2";
@nc-bag-edit: "\eac3";
@nc-bag-remove-19: "\eac4";
@nc-bag-remove-22: "\eac5";
@nc-bag-time: "\eac6";
@nc-baggage-collection: "\eac7";
@nc-baggage-scale: "\eac8";
@nc-baguette: "\eac9";
@nc-bahai: "\eaca";
@nc-bakery: "\eacb";
@nc-balance: "\eacc";
@nc-baloon: "\eacd";
@nc-bamboo: "\eace";
@nc-ban: "\eacf";
@nc-banana: "\ead0";
@nc-bank-statement: "\ead1";
@nc-barbecue: "\ead2";
@nc-barbecue-15: "\ead3";
@nc-barbecue-tools: "\ead4";
@nc-barbell: "\ead5";
@nc-barbershop: "\ead6";
@nc-barcode: "\ead7";
@nc-barcode-qr: "\ead8";
@nc-barcode-scan: "\ead9";
@nc-bars-anim: "\eada";
@nc-bars-anim-2: "\eadb";
@nc-bars-anim-3: "\eadc";
@nc-baseball: "\eadd";
@nc-baseball-bat: "\eade";
@nc-baseball-pitch: "\eadf";
@nc-baseball-player: "\eae0";
@nc-basket: "\eae1";
@nc-basket-add: "\eae2";
@nc-basket-edit: "\eae3";
@nc-basket-favorite: "\eae4";
@nc-basket-remove: "\eae5";
@nc-basket-search: "\eae6";
@nc-basket-share: "\eae7";
@nc-basket-simple: "\eae8";
@nc-basket-simple-add: "\eae9";
@nc-basket-simple-remove: "\eaea";
@nc-basket-update: "\eaeb";
@nc-basketball: "\eaec";
@nc-basketball-board: "\eaed";
@nc-basketball-player: "\eaee";
@nc-basketball-ring: "\eaef";
@nc-bat: "\eaf0";
@nc-bath-faucet: "\eaf1";
@nc-bathroom-cabinet: "\eaf2";
@nc-bathtub: "\eaf3";
@nc-battery: "\eaf4";
@nc-battery-charging: "\eaf5";
@nc-battery-level: "\eaf6";
@nc-battery-low: "\eaf7";
@nc-battery-power: "\eaf8";
@nc-battery-status: "\eaf9";
@nc-beach-bat: "\eafa";
@nc-beach-umbrella: "\eafb";
@nc-bear: "\eafc";
@nc-bear-2: "\eafd";
@nc-beard: "\eafe";
@nc-bed: "\eaff";
@nc-bedroom: "\eb00";
@nc-bee: "\eb01";
@nc-beer-95: "\eb02";
@nc-beer-96: "\eb03";
@nc-bell: "\eb04";
@nc-belt: "\eb05";
@nc-beverage: "\eb06";
@nc-bicep: "\eb07";
@nc-big-ben: "\eb08";
@nc-big-eyes: "\eb09";
@nc-big-smile: "\eb0a";
@nc-bigmouth: "\eb0b";
@nc-bike: "\eb0c";
@nc-bike-bmx: "\eb0d";
@nc-bikini: "\eb0e";
@nc-bill: "\eb0f";
@nc-billboard: "\eb10";
@nc-billiard-ball: "\eb11";
@nc-bin: "\eb12";
@nc-binary-code: "\eb13";
@nc-binoculars: "\eb14";
@nc-biochemistry: "\eb15";
@nc-biology: "\eb16";
@nc-biscuit: "\eb17";
@nc-bitcoin: "\eb18";
@nc-bleah: "\eb19";
@nc-blend: "\eb1a";
@nc-blender: "\eb1b";
@nc-blindness: "\eb1c";
@nc-block: "\eb1d";
@nc-block-down: "\eb1e";
@nc-block-left: "\eb1f";
@nc-block-right: "\eb20";
@nc-block-up: "\eb21";
@nc-blockchain: "\eb22";
@nc-blockquote: "\eb23";
@nc-blog: "\eb24";
@nc-blue-mosque: "\eb25";
@nc-blueberries: "\eb26";
@nc-blueprint: "\eb27";
@nc-bluetooth: "\eb28";
@nc-board: "\eb29";
@nc-board-2: "\eb2a";
@nc-board-27: "\eb2b";
@nc-board-28: "\eb2c";
@nc-board-29: "\eb2d";
@nc-board-30: "\eb2e";
@nc-board-51: "\eb2f";
@nc-board-game: "\eb30";
@nc-boat: "\eb31";
@nc-boat-front: "\eb32";
@nc-boat-small-02: "\eb33";
@nc-boat-small-03: "\eb34";
@nc-body-back: "\eb35";
@nc-body-butt: "\eb36";
@nc-body-cream: "\eb37";
@nc-bodybuilder: "\eb38";
@nc-boiling-water: "\eb39";
@nc-bold: "\eb3a";
@nc-bolt: "\eb3b";
@nc-bomb: "\eb3c";
@nc-bones: "\eb3d";
@nc-book: "\eb3e";
@nc-book-39: "\eb3f";
@nc-book-bookmark: "\eb40";
@nc-book-bookmark-2: "\eb41";
@nc-book-open: "\eb42";
@nc-book-open-2: "\eb43";
@nc-bookmark: "\eb44";
@nc-bookmark-add: "\eb45";
@nc-bookmark-add-2: "\eb46";
@nc-bookmark-delete: "\eb47";
@nc-bookmark-delete-2: "\eb48";
@nc-bookmarks: "\eb49";
@nc-books: "\eb4a";
@nc-books-46: "\eb4b";
@nc-boot: "\eb4c";
@nc-boot-2: "\eb4d";
@nc-boot-woman: "\eb4e";
@nc-boots: "\eb4f";
@nc-border: "\eb50";
@nc-border-collie: "\eb51";
@nc-border-radius: "\eb52";
@nc-border-width: "\eb53";
@nc-bored: "\eb54";
@nc-botany: "\eb55";
@nc-bottle: "\eb56";
@nc-bottle-wine: "\eb57";
@nc-bouquet: "\eb58";
@nc-bow: "\eb59";
@nc-bowl: "\eb5a";
@nc-bowling-ball: "\eb5b";
@nc-bowling-pins: "\eb5c";
@nc-box: "\eb5d";
@nc-box-2: "\eb5e";
@nc-box-3d-50: "\eb5f";
@nc-box-arrow-bottom-left: "\eb60";
@nc-box-arrow-bottom-right: "\eb61";
@nc-box-arrow-down: "\eb62";
@nc-box-arrow-left: "\eb63";
@nc-box-arrow-right: "\eb64";
@nc-box-arrow-top-left: "\eb65";
@nc-box-arrow-top-right: "\eb66";
@nc-box-arrow-up: "\eb67";
@nc-box-caret-down: "\eb68";
@nc-box-caret-left: "\eb69";
@nc-box-caret-right: "\eb6a";
@nc-box-caret-up: "\eb6b";
@nc-box-ctrl-down: "\eb6c";
@nc-box-ctrl-left: "\eb6d";
@nc-box-ctrl-right: "\eb6e";
@nc-box-ctrl-up: "\eb6f";
@nc-box-ribbon: "\eb70";
@nc-boxing: "\eb71";
@nc-boxing-bag: "\eb72";
@nc-boxing-glove: "\eb73";
@nc-bra: "\eb74";
@nc-braille: "\eb75";
@nc-brain: "\eb76";
@nc-brakes: "\eb77";
@nc-brandenburg-gate: "\eb78";
@nc-bread: "\eb79";
@nc-bride: "\eb7a";
@nc-briefcase-24: "\eb7b";
@nc-briefcase-25: "\eb7c";
@nc-briefcase-26: "\eb7d";
@nc-brightness: "\eb7e";
@nc-brioche: "\eb7f";
@nc-broccoli: "\eb80";
@nc-broken-heart: "\eb81";
@nc-broom: "\eb82";
@nc-browse: "\eb83";
@nc-browser-chrome: "\eb84";
@nc-browser-edge: "\eb85";
@nc-browser-edge-legacy: "\eb86";
@nc-browser-firefox: "\eb87";
@nc-browser-ie: "\eb88";
@nc-browser-opera: "\eb89";
@nc-browser-safari: "\eb8a";
@nc-brush: "\eb8b";
@nc-btn-play: "\eb8c";
@nc-btn-play-2: "\eb8d";
@nc-btn-stop: "\eb8e";
@nc-bucket: "\eb8f";
@nc-bucket-2: "\eb90";
@nc-buddhism: "\eb91";
@nc-buddhist-temple: "\eb92";
@nc-bug: "\eb93";
@nc-bulb: "\eb94";
@nc-bulb-61: "\eb95";
@nc-bulb-62: "\eb96";
@nc-bulb-63: "\eb97";
@nc-bulb-saver: "\eb98";
@nc-bullet-list: "\eb99";
@nc-bullet-list-67: "\eb9a";
@nc-bullet-list-68: "\eb9b";
@nc-bullet-list-69: "\eb9c";
@nc-bullet-list-70: "\eb9d";
@nc-bullets: "\eb9e";
@nc-bureau-dresser: "\eb9f";
@nc-burj-al-arab: "\eba0";
@nc-bus: "\eba1";
@nc-bus-front-10: "\eba2";
@nc-bus-front-12: "\eba3";
@nc-business-agent: "\eba4";
@nc-business-contact-85: "\eba5";
@nc-business-contact-86: "\eba6";
@nc-business-contact-87: "\eba7";
@nc-business-contact-88: "\eba8";
@nc-businessman-03: "\eba9";
@nc-businessman-04: "\ebaa";
@nc-butter: "\ebab";
@nc-butterfly: "\ebac";
@nc-button: "\ebad";
@nc-button-2: "\ebae";
@nc-button-eject: "\ebaf";
@nc-button-next: "\ebb0";
@nc-button-pause: "\ebb1";
@nc-button-play: "\ebb2";
@nc-button-power: "\ebb3";
@nc-button-previous: "\ebb4";
@nc-button-record: "\ebb5";
@nc-button-rewind: "\ebb6";
@nc-button-skip: "\ebb7";
@nc-button-stop: "\ebb8";
@nc-buzz: "\ebb9";
@nc-c-add: "\ebba";
@nc-c-check: "\ebbb";
@nc-c-delete: "\ebbc";
@nc-c-edit: "\ebbd";
@nc-c-info: "\ebbe";
@nc-c-pulse: "\ebbf";
@nc-c-question: "\ebc0";
@nc-c-remove: "\ebc1";
@nc-c-warning: "\ebc2";
@nc-cabinet: "\ebc3";
@nc-cable: "\ebc4";
@nc-cactus: "\ebc5";
@nc-cake: "\ebc6";
@nc-cake-13: "\ebc7";
@nc-cake-2: "\ebc8";
@nc-cake-slice: "\ebc9";
@nc-calculator: "\ebca";
@nc-calendar: "\ebcb";
@nc-calendar-2: "\ebcc";
@nc-calendar-date: "\ebcd";
@nc-calendar-date-2: "\ebce";
@nc-calendar-day-view: "\ebcf";
@nc-calendar-event: "\ebd0";
@nc-calendar-event-2: "\ebd1";
@nc-calendar-event-create: "\ebd2";
@nc-call-doctor: "\ebd3";
@nc-camcorder: "\ebd4";
@nc-camera: "\ebd5";
@nc-camera-2: "\ebd6";
@nc-camera-3: "\ebd7";
@nc-camera-button: "\ebd8";
@nc-camera-flash: "\ebd9";
@nc-camera-flashlight: "\ebda";
@nc-camera-focus: "\ebdb";
@nc-camera-focus-2: "\ebdc";
@nc-camera-lens: "\ebdd";
@nc-camera-roll: "\ebde";
@nc-camera-screen: "\ebdf";
@nc-camera-shooting: "\ebe0";
@nc-camera-timer: "\ebe1";
@nc-camper: "\ebe2";
@nc-camping: "\ebe3";
@nc-camping-lantern: "\ebe4";
@nc-can: "\ebe5";
@nc-candle: "\ebe6";
@nc-candlestick-chart: "\ebe7";
@nc-candy: "\ebe8";
@nc-candy-2: "\ebe9";
@nc-canvas: "\ebea";
@nc-cap: "\ebeb";
@nc-car: "\ebec";
@nc-car-2: "\ebed";
@nc-car-accident: "\ebee";
@nc-car-connect: "\ebef";
@nc-car-door: "\ebf0";
@nc-car-front: "\ebf1";
@nc-car-lights: "\ebf2";
@nc-car-parking: "\ebf3";
@nc-car-simple: "\ebf4";
@nc-car-sport: "\ebf5";
@nc-car-ventilation: "\ebf6";
@nc-car-wash: "\ebf7";
@nc-card-edit: "\ebf8";
@nc-card-favorite: "\ebf9";
@nc-card-remove: "\ebfa";
@nc-card-update: "\ebfb";
@nc-cards: "\ebfc";
@nc-caret-sm-up: "\ebfd";
@nc-carrot: "\ebfe";
@nc-cart: "\ebff";
@nc-cart-add: "\ec00";
@nc-cart-add-9: "\ec01";
@nc-cart-favorite: "\ec02";
@nc-cart-full: "\ec03";
@nc-cart-refresh: "\ec04";
@nc-cart-remove: "\ec05";
@nc-cart-remove-9: "\ec06";
@nc-cart-return: "\ec07";
@nc-cart-simple-add: "\ec08";
@nc-cart-simple-remove: "\ec09";
@nc-cart-speed: "\ec0a";
@nc-cash-register: "\ec0b";
@nc-casino: "\ec0c";
@nc-casino-chip: "\ec0d";
@nc-castle: "\ec0e";
@nc-cat: "\ec0f";
@nc-catalog: "\ec10";
@nc-cauldron: "\ec11";
@nc-cctv: "\ec12";
@nc-cd-reader: "\ec13";
@nc-celsius: "\ec14";
@nc-centralize: "\ec15";
@nc-certificate: "\ec16";
@nc-chain: "\ec17";
@nc-chair: "\ec18";
@nc-chalkboard: "\ec19";
@nc-champagne: "\ec1a";
@nc-chandelier: "\ec1b";
@nc-change-direction: "\ec1c";
@nc-charger-cable: "\ec1d";
@nc-chart: "\ec1e";
@nc-chart-bar-32: "\ec1f";
@nc-chart-bar-33: "\ec20";
@nc-chart-growth: "\ec21";
@nc-chart-pie-35: "\ec22";
@nc-chart-pie-36: "\ec23";
@nc-chat: "\ec24";
@nc-chat-bot: "\ec25";
@nc-check: "\ec26";
@nc-check-all: "\ec27";
@nc-check-double: "\ec28";
@nc-check-in: "\ec29";
@nc-check-list: "\ec2a";
@nc-check-out: "\ec2b";
@nc-check-single: "\ec2c";
@nc-checkbox-btn: "\ec2d";
@nc-checkbox-btn-checked: "\ec2e";
@nc-cheese: "\ec2f";
@nc-cheese-87: "\ec30";
@nc-cheeseburger: "\ec31";
@nc-chef: "\ec32";
@nc-chef-hat: "\ec33";
@nc-chemistry: "\ec34";
@nc-cheque: "\ec35";
@nc-cheque-2: "\ec36";
@nc-cheque-3: "\ec37";
@nc-chequered-flag: "\ec38";
@nc-cherry: "\ec39";
@nc-chess-bishop: "\ec3a";
@nc-chess-king: "\ec3b";
@nc-chess-knight: "\ec3c";
@nc-chess-pawn: "\ec3d";
@nc-chess-queen: "\ec3e";
@nc-chess-tower: "\ec3f";
@nc-chicken: "\ec40";
@nc-chicken-2: "\ec41";
@nc-chicken-front: "\ec42";
@nc-child: "\ec43";
@nc-chili: "\ec44";
@nc-chimney: "\ec45";
@nc-chips: "\ec46";
@nc-choco-cream: "\ec47";
@nc-chocolate: "\ec48";
@nc-chocolate-mousse: "\ec49";
@nc-christianity: "\ec4a";
@nc-church: "\ec4b";
@nc-churros: "\ec4c";
@nc-cinema: "\ec4d";
@nc-circle: "\ec4e";
@nc-circle-anim: "\ec4f";
@nc-circle-anim-2: "\ec50";
@nc-circle-anim-3: "\ec51";
@nc-circle-arrow-down: "\ec52";
@nc-circle-arrow-left: "\ec53";
@nc-circle-arrow-right: "\ec54";
@nc-circle-arrow-up: "\ec55";
@nc-circle-caret-down: "\ec56";
@nc-circle-caret-left: "\ec57";
@nc-circle-caret-right: "\ec58";
@nc-circle-caret-up: "\ec59";
@nc-circle-ctrl-down: "\ec5a";
@nc-circle-ctrl-left: "\ec5b";
@nc-circle-ctrl-right: "\ec5c";
@nc-circle-ctrl-up: "\ec5d";
@nc-circle-in: "\ec5e";
@nc-circle-out: "\ec5f";
@nc-circuit: "\ec60";
@nc-circuit-round: "\ec61";
@nc-clapperboard: "\ec62";
@nc-clapperboard-2: "\ec63";
@nc-clarinet: "\ec64";
@nc-clear-data: "\ec65";
@nc-climbing: "\ec66";
@nc-clock: "\ec67";
@nc-clock-anim: "\ec68";
@nc-clone: "\ec69";
@nc-closed-captioning: "\ec6a";
@nc-clothes-hanger: "\ec6b";
@nc-clothing-hanger: "\ec6c";
@nc-cloud: "\ec6d";
@nc-cloud-data-download: "\ec6e";
@nc-cloud-download: "\ec6f";
@nc-cloud-drop: "\ec70";
@nc-cloud-fog-31: "\ec71";
@nc-cloud-fog-32: "\ec72";
@nc-cloud-forecast: "\ec73";
@nc-cloud-hail: "\ec74";
@nc-cloud-light: "\ec75";
@nc-cloud-mining: "\ec76";
@nc-cloud-moon: "\ec77";
@nc-cloud-rain: "\ec78";
@nc-cloud-rainbow: "\ec79";
@nc-cloud-snow-34: "\ec7a";
@nc-cloud-snow-42: "\ec7b";
@nc-cloud-sun-17: "\ec7c";
@nc-cloud-sun-19: "\ec7d";
@nc-cloud-upload: "\ec7e";
@nc-clover: "\ec7f";
@nc-clubs-suit: "\ec80";
@nc-cn-tower: "\ec81";
@nc-coat: "\ec82";
@nc-coat-hanger: "\ec83";
@nc-cockade: "\ec84";
@nc-cocktail: "\ec85";
@nc-code: "\ec86";
@nc-code-editor: "\ec87";
@nc-coffe-long: "\ec88";
@nc-coffee: "\ec89";
@nc-coffee-bean: "\ec8a";
@nc-coffee-long: "\ec8b";
@nc-coffee-maker: "\ec8c";
@nc-coffin: "\ec8d";
@nc-cogwheel: "\ec8e";
@nc-coins: "\ec8f";
@nc-collar: "\ec90";
@nc-collection: "\ec91";
@nc-colosseum: "\ec92";
@nc-comb: "\ec93";
@nc-command: "\ec94";
@nc-comment: "\ec95";
@nc-comment-add: "\ec96";
@nc-comments: "\ec97";
@nc-compact-camera: "\ec98";
@nc-compare: "\ec99";
@nc-compare-items: "\ec9a";
@nc-compass: "\ec9b";
@nc-compass-04: "\ec9c";
@nc-compass-05: "\ec9d";
@nc-compass-06: "\ec9e";
@nc-compass-2: "\ec9f";
@nc-compass-3: "\eca0";
@nc-components: "\eca1";
@nc-compressed-file: "\eca2";
@nc-computer: "\eca3";
@nc-computer-monitor: "\eca4";
@nc-computer-upload: "\eca5";
@nc-concierge: "\eca6";
@nc-condom: "\eca7";
@nc-cone: "\eca8";
@nc-conference-room: "\eca9";
@nc-configuration-tools: "\ecaa";
@nc-connect: "\ecab";
@nc-connection: "\ecac";
@nc-construction-sign: "\ecad";
@nc-contact: "\ecae";
@nc-contact-list: "\ecaf";
@nc-contactless: "\ecb0";
@nc-contactless-card: "\ecb1";
@nc-contacts: "\ecb2";
@nc-content-360deg: "\ecb3";
@nc-content-delivery: "\ecb4";
@nc-contrast: "\ecb5";
@nc-contrast-2: "\ecb6";
@nc-control-panel: "\ecb7";
@nc-controller: "\ecb8";
@nc-controller-2: "\ecb9";
@nc-conversion: "\ecba";
@nc-cookies: "\ecbb";
@nc-copy: "\ecbc";
@nc-copy-2: "\ecbd";
@nc-copyright: "\ecbe";
@nc-corn: "\ecbf";
@nc-corner-bottom-left: "\ecc0";
@nc-corner-bottom-right: "\ecc1";
@nc-corner-down-round: "\ecc2";
@nc-corner-left-down: "\ecc3";
@nc-corner-left-round: "\ecc4";
@nc-corner-right-down: "\ecc5";
@nc-corner-right-round: "\ecc6";
@nc-corner-top-left: "\ecc7";
@nc-corner-top-right: "\ecc8";
@nc-corner-up-left: "\ecc9";
@nc-corner-up-right: "\ecca";
@nc-corner-up-round: "\eccb";
@nc-cornucopia: "\eccc";
@nc-corset: "\eccd";
@nc-coughing: "\ecce";
@nc-countdown: "\eccf";
@nc-countdown-2: "\ecd0";
@nc-couple-gay: "\ecd1";
@nc-couple-lesbian: "\ecd2";
@nc-coupon: "\ecd3";
@nc-cow: "\ecd4";
@nc-cpu: "\ecd5";
@nc-crab: "\ecd6";
@nc-cradle: "\ecd7";
@nc-crane: "\ecd8";
@nc-creative-commons: "\ecd9";
@nc-credit-card: "\ecda";
@nc-credit-card-in: "\ecdb";
@nc-credit-locked: "\ecdc";
@nc-crepe: "\ecdd";
@nc-cricket-bat: "\ecde";
@nc-croissant: "\ecdf";
@nc-crop: "\ece0";
@nc-cross: "\ece1";
@nc-cross-down: "\ece2";
@nc-cross-horizontal: "\ece3";
@nc-cross-left: "\ece4";
@nc-cross-right: "\ece5";
@nc-cross-up: "\ece6";
@nc-cross-vertical: "\ece7";
@nc-crosshair: "\ece8";
@nc-crossing-directions: "\ece9";
@nc-crossroad: "\ecea";
@nc-croupier: "\eceb";
@nc-crown: "\ecec";
@nc-crumpet: "\eced";
@nc-crunches: "\ecee";
@nc-cry-15: "\ecef";
@nc-cry-57: "\ecf0";
@nc-crying-baby: "\ecf1";
@nc-crypto-wallet: "\ecf2";
@nc-cryptography: "\ecf3";
@nc-css3: "\ecf4";
@nc-ctrl-backward: "\ecf5";
@nc-ctrl-down: "\ecf6";
@nc-ctrl-forward: "\ecf7";
@nc-ctrl-left: "\ecf8";
@nc-ctrl-right: "\ecf9";
@nc-ctrl-up: "\ecfa";
@nc-cupcake: "\ecfb";
@nc-cure: "\ecfc";
@nc-curling: "\ecfd";
@nc-curling-stone: "\ecfe";
@nc-currency-dollar: "\ecff";
@nc-currency-euro: "\ed00";
@nc-currency-exchange: "\ed01";
@nc-currency-exchange-2: "\ed02";
@nc-currency-pound: "\ed03";
@nc-currency-yen: "\ed04";
@nc-cursor-48: "\ed05";
@nc-cursor-49: "\ed06";
@nc-cursor-add: "\ed07";
@nc-cursor-grab: "\ed08";
@nc-cursor-load: "\ed09";
@nc-cursor-menu: "\ed0a";
@nc-cursor-not-allowed: "\ed0b";
@nc-cursor-pointer: "\ed0c";
@nc-cursor-text: "\ed0d";
@nc-curtains: "\ed0e";
@nc-curved-arrow-down: "\ed0f";
@nc-curved-arrow-left: "\ed10";
@nc-curved-arrow-right: "\ed11";
@nc-curved-circuit: "\ed12";
@nc-customer-support: "\ed13";
@nc-cut: "\ed14";
@nc-cute: "\ed15";
@nc-cutlery: "\ed16";
@nc-cutlery-75: "\ed17";
@nc-cutlery-76: "\ed18";
@nc-cutlery-77: "\ed19";
@nc-cyborg: "\ed1a";
@nc-cycle: "\ed1b";
@nc-cycling: "\ed1c";
@nc-cycling-track: "\ed1d";
@nc-d-add: "\ed1e";
@nc-d-chart: "\ed1f";
@nc-d-check: "\ed20";
@nc-d-delete: "\ed21";
@nc-d-edit: "\ed22";
@nc-d-remove: "\ed23";
@nc-dancer: "\ed24";
@nc-dart: "\ed25";
@nc-dashboard: "\ed26";
@nc-data-download: "\ed27";
@nc-data-settings: "\ed28";
@nc-data-table: "\ed29";
@nc-data-upload: "\ed2a";
@nc-database: "\ed2b";
@nc-dead-hand: "\ed2c";
@nc-deadlift: "\ed2d";
@nc-deaf: "\ed2e";
@nc-debt: "\ed2f";
@nc-decentralize: "\ed30";
@nc-decision-process: "\ed31";
@nc-decoration: "\ed32";
@nc-decrease-font-size: "\ed33";
@nc-decrease-indent: "\ed34";
@nc-deer: "\ed35";
@nc-delete: "\ed36";
@nc-delete-forever: "\ed37";
@nc-delete-key: "\ed38";
@nc-delete-x: "\ed39";
@nc-delivery: "\ed3a";
@nc-delivery-2: "\ed3b";
@nc-delivery-3: "\ed3c";
@nc-delivery-fast: "\ed3d";
@nc-delivery-time: "\ed3e";
@nc-delivery-track: "\ed3f";
@nc-depth: "\ed40";
@nc-design: "\ed41";
@nc-design-system: "\ed42";
@nc-desk: "\ed43";
@nc-desk-drawer: "\ed44";
@nc-desk-lamp: "\ed45";
@nc-detached-property: "\ed46";
@nc-detox: "\ed47";
@nc-device-connection: "\ed48";
@nc-devil: "\ed49";
@nc-devto: "\ed4a";
@nc-diamond: "\ed4b";
@nc-diamonds-suits: "\ed4c";
@nc-diaper: "\ed4d";
@nc-diaper-changing-area: "\ed4e";
@nc-dice: "\ed4f";
@nc-dice-2: "\ed50";
@nc-diet: "\ed51";
@nc-diet-food: "\ed52";
@nc-diet-plan: "\ed53";
@nc-digital-image: "\ed54";
@nc-digital-key: "\ed55";
@nc-digital-piano: "\ed56";
@nc-direction: "\ed57";
@nc-direction-down: "\ed58";
@nc-direction-left: "\ed59";
@nc-direction-right: "\ed5a";
@nc-direction-up: "\ed5b";
@nc-directions: "\ed5c";
@nc-discord: "\ed5d";
@nc-discount-2: "\ed5e";
@nc-disgusted: "\ed5f";
@nc-dish: "\ed60";
@nc-dishwasher: "\ed61";
@nc-disinfectant: "\ed62";
@nc-disk: "\ed63";
@nc-disk-reader: "\ed64";
@nc-disperse: "\ed65";
@nc-distance: "\ed66";
@nc-distribute-horizontal: "\ed67";
@nc-distribute-vertical: "\ed68";
@nc-divider: "\ed69";
@nc-dizzy-face: "\ed6a";
@nc-dna-27: "\ed6b";
@nc-dna-38: "\ed6c";
@nc-doc-folder: "\ed6d";
@nc-dock-bottom: "\ed6e";
@nc-dock-left: "\ed6f";
@nc-dock-right: "\ed70";
@nc-dock-top: "\ed71";
@nc-doctor: "\ed72";
@nc-document: "\ed73";
@nc-document-2: "\ed74";
@nc-document-copy: "\ed75";
@nc-dog: "\ed76";
@nc-dog-house: "\ed77";
@nc-dog-leash: "\ed78";
@nc-dont-touch-eyes: "\ed79";
@nc-dont-touch-mouth: "\ed7a";
@nc-donut: "\ed7b";
@nc-door: "\ed7c";
@nc-door-2: "\ed7d";
@nc-door-3: "\ed7e";
@nc-door-handle: "\ed7f";
@nc-doorphone: "\ed80";
@nc-dots: "\ed81";
@nc-dots-anim: "\ed82";
@nc-dots-anim-2: "\ed83";
@nc-dots-anim-3: "\ed84";
@nc-dots-anim-4: "\ed85";
@nc-dots-anim-5: "\ed86";
@nc-dots-anim-6: "\ed87";
@nc-dots-anim-7: "\ed88";
@nc-double-arrow-left: "\ed89";
@nc-double-arrow-right: "\ed8a";
@nc-double-bed: "\ed8b";
@nc-double-tap: "\ed8c";
@nc-down-arrow: "\ed8d";
@nc-download: "\ed8e";
@nc-download-data: "\ed8f";
@nc-download-file: "\ed90";
@nc-drag: "\ed91";
@nc-drag-21: "\ed92";
@nc-drag-31: "\ed93";
@nc-drag-down: "\ed94";
@nc-drag-left: "\ed95";
@nc-drag-right: "\ed96";
@nc-drag-up: "\ed97";
@nc-drawer: "\ed98";
@nc-drawer-2: "\ed99";
@nc-dress-man: "\ed9a";
@nc-dress-woman: "\ed9b";
@nc-dresser: "\ed9c";
@nc-dresser-2: "\ed9d";
@nc-dresser-3: "\ed9e";
@nc-drill: "\ed9f";
@nc-drink: "\eda0";
@nc-drink-2: "\eda1";
@nc-drink-list: "\eda2";
@nc-drinking-bottle: "\eda3";
@nc-drone: "\eda4";
@nc-drone-2: "\eda5";
@nc-drop: "\eda6";
@nc-drop-15: "\eda7";
@nc-drop-cap: "\eda8";
@nc-drops: "\eda9";
@nc-druidism: "\edaa";
@nc-drums: "\edab";
@nc-duck: "\edac";
@nc-dumbbell: "\edad";
@nc-duplicate: "\edae";
@nc-e-add: "\edaf";
@nc-e-delete: "\edb0";
@nc-e-reader: "\edb1";
@nc-e-remove: "\edb2";
@nc-earbuds: "\edb3";
@nc-earth-science: "\edb4";
@nc-eclipse: "\edb5";
@nc-eco-home: "\edb6";
@nc-ecology: "\edb7";
@nc-edge-razor: "\edb8";
@nc-edit: "\edb9";
@nc-edit-color: "\edba";
@nc-edit-contrast: "\edbb";
@nc-edit-curves: "\edbc";
@nc-edit-levels: "\edbd";
@nc-edit-note: "\edbe";
@nc-edit-saturation: "\edbf";
@nc-egg: "\edc0";
@nc-egg-38: "\edc1";
@nc-egg-39: "\edc2";
@nc-eggs: "\edc3";
@nc-eight: "\edc4";
@nc-eject: "\edc5";
@nc-electronic-circuit: "\edc6";
@nc-elephant: "\edc7";
@nc-elliptical-cross-trainer: "\edc8";
@nc-email: "\edc9";
@nc-email-open: "\edca";
@nc-embryo: "\edcb";
@nc-empty: "\edcc";
@nc-energy: "\edcd";
@nc-energy-drink: "\edce";
@nc-energy-shaker: "\edcf";
@nc-energy-supplement: "\edd0";
@nc-engine: "\edd1";
@nc-engine-start: "\edd2";
@nc-enlarge: "\edd3";
@nc-enlarge-diagonal: "\edd4";
@nc-enlarge-diagonal-2: "\edd5";
@nc-enlarge-h: "\edd6";
@nc-enlarge-horizontal: "\edd7";
@nc-enlarge-vertical: "\edd8";
@nc-enter: "\edd9";
@nc-equation: "\edda";
@nc-equestrian-helmet: "\eddb";
@nc-eraser-32: "\eddc";
@nc-eraser-33: "\eddd";
@nc-eraser-46: "\edde";
@nc-escalator: "\eddf";
@nc-event-confirm: "\ede0";
@nc-event-create: "\ede1";
@nc-event-ticket: "\ede2";
@nc-exchange: "\ede3";
@nc-exclamation-mark: "\ede4";
@nc-exercise-bike: "\ede5";
@nc-exhibition: "\ede6";
@nc-exit-right: "\ede7";
@nc-expand: "\ede8";
@nc-expand-2: "\ede9";
@nc-expand-h: "\edea";
@nc-expand-window: "\edeb";
@nc-explore: "\edec";
@nc-explore-2: "\eded";
@nc-explore-user: "\edee";
@nc-export: "\edef";
@nc-eye: "\edf0";
@nc-eye-recognition: "\edf1";
@nc-eyelash: "\edf2";
@nc-eyeliner: "\edf3";
@nc-eyeshadow: "\edf4";
@nc-ez-bar: "\edf5";
@nc-f-add: "\edf6";
@nc-f-chat: "\edf7";
@nc-f-check: "\edf8";
@nc-f-comment: "\edf9";
@nc-f-dashboard: "\edfa";
@nc-f-delete: "\edfb";
@nc-f-remove: "\edfc";
@nc-face-man: "\edfd";
@nc-face-powder: "\edfe";
@nc-face-recognition: "\edff";
@nc-face-woman: "\ee00";
@nc-factory: "\ee01";
@nc-fahrenheit: "\ee02";
@nc-fairy-wand: "\ee03";
@nc-family: "\ee04";
@nc-family-roof: "\ee05";
@nc-fan: "\ee06";
@nc-farm-2: "\ee07";
@nc-farm-rows: "\ee08";
@nc-farmer: "\ee09";
@nc-farmer-market: "\ee0a";
@nc-fav-list: "\ee0b";
@nc-fav-property: "\ee0c";
@nc-fav-remove: "\ee0d";
@nc-favorite: "\ee0e";
@nc-feedback: "\ee0f";
@nc-feeding-bottle: "\ee10";
@nc-female-figure: "\ee11";
@nc-female-sign: "\ee12";
@nc-fence: "\ee13";
@nc-fencing: "\ee14";
@nc-fencing-swords: "\ee15";
@nc-fertilizer: "\ee16";
@nc-file: "\ee17";
@nc-file-2: "\ee18";
@nc-file-add: "\ee19";
@nc-file-alert: "\ee1a";
@nc-file-archive: "\ee1b";
@nc-file-article: "\ee1c";
@nc-file-audio: "\ee1d";
@nc-file-audio-2: "\ee1e";
@nc-file-bookmark: "\ee1f";
@nc-file-chart-bar: "\ee20";
@nc-file-chart-pie: "\ee21";
@nc-file-check: "\ee22";
@nc-file-cloud: "\ee23";
@nc-file-copies: "\ee24";
@nc-file-copy: "\ee25";
@nc-file-delete: "\ee26";
@nc-file-dev: "\ee27";
@nc-file-download: "\ee28";
@nc-file-download-3: "\ee29";
@nc-file-edit: "\ee2a";
@nc-file-export: "\ee2b";
@nc-file-favorite: "\ee2c";
@nc-file-folder: "\ee2d";
@nc-file-gallery: "\ee2e";
@nc-file-history: "\ee2f";
@nc-file-image: "\ee30";
@nc-file-import: "\ee31";
@nc-file-info: "\ee32";
@nc-file-link: "\ee33";
@nc-file-locked: "\ee34";
@nc-file-money: "\ee35";
@nc-file-new: "\ee36";
@nc-file-no-access: "\ee37";
@nc-file-play: "\ee38";
@nc-file-preferences: "\ee39";
@nc-file-question: "\ee3a";
@nc-file-remove: "\ee3b";
@nc-file-replace: "\ee3c";
@nc-file-search: "\ee3d";
@nc-file-settings: "\ee3e";
@nc-file-shared: "\ee3f";
@nc-file-starred: "\ee40";
@nc-file-sync: "\ee41";
@nc-file-text: "\ee42";
@nc-file-upload: "\ee43";
@nc-file-upload-2: "\ee44";
@nc-file-upload-3: "\ee45";
@nc-file-user: "\ee46";
@nc-file-vector: "\ee47";
@nc-film: "\ee48";
@nc-filter: "\ee49";
@nc-filter-check: "\ee4a";
@nc-filter-organization: "\ee4b";
@nc-filter-remove: "\ee4c";
@nc-filter-tool: "\ee4d";
@nc-final-score: "\ee4e";
@nc-find-baggage: "\ee4f";
@nc-find-replace: "\ee50";
@nc-finger-snap: "\ee51";
@nc-fire: "\ee52";
@nc-firearm: "\ee53";
@nc-fireplace: "\ee54";
@nc-firewall: "\ee55";
@nc-fireworks: "\ee56";
@nc-fish: "\ee57";
@nc-fishbone: "\ee58";
@nc-fist: "\ee59";
@nc-fit-horizontal: "\ee5a";
@nc-fit-vertical: "\ee5b";
@nc-five: "\ee5c";
@nc-flag: "\ee5d";
@nc-flag-complex: "\ee5e";
@nc-flag-diagonal-33: "\ee5f";
@nc-flag-diagonal-34: "\ee60";
@nc-flag-points-31: "\ee61";
@nc-flag-points-32: "\ee62";
@nc-flag-simple: "\ee63";
@nc-flame: "\ee64";
@nc-flash-off: "\ee65";
@nc-flash-off-2: "\ee66";
@nc-flashlight: "\ee67";
@nc-flask: "\ee68";
@nc-flask-2: "\ee69";
@nc-flick-down: "\ee6a";
@nc-flick-left: "\ee6b";
@nc-flick-right: "\ee6c";
@nc-flick-up: "\ee6d";
@nc-flight: "\ee6e";
@nc-flight-connection: "\ee6f";
@nc-flip: "\ee70";
@nc-flip-horizontal: "\ee71";
@nc-flip-up: "\ee72";
@nc-flip-vertical: "\ee73";
@nc-floor: "\ee74";
@nc-floor-lamp: "\ee75";
@nc-floors: "\ee76";
@nc-floppy-disk: "\ee77";
@nc-flower: "\ee78";
@nc-flower-07: "\ee79";
@nc-flower-2: "\ee7a";
@nc-flower-rose: "\ee7b";
@nc-fog: "\ee7c";
@nc-folder: "\ee7d";
@nc-folder-2: "\ee7e";
@nc-folder-3: "\ee7f";
@nc-folder-add: "\ee80";
@nc-folder-alert: "\ee81";
@nc-folder-audio: "\ee82";
@nc-folder-bookmark: "\ee83";
@nc-folder-chart-bar: "\ee84";
@nc-folder-chart-pie: "\ee85";
@nc-folder-check: "\ee86";
@nc-folder-cloud: "\ee87";
@nc-folder-dev: "\ee88";
@nc-folder-download: "\ee89";
@nc-folder-edit: "\ee8a";
@nc-folder-favorite: "\ee8b";
@nc-folder-gallery: "\ee8c";
@nc-folder-history: "\ee8d";
@nc-folder-image: "\ee8e";
@nc-folder-info: "\ee8f";
@nc-folder-link: "\ee90";
@nc-folder-locked: "\ee91";
@nc-folder-money: "\ee92";
@nc-folder-music: "\ee93";
@nc-folder-no-access: "\ee94";
@nc-folder-play: "\ee95";
@nc-folder-preferences: "\ee96";
@nc-folder-question: "\ee97";
@nc-folder-remove: "\ee98";
@nc-folder-replace: "\ee99";
@nc-folder-search: "\ee9a";
@nc-folder-settings: "\ee9b";
@nc-folder-shared: "\ee9c";
@nc-folder-starred: "\ee9d";
@nc-folder-sync: "\ee9e";
@nc-folder-upload: "\ee9f";
@nc-folder-user: "\eea0";
@nc-folder-vector: "\eea1";
@nc-food-course: "\eea2";
@nc-food-dog: "\eea3";
@nc-food-scale: "\eea4";
@nc-food-supplement: "\eea5";
@nc-football-headguard: "\eea6";
@nc-forecast: "\eea7";
@nc-forest: "\eea8";
@nc-fork: "\eea9";
@nc-fork-2: "\eeaa";
@nc-forklift: "\eeab";
@nc-form: "\eeac";
@nc-format-left: "\eead";
@nc-format-right: "\eeae";
@nc-forward: "\eeaf";
@nc-four: "\eeb0";
@nc-frame: "\eeb1";
@nc-frame-effect: "\eeb2";
@nc-frankenstein: "\eeb3";
@nc-fridge: "\eeb4";
@nc-fruit-crate: "\eeb5";
@nc-fruit-picking: "\eeb6";
@nc-fuel: "\eeb7";
@nc-fuel-2: "\eeb8";
@nc-fuel-electric: "\eeb9";
@nc-full-screen: "\eeba";
@nc-fullscreen: "\eebb";
@nc-fullscreen-2: "\eebc";
@nc-fullsize: "\eebd";
@nc-function: "\eebe";
@nc-funnel: "\eebf";
@nc-furnished-property: "\eec0";
@nc-g-chart: "\eec1";
@nc-g-check: "\eec2";
@nc-gallery-layout: "\eec3";
@nc-gallery-view: "\eec4";
@nc-gaming-console: "\eec5";
@nc-gaming-controller: "\eec6";
@nc-gantt: "\eec7";
@nc-garden-fork: "\eec8";
@nc-garden-shears: "\eec9";
@nc-gardening-tools: "\eeca";
@nc-garlic: "\eecb";
@nc-gas-mask: "\eecc";
@nc-gathering-restrictions: "\eecd";
@nc-gear: "\eece";
@nc-geometry: "\eecf";
@nc-ghost: "\eed0";
@nc-ghost-2: "\eed1";
@nc-gift: "\eed2";
@nc-gift-exchange: "\eed3";
@nc-git-commit: "\eed4";
@nc-git-merge: "\eed5";
@nc-glass: "\eed6";
@nc-glass-water: "\eed7";
@nc-glasses: "\eed8";
@nc-glasses-2: "\eed9";
@nc-globe: "\eeda";
@nc-globe-2: "\eedb";
@nc-glove: "\eedc";
@nc-gloves: "\eedd";
@nc-goal-65: "\eede";
@nc-gold: "\eedf";
@nc-gold-coin: "\eee0";
@nc-golden-gate-bridge: "\eee1";
@nc-golf-ball: "\eee2";
@nc-golf-club: "\eee3";
@nc-golf-course: "\eee4";
@nc-golf-player: "\eee5";
@nc-golf-strike: "\eee6";
@nc-gooey-anim: "\eee7";
@nc-google: "\eee8";
@nc-goose: "\eee9";
@nc-gps: "\eeea";
@nc-grab: "\eeeb";
@nc-gradient: "\eeec";
@nc-grain: "\eeed";
@nc-grain-2: "\eeee";
@nc-grain-effect: "\eeef";
@nc-grammar-check: "\eef0";
@nc-grammar-check-2: "\eef1";
@nc-grandparent: "\eef2";
@nc-grape: "\eef3";
@nc-graphics-tablet: "\eef4";
@nc-grave: "\eef5";
@nc-great-wall: "\eef6";
@nc-greenhouse: "\eef7";
@nc-grenade: "\eef8";
@nc-grid: "\eef9";
@nc-grid-interface: "\eefa";
@nc-grid-layout: "\eefb";
@nc-grid-system: "\eefc";
@nc-grid-view: "\eefd";
@nc-groom: "\eefe";
@nc-group: "\eeff";
@nc-guitar: "\ef00";
@nc-gym: "\ef01";
@nc-gym-class: "\ef02";
@nc-gym-shoes: "\ef03";
@nc-gymnastics: "\ef04";
@nc-hacker: "\ef05";
@nc-hair-clipper: "\ef06";
@nc-hair-dryer: "\ef07";
@nc-hair-gel: "\ef08";
@nc-hair-man: "\ef09";
@nc-hair-straightener: "\ef0a";
@nc-hair-towel: "\ef0b";
@nc-hair-woman: "\ef0c";
@nc-hairdresser: "\ef0d";
@nc-halloween-pumpkin: "\ef0e";
@nc-hammer: "\ef0f";
@nc-hand-card: "\ef10";
@nc-hand-heart: "\ef11";
@nc-hand-mixer: "\ef12";
@nc-handball: "\ef13";
@nc-handheld-console: "\ef14";
@nc-handout: "\ef15";
@nc-hands-heart: "\ef16";
@nc-handshake: "\ef17";
@nc-hanging-toys: "\ef18";
@nc-happy-baby: "\ef19";
@nc-happy-sun: "\ef1a";
@nc-hash-mark: "\ef1b";
@nc-hat: "\ef1c";
@nc-hat-2: "\ef1d";
@nc-hat-3: "\ef1e";
@nc-hat-top: "\ef1f";
@nc-hazelnut: "\ef20";
@nc-hdmi: "\ef21";
@nc-heading-1: "\ef22";
@nc-heading-2: "\ef23";
@nc-heading-3: "\ef24";
@nc-heading-4: "\ef25";
@nc-heading-5: "\ef26";
@nc-heading-6: "\ef27";
@nc-headphones: "\ef28";
@nc-headphones-2: "\ef29";
@nc-headphones-3: "\ef2a";
@nc-headphones-mic: "\ef2b";
@nc-headset: "\ef2c";
@nc-heart: "\ef2d";
@nc-heart-anim: "\ef2e";
@nc-heart-balloons: "\ef2f";
@nc-heart-lock: "\ef30";
@nc-heartbeat: "\ef31";
@nc-hearts-suit: "\ef32";
@nc-heater: "\ef33";
@nc-height: "\ef34";
@nc-height-2: "\ef35";
@nc-helicopter: "\ef36";
@nc-helmet: "\ef37";
@nc-hide: "\ef38";
@nc-hierarchy-53: "\ef39";
@nc-hierarchy-54: "\ef3a";
@nc-hierarchy-55: "\ef3b";
@nc-hierarchy-56: "\ef3c";
@nc-high-priority: "\ef3d";
@nc-hinduism: "\ef3e";
@nc-hob: "\ef3f";
@nc-hockey: "\ef40";
@nc-hockey-stick: "\ef41";
@nc-hold: "\ef42";
@nc-home: "\ef43";
@nc-home-2: "\ef44";
@nc-home-3: "\ef45";
@nc-home-search: "\ef46";
@nc-honey: "\ef47";
@nc-honeymoon: "\ef48";
@nc-hoodie: "\ef49";
@nc-hook: "\ef4a";
@nc-horizontal-divider: "\ef4b";
@nc-horse: "\ef4c";
@nc-horse-2: "\ef4d";
@nc-horse-hopper: "\ef4e";
@nc-horseshoe: "\ef4f";
@nc-hose: "\ef50";
@nc-hospital-32: "\ef51";
@nc-hospital-33: "\ef52";
@nc-hospital-34: "\ef53";
@nc-hospital-bed: "\ef54";
@nc-hot-dog: "\ef55";
@nc-hot-key: "\ef56";
@nc-hotel: "\ef57";
@nc-hotel-bell: "\ef58";
@nc-hotel-symbol: "\ef59";
@nc-hotspot: "\ef5a";
@nc-hourglass: "\ef5b";
@nc-house: "\ef5c";
@nc-house-pricing: "\ef5d";
@nc-house-property: "\ef5e";
@nc-house-search-engine: "\ef5f";
@nc-html5: "\ef60";
@nc-humanoid: "\ef61";
@nc-humidity-26: "\ef62";
@nc-humidity-52: "\ef63";
@nc-hurricane-44: "\ef64";
@nc-hurricane-45: "\ef65";
@nc-hut: "\ef66";
@nc-hybrid-car: "\ef67";
@nc-hyperlink: "\ef68";
@nc-hyperlink-broken: "\ef69";
@nc-i-add: "\ef6a";
@nc-i-check: "\ef6b";
@nc-i-delete: "\ef6c";
@nc-i-edit: "\ef6d";
@nc-i-remove: "\ef6e";
@nc-ice-cream: "\ef6f";
@nc-ice-cream-22: "\ef70";
@nc-ice-cream-72: "\ef71";
@nc-ice-skates: "\ef72";
@nc-igloo: "\ef73";
@nc-image: "\ef74";
@nc-image-2: "\ef75";
@nc-image-add: "\ef76";
@nc-image-delete: "\ef77";
@nc-image-location: "\ef78";
@nc-img: "\ef79";
@nc-img-rotate-left: "\ef7a";
@nc-img-rotate-right: "\ef7b";
@nc-img-stack: "\ef7c";
@nc-incense: "\ef7d";
@nc-incognito: "\ef7e";
@nc-increase: "\ef7f";
@nc-increase-font-size: "\ef80";
@nc-increase-indent: "\ef81";
@nc-infinite: "\ef82";
@nc-infinite-loop: "\ef83";
@nc-info: "\ef84";
@nc-info-point: "\ef85";
@nc-infrared-thermometer: "\ef86";
@nc-input: "\ef87";
@nc-instant-camera: "\ef88";
@nc-instant-camera-2: "\ef89";
@nc-interview: "\ef8a";
@nc-intestine: "\ef8b";
@nc-invert-direction: "\ef8c";
@nc-invert-process: "\ef8d";
@nc-iron: "\ef8e";
@nc-iron-2: "\ef8f";
@nc-iron-dont: "\ef90";
@nc-islam: "\ef91";
@nc-jacuzzi: "\ef92";
@nc-jam: "\ef93";
@nc-jeans-41: "\ef94";
@nc-jeans-43: "\ef95";
@nc-jeans-pocket: "\ef96";
@nc-jelly: "\ef97";
@nc-jellyfish: "\ef98";
@nc-jewel: "\ef99";
@nc-joint-account: "\ef9a";
@nc-journey: "\ef9b";
@nc-journey-06: "\ef9c";
@nc-journey-07: "\ef9d";
@nc-journey-08: "\ef9e";
@nc-js-console: "\ef9f";
@nc-json-logo: "\efa0";
@nc-judaism: "\efa1";
@nc-juice: "\efa2";
@nc-jump-rope: "\efa3";
@nc-karate: "\efa4";
@nc-ketchup: "\efa5";
@nc-kettle: "\efa6";
@nc-kettlebell: "\efa7";
@nc-key: "\efa8";
@nc-keyboard: "\efa9";
@nc-keyboard-hide: "\efaa";
@nc-keyboard-mouse: "\efab";
@nc-keyboard-wired: "\efac";
@nc-keyboard-wireless: "\efad";
@nc-kid: "\efae";
@nc-kid-2: "\efaf";
@nc-kiss: "\efb0";
@nc-kitchen-fan: "\efb1";
@nc-kiwi: "\efb2";
@nc-knife: "\efb3";
@nc-knob: "\efb4";
@nc-l-add: "\efb5";
@nc-l-check: "\efb6";
@nc-l-circle: "\efb7";
@nc-l-circles: "\efb8";
@nc-l-location: "\efb9";
@nc-l-remove: "\efba";
@nc-l-search: "\efbb";
@nc-l-security: "\efbc";
@nc-l-settings: "\efbd";
@nc-l-sync: "\efbe";
@nc-l-system-update: "\efbf";
@nc-label: "\efc0";
@nc-ladder: "\efc1";
@nc-ladybug: "\efc2";
@nc-land: "\efc3";
@nc-landing: "\efc4";
@nc-landscape-orientation: "\efc5";
@nc-language: "\efc6";
@nc-laptop: "\efc7";
@nc-laptop-1: "\efc8";
@nc-laptop-2: "\efc9";
@nc-laptop-71: "\efca";
@nc-laptop-72: "\efcb";
@nc-lat-station: "\efcc";
@nc-laugh-17: "\efcd";
@nc-laugh-35: "\efce";
@nc-launch: "\efcf";
@nc-launch-app: "\efd0";
@nc-laundry: "\efd1";
@nc-law: "\efd2";
@nc-layers: "\efd3";
@nc-layers-2: "\efd4";
@nc-layout-11: "\efd5";
@nc-layout-25: "\efd6";
@nc-layout-grid: "\efd7";
@nc-leaf: "\efd8";
@nc-leaf-36: "\efd9";
@nc-leaf-38: "\efda";
@nc-leave: "\efdb";
@nc-left-arrow: "\efdc";
@nc-leggins: "\efdd";
@nc-lemon: "\efde";
@nc-lemon-slice: "\efdf";
@nc-letter: "\efe0";
@nc-letter-a: "\efe1";
@nc-letter-b: "\efe2";
@nc-letter-c: "\efe3";
@nc-letter-d: "\efe4";
@nc-letter-e: "\efe5";
@nc-letter-f: "\efe6";
@nc-letter-g: "\efe7";
@nc-letter-h: "\efe8";
@nc-letter-i: "\efe9";
@nc-letter-j: "\efea";
@nc-letter-k: "\efeb";
@nc-letter-l: "\efec";
@nc-letter-m: "\efed";
@nc-letter-n: "\efee";
@nc-letter-o: "\efef";
@nc-letter-p: "\eff0";
@nc-letter-q: "\eff1";
@nc-letter-r: "\eff2";
@nc-letter-s: "\eff3";
@nc-letter-t: "\eff4";
@nc-letter-u: "\eff5";
@nc-letter-v: "\eff6";
@nc-letter-w: "\eff7";
@nc-letter-x: "\eff8";
@nc-letter-y: "\eff9";
@nc-letter-z: "\effa";
@nc-library: "\effb";
@nc-license-key: "\effc";
@nc-lifering: "\effd";
@nc-lift: "\effe";
@nc-light-2: "\efff";
@nc-light-control: "\f000";
@nc-light-switch: "\f001";
@nc-light-traffic: "\f002";
@nc-lighter: "\f003";
@nc-lighthouse: "\f004";
@nc-lightning: "\f005";
@nc-like: "\f006";
@nc-line-chart: "\f007";
@nc-line-height: "\f008";
@nc-link: "\f009";
@nc-linux: "\f00a";
@nc-lip-gloss: "\f00b";
@nc-lips: "\f00c";
@nc-lipstick: "\f00d";
@nc-lipstick-2: "\f00e";
@nc-liquid-soap-container: "\f00f";
@nc-list: "\f010";
@nc-live-streaming: "\f011";
@nc-loader-bars: "\f012";
@nc-loan: "\f013";
@nc-lobster: "\f014";
@nc-lock: "\f015";
@nc-lock-landscape: "\f016";
@nc-lock-orientation: "\f017";
@nc-lock-portrait: "\f018";
@nc-log-in: "\f019";
@nc-log-out: "\f01a";
@nc-logic: "\f01b";
@nc-logo-500px: "\f01c";
@nc-logo-angellist: "\f01d";
@nc-logo-behance: "\f01e";
@nc-logo-blogger: "\f01f";
@nc-logo-buffer: "\f020";
@nc-logo-buysellads: "\f021";
@nc-logo-codepen: "\f022";
@nc-logo-creative-market: "\f023";
@nc-logo-crunchbase: "\f024";
@nc-logo-deviantart: "\f025";
@nc-logo-dribbble: "\f026";
@nc-logo-dropbox: "\f027";
@nc-logo-envato: "\f028";
@nc-logo-evernote: "\f029";
@nc-logo-facebook: "\f02a";
@nc-logo-fb-simple: "\f02b";
@nc-logo-feedly: "\f02c";
@nc-logo-flickr: "\f02d";
@nc-logo-github: "\f02e";
@nc-logo-google-plus: "\f02f";
@nc-logo-instagram: "\f030";
@nc-logo-lastfm: "\f031";
@nc-logo-linkedin: "\f032";
@nc-logo-medium: "\f033";
@nc-logo-meetup: "\f034";
@nc-logo-messenger: "\f035";
@nc-logo-mixer: "\f036";
@nc-logo-myspace: "\f037";
@nc-logo-paypal: "\f038";
@nc-logo-pinterest: "\f039";
@nc-logo-product-hunt: "\f03a";
@nc-logo-qq: "\f03b";
@nc-logo-reddit: "\f03c";
@nc-logo-rss: "\f03d";
@nc-logo-shopify: "\f03e";
@nc-logo-skype: "\f03f";
@nc-logo-slack: "\f040";
@nc-logo-snapchat: "\f041";
@nc-logo-soundcloud: "\f042";
@nc-logo-spotify: "\f043";
@nc-logo-squarespace: "\f044";
@nc-logo-trello: "\f045";
@nc-logo-tumblr: "\f046";
@nc-logo-twitter: "\f047";
@nc-logo-unsplash: "\f048";
@nc-logo-vimeo: "\f049";
@nc-logo-vine: "\f04a";
@nc-logo-vk: "\f04b";
@nc-logo-wechat: "\f04c";
@nc-logo-weibo: "\f04d";
@nc-logo-whatsapp: "\f04e";
@nc-logo-wikipedia: "\f04f";
@nc-logo-wordpress: "\f050";
@nc-logo-yelp: "\f051";
@nc-logo-youtube: "\f052";
@nc-logout: "\f053";
@nc-lollipop: "\f054";
@nc-long-sleeve: "\f055";
@nc-loop: "\f056";
@nc-loop-2: "\f057";
@nc-lotus-flower: "\f058";
@nc-loudspeaker: "\f059";
@nc-love: "\f05a";
@nc-love-camera: "\f05b";
@nc-love-car: "\f05c";
@nc-love-card: "\f05d";
@nc-love-heart-pin: "\f05e";
@nc-love-letter: "\f05f";
@nc-love-message: "\f060";
@nc-love-movie: "\f061";
@nc-love-song: "\f062";
@nc-low-priority: "\f063";
@nc-low-vision: "\f064";
@nc-lowercase: "\f065";
@nc-lucky-seven: "\f066";
@nc-luggage: "\f067";
@nc-lungs: "\f068";
@nc-lungs-infection: "\f069";
@nc-m-add: "\f06a";
@nc-m-check: "\f06b";
@nc-m-delete: "\f06c";
@nc-m-edit: "\f06d";
@nc-m-heart: "\f06e";
@nc-m-location: "\f06f";
@nc-m-remove: "\f070";
@nc-m-search: "\f071";
@nc-m-security: "\f072";
@nc-m-settings: "\f073";
@nc-m-share: "\f074";
@nc-m-star: "\f075";
@nc-m-sync: "\f076";
@nc-m-time: "\f077";
@nc-m-update: "\f078";
@nc-machine-learning: "\f079";
@nc-macro: "\f07a";
@nc-mad-12: "\f07b";
@nc-mad-58: "\f07c";
@nc-magic-wand: "\f07d";
@nc-magnet: "\f07e";
@nc-magnifier: "\f07f";
@nc-magnifier-zoom-in: "\f080";
@nc-magnifier-zoom-out: "\f081";
@nc-mail: "\f082";
@nc-makeup: "\f083";
@nc-makeup-blush: "\f084";
@nc-makeup-brush: "\f085";
@nc-makeup-cream: "\f086";
@nc-makeup-foundation: "\f087";
@nc-makeup-mirror: "\f088";
@nc-makeup-palette: "\f089";
@nc-male-sign: "\f08a";
@nc-malicious: "\f08b";
@nc-man: "\f08c";
@nc-man-arrow-down: "\f08d";
@nc-man-arrow-up: "\f08e";
@nc-man-profile: "\f08f";
@nc-man-up-front: "\f090";
@nc-manga-62: "\f091";
@nc-manga-63: "\f092";
@nc-map: "\f093";
@nc-map-big: "\f094";
@nc-map-compass: "\f095";
@nc-map-gps: "\f096";
@nc-map-marker: "\f097";
@nc-map-pin: "\f098";
@nc-maple-leaf: "\f099";
@nc-mario-mushroom: "\f09a";
@nc-markdown: "\f09b";
@nc-marker: "\f09c";
@nc-marker-2: "\f09d";
@nc-marker-3: "\f09e";
@nc-market-music: "\f09f";
@nc-market-play: "\f0a0";
@nc-mascara: "\f0a1";
@nc-mask-face: "\f0a2";
@nc-mask-oval: "\f0a3";
@nc-mask-rect: "\f0a4";
@nc-massage: "\f0a5";
@nc-mat: "\f0a6";
@nc-matches: "\f0a7";
@nc-math: "\f0a8";
@nc-maximize: "\f0a9";
@nc-maximize-area: "\f0aa";
@nc-mayo: "\f0ab";
@nc-measure-02: "\f0ac";
@nc-measure-17: "\f0ad";
@nc-measure-big: "\f0ae";
@nc-measurement: "\f0af";
@nc-measuring-cup: "\f0b0";
@nc-meat-spit: "\f0b1";
@nc-medal: "\f0b2";
@nc-media-player: "\f0b3";
@nc-media-stream: "\f0b4";
@nc-medical-clipboard: "\f0b5";
@nc-medical-mask: "\f0b6";
@nc-medication: "\f0b7";
@nc-medicine: "\f0b8";
@nc-medicine-ball: "\f0b9";
@nc-meeting: "\f0ba";
@nc-megaphone: "\f0bb";
@nc-menu: "\f0bc";
@nc-menu-2: "\f0bd";
@nc-menu-3: "\f0be";
@nc-menu-4: "\f0bf";
@nc-menu-6: "\f0c0";
@nc-menu-7: "\f0c1";
@nc-menu-8: "\f0c2";
@nc-menu-dots: "\f0c3";
@nc-merge: "\f0c4";
@nc-merge-2: "\f0c5";
@nc-messaging: "\f0c6";
@nc-metrics: "\f0c7";
@nc-mic: "\f0c8";
@nc-mic-2: "\f0c9";
@nc-mickey-mouse: "\f0ca";
@nc-microbiology: "\f0cb";
@nc-microphone: "\f0cc";
@nc-microphone-2: "\f0cd";
@nc-microphone-off: "\f0ce";
@nc-microscope: "\f0cf";
@nc-microsoft: "\f0d0";
@nc-microsoft-onenote: "\f0d1";
@nc-microwave: "\f0d2";
@nc-migration: "\f0d3";
@nc-military-camp: "\f0d4";
@nc-military-knife: "\f0d5";
@nc-military-medal: "\f0d6";
@nc-military-tag: "\f0d7";
@nc-military-tank: "\f0d8";
@nc-military-vest: "\f0d9";
@nc-milk: "\f0da";
@nc-miner: "\f0db";
@nc-mirror: "\f0dc";
@nc-mirror-2: "\f0dd";
@nc-mirror-display: "\f0de";
@nc-mirror-tablet-phone: "\f0df";
@nc-missile: "\f0e0";
@nc-mistletoe: "\f0e1";
@nc-mobile-banking: "\f0e2";
@nc-mobile-card: "\f0e3";
@nc-mobile-chat: "\f0e4";
@nc-mobile-contact: "\f0e5";
@nc-mobile-design: "\f0e6";
@nc-mobile-dev: "\f0e7";
@nc-mobile-phone: "\f0e8";
@nc-moka: "\f0e9";
@nc-molecule: "\f0ea";
@nc-molecule-39: "\f0eb";
@nc-molecule-40: "\f0ec";
@nc-money-11: "\f0ed";
@nc-money-12: "\f0ee";
@nc-money-13: "\f0ef";
@nc-money-bag: "\f0f0";
@nc-money-coins: "\f0f1";
@nc-money-growth: "\f0f2";
@nc-money-time: "\f0f3";
@nc-money-transfer: "\f0f4";
@nc-monster: "\f0f5";
@nc-moon: "\f0f6";
@nc-moon-cloud-drop: "\f0f7";
@nc-moon-cloud-fog: "\f0f8";
@nc-moon-cloud-hail: "\f0f9";
@nc-moon-cloud-light: "\f0fa";
@nc-moon-cloud-rain: "\f0fb";
@nc-moon-cloud-snow-61: "\f0fc";
@nc-moon-cloud-snow-62: "\f0fd";
@nc-moon-fog: "\f0fe";
@nc-moon-full: "\f0ff";
@nc-moon-stars: "\f100";
@nc-mortar: "\f101";
@nc-mortgage: "\f102";
@nc-mosque: "\f103";
@nc-moto: "\f104";
@nc-mountain: "\f105";
@nc-mouse: "\f106";
@nc-mouse-2: "\f107";
@nc-mouse-anim: "\f108";
@nc-move: "\f109";
@nc-move-2: "\f10a";
@nc-move-3: "\f10b";
@nc-move-down: "\f10c";
@nc-move-down-2: "\f10d";
@nc-move-down-right: "\f10e";
@nc-move-layer-down: "\f10f";
@nc-move-layer-left: "\f110";
@nc-move-layer-right: "\f111";
@nc-move-layer-up: "\f112";
@nc-move-left: "\f113";
@nc-move-right: "\f114";
@nc-move-up: "\f115";
@nc-move-up-2: "\f116";
@nc-move-up-left: "\f117";
@nc-movie: "\f118";
@nc-movie-2: "\f119";
@nc-movie-3: "\f11a";
@nc-movie-reel: "\f11b";
@nc-mower: "\f11c";
@nc-muffin: "\f11d";
@nc-mug: "\f11e";
@nc-mushroom: "\f11f";
@nc-music: "\f120";
@nc-music-album: "\f121";
@nc-music-cloud: "\f122";
@nc-music-note: "\f123";
@nc-music-player: "\f124";
@nc-music-playlist: "\f125";
@nc-mustache: "\f126";
@nc-n-check: "\f127";
@nc-n-edit: "\f128";
@nc-nail-file: "\f129";
@nc-nail-polish: "\f12a";
@nc-nail-polish-2: "\f12b";
@nc-name-card: "\f12c";
@nc-nav-down: "\f12d";
@nc-nav-left: "\f12e";
@nc-nav-right: "\f12f";
@nc-nav-up: "\f130";
@nc-navigation: "\f131";
@nc-neck-duster: "\f132";
@nc-needle: "\f133";
@nc-negative-judgement: "\f134";
@nc-nerd: "\f135";
@nc-net: "\f136";
@nc-network-communication: "\f137";
@nc-network-connection: "\f138";
@nc-networking: "\f139";
@nc-new: "\f13a";
@nc-new-construction: "\f13b";
@nc-new-notification: "\f13c";
@nc-news: "\f13d";
@nc-newsletter: "\f13e";
@nc-newsletter-dev: "\f13f";
@nc-night: "\f140";
@nc-night-table: "\f141";
@nc-nine: "\f142";
@nc-ninja: "\f143";
@nc-no-contact: "\f144";
@nc-no-guns: "\f145";
@nc-no-photo: "\f146";
@nc-no-results: "\f147";
@nc-no-smoking: "\f148";
@nc-no-words: "\f149";
@nc-nodes: "\f14a";
@nc-noodles: "\f14b";
@nc-note: "\f14c";
@nc-note-code: "\f14d";
@nc-notebook: "\f14e";
@nc-notepad: "\f14f";
@nc-notes: "\f150";
@nc-notification: "\f151";
@nc-notification-2: "\f152";
@nc-nurse: "\f153";
@nc-nutrition: "\f154";
@nc-ny-building: "\f155";
@nc-o-check: "\f156";
@nc-o-warning: "\f157";
@nc-octagon: "\f158";
@nc-octagon-m: "\f159";
@nc-octopus: "\f15a";
@nc-office: "\f15b";
@nc-office-badge: "\f15c";
@nc-office-chair: "\f15d";
@nc-office-pass: "\f15e";
@nc-offline: "\f15f";
@nc-oil: "\f160";
@nc-oil-2: "\f161";
@nc-olympic-flame: "\f162";
@nc-one: "\f163";
@nc-onion: "\f164";
@nc-online-banking: "\f165";
@nc-open-ai: "\f166";
@nc-open-book: "\f167";
@nc-open-folder: "\f168";
@nc-open-in-browser: "\f169";
@nc-opening-times: "\f16a";
@nc-opposite-directions: "\f16b";
@nc-opposite-directions-2: "\f16c";
@nc-options: "\f16d";
@nc-orange: "\f16e";
@nc-ordered-list: "\f16f";
@nc-organic: "\f170";
@nc-organic-2: "\f171";
@nc-orientation: "\f172";
@nc-oven: "\f173";
@nc-ovum-sperm: "\f174";
@nc-owl: "\f175";
@nc-p-add: "\f176";
@nc-p-chart: "\f177";
@nc-p-check: "\f178";
@nc-p-edit: "\f179";
@nc-p-heart: "\f17a";
@nc-p-location: "\f17b";
@nc-p-remove: "\f17c";
@nc-p-search: "\f17d";
@nc-p-settings: "\f17e";
@nc-p-share: "\f17f";
@nc-p-sync: "\f180";
@nc-p-system-update: "\f181";
@nc-p-time: "\f182";
@nc-pacifier: "\f183";
@nc-pacman: "\f184";
@nc-padlock: "\f185";
@nc-padlock-unlocked: "\f186";
@nc-pagoda: "\f187";
@nc-paint-16: "\f188";
@nc-paint-37: "\f189";
@nc-paint-38: "\f18a";
@nc-paint-brush: "\f18b";
@nc-paint-bucket-39: "\f18c";
@nc-paint-bucket-40: "\f18d";
@nc-pajamas: "\f18e";
@nc-palette: "\f18f";
@nc-pallet: "\f190";
@nc-pallet-stacked-boxes: "\f191";
@nc-palm-tree: "\f192";
@nc-pan: "\f193";
@nc-pancake: "\f194";
@nc-panda: "\f195";
@nc-panel: "\f196";
@nc-pantone: "\f197";
@nc-paper: "\f198";
@nc-paper-design: "\f199";
@nc-paper-dev: "\f19a";
@nc-paper-diploma: "\f19b";
@nc-parachute: "\f19c";
@nc-paragraph: "\f19d";
@nc-paragraph-2: "\f19e";
@nc-paralympic-games: "\f19f";
@nc-parenting: "\f1a0";
@nc-paris-tower: "\f1a1";
@nc-park: "\f1a2";
@nc-parking: "\f1a3";
@nc-parking-sensors: "\f1a4";
@nc-parrot: "\f1a5";
@nc-party: "\f1a6";
@nc-passenger: "\f1a7";
@nc-passport: "\f1a8";
@nc-password: "\f1a9";
@nc-pasta: "\f1aa";
@nc-patch: "\f1ab";
@nc-patch-19: "\f1ac";
@nc-patch-34: "\f1ad";
@nc-path-exclude: "\f1ae";
@nc-path-intersect: "\f1af";
@nc-path-minus: "\f1b0";
@nc-path-unite: "\f1b1";
@nc-pattern-recognition: "\f1b2";
@nc-paw: "\f1b3";
@nc-payee: "\f1b4";
@nc-payment: "\f1b5";
@nc-payment-method: "\f1b6";
@nc-payor: "\f1b7";
@nc-pc: "\f1b8";
@nc-pc-monitor: "\f1b9";
@nc-pc-mouse: "\f1ba";
@nc-pc-play-media: "\f1bb";
@nc-pci-card: "\f1bc";
@nc-peanut: "\f1bd";
@nc-pear: "\f1be";
@nc-peas: "\f1bf";
@nc-pectoral-machine: "\f1c0";
@nc-pen: "\f1c1";
@nc-pen-01: "\f1c2";
@nc-pen-2: "\f1c3";
@nc-pen-23: "\f1c4";
@nc-pen-tool: "\f1c5";
@nc-pencil: "\f1c6";
@nc-pencil-47: "\f1c7";
@nc-pendant-lighting: "\f1c8";
@nc-pendulum: "\f1c9";
@nc-penguin: "\f1ca";
@nc-pennant: "\f1cb";
@nc-people-network: "\f1cc";
@nc-pepper: "\f1cd";
@nc-percent-sign: "\f1ce";
@nc-percentage-38: "\f1cf";
@nc-percentage-39: "\f1d0";
@nc-perfume: "\f1d1";
@nc-personal-trainer: "\f1d2";
@nc-pesticide: "\f1d3";
@nc-pet-food: "\f1d4";
@nc-pharmacy: "\f1d5";
@nc-phone: "\f1d6";
@nc-phone-button: "\f1d7";
@nc-phone-call: "\f1d8";
@nc-phone-call-end: "\f1d9";
@nc-phone-camera-back: "\f1da";
@nc-phone-camera-front: "\f1db";
@nc-phone-charging: "\f1dc";
@nc-phone-charging-2: "\f1dd";
@nc-phone-charging-3: "\f1de";
@nc-phone-dock: "\f1df";
@nc-phone-heart: "\f1e0";
@nc-phone-heartbeat: "\f1e1";
@nc-phone-music: "\f1e2";
@nc-phone-toolbar: "\f1e3";
@nc-photo: "\f1e4";
@nc-photo-album: "\f1e5";
@nc-photo-editor: "\f1e6";
@nc-photo-frame: "\f1e7";
@nc-photo-not-allowed: "\f1e8";
@nc-piano: "\f1e9";
@nc-piano-2: "\f1ea";
@nc-pickaxe: "\f1eb";
@nc-pickle: "\f1ec";
@nc-picnic-basket: "\f1ed";
@nc-picture: "\f1ee";
@nc-pie: "\f1ef";
@nc-pig: "\f1f0";
@nc-pig-2: "\f1f1";
@nc-pilcrow: "\f1f2";
@nc-pilgrim-hat: "\f1f3";
@nc-pill-42: "\f1f4";
@nc-pill-43: "\f1f5";
@nc-pill-bottle: "\f1f6";
@nc-pin: "\f1f7";
@nc-pin-2: "\f1f8";
@nc-pin-3: "\f1f9";
@nc-pin-add: "\f1fa";
@nc-pin-add-2: "\f1fb";
@nc-pin-check: "\f1fc";
@nc-pin-copy: "\f1fd";
@nc-pin-delete: "\f1fe";
@nc-pin-edit: "\f1ff";
@nc-pin-heart: "\f200";
@nc-pin-remove: "\f201";
@nc-pin-remove-2: "\f202";
@nc-pin-search: "\f203";
@nc-pin-security: "\f204";
@nc-pin-settings: "\f205";
@nc-pin-share: "\f206";
@nc-pin-star: "\f207";
@nc-pin-sync: "\f208";
@nc-pin-time: "\f209";
@nc-pin-user: "\f20a";
@nc-pinch: "\f20b";
@nc-pineapple: "\f20c";
@nc-pins: "\f20d";
@nc-pipe: "\f20e";
@nc-pirate: "\f20f";
@nc-pisa-tower: "\f210";
@nc-pizza: "\f211";
@nc-pizza-slice: "\f212";
@nc-plane: "\f213";
@nc-planet: "\f214";
@nc-plant-leaf: "\f215";
@nc-plant-soil: "\f216";
@nc-plant-vase: "\f217";
@nc-plate: "\f218";
@nc-play-media: "\f219";
@nc-play-movie: "\f21a";
@nc-player: "\f21b";
@nc-playground: "\f21c";
@nc-playing-cards: "\f21d";
@nc-playlist: "\f21e";
@nc-plug: "\f21f";
@nc-plug-2: "\f220";
@nc-podcast: "\f221";
@nc-podcast-mic: "\f222";
@nc-podium: "\f223";
@nc-podium-trophy: "\f224";
@nc-point-a: "\f225";
@nc-point-b: "\f226";
@nc-pointing-down: "\f227";
@nc-pointing-left: "\f228";
@nc-pointing-right: "\f229";
@nc-pointing-up: "\f22a";
@nc-polaroid: "\f22b";
@nc-polaroid-photo: "\f22c";
@nc-polaroid-portrait: "\f22d";
@nc-polaroid-shot-delete: "\f22e";
@nc-polaroid-shot-new: "\f22f";
@nc-polaroid-shots: "\f230";
@nc-police-officer: "\f231";
@nc-poop: "\f232";
@nc-popcorn: "\f233";
@nc-pos: "\f234";
@nc-position: "\f235";
@nc-position-marker: "\f236";
@nc-position-pin: "\f237";
@nc-position-user: "\f238";
@nc-positive-judgement: "\f239";
@nc-pot: "\f23a";
@nc-potato: "\f23b";
@nc-potion: "\f23c";
@nc-power-level: "\f23d";
@nc-power-lifting: "\f23e";
@nc-power-rack: "\f23f";
@nc-pram: "\f240";
@nc-preferences: "\f241";
@nc-pregnancy-test: "\f242";
@nc-pregnant-woman: "\f243";
@nc-present: "\f244";
@nc-presentation: "\f245";
@nc-print: "\f246";
@nc-printer: "\f247";
@nc-priority-high: "\f248";
@nc-priority-highest: "\f249";
@nc-priority-low: "\f24a";
@nc-priority-lowest: "\f24b";
@nc-priority-normal: "\f24c";
@nc-privacy: "\f24d";
@nc-privacy-policy: "\f24e";
@nc-privacy-settings: "\f24f";
@nc-profile: "\f250";
@nc-progress: "\f251";
@nc-progress-2: "\f252";
@nc-progress-indicator: "\f253";
@nc-projector: "\f254";
@nc-property: "\f255";
@nc-property-agreement: "\f256";
@nc-property-app: "\f257";
@nc-property-for-sale: "\f258";
@nc-property-location: "\f259";
@nc-property-sold: "\f25a";
@nc-property-to-rent: "\f25b";
@nc-prosciutto: "\f25c";
@nc-prototype: "\f25d";
@nc-pruning-saws: "\f25e";
@nc-pulse: "\f25f";
@nc-pulse-chart: "\f260";
@nc-pulse-sleep: "\f261";
@nc-pumpkin: "\f262";
@nc-puzzle-09: "\f263";
@nc-puzzle-10: "\f264";
@nc-puzzle-toy: "\f265";
@nc-puzzled: "\f266";
@nc-pyramid: "\f267";
@nc-question-mark: "\f268";
@nc-questionnaire: "\f269";
@nc-quite-happy: "\f26a";
@nc-quote: "\f26b";
@nc-r-chat: "\f26c";
@nc-r-down-left-arrows: "\f26d";
@nc-r-down-right-arrows: "\f26e";
@nc-r-up-left-arrows: "\f26f";
@nc-r-up-right-arrows: "\f270";
@nc-rabbit: "\f271";
@nc-radar: "\f272";
@nc-radiation: "\f273";
@nc-radio: "\f274";
@nc-radio-btn: "\f275";
@nc-radio-btn-checked: "\f276";
@nc-rain: "\f277";
@nc-rain-hail: "\f278";
@nc-rainbow: "\f279";
@nc-rake: "\f27a";
@nc-ram: "\f27b";
@nc-ram-2: "\f27c";
@nc-random: "\f27d";
@nc-ranking: "\f27e";
@nc-rat: "\f27f";
@nc-rat-head: "\f280";
@nc-rate-down: "\f281";
@nc-rate-up: "\f282";
@nc-raw-image: "\f283";
@nc-razor: "\f284";
@nc-read: "\f285";
@nc-reading: "\f286";
@nc-reading-tablet: "\f287";
@nc-real-estate: "\f288";
@nc-receipt: "\f289";
@nc-receipt-list-42: "\f28a";
@nc-receipt-list-43: "\f28b";
@nc-recipe: "\f28c";
@nc-recipe-book-46: "\f28d";
@nc-recipe-book-47: "\f28e";
@nc-recipe-create: "\f28f";
@nc-record-player: "\f290";
@nc-recycling: "\f291";
@nc-redo: "\f292";
@nc-referee: "\f293";
@nc-refresh: "\f294";
@nc-refresh-01: "\f295";
@nc-refresh-02: "\f296";
@nc-refund: "\f297";
@nc-reload: "\f298";
@nc-remote-control: "\f299";
@nc-remove: "\f29a";
@nc-remove-fav: "\f29b";
@nc-remove-favorite: "\f29c";
@nc-remove-like: "\f29d";
@nc-repeat: "\f29e";
@nc-repeat-cycle: "\f29f";
@nc-replay: "\f2a0";
@nc-reply: "\f2a1";
@nc-reply-all: "\f2a2";
@nc-reply-arrow: "\f2a3";
@nc-research: "\f2a4";
@nc-reservation: "\f2a5";
@nc-resistance-band: "\f2a6";
@nc-resize-x: "\f2a7";
@nc-resize-y: "\f2a8";
@nc-respond-arrow: "\f2a9";
@nc-restaurant-menu: "\f2aa";
@nc-restore: "\f2ab";
@nc-rice: "\f2ac";
@nc-right-arrow: "\f2ad";
@nc-rim: "\f2ae";
@nc-ring: "\f2af";
@nc-rings: "\f2b0";
@nc-rio-statue: "\f2b1";
@nc-ripple-anim: "\f2b2";
@nc-road: "\f2b3";
@nc-road-2: "\f2b4";
@nc-road-sign-left: "\f2b5";
@nc-road-sign-right: "\f2b6";
@nc-roadmap: "\f2b7";
@nc-roast-chicken: "\f2b8";
@nc-roast-turkey: "\f2b9";
@nc-robot: "\f2ba";
@nc-robot-cleaner: "\f2bb";
@nc-robotic-arm: "\f2bc";
@nc-rock: "\f2bd";
@nc-rolling-pin: "\f2be";
@nc-romantic-dinner: "\f2bf";
@nc-romantic-restaurant: "\f2c0";
@nc-rotate-22: "\f2c1";
@nc-rotate-23: "\f2c2";
@nc-rotate-camera: "\f2c3";
@nc-rotate-left: "\f2c4";
@nc-rotate-right: "\f2c5";
@nc-rotating-bars-anim: "\f2c6";
@nc-roulette: "\f2c7";
@nc-round-dollar: "\f2c8";
@nc-round-euro: "\f2c9";
@nc-round-pound: "\f2ca";
@nc-round-yen: "\f2cb";
@nc-route: "\f2cc";
@nc-route-alert: "\f2cd";
@nc-route-close: "\f2ce";
@nc-route-open: "\f2cf";
@nc-router: "\f2d0";
@nc-row-machine: "\f2d1";
@nc-row-table: "\f2d2";
@nc-rowing: "\f2d3";
@nc-rowing-oars: "\f2d4";
@nc-rugby: "\f2d5";
@nc-rugby-ball: "\f2d6";
@nc-ruler-pencil: "\f2d7";
@nc-run-shoes: "\f2d8";
@nc-runny-nose: "\f2d9";
@nc-s-add: "\f2da";
@nc-s-ban: "\f2db";
@nc-s-check: "\f2dc";
@nc-s-delete: "\f2dd";
@nc-s-edit: "\f2de";
@nc-s-info: "\f2df";
@nc-s-pulse: "\f2e0";
@nc-s-question: "\f2e1";
@nc-s-remove: "\f2e2";
@nc-s-warning: "\f2e3";
@nc-sad: "\f2e4";
@nc-safe: "\f2e5";
@nc-sagrada-familia: "\f2e6";
@nc-saint-basil-cathedral: "\f2e7";
@nc-salad: "\f2e8";
@nc-sale: "\f2e9";
@nc-salt: "\f2ea";
@nc-santa-hat: "\f2eb";
@nc-satellite: "\f2ec";
@nc-satellite-dish: "\f2ed";
@nc-satisfied: "\f2ee";
@nc-sauna: "\f2ef";
@nc-sausage: "\f2f0";
@nc-save-for-later: "\f2f1";
@nc-save-planet: "\f2f2";
@nc-save-the-date: "\f2f3";
@nc-save-to-list: "\f2f4";
@nc-saved-items: "\f2f5";
@nc-savings: "\f2f6";
@nc-saxophone: "\f2f7";
@nc-scale: "\f2f8";
@nc-scale-2: "\f2f9";
@nc-scale-3: "\f2fa";
@nc-scale-4: "\f2fb";
@nc-scale-down: "\f2fc";
@nc-scale-up: "\f2fd";
@nc-scan: "\f2fe";
@nc-scarecrow: "\f2ff";
@nc-scarf: "\f300";
@nc-scented-candle: "\f301";
@nc-school: "\f302";
@nc-scissors: "\f303";
@nc-scissors-2: "\f304";
@nc-scissors-dashed: "\f305";
@nc-scooter: "\f306";
@nc-scotch: "\f307";
@nc-screen-enlarge: "\f308";
@nc-screen-expand: "\f309";
@nc-screen-maximize: "\f30a";
@nc-screen-reader: "\f30b";
@nc-screen-rotation: "\f30c";
@nc-screen-sharing-2: "\f30d";
@nc-screen-sharing-off-2: "\f30e";
@nc-screen-touch: "\f30f";
@nc-scroll-horizontal: "\f310";
@nc-scroll-vertical: "\f311";
@nc-sd-card: "\f312";
@nc-search: "\f313";
@nc-search-content: "\f314";
@nc-search-property: "\f315";
@nc-search-user: "\f316";
@nc-search-zoom-in: "\f317";
@nc-search-zoom-out: "\f318";
@nc-seat: "\f319";
@nc-seatbelt: "\f31a";
@nc-security: "\f31b";
@nc-security-gate: "\f31c";
@nc-security-officer: "\f31d";
@nc-segmentation: "\f31e";
@nc-select: "\f31f";
@nc-selection: "\f320";
@nc-selfie: "\f321";
@nc-selfie-2: "\f322";
@nc-send: "\f323";
@nc-send-message: "\f324";
@nc-send-to-phone: "\f325";
@nc-sensor: "\f326";
@nc-separate: "\f327";
@nc-separate-branch: "\f328";
@nc-separate-directions: "\f329";
@nc-server: "\f32a";
@nc-server-rack: "\f32b";
@nc-settings: "\f32c";
@nc-settings-gear: "\f32d";
@nc-settings-wheel: "\f32e";
@nc-setup-options: "\f32f";
@nc-setup-preferences: "\f330";
@nc-setup-tools: "\f331";
@nc-seven: "\f332";
@nc-shaker: "\f333";
@nc-shape-adjust: "\f334";
@nc-shape-arrow: "\f335";
@nc-shape-circle: "\f336";
@nc-shape-custom: "\f337";
@nc-shape-line: "\f338";
@nc-shape-oval: "\f339";
@nc-shape-polygon: "\f33a";
@nc-shape-polygon-2: "\f33b";
@nc-shape-rectangle: "\f33c";
@nc-shape-square: "\f33d";
@nc-shape-star: "\f33e";
@nc-shape-triangle: "\f33f";
@nc-shape-triangle-2: "\f340";
@nc-shapes: "\f341";
@nc-share: "\f342";
@nc-share-2: "\f343";
@nc-share-3: "\f344";
@nc-sharing: "\f345";
@nc-shark: "\f346";
@nc-shark-2: "\f347";
@nc-sharpen: "\f348";
@nc-sharpener: "\f349";
@nc-sheep: "\f34a";
@nc-shell: "\f34b";
@nc-shield: "\f34c";
@nc-shinto: "\f34d";
@nc-shipping-container: "\f34e";
@nc-shirt: "\f34f";
@nc-shirt-business: "\f350";
@nc-shirt-buttons: "\f351";
@nc-shirt-neck: "\f352";
@nc-shoe-man: "\f353";
@nc-shoe-woman: "\f354";
@nc-shop: "\f355";
@nc-shop-location: "\f356";
@nc-shopping-bag: "\f357";
@nc-shopping-cart: "\f358";
@nc-shopping-cart-2: "\f359";
@nc-shopping-label: "\f35a";
@nc-shopping-tag: "\f35b";
@nc-shorts: "\f35c";
@nc-shotgun: "\f35d";
@nc-shovel: "\f35e";
@nc-show: "\f35f";
@nc-shower: "\f360";
@nc-shrimp: "\f361";
@nc-shuffle: "\f362";
@nc-shuffle-2: "\f363";
@nc-shuttle: "\f364";
@nc-shuttlecock: "\f365";
@nc-shy: "\f366";
@nc-sick: "\f367";
@nc-sickle: "\f368";
@nc-sickle-2: "\f369";
@nc-sidebar: "\f36a";
@nc-sign: "\f36b";
@nc-sign-board: "\f36c";
@nc-sign-down: "\f36d";
@nc-sign-left: "\f36e";
@nc-sign-right: "\f36f";
@nc-sign-up: "\f370";
@nc-signal: "\f371";
@nc-signature: "\f372";
@nc-silly: "\f373";
@nc-sim-card: "\f374";
@nc-single-bed: "\f375";
@nc-sink: "\f376";
@nc-sink-faucet: "\f377";
@nc-six: "\f378";
@nc-size-large: "\f379";
@nc-size-medium: "\f37a";
@nc-size-small: "\f37b";
@nc-skateboard: "\f37c";
@nc-skateboard-2: "\f37d";
@nc-skateboarding: "\f37e";
@nc-skating: "\f37f";
@nc-skiing: "\f380";
@nc-skipping-rope: "\f381";
@nc-skirt: "\f382";
@nc-skull: "\f383";
@nc-skull-2: "\f384";
@nc-slacks-12: "\f385";
@nc-slacks-13: "\f386";
@nc-sleep: "\f387";
@nc-sleep-2: "\f388";
@nc-sleeping-baby: "\f389";
@nc-slice: "\f38a";
@nc-slide-left: "\f38b";
@nc-slide-right: "\f38c";
@nc-slider: "\f38d";
@nc-slideshow-anim: "\f38e";
@nc-slippers: "\f38f";
@nc-slot-machine: "\f390";
@nc-sloth: "\f391";
@nc-smart: "\f392";
@nc-smart-house: "\f393";
@nc-smartphone: "\f394";
@nc-smartwatch: "\f395";
@nc-smile: "\f396";
@nc-smiling-face-glasses: "\f397";
@nc-smiling-face-sunglasses: "\f398";
@nc-smoking: "\f399";
@nc-smoothie: "\f39a";
@nc-snack: "\f39b";
@nc-snake: "\f39c";
@nc-sneeze: "\f39d";
@nc-sniper-rifle: "\f39e";
@nc-snorkel-mask: "\f39f";
@nc-snow: "\f3a0";
@nc-snow-ball: "\f3a1";
@nc-snowboard: "\f3a2";
@nc-snowboarding: "\f3a3";
@nc-snowman: "\f3a4";
@nc-snowman-head: "\f3a5";
@nc-soap: "\f3a6";
@nc-soccer: "\f3a7";
@nc-soccer-ball: "\f3a8";
@nc-soccer-field: "\f3a9";
@nc-social-distancing: "\f3aa";
@nc-social-sharing: "\f3ab";
@nc-sock: "\f3ac";
@nc-socket: "\f3ad";
@nc-socket-europe-1: "\f3ae";
@nc-socket-europe-2: "\f3af";
@nc-socket-uk: "\f3b0";
@nc-sofa: "\f3b1";
@nc-soft-drink: "\f3b2";
@nc-soldier: "\f3b3";
@nc-solider-helmet: "\f3b4";
@nc-sort-tool: "\f3b5";
@nc-sound: "\f3b6";
@nc-sound-wave: "\f3b7";
@nc-soundwave: "\f3b8";
@nc-soup: "\f3b9";
@nc-soy-sauce: "\f3ba";
@nc-spa: "\f3bb";
@nc-spa-rocks: "\f3bc";
@nc-space-divider: "\f3bd";
@nc-spaceship: "\f3be";
@nc-spade: "\f3bf";
@nc-spades-suit: "\f3c0";
@nc-sparks: "\f3c1";
@nc-speaker: "\f3c2";
@nc-speaker-2: "\f3c3";
@nc-spectrum: "\f3c4";
@nc-speechless: "\f3c5";
@nc-speedometer: "\f3c6";
@nc-sperm: "\f3c7";
@nc-sphinx: "\f3c8";
@nc-spider: "\f3c9";
@nc-spinning-bike: "\f3ca";
@nc-spiteful: "\f3cb";
@nc-split: "\f3cc";
@nc-split-branch: "\f3cd";
@nc-sport-bag: "\f3ce";
@nc-sport-mode: "\f3cf";
@nc-sports-bra: "\f3d0";
@nc-sports-fan: "\f3d1";
@nc-sports-tank: "\f3d2";
@nc-spray-bottle: "\f3d3";
@nc-spray-can: "\f3d4";
@nc-square-marker: "\f3d5";
@nc-square-pin: "\f3d6";
@nc-squares-anim: "\f3d7";
@nc-squares-anim-2: "\f3d8";
@nc-ssd: "\f3d9";
@nc-stack: "\f3da";
@nc-stadium: "\f3db";
@nc-stair-climber: "\f3dc";
@nc-stairs: "\f3dd";
@nc-stamp: "\f3de";
@nc-standing-man: "\f3df";
@nc-standing-woman: "\f3e0";
@nc-star: "\f3e1";
@nc-star-rate: "\f3e2";
@nc-statistics: "\f3e3";
@nc-statue-of-liberty: "\f3e4";
@nc-stay-home: "\f3e5";
@nc-steak: "\f3e6";
@nc-steak-2: "\f3e7";
@nc-steam-iron: "\f3e8";
@nc-steering-wheel: "\f3e9";
@nc-steps: "\f3ea";
@nc-stethoscope: "\f3eb";
@nc-sticker: "\f3ec";
@nc-stock-2: "\f3ed";
@nc-stock-market: "\f3ee";
@nc-stop-sign: "\f3ef";
@nc-stopwatch: "\f3f0";
@nc-storage-hanger: "\f3f1";
@nc-storage-shelves: "\f3f2";
@nc-storage-unit: "\f3f3";
@nc-store: "\f3f4";
@nc-strawberry: "\f3f5";
@nc-stretch: "\f3f6";
@nc-stretching: "\f3f7";
@nc-strikethrough: "\f3f8";
@nc-style: "\f3f9";
@nc-submachine-gun: "\f3fa";
@nc-submarine: "\f3fb";
@nc-subscript: "\f3fc";
@nc-subtitles: "\f3fd";
@nc-sugar: "\f3fe";
@nc-sun: "\f3ff";
@nc-sun-cloud: "\f400";
@nc-sun-cloud-drop: "\f401";
@nc-sun-cloud-fog: "\f402";
@nc-sun-cloud-hail: "\f403";
@nc-sun-cloud-light: "\f404";
@nc-sun-cloud-rain: "\f405";
@nc-sun-cloud-snow-54: "\f406";
@nc-sun-cloud-snow-55: "\f407";
@nc-sun-fog-29: "\f408";
@nc-sun-fog-30: "\f409";
@nc-sun-fog-43: "\f40a";
@nc-sunglasses: "\f40b";
@nc-sunglasses-48: "\f40c";
@nc-superscript: "\f40d";
@nc-support: "\f40e";
@nc-surfboard: "\f40f";
@nc-surprise: "\f410";
@nc-survey: "\f411";
@nc-sushi: "\f412";
@nc-swap-horizontal: "\f413";
@nc-swap-vertical: "\f414";
@nc-swimming: "\f415";
@nc-swimming-pool: "\f416";
@nc-swimsuit: "\f417";
@nc-swipe-bottom: "\f418";
@nc-swipe-left: "\f419";
@nc-swipe-right: "\f41a";
@nc-swipe-up: "\f41b";
@nc-swiss-knife: "\f41c";
@nc-switches: "\f41d";
@nc-sword: "\f41e";
@nc-sydney-opera-house: "\f41f";
@nc-sync-devices: "\f420";
@nc-syringe: "\f421";
@nc-system-configuration: "\f422";
@nc-system-preferences: "\f423";
@nc-system-update: "\f424";
@nc-t-add: "\f425";
@nc-t-delete: "\f426";
@nc-t-remove: "\f427";
@nc-table: "\f428";
@nc-table-lamp: "\f429";
@nc-table-layout: "\f42a";
@nc-table-move: "\f42b";
@nc-table-slide: "\f42c";
@nc-table-tennis-bat: "\f42d";
@nc-tablet: "\f42e";
@nc-tablet-2: "\f42f";
@nc-tablet-charging: "\f430";
@nc-tablet-mobile: "\f431";
@nc-tablet-toolbar: "\f432";
@nc-tacos: "\f433";
@nc-tactic: "\f434";
@nc-tag: "\f435";
@nc-tag-add: "\f436";
@nc-tag-check: "\f437";
@nc-tag-cut: "\f438";
@nc-tag-loyalty: "\f439";
@nc-tag-remove: "\f43a";
@nc-tag-sale: "\f43b";
@nc-tags-stack: "\f43c";
@nc-take-off: "\f43d";
@nc-takeaway: "\f43e";
@nc-taoism: "\f43f";
@nc-tap-01: "\f440";
@nc-tap-02: "\f441";
@nc-tape: "\f442";
@nc-target: "\f443";
@nc-taxi: "\f444";
@nc-tea: "\f445";
@nc-tea-bag: "\f446";
@nc-team: "\f447";
@nc-teddy-bear: "\f448";
@nc-telegram: "\f449";
@nc-telephone: "\f44a";
@nc-telescope: "\f44b";
@nc-temperature: "\f44c";
@nc-temperature-2: "\f44d";
@nc-temple: "\f44e";
@nc-temple-2: "\f44f";
@nc-tennis: "\f450";
@nc-tennis-ball: "\f451";
@nc-tennis-racket: "\f452";
@nc-terrace: "\f453";
@nc-text: "\f454";
@nc-text-align-center: "\f455";
@nc-text-align-justify: "\f456";
@nc-text-align-left: "\f457";
@nc-text-align-right: "\f458";
@nc-text-bg-color: "\f459";
@nc-text-color: "\f45a";
@nc-text-horizontal-scale: "\f45b";
@nc-text-italic: "\f45c";
@nc-text-size: "\f45d";
@nc-text-tracking: "\f45e";
@nc-text-vertical-scale: "\f45f";
@nc-texture: "\f460";
@nc-theater: "\f461";
@nc-theater-curtains: "\f462";
@nc-thermometer: "\f463";
@nc-three: "\f464";
@nc-three-dimensional-object: "\f465";
@nc-three-dimensional-world: "\f466";
@nc-three-way-direction: "\f467";
@nc-thumb-down: "\f468";
@nc-thumb-up: "\f469";
@nc-ticket: "\f46a";
@nc-tie-01: "\f46b";
@nc-tie-02: "\f46c";
@nc-tie-bow: "\f46d";
@nc-tiktok: "\f46e";
@nc-time-alarm: "\f46f";
@nc-time-clock: "\f470";
@nc-time-machine: "\f471";
@nc-timeline: "\f472";
@nc-timer: "\f473";
@nc-title-case: "\f474";
@nc-tnt-explosives: "\f475";
@nc-toast: "\f476";
@nc-toaster: "\f477";
@nc-todo: "\f478";
@nc-toggle: "\f479";
@nc-toilet: "\f47a";
@nc-toilet-paper: "\f47b";
@nc-toilette: "\f47c";
@nc-tomato: "\f47d";
@nc-tool-blur: "\f47e";
@nc-tool-hand: "\f47f";
@nc-tool-select: "\f480";
@nc-tooth: "\f481";
@nc-towel: "\f482";
@nc-towel-hanger: "\f483";
@nc-track-delivery: "\f484";
@nc-tractor: "\f485";
@nc-traffic: "\f486";
@nc-train: "\f487";
@nc-train-speed: "\f488";
@nc-tram: "\f489";
@nc-transaction: "\f48a";
@nc-transactions: "\f48b";
@nc-transform: "\f48c";
@nc-transform-2d: "\f48d";
@nc-transform-origin: "\f48e";
@nc-translation: "\f48f";
@nc-transparent: "\f490";
@nc-trash: "\f491";
@nc-trash-can: "\f492";
@nc-travel-makeup-mirror: "\f493";
@nc-treadmill: "\f494";
@nc-treasure-map-21: "\f495";
@nc-treasure-map-40: "\f496";
@nc-tree: "\f497";
@nc-tree-01: "\f498";
@nc-tree-03: "\f499";
@nc-tree-2: "\f49a";
@nc-tree-ball: "\f49b";
@nc-trend-down: "\f49c";
@nc-trend-up: "\f49d";
@nc-triangle-down: "\f49e";
@nc-triangle-left: "\f49f";
@nc-triangle-line-down: "\f4a0";
@nc-triangle-line-left: "\f4a1";
@nc-triangle-line-right: "\f4a2";
@nc-triangle-line-up: "\f4a3";
@nc-triangle-right: "\f4a4";
@nc-triangle-sm-down: "\f4a5";
@nc-triangle-sm-left: "\f4a6";
@nc-triangle-sm-right: "\f4a7";
@nc-triangle-sm-up: "\f4a8";
@nc-triangle-up: "\f4a9";
@nc-tripod: "\f4aa";
@nc-trophy: "\f4ab";
@nc-trowel: "\f4ac";
@nc-truck-front: "\f4ad";
@nc-trumpet: "\f4ae";
@nc-trunk: "\f4af";
@nc-tshirt-53: "\f4b0";
@nc-tshirt-54: "\f4b1";
@nc-tshirt-sport: "\f4b2";
@nc-tty: "\f4b3";
@nc-turkey-head: "\f4b4";
@nc-turn-e: "\f4b5";
@nc-turn-n: "\f4b6";
@nc-turn-s: "\f4b7";
@nc-turn-w: "\f4b8";
@nc-turtle: "\f4b9";
@nc-tv: "\f4ba";
@nc-tv-stand: "\f4bb";
@nc-twitch: "\f4bc";
@nc-two: "\f4bd";
@nc-two-way-direction: "\f4be";
@nc-type-tool: "\f4bf";
@nc-typography: "\f4c0";
@nc-umbrella-13: "\f4c1";
@nc-underline: "\f4c2";
@nc-underwear: "\f4c3";
@nc-underwear-man: "\f4c4";
@nc-undo: "\f4c5";
@nc-ungroup: "\f4c6";
@nc-unite: "\f4c7";
@nc-unite-2: "\f4c8";
@nc-unlink: "\f4c9";
@nc-unlocked: "\f4ca";
@nc-unordered-list: "\f4cb";
@nc-up-arrow: "\f4cc";
@nc-upload: "\f4cd";
@nc-upload-data: "\f4ce";
@nc-upload-file: "\f4cf";
@nc-uppercase: "\f4d0";
@nc-upset-13: "\f4d1";
@nc-upset-14: "\f4d2";
@nc-url: "\f4d3";
@nc-usb: "\f4d4";
@nc-user: "\f4d5";
@nc-user-2: "\f4d6";
@nc-user-add: "\f4d7";
@nc-user-bubble: "\f4d8";
@nc-user-c-frame: "\f4d9";
@nc-user-c-frame-2: "\f4da";
@nc-user-c-frame-3: "\f4db";
@nc-user-check: "\f4dc";
@nc-user-create: "\f4dd";
@nc-user-delete: "\f4de";
@nc-user-delete-cross: "\f4df";
@nc-user-delete-line: "\f4e0";
@nc-user-edit: "\f4e1";
@nc-user-focus: "\f4e2";
@nc-user-group: "\f4e3";
@nc-user-heart: "\f4e4";
@nc-user-list: "\f4e5";
@nc-user-location: "\f4e6";
@nc-user-location-2: "\f4e7";
@nc-user-lock: "\f4e8";
@nc-user-new: "\f4e9";
@nc-user-remove: "\f4ea";
@nc-user-s-frame: "\f4eb";
@nc-user-s-frame-2: "\f4ec";
@nc-user-s-frame-3: "\f4ed";
@nc-user-search: "\f4ee";
@nc-user-share: "\f4ef";
@nc-user-star: "\f4f0";
@nc-user-sync: "\f4f1";
@nc-user-time: "\f4f2";
@nc-user-wearing-glasses: "\f4f3";
@nc-users: "\f4f4";
@nc-users-add: "\f4f5";
@nc-users-check: "\f4f6";
@nc-users-heart: "\f4f7";
@nc-users-location: "\f4f8";
@nc-users-lock: "\f4f9";
@nc-users-meeting: "\f4fa";
@nc-users-mm: "\f4fb";
@nc-users-remove: "\f4fc";
@nc-users-wm: "\f4fd";
@nc-users-ww: "\f4fe";
@nc-utility-bench: "\f4ff";
@nc-vacuum-cleaner: "\f500";
@nc-vampire: "\f501";
@nc-vector: "\f502";
@nc-vegan: "\f503";
@nc-ventilation: "\f504";
@nc-verified: "\f505";
@nc-vertical-divider: "\f506";
@nc-vespa: "\f507";
@nc-vespa-front: "\f508";
@nc-vest: "\f509";
@nc-vest-31: "\f50a";
@nc-vibrance: "\f50b";
@nc-video: "\f50c";
@nc-video-camera: "\f50d";
@nc-video-gallery: "\f50e";
@nc-video-gallery-2: "\f50f";
@nc-video-off: "\f510";
@nc-video-player: "\f511";
@nc-video-playlist: "\f512";
@nc-view: "\f513";
@nc-vignette: "\f514";
@nc-vintage-computer: "\f515";
@nc-vintage-tv: "\f516";
@nc-violin: "\f517";
@nc-virtual-assistant: "\f518";
@nc-virtual-assistant-2: "\f519";
@nc-virtual-environment: "\f51a";
@nc-virtual-reality: "\f51b";
@nc-virus: "\f51c";
@nc-voice-recognition: "\f51d";
@nc-voice-record: "\f51e";
@nc-volleyball: "\f51f";
@nc-volleyball-player: "\f520";
@nc-volume: "\f521";
@nc-volume-2: "\f522";
@nc-volume-down: "\f523";
@nc-volume-mute: "\f524";
@nc-volume-off: "\f525";
@nc-volume-up: "\f526";
@nc-vpn: "\f527";
@nc-vr-controller: "\f528";
@nc-vr-headset: "\f529";
@nc-waffle: "\f52a";
@nc-walk: "\f52b";
@nc-walking-aid: "\f52c";
@nc-walking-support: "\f52d";
@nc-wallet: "\f52e";
@nc-wallet-43: "\f52f";
@nc-wallet-44: "\f530";
@nc-wallet-90: "\f531";
@nc-wand: "\f532";
@nc-wardrobe: "\f533";
@nc-wardrobe-2: "\f534";
@nc-wardrobe-3: "\f535";
@nc-wardrobe-4: "\f536";
@nc-warning-sign: "\f537";
@nc-wash-30: "\f538";
@nc-wash-60: "\f539";
@nc-wash-90: "\f53a";
@nc-wash-hand: "\f53b";
@nc-wash-hands: "\f53c";
@nc-washing-fluid: "\f53d";
@nc-washing-machine: "\f53e";
@nc-waste: "\f53f";
@nc-waste-danger: "\f540";
@nc-waste-recycling: "\f541";
@nc-watch: "\f542";
@nc-watch-2: "\f543";
@nc-watch-dev: "\f544";
@nc-watch-heart: "\f545";
@nc-watch-heartbeat: "\f546";
@nc-water: "\f547";
@nc-water-aerobics: "\f548";
@nc-water-hand: "\f549";
@nc-water-polo: "\f54a";
@nc-water-polo-ball: "\f54b";
@nc-water-sink: "\f54c";
@nc-water-surface: "\f54d";
@nc-water-wave: "\f54e";
@nc-watering-can: "\f54f";
@nc-watering-plants: "\f550";
@nc-watermelon: "\f551";
@nc-wc-sign: "\f552";
@nc-web-design: "\f553";
@nc-web-hyperlink: "\f554";
@nc-web-link: "\f555";
@nc-web-url: "\f556";
@nc-webcam: "\f557";
@nc-webcam-2: "\f558";
@nc-webpage: "\f559";
@nc-wedding-arch: "\f55a";
@nc-wedding-cake: "\f55b";
@nc-wedding-ring: "\f55c";
@nc-wedding-rings: "\f55d";
@nc-weed: "\f55e";
@nc-weight-bench: "\f55f";
@nc-weight-gain: "\f560";
@nc-weight-loss: "\f561";
@nc-weight-plate: "\f562";
@nc-weight-scale: "\f563";
@nc-what: "\f564";
@nc-wheel: "\f565";
@nc-wheel-2: "\f566";
@nc-wheelbarrow: "\f567";
@nc-wheelchair: "\f568";
@nc-wheelchair-2: "\f569";
@nc-wheelchair-ramp: "\f56a";
@nc-whisk: "\f56b";
@nc-whiskers: "\f56c";
@nc-whistle: "\f56d";
@nc-white-balance: "\f56e";
@nc-white-house: "\f56f";
@nc-widget: "\f570";
@nc-width: "\f571";
@nc-wifi: "\f572";
@nc-wifi-2: "\f573";
@nc-wifi-off: "\f574";
@nc-wifi-protected: "\f575";
@nc-wifi-router: "\f576";
@nc-wind: "\f577";
@nc-wind-2: "\f578";
@nc-windmill: "\f579";
@nc-windmill-2: "\f57a";
@nc-window: "\f57b";
@nc-window-add: "\f57c";
@nc-window-code: "\f57d";
@nc-window-delete: "\f57e";
@nc-window-dev: "\f57f";
@nc-window-maximize: "\f580";
@nc-window-minimize: "\f581";
@nc-window-paragraph: "\f582";
@nc-window-responsive: "\f583";
@nc-windsurfing: "\f584";
@nc-wine-list: "\f585";
@nc-wink-06: "\f586";
@nc-wink-11: "\f587";
@nc-wink-69: "\f588";
@nc-winner: "\f589";
@nc-wireframe: "\f58a";
@nc-wireless-charging: "\f58b";
@nc-witch-hat: "\f58c";
@nc-wolf: "\f58d";
@nc-woman: "\f58e";
@nc-woman-arrow-down: "\f58f";
@nc-woman-arrow-up: "\f590";
@nc-woman-man: "\f591";
@nc-woman-profile: "\f592";
@nc-woman-up-front: "\f593";
@nc-wood: "\f594";
@nc-wool-ball: "\f595";
@nc-work-badge: "\f596";
@nc-workout-plan: "\f597";
@nc-world: "\f598";
@nc-world-2: "\f599";
@nc-world-marker: "\f59a";
@nc-world-pin: "\f59b";
@nc-wrench: "\f59c";
@nc-wrench-tool: "\f59d";
@nc-xmas-sock: "\f59e";
@nc-yoga: "\f59f";
@nc-yogurt: "\f5a0";
@nc-zero: "\f5a1";
@nc-zipped-file: "\f5a2";
@nc-zombie: "\f5a3";
@nc-zoom: "\f5a4";
@nc-zoom-in: "\f5a5";
@nc-zoom-out: "\f5a6";

.nc-2x-drag-down::before {
  content: @nc-2x-drag-down;
}

.nc-2x-drag-up::before {
  content: @nc-2x-drag-up;
}

.nc-2x-swipe-down::before {
  content: @nc-2x-swipe-down;
}

.nc-2x-swipe-left::before {
  content: @nc-2x-swipe-left;
}

.nc-2x-swipe-right::before {
  content: @nc-2x-swipe-right;
}

.nc-2x-swipe-up::before {
  content: @nc-2x-swipe-up;
}

.nc-2x-tap::before {
  content: @nc-2x-tap;
}

.nc-3d-29::before {
  content: @nc-3d-29;
}

.nc-3d-glasses::before {
  content: @nc-3d-glasses;
}

.nc-3d-model::before {
  content: @nc-3d-model;
}

.nc-3d-printing::before {
  content: @nc-3d-printing;
}

.nc-3x-swipe-left::before {
  content: @nc-3x-swipe-left;
}

.nc-3x-swipe-right::before {
  content: @nc-3x-swipe-right;
}

.nc-3x-swipe-up::before {
  content: @nc-3x-swipe-up;
}

.nc-3x-tap::before {
  content: @nc-3x-tap;
}

.nc-4x-swipe-left::before {
  content: @nc-4x-swipe-left;
}

.nc-4x-swipe-right::before {
  content: @nc-4x-swipe-right;
}

.nc-4x-swipe-up::before {
  content: @nc-4x-swipe-up;
}

.nc-a-chart::before {
  content: @nc-a-chart;
}

.nc-a-chat::before {
  content: @nc-a-chat;
}

.nc-a-tag::before {
  content: @nc-a-tag;
}

.nc-a-tag-add::before {
  content: @nc-a-tag-add;
}

.nc-a-tag-remove::before {
  content: @nc-a-tag-remove;
}

.nc-abc::before {
  content: @nc-abc;
}

.nc-access-key::before {
  content: @nc-access-key;
}

.nc-accessibility::before {
  content: @nc-accessibility;
}

.nc-accessibility-lift::before {
  content: @nc-accessibility-lift;
}

.nc-account::before {
  content: @nc-account;
}

.nc-acorn::before {
  content: @nc-acorn;
}

.nc-active-38::before {
  content: @nc-active-38;
}

.nc-active-40::before {
  content: @nc-active-40;
}

.nc-adaptive-bike::before {
  content: @nc-adaptive-bike;
}

.nc-add::before {
  content: @nc-add;
}

.nc-add-fav::before {
  content: @nc-add-fav;
}

.nc-add-favorite::before {
  content: @nc-add-favorite;
}

.nc-add-like::before {
  content: @nc-add-like;
}

.nc-add-notification::before {
  content: @nc-add-notification;
}

.nc-add-to-cart::before {
  content: @nc-add-to-cart;
}

.nc-add-to-cart-2::before {
  content: @nc-add-to-cart-2;
}

.nc-address-book::before {
  content: @nc-address-book;
}

.nc-adult-content::before {
  content: @nc-adult-content;
}

.nc-agenda::before {
  content: @nc-agenda;
}

.nc-agenda-bookmark::before {
  content: @nc-agenda-bookmark;
}

.nc-ai::before {
  content: @nc-ai;
}

.nc-ai-generated-img::before {
  content: @nc-ai-generated-img;
}

.nc-air-baloon::before {
  content: @nc-air-baloon;
}

.nc-air-bomb::before {
  content: @nc-air-bomb;
}

.nc-air-conditioner::before {
  content: @nc-air-conditioner;
}

.nc-airbag::before {
  content: @nc-airbag;
}

.nc-airplane::before {
  content: @nc-airplane;
}

.nc-airport::before {
  content: @nc-airport;
}

.nc-airport-trolley::before {
  content: @nc-airport-trolley;
}

.nc-alarm::before {
  content: @nc-alarm;
}

.nc-alarm-add::before {
  content: @nc-alarm-add;
}

.nc-alarm-disable::before {
  content: @nc-alarm-disable;
}

.nc-album::before {
  content: @nc-album;
}

.nc-alcohol::before {
  content: @nc-alcohol;
}

.nc-algorithm::before {
  content: @nc-algorithm;
}

.nc-alien-29::before {
  content: @nc-alien-29;
}

.nc-alien-33::before {
  content: @nc-alien-33;
}

.nc-align-bottom::before {
  content: @nc-align-bottom;
}

.nc-align-center-horizontal::before {
  content: @nc-align-center-horizontal;
}

.nc-align-center-vertical::before {
  content: @nc-align-center-vertical;
}

.nc-align-left::before {
  content: @nc-align-left;
}

.nc-align-right::before {
  content: @nc-align-right;
}

.nc-align-top::before {
  content: @nc-align-top;
}

.nc-all-directions::before {
  content: @nc-all-directions;
}

.nc-alpha-order::before {
  content: @nc-alpha-order;
}

.nc-ambulance::before {
  content: @nc-ambulance;
}

.nc-ampersand::before {
  content: @nc-ampersand;
}

.nc-analytics::before {
  content: @nc-analytics;
}

.nc-anchor::before {
  content: @nc-anchor;
}

.nc-android::before {
  content: @nc-android;
}

.nc-angle::before {
  content: @nc-angle;
}

.nc-angry-10::before {
  content: @nc-angry-10;
}

.nc-angry-44::before {
  content: @nc-angry-44;
}

.nc-animation-14::before {
  content: @nc-animation-14;
}

.nc-animation-31::before {
  content: @nc-animation-31;
}

.nc-animation-32::before {
  content: @nc-animation-32;
}

.nc-antenna::before {
  content: @nc-antenna;
}

.nc-anti-shake::before {
  content: @nc-anti-shake;
}

.nc-apartment::before {
  content: @nc-apartment;
}

.nc-aperture::before {
  content: @nc-aperture;
}

.nc-api::before {
  content: @nc-api;
}

.nc-app::before {
  content: @nc-app;
}

.nc-app-services::before {
  content: @nc-app-services;
}

.nc-app-store::before {
  content: @nc-app-store;
}

.nc-apple::before {
  content: @nc-apple;
}

.nc-apple-2::before {
  content: @nc-apple-2;
}

.nc-appointment::before {
  content: @nc-appointment;
}

.nc-apps::before {
  content: @nc-apps;
}

.nc-apron::before {
  content: @nc-apron;
}

.nc-arcade::before {
  content: @nc-arcade;
}

.nc-archer::before {
  content: @nc-archer;
}

.nc-archery::before {
  content: @nc-archery;
}

.nc-archery-target::before {
  content: @nc-archery-target;
}

.nc-archive::before {
  content: @nc-archive;
}

.nc-archive-check::before {
  content: @nc-archive-check;
}

.nc-archive-content::before {
  content: @nc-archive-content;
}

.nc-archive-doc::before {
  content: @nc-archive-doc;
}

.nc-archive-doc-check::before {
  content: @nc-archive-doc-check;
}

.nc-archive-drawer::before {
  content: @nc-archive-drawer;
}

.nc-archive-file::before {
  content: @nc-archive-file;
}

.nc-archive-file-check::before {
  content: @nc-archive-file-check;
}

.nc-armchair::before {
  content: @nc-armchair;
}

.nc-armor::before {
  content: @nc-armor;
}

.nc-army::before {
  content: @nc-army;
}

.nc-arrow-bottom-left::before {
  content: @nc-arrow-bottom-left;
}

.nc-arrow-bottom-right::before {
  content: @nc-arrow-bottom-right;
}

.nc-arrow-down::before {
  content: @nc-arrow-down;
}

.nc-arrow-down-2::before {
  content: @nc-arrow-down-2;
}

.nc-arrow-down-3::before {
  content: @nc-arrow-down-3;
}

.nc-arrow-e::before {
  content: @nc-arrow-e;
}

.nc-arrow-left::before {
  content: @nc-arrow-left;
}

.nc-arrow-left-2::before {
  content: @nc-arrow-left-2;
}

.nc-arrow-left-3::before {
  content: @nc-arrow-left-3;
}

.nc-arrow-n::before {
  content: @nc-arrow-n;
}

.nc-arrow-right::before {
  content: @nc-arrow-right;
}

.nc-arrow-right-2::before {
  content: @nc-arrow-right-2;
}

.nc-arrow-right-3::before {
  content: @nc-arrow-right-3;
}

.nc-arrow-s::before {
  content: @nc-arrow-s;
}

.nc-arrow-sm-down::before {
  content: @nc-arrow-sm-down;
}

.nc-arrow-sm-left::before {
  content: @nc-arrow-sm-left;
}

.nc-arrow-sm-right::before {
  content: @nc-arrow-sm-right;
}

.nc-arrow-tool::before {
  content: @nc-arrow-tool;
}

.nc-arrow-top-left::before {
  content: @nc-arrow-top-left;
}

.nc-arrow-top-right::before {
  content: @nc-arrow-top-right;
}

.nc-arrow-up::before {
  content: @nc-arrow-up;
}

.nc-arrow-up-2::before {
  content: @nc-arrow-up-2;
}

.nc-arrow-up-3::before {
  content: @nc-arrow-up-3;
}

.nc-arrow-w::before {
  content: @nc-arrow-w;
}

.nc-arrows-expand::before {
  content: @nc-arrows-expand;
}

.nc-arrows-expand-2::before {
  content: @nc-arrows-expand-2;
}

.nc-arrows-fullscreen::before {
  content: @nc-arrows-fullscreen;
}

.nc-arrows-fullscreen-2::before {
  content: @nc-arrows-fullscreen-2;
}

.nc-arrows-maximize::before {
  content: @nc-arrows-maximize;
}

.nc-arrows-maximize-2::before {
  content: @nc-arrows-maximize-2;
}

.nc-arrows-opposite-directions::before {
  content: @nc-arrows-opposite-directions;
}

.nc-arrows-same-direction::before {
  content: @nc-arrows-same-direction;
}

.nc-artboard::before {
  content: @nc-artboard;
}

.nc-artificial-brain::before {
  content: @nc-artificial-brain;
}

.nc-artificial-intelligence::before {
  content: @nc-artificial-intelligence;
}

.nc-assault-rifle::before {
  content: @nc-assault-rifle;
}

.nc-astronaut::before {
  content: @nc-astronaut;
}

.nc-astronomy::before {
  content: @nc-astronomy;
}

.nc-at-sign::before {
  content: @nc-at-sign;
}

.nc-at-sign-2::before {
  content: @nc-at-sign-2;
}

.nc-athletics::before {
  content: @nc-athletics;
}

.nc-atm::before {
  content: @nc-atm;
}

.nc-atom::before {
  content: @nc-atom;
}

.nc-attach::before {
  content: @nc-attach;
}

.nc-attachment::before {
  content: @nc-attachment;
}

.nc-aubergine::before {
  content: @nc-aubergine;
}

.nc-audio-description::before {
  content: @nc-audio-description;
}

.nc-audio-jack::before {
  content: @nc-audio-jack;
}

.nc-audio-mixer::before {
  content: @nc-audio-mixer;
}

.nc-augmented-reality::before {
  content: @nc-augmented-reality;
}

.nc-auto-flash::before {
  content: @nc-auto-flash;
}

.nc-auto-flash-2::before {
  content: @nc-auto-flash-2;
}

.nc-auto-focus::before {
  content: @nc-auto-focus;
}

.nc-automated-logistics::before {
  content: @nc-automated-logistics;
}

.nc-avocado::before {
  content: @nc-avocado;
}

.nc-award::before {
  content: @nc-award;
}

.nc-award-49::before {
  content: @nc-award-49;
}

.nc-axe::before {
  content: @nc-axe;
}

.nc-b-chart::before {
  content: @nc-b-chart;
}

.nc-b-comment::before {
  content: @nc-b-comment;
}

.nc-b-eye::before {
  content: @nc-b-eye;
}

.nc-baby::before {
  content: @nc-baby;
}

.nc-baby-bottle::before {
  content: @nc-baby-bottle;
}

.nc-baby-car-seat::before {
  content: @nc-baby-car-seat;
}

.nc-baby-clothes::before {
  content: @nc-baby-clothes;
}

.nc-baby-monitor::before {
  content: @nc-baby-monitor;
}

.nc-baby-stroller::before {
  content: @nc-baby-stroller;
}

.nc-back-arrow::before {
  content: @nc-back-arrow;
}

.nc-backpack::before {
  content: @nc-backpack;
}

.nc-backpack-2::before {
  content: @nc-backpack-2;
}

.nc-backpack-57::before {
  content: @nc-backpack-57;
}

.nc-backpack-58::before {
  content: @nc-backpack-58;
}

.nc-backup::before {
  content: @nc-backup;
}

.nc-backward::before {
  content: @nc-backward;
}

.nc-bacon::before {
  content: @nc-bacon;
}

.nc-badge::before {
  content: @nc-badge;
}

.nc-bag::before {
  content: @nc-bag;
}

.nc-bag-16::before {
  content: @nc-bag-16;
}

.nc-bag-17::before {
  content: @nc-bag-17;
}

.nc-bag-20::before {
  content: @nc-bag-20;
}

.nc-bag-21::before {
  content: @nc-bag-21;
}

.nc-bag-22::before {
  content: @nc-bag-22;
}

.nc-bag-49::before {
  content: @nc-bag-49;
}

.nc-bag-50::before {
  content: @nc-bag-50;
}

.nc-bag-add-18::before {
  content: @nc-bag-add-18;
}

.nc-bag-add-21::before {
  content: @nc-bag-add-21;
}

.nc-bag-delivery::before {
  content: @nc-bag-delivery;
}

.nc-bag-edit::before {
  content: @nc-bag-edit;
}

.nc-bag-remove-19::before {
  content: @nc-bag-remove-19;
}

.nc-bag-remove-22::before {
  content: @nc-bag-remove-22;
}

.nc-bag-time::before {
  content: @nc-bag-time;
}

.nc-baggage-collection::before {
  content: @nc-baggage-collection;
}

.nc-baggage-scale::before {
  content: @nc-baggage-scale;
}

.nc-baguette::before {
  content: @nc-baguette;
}

.nc-bahai::before {
  content: @nc-bahai;
}

.nc-bakery::before {
  content: @nc-bakery;
}

.nc-balance::before {
  content: @nc-balance;
}

.nc-baloon::before {
  content: @nc-baloon;
}

.nc-bamboo::before {
  content: @nc-bamboo;
}

.nc-ban::before {
  content: @nc-ban;
}

.nc-banana::before {
  content: @nc-banana;
}

.nc-bank-statement::before {
  content: @nc-bank-statement;
}

.nc-barbecue::before {
  content: @nc-barbecue;
}

.nc-barbecue-15::before {
  content: @nc-barbecue-15;
}

.nc-barbecue-tools::before {
  content: @nc-barbecue-tools;
}

.nc-barbell::before {
  content: @nc-barbell;
}

.nc-barbershop::before {
  content: @nc-barbershop;
}

.nc-barcode::before {
  content: @nc-barcode;
}

.nc-barcode-qr::before {
  content: @nc-barcode-qr;
}

.nc-barcode-scan::before {
  content: @nc-barcode-scan;
}

.nc-bars-anim::before {
  content: @nc-bars-anim;
}

.nc-bars-anim-2::before {
  content: @nc-bars-anim-2;
}

.nc-bars-anim-3::before {
  content: @nc-bars-anim-3;
}

.nc-baseball::before {
  content: @nc-baseball;
}

.nc-baseball-bat::before {
  content: @nc-baseball-bat;
}

.nc-baseball-pitch::before {
  content: @nc-baseball-pitch;
}

.nc-baseball-player::before {
  content: @nc-baseball-player;
}

.nc-basket::before {
  content: @nc-basket;
}

.nc-basket-add::before {
  content: @nc-basket-add;
}

.nc-basket-edit::before {
  content: @nc-basket-edit;
}

.nc-basket-favorite::before {
  content: @nc-basket-favorite;
}

.nc-basket-remove::before {
  content: @nc-basket-remove;
}

.nc-basket-search::before {
  content: @nc-basket-search;
}

.nc-basket-share::before {
  content: @nc-basket-share;
}

.nc-basket-simple::before {
  content: @nc-basket-simple;
}

.nc-basket-simple-add::before {
  content: @nc-basket-simple-add;
}

.nc-basket-simple-remove::before {
  content: @nc-basket-simple-remove;
}

.nc-basket-update::before {
  content: @nc-basket-update;
}

.nc-basketball::before {
  content: @nc-basketball;
}

.nc-basketball-board::before {
  content: @nc-basketball-board;
}

.nc-basketball-player::before {
  content: @nc-basketball-player;
}

.nc-basketball-ring::before {
  content: @nc-basketball-ring;
}

.nc-bat::before {
  content: @nc-bat;
}

.nc-bath-faucet::before {
  content: @nc-bath-faucet;
}

.nc-bathroom-cabinet::before {
  content: @nc-bathroom-cabinet;
}

.nc-bathtub::before {
  content: @nc-bathtub;
}

.nc-battery::before {
  content: @nc-battery;
}

.nc-battery-charging::before {
  content: @nc-battery-charging;
}

.nc-battery-level::before {
  content: @nc-battery-level;
}

.nc-battery-low::before {
  content: @nc-battery-low;
}

.nc-battery-power::before {
  content: @nc-battery-power;
}

.nc-battery-status::before {
  content: @nc-battery-status;
}

.nc-beach-bat::before {
  content: @nc-beach-bat;
}

.nc-beach-umbrella::before {
  content: @nc-beach-umbrella;
}

.nc-bear::before {
  content: @nc-bear;
}

.nc-bear-2::before {
  content: @nc-bear-2;
}

.nc-beard::before {
  content: @nc-beard;
}

.nc-bed::before {
  content: @nc-bed;
}

.nc-bedroom::before {
  content: @nc-bedroom;
}

.nc-bee::before {
  content: @nc-bee;
}

.nc-beer-95::before {
  content: @nc-beer-95;
}

.nc-beer-96::before {
  content: @nc-beer-96;
}

.nc-bell::before {
  content: @nc-bell;
}

.nc-belt::before {
  content: @nc-belt;
}

.nc-beverage::before {
  content: @nc-beverage;
}

.nc-bicep::before {
  content: @nc-bicep;
}

.nc-big-ben::before {
  content: @nc-big-ben;
}

.nc-big-eyes::before {
  content: @nc-big-eyes;
}

.nc-big-smile::before {
  content: @nc-big-smile;
}

.nc-bigmouth::before {
  content: @nc-bigmouth;
}

.nc-bike::before {
  content: @nc-bike;
}

.nc-bike-bmx::before {
  content: @nc-bike-bmx;
}

.nc-bikini::before {
  content: @nc-bikini;
}

.nc-bill::before {
  content: @nc-bill;
}

.nc-billboard::before {
  content: @nc-billboard;
}

.nc-billiard-ball::before {
  content: @nc-billiard-ball;
}

.nc-bin::before {
  content: @nc-bin;
}

.nc-binary-code::before {
  content: @nc-binary-code;
}

.nc-binoculars::before {
  content: @nc-binoculars;
}

.nc-biochemistry::before {
  content: @nc-biochemistry;
}

.nc-biology::before {
  content: @nc-biology;
}

.nc-biscuit::before {
  content: @nc-biscuit;
}

.nc-bitcoin::before {
  content: @nc-bitcoin;
}

.nc-bleah::before {
  content: @nc-bleah;
}

.nc-blend::before {
  content: @nc-blend;
}

.nc-blender::before {
  content: @nc-blender;
}

.nc-blindness::before {
  content: @nc-blindness;
}

.nc-block::before {
  content: @nc-block;
}

.nc-block-down::before {
  content: @nc-block-down;
}

.nc-block-left::before {
  content: @nc-block-left;
}

.nc-block-right::before {
  content: @nc-block-right;
}

.nc-block-up::before {
  content: @nc-block-up;
}

.nc-blockchain::before {
  content: @nc-blockchain;
}

.nc-blockquote::before {
  content: @nc-blockquote;
}

.nc-blog::before {
  content: @nc-blog;
}

.nc-blue-mosque::before {
  content: @nc-blue-mosque;
}

.nc-blueberries::before {
  content: @nc-blueberries;
}

.nc-blueprint::before {
  content: @nc-blueprint;
}

.nc-bluetooth::before {
  content: @nc-bluetooth;
}

.nc-board::before {
  content: @nc-board;
}

.nc-board-2::before {
  content: @nc-board-2;
}

.nc-board-27::before {
  content: @nc-board-27;
}

.nc-board-28::before {
  content: @nc-board-28;
}

.nc-board-29::before {
  content: @nc-board-29;
}

.nc-board-30::before {
  content: @nc-board-30;
}

.nc-board-51::before {
  content: @nc-board-51;
}

.nc-board-game::before {
  content: @nc-board-game;
}

.nc-boat::before {
  content: @nc-boat;
}

.nc-boat-front::before {
  content: @nc-boat-front;
}

.nc-boat-small-02::before {
  content: @nc-boat-small-02;
}

.nc-boat-small-03::before {
  content: @nc-boat-small-03;
}

.nc-body-back::before {
  content: @nc-body-back;
}

.nc-body-butt::before {
  content: @nc-body-butt;
}

.nc-body-cream::before {
  content: @nc-body-cream;
}

.nc-bodybuilder::before {
  content: @nc-bodybuilder;
}

.nc-boiling-water::before {
  content: @nc-boiling-water;
}

.nc-bold::before {
  content: @nc-bold;
}

.nc-bolt::before {
  content: @nc-bolt;
}

.nc-bomb::before {
  content: @nc-bomb;
}

.nc-bones::before {
  content: @nc-bones;
}

.nc-book::before {
  content: @nc-book;
}

.nc-book-39::before {
  content: @nc-book-39;
}

.nc-book-bookmark::before {
  content: @nc-book-bookmark;
}

.nc-book-bookmark-2::before {
  content: @nc-book-bookmark-2;
}

.nc-book-open::before {
  content: @nc-book-open;
}

.nc-book-open-2::before {
  content: @nc-book-open-2;
}

.nc-bookmark::before {
  content: @nc-bookmark;
}

.nc-bookmark-add::before {
  content: @nc-bookmark-add;
}

.nc-bookmark-add-2::before {
  content: @nc-bookmark-add-2;
}

.nc-bookmark-delete::before {
  content: @nc-bookmark-delete;
}

.nc-bookmark-delete-2::before {
  content: @nc-bookmark-delete-2;
}

.nc-bookmarks::before {
  content: @nc-bookmarks;
}

.nc-books::before {
  content: @nc-books;
}

.nc-books-46::before {
  content: @nc-books-46;
}

.nc-boot::before {
  content: @nc-boot;
}

.nc-boot-2::before {
  content: @nc-boot-2;
}

.nc-boot-woman::before {
  content: @nc-boot-woman;
}

.nc-boots::before {
  content: @nc-boots;
}

.nc-border::before {
  content: @nc-border;
}

.nc-border-collie::before {
  content: @nc-border-collie;
}

.nc-border-radius::before {
  content: @nc-border-radius;
}

.nc-border-width::before {
  content: @nc-border-width;
}

.nc-bored::before {
  content: @nc-bored;
}

.nc-botany::before {
  content: @nc-botany;
}

.nc-bottle::before {
  content: @nc-bottle;
}

.nc-bottle-wine::before {
  content: @nc-bottle-wine;
}

.nc-bouquet::before {
  content: @nc-bouquet;
}

.nc-bow::before {
  content: @nc-bow;
}

.nc-bowl::before {
  content: @nc-bowl;
}

.nc-bowling-ball::before {
  content: @nc-bowling-ball;
}

.nc-bowling-pins::before {
  content: @nc-bowling-pins;
}

.nc-box::before {
  content: @nc-box;
}

.nc-box-2::before {
  content: @nc-box-2;
}

.nc-box-3d-50::before {
  content: @nc-box-3d-50;
}

.nc-box-arrow-bottom-left::before {
  content: @nc-box-arrow-bottom-left;
}

.nc-box-arrow-bottom-right::before {
  content: @nc-box-arrow-bottom-right;
}

.nc-box-arrow-down::before {
  content: @nc-box-arrow-down;
}

.nc-box-arrow-left::before {
  content: @nc-box-arrow-left;
}

.nc-box-arrow-right::before {
  content: @nc-box-arrow-right;
}

.nc-box-arrow-top-left::before {
  content: @nc-box-arrow-top-left;
}

.nc-box-arrow-top-right::before {
  content: @nc-box-arrow-top-right;
}

.nc-box-arrow-up::before {
  content: @nc-box-arrow-up;
}

.nc-box-caret-down::before {
  content: @nc-box-caret-down;
}

.nc-box-caret-left::before {
  content: @nc-box-caret-left;
}

.nc-box-caret-right::before {
  content: @nc-box-caret-right;
}

.nc-box-caret-up::before {
  content: @nc-box-caret-up;
}

.nc-box-ctrl-down::before {
  content: @nc-box-ctrl-down;
}

.nc-box-ctrl-left::before {
  content: @nc-box-ctrl-left;
}

.nc-box-ctrl-right::before {
  content: @nc-box-ctrl-right;
}

.nc-box-ctrl-up::before {
  content: @nc-box-ctrl-up;
}

.nc-box-ribbon::before {
  content: @nc-box-ribbon;
}

.nc-boxing::before {
  content: @nc-boxing;
}

.nc-boxing-bag::before {
  content: @nc-boxing-bag;
}

.nc-boxing-glove::before {
  content: @nc-boxing-glove;
}

.nc-bra::before {
  content: @nc-bra;
}

.nc-braille::before {
  content: @nc-braille;
}

.nc-brain::before {
  content: @nc-brain;
}

.nc-brakes::before {
  content: @nc-brakes;
}

.nc-brandenburg-gate::before {
  content: @nc-brandenburg-gate;
}

.nc-bread::before {
  content: @nc-bread;
}

.nc-bride::before {
  content: @nc-bride;
}

.nc-briefcase-24::before {
  content: @nc-briefcase-24;
}

.nc-briefcase-25::before {
  content: @nc-briefcase-25;
}

.nc-briefcase-26::before {
  content: @nc-briefcase-26;
}

.nc-brightness::before {
  content: @nc-brightness;
}

.nc-brioche::before {
  content: @nc-brioche;
}

.nc-broccoli::before {
  content: @nc-broccoli;
}

.nc-broken-heart::before {
  content: @nc-broken-heart;
}

.nc-broom::before {
  content: @nc-broom;
}

.nc-browse::before {
  content: @nc-browse;
}

.nc-browser-chrome::before {
  content: @nc-browser-chrome;
}

.nc-browser-edge::before {
  content: @nc-browser-edge;
}

.nc-browser-edge-legacy::before {
  content: @nc-browser-edge-legacy;
}

.nc-browser-firefox::before {
  content: @nc-browser-firefox;
}

.nc-browser-ie::before {
  content: @nc-browser-ie;
}

.nc-browser-opera::before {
  content: @nc-browser-opera;
}

.nc-browser-safari::before {
  content: @nc-browser-safari;
}

.nc-brush::before {
  content: @nc-brush;
}

.nc-btn-play::before {
  content: @nc-btn-play;
}

.nc-btn-play-2::before {
  content: @nc-btn-play-2;
}

.nc-btn-stop::before {
  content: @nc-btn-stop;
}

.nc-bucket::before {
  content: @nc-bucket;
}

.nc-bucket-2::before {
  content: @nc-bucket-2;
}

.nc-buddhism::before {
  content: @nc-buddhism;
}

.nc-buddhist-temple::before {
  content: @nc-buddhist-temple;
}

.nc-bug::before {
  content: @nc-bug;
}

.nc-bulb::before {
  content: @nc-bulb;
}

.nc-bulb-61::before {
  content: @nc-bulb-61;
}

.nc-bulb-62::before {
  content: @nc-bulb-62;
}

.nc-bulb-63::before {
  content: @nc-bulb-63;
}

.nc-bulb-saver::before {
  content: @nc-bulb-saver;
}

.nc-bullet-list::before {
  content: @nc-bullet-list;
}

.nc-bullet-list-67::before {
  content: @nc-bullet-list-67;
}

.nc-bullet-list-68::before {
  content: @nc-bullet-list-68;
}

.nc-bullet-list-69::before {
  content: @nc-bullet-list-69;
}

.nc-bullet-list-70::before {
  content: @nc-bullet-list-70;
}

.nc-bullets::before {
  content: @nc-bullets;
}

.nc-bureau-dresser::before {
  content: @nc-bureau-dresser;
}

.nc-burj-al-arab::before {
  content: @nc-burj-al-arab;
}

.nc-bus::before {
  content: @nc-bus;
}

.nc-bus-front-10::before {
  content: @nc-bus-front-10;
}

.nc-bus-front-12::before {
  content: @nc-bus-front-12;
}

.nc-business-agent::before {
  content: @nc-business-agent;
}

.nc-business-contact-85::before {
  content: @nc-business-contact-85;
}

.nc-business-contact-86::before {
  content: @nc-business-contact-86;
}

.nc-business-contact-87::before {
  content: @nc-business-contact-87;
}

.nc-business-contact-88::before {
  content: @nc-business-contact-88;
}

.nc-businessman-03::before {
  content: @nc-businessman-03;
}

.nc-businessman-04::before {
  content: @nc-businessman-04;
}

.nc-butter::before {
  content: @nc-butter;
}

.nc-butterfly::before {
  content: @nc-butterfly;
}

.nc-button::before {
  content: @nc-button;
}

.nc-button-2::before {
  content: @nc-button-2;
}

.nc-button-eject::before {
  content: @nc-button-eject;
}

.nc-button-next::before {
  content: @nc-button-next;
}

.nc-button-pause::before {
  content: @nc-button-pause;
}

.nc-button-play::before {
  content: @nc-button-play;
}

.nc-button-power::before {
  content: @nc-button-power;
}

.nc-button-previous::before {
  content: @nc-button-previous;
}

.nc-button-record::before {
  content: @nc-button-record;
}

.nc-button-rewind::before {
  content: @nc-button-rewind;
}

.nc-button-skip::before {
  content: @nc-button-skip;
}

.nc-button-stop::before {
  content: @nc-button-stop;
}

.nc-buzz::before {
  content: @nc-buzz;
}

.nc-c-add::before {
  content: @nc-c-add;
}

.nc-c-check::before {
  content: @nc-c-check;
}

.nc-c-delete::before {
  content: @nc-c-delete;
}

.nc-c-edit::before {
  content: @nc-c-edit;
}

.nc-c-info::before {
  content: @nc-c-info;
}

.nc-c-pulse::before {
  content: @nc-c-pulse;
}

.nc-c-question::before {
  content: @nc-c-question;
}

.nc-c-remove::before {
  content: @nc-c-remove;
}

.nc-c-warning::before {
  content: @nc-c-warning;
}

.nc-cabinet::before {
  content: @nc-cabinet;
}

.nc-cable::before {
  content: @nc-cable;
}

.nc-cactus::before {
  content: @nc-cactus;
}

.nc-cake::before {
  content: @nc-cake;
}

.nc-cake-13::before {
  content: @nc-cake-13;
}

.nc-cake-2::before {
  content: @nc-cake-2;
}

.nc-cake-slice::before {
  content: @nc-cake-slice;
}

.nc-calculator::before {
  content: @nc-calculator;
}

.nc-calendar::before {
  content: @nc-calendar;
}

.nc-calendar-2::before {
  content: @nc-calendar-2;
}

.nc-calendar-date::before {
  content: @nc-calendar-date;
}

.nc-calendar-date-2::before {
  content: @nc-calendar-date-2;
}

.nc-calendar-day-view::before {
  content: @nc-calendar-day-view;
}

.nc-calendar-event::before {
  content: @nc-calendar-event;
}

.nc-calendar-event-2::before {
  content: @nc-calendar-event-2;
}

.nc-calendar-event-create::before {
  content: @nc-calendar-event-create;
}

.nc-call-doctor::before {
  content: @nc-call-doctor;
}

.nc-camcorder::before {
  content: @nc-camcorder;
}

.nc-camera::before {
  content: @nc-camera;
}

.nc-camera-2::before {
  content: @nc-camera-2;
}

.nc-camera-3::before {
  content: @nc-camera-3;
}

.nc-camera-button::before {
  content: @nc-camera-button;
}

.nc-camera-flash::before {
  content: @nc-camera-flash;
}

.nc-camera-flashlight::before {
  content: @nc-camera-flashlight;
}

.nc-camera-focus::before {
  content: @nc-camera-focus;
}

.nc-camera-focus-2::before {
  content: @nc-camera-focus-2;
}

.nc-camera-lens::before {
  content: @nc-camera-lens;
}

.nc-camera-roll::before {
  content: @nc-camera-roll;
}

.nc-camera-screen::before {
  content: @nc-camera-screen;
}

.nc-camera-shooting::before {
  content: @nc-camera-shooting;
}

.nc-camera-timer::before {
  content: @nc-camera-timer;
}

.nc-camper::before {
  content: @nc-camper;
}

.nc-camping::before {
  content: @nc-camping;
}

.nc-camping-lantern::before {
  content: @nc-camping-lantern;
}

.nc-can::before {
  content: @nc-can;
}

.nc-candle::before {
  content: @nc-candle;
}

.nc-candlestick-chart::before {
  content: @nc-candlestick-chart;
}

.nc-candy::before {
  content: @nc-candy;
}

.nc-candy-2::before {
  content: @nc-candy-2;
}

.nc-canvas::before {
  content: @nc-canvas;
}

.nc-cap::before {
  content: @nc-cap;
}

.nc-car::before {
  content: @nc-car;
}

.nc-car-2::before {
  content: @nc-car-2;
}

.nc-car-accident::before {
  content: @nc-car-accident;
}

.nc-car-connect::before {
  content: @nc-car-connect;
}

.nc-car-door::before {
  content: @nc-car-door;
}

.nc-car-front::before {
  content: @nc-car-front;
}

.nc-car-lights::before {
  content: @nc-car-lights;
}

.nc-car-parking::before {
  content: @nc-car-parking;
}

.nc-car-simple::before {
  content: @nc-car-simple;
}

.nc-car-sport::before {
  content: @nc-car-sport;
}

.nc-car-ventilation::before {
  content: @nc-car-ventilation;
}

.nc-car-wash::before {
  content: @nc-car-wash;
}

.nc-card-edit::before {
  content: @nc-card-edit;
}

.nc-card-favorite::before {
  content: @nc-card-favorite;
}

.nc-card-remove::before {
  content: @nc-card-remove;
}

.nc-card-update::before {
  content: @nc-card-update;
}

.nc-cards::before {
  content: @nc-cards;
}

.nc-caret-sm-up::before {
  content: @nc-caret-sm-up;
}

.nc-carrot::before {
  content: @nc-carrot;
}

.nc-cart::before {
  content: @nc-cart;
}

.nc-cart-add::before {
  content: @nc-cart-add;
}

.nc-cart-add-9::before {
  content: @nc-cart-add-9;
}

.nc-cart-favorite::before {
  content: @nc-cart-favorite;
}

.nc-cart-full::before {
  content: @nc-cart-full;
}

.nc-cart-refresh::before {
  content: @nc-cart-refresh;
}

.nc-cart-remove::before {
  content: @nc-cart-remove;
}

.nc-cart-remove-9::before {
  content: @nc-cart-remove-9;
}

.nc-cart-return::before {
  content: @nc-cart-return;
}

.nc-cart-simple-add::before {
  content: @nc-cart-simple-add;
}

.nc-cart-simple-remove::before {
  content: @nc-cart-simple-remove;
}

.nc-cart-speed::before {
  content: @nc-cart-speed;
}

.nc-cash-register::before {
  content: @nc-cash-register;
}

.nc-casino::before {
  content: @nc-casino;
}

.nc-casino-chip::before {
  content: @nc-casino-chip;
}

.nc-castle::before {
  content: @nc-castle;
}

.nc-cat::before {
  content: @nc-cat;
}

.nc-catalog::before {
  content: @nc-catalog;
}

.nc-cauldron::before {
  content: @nc-cauldron;
}

.nc-cctv::before {
  content: @nc-cctv;
}

.nc-cd-reader::before {
  content: @nc-cd-reader;
}

.nc-celsius::before {
  content: @nc-celsius;
}

.nc-centralize::before {
  content: @nc-centralize;
}

.nc-certificate::before {
  content: @nc-certificate;
}

.nc-chain::before {
  content: @nc-chain;
}

.nc-chair::before {
  content: @nc-chair;
}

.nc-chalkboard::before {
  content: @nc-chalkboard;
}

.nc-champagne::before {
  content: @nc-champagne;
}

.nc-chandelier::before {
  content: @nc-chandelier;
}

.nc-change-direction::before {
  content: @nc-change-direction;
}

.nc-charger-cable::before {
  content: @nc-charger-cable;
}

.nc-chart::before {
  content: @nc-chart;
}

.nc-chart-bar-32::before {
  content: @nc-chart-bar-32;
}

.nc-chart-bar-33::before {
  content: @nc-chart-bar-33;
}

.nc-chart-growth::before {
  content: @nc-chart-growth;
}

.nc-chart-pie-35::before {
  content: @nc-chart-pie-35;
}

.nc-chart-pie-36::before {
  content: @nc-chart-pie-36;
}

.nc-chat::before {
  content: @nc-chat;
}

.nc-chat-bot::before {
  content: @nc-chat-bot;
}

.nc-check::before {
  content: @nc-check;
}

.nc-check-all::before {
  content: @nc-check-all;
}

.nc-check-double::before {
  content: @nc-check-double;
}

.nc-check-in::before {
  content: @nc-check-in;
}

.nc-check-list::before {
  content: @nc-check-list;
}

.nc-check-out::before {
  content: @nc-check-out;
}

.nc-check-single::before {
  content: @nc-check-single;
}

.nc-checkbox-btn::before {
  content: @nc-checkbox-btn;
}

.nc-checkbox-btn-checked::before {
  content: @nc-checkbox-btn-checked;
}

.nc-cheese::before {
  content: @nc-cheese;
}

.nc-cheese-87::before {
  content: @nc-cheese-87;
}

.nc-cheeseburger::before {
  content: @nc-cheeseburger;
}

.nc-chef::before {
  content: @nc-chef;
}

.nc-chef-hat::before {
  content: @nc-chef-hat;
}

.nc-chemistry::before {
  content: @nc-chemistry;
}

.nc-cheque::before {
  content: @nc-cheque;
}

.nc-cheque-2::before {
  content: @nc-cheque-2;
}

.nc-cheque-3::before {
  content: @nc-cheque-3;
}

.nc-chequered-flag::before {
  content: @nc-chequered-flag;
}

.nc-cherry::before {
  content: @nc-cherry;
}

.nc-chess-bishop::before {
  content: @nc-chess-bishop;
}

.nc-chess-king::before {
  content: @nc-chess-king;
}

.nc-chess-knight::before {
  content: @nc-chess-knight;
}

.nc-chess-pawn::before {
  content: @nc-chess-pawn;
}

.nc-chess-queen::before {
  content: @nc-chess-queen;
}

.nc-chess-tower::before {
  content: @nc-chess-tower;
}

.nc-chicken::before {
  content: @nc-chicken;
}

.nc-chicken-2::before {
  content: @nc-chicken-2;
}

.nc-chicken-front::before {
  content: @nc-chicken-front;
}

.nc-child::before {
  content: @nc-child;
}

.nc-chili::before {
  content: @nc-chili;
}

.nc-chimney::before {
  content: @nc-chimney;
}

.nc-chips::before {
  content: @nc-chips;
}

.nc-choco-cream::before {
  content: @nc-choco-cream;
}

.nc-chocolate::before {
  content: @nc-chocolate;
}

.nc-chocolate-mousse::before {
  content: @nc-chocolate-mousse;
}

.nc-christianity::before {
  content: @nc-christianity;
}

.nc-church::before {
  content: @nc-church;
}

.nc-churros::before {
  content: @nc-churros;
}

.nc-cinema::before {
  content: @nc-cinema;
}

.nc-circle::before {
  content: @nc-circle;
}

.nc-circle-anim::before {
  content: @nc-circle-anim;
}

.nc-circle-anim-2::before {
  content: @nc-circle-anim-2;
}

.nc-circle-anim-3::before {
  content: @nc-circle-anim-3;
}

.nc-circle-arrow-down::before {
  content: @nc-circle-arrow-down;
}

.nc-circle-arrow-left::before {
  content: @nc-circle-arrow-left;
}

.nc-circle-arrow-right::before {
  content: @nc-circle-arrow-right;
}

.nc-circle-arrow-up::before {
  content: @nc-circle-arrow-up;
}

.nc-circle-caret-down::before {
  content: @nc-circle-caret-down;
}

.nc-circle-caret-left::before {
  content: @nc-circle-caret-left;
}

.nc-circle-caret-right::before {
  content: @nc-circle-caret-right;
}

.nc-circle-caret-up::before {
  content: @nc-circle-caret-up;
}

.nc-circle-ctrl-down::before {
  content: @nc-circle-ctrl-down;
}

.nc-circle-ctrl-left::before {
  content: @nc-circle-ctrl-left;
}

.nc-circle-ctrl-right::before {
  content: @nc-circle-ctrl-right;
}

.nc-circle-ctrl-up::before {
  content: @nc-circle-ctrl-up;
}

.nc-circle-in::before {
  content: @nc-circle-in;
}

.nc-circle-out::before {
  content: @nc-circle-out;
}

.nc-circuit::before {
  content: @nc-circuit;
}

.nc-circuit-round::before {
  content: @nc-circuit-round;
}

.nc-clapperboard::before {
  content: @nc-clapperboard;
}

.nc-clapperboard-2::before {
  content: @nc-clapperboard-2;
}

.nc-clarinet::before {
  content: @nc-clarinet;
}

.nc-clear-data::before {
  content: @nc-clear-data;
}

.nc-climbing::before {
  content: @nc-climbing;
}

.nc-clock::before {
  content: @nc-clock;
}

.nc-clock-anim::before {
  content: @nc-clock-anim;
}

.nc-clone::before {
  content: @nc-clone;
}

.nc-closed-captioning::before {
  content: @nc-closed-captioning;
}

.nc-clothes-hanger::before {
  content: @nc-clothes-hanger;
}

.nc-clothing-hanger::before {
  content: @nc-clothing-hanger;
}

.nc-cloud::before {
  content: @nc-cloud;
}

.nc-cloud-data-download::before {
  content: @nc-cloud-data-download;
}

.nc-cloud-download::before {
  content: @nc-cloud-download;
}

.nc-cloud-drop::before {
  content: @nc-cloud-drop;
}

.nc-cloud-fog-31::before {
  content: @nc-cloud-fog-31;
}

.nc-cloud-fog-32::before {
  content: @nc-cloud-fog-32;
}

.nc-cloud-forecast::before {
  content: @nc-cloud-forecast;
}

.nc-cloud-hail::before {
  content: @nc-cloud-hail;
}

.nc-cloud-light::before {
  content: @nc-cloud-light;
}

.nc-cloud-mining::before {
  content: @nc-cloud-mining;
}

.nc-cloud-moon::before {
  content: @nc-cloud-moon;
}

.nc-cloud-rain::before {
  content: @nc-cloud-rain;
}

.nc-cloud-rainbow::before {
  content: @nc-cloud-rainbow;
}

.nc-cloud-snow-34::before {
  content: @nc-cloud-snow-34;
}

.nc-cloud-snow-42::before {
  content: @nc-cloud-snow-42;
}

.nc-cloud-sun-17::before {
  content: @nc-cloud-sun-17;
}

.nc-cloud-sun-19::before {
  content: @nc-cloud-sun-19;
}

.nc-cloud-upload::before {
  content: @nc-cloud-upload;
}

.nc-clover::before {
  content: @nc-clover;
}

.nc-clubs-suit::before {
  content: @nc-clubs-suit;
}

.nc-cn-tower::before {
  content: @nc-cn-tower;
}

.nc-coat::before {
  content: @nc-coat;
}

.nc-coat-hanger::before {
  content: @nc-coat-hanger;
}

.nc-cockade::before {
  content: @nc-cockade;
}

.nc-cocktail::before {
  content: @nc-cocktail;
}

.nc-code::before {
  content: @nc-code;
}

.nc-code-editor::before {
  content: @nc-code-editor;
}

.nc-coffe-long::before {
  content: @nc-coffe-long;
}

.nc-coffee::before {
  content: @nc-coffee;
}

.nc-coffee-bean::before {
  content: @nc-coffee-bean;
}

.nc-coffee-long::before {
  content: @nc-coffee-long;
}

.nc-coffee-maker::before {
  content: @nc-coffee-maker;
}

.nc-coffin::before {
  content: @nc-coffin;
}

.nc-cogwheel::before {
  content: @nc-cogwheel;
}

.nc-coins::before {
  content: @nc-coins;
}

.nc-collar::before {
  content: @nc-collar;
}

.nc-collection::before {
  content: @nc-collection;
}

.nc-colosseum::before {
  content: @nc-colosseum;
}

.nc-comb::before {
  content: @nc-comb;
}

.nc-command::before {
  content: @nc-command;
}

.nc-comment::before {
  content: @nc-comment;
}

.nc-comment-add::before {
  content: @nc-comment-add;
}

.nc-comments::before {
  content: @nc-comments;
}

.nc-compact-camera::before {
  content: @nc-compact-camera;
}

.nc-compare::before {
  content: @nc-compare;
}

.nc-compare-items::before {
  content: @nc-compare-items;
}

.nc-compass::before {
  content: @nc-compass;
}

.nc-compass-04::before {
  content: @nc-compass-04;
}

.nc-compass-05::before {
  content: @nc-compass-05;
}

.nc-compass-06::before {
  content: @nc-compass-06;
}

.nc-compass-2::before {
  content: @nc-compass-2;
}

.nc-compass-3::before {
  content: @nc-compass-3;
}

.nc-components::before {
  content: @nc-components;
}

.nc-compressed-file::before {
  content: @nc-compressed-file;
}

.nc-computer::before {
  content: @nc-computer;
}

.nc-computer-monitor::before {
  content: @nc-computer-monitor;
}

.nc-computer-upload::before {
  content: @nc-computer-upload;
}

.nc-concierge::before {
  content: @nc-concierge;
}

.nc-condom::before {
  content: @nc-condom;
}

.nc-cone::before {
  content: @nc-cone;
}

.nc-conference-room::before {
  content: @nc-conference-room;
}

.nc-configuration-tools::before {
  content: @nc-configuration-tools;
}

.nc-connect::before {
  content: @nc-connect;
}

.nc-connection::before {
  content: @nc-connection;
}

.nc-construction-sign::before {
  content: @nc-construction-sign;
}

.nc-contact::before {
  content: @nc-contact;
}

.nc-contact-list::before {
  content: @nc-contact-list;
}

.nc-contactless::before {
  content: @nc-contactless;
}

.nc-contactless-card::before {
  content: @nc-contactless-card;
}

.nc-contacts::before {
  content: @nc-contacts;
}

.nc-content-360deg::before {
  content: @nc-content-360deg;
}

.nc-content-delivery::before {
  content: @nc-content-delivery;
}

.nc-contrast::before {
  content: @nc-contrast;
}

.nc-contrast-2::before {
  content: @nc-contrast-2;
}

.nc-control-panel::before {
  content: @nc-control-panel;
}

.nc-controller::before {
  content: @nc-controller;
}

.nc-controller-2::before {
  content: @nc-controller-2;
}

.nc-conversion::before {
  content: @nc-conversion;
}

.nc-cookies::before {
  content: @nc-cookies;
}

.nc-copy::before {
  content: @nc-copy;
}

.nc-copy-2::before {
  content: @nc-copy-2;
}

.nc-copyright::before {
  content: @nc-copyright;
}

.nc-corn::before {
  content: @nc-corn;
}

.nc-corner-bottom-left::before {
  content: @nc-corner-bottom-left;
}

.nc-corner-bottom-right::before {
  content: @nc-corner-bottom-right;
}

.nc-corner-down-round::before {
  content: @nc-corner-down-round;
}

.nc-corner-left-down::before {
  content: @nc-corner-left-down;
}

.nc-corner-left-round::before {
  content: @nc-corner-left-round;
}

.nc-corner-right-down::before {
  content: @nc-corner-right-down;
}

.nc-corner-right-round::before {
  content: @nc-corner-right-round;
}

.nc-corner-top-left::before {
  content: @nc-corner-top-left;
}

.nc-corner-top-right::before {
  content: @nc-corner-top-right;
}

.nc-corner-up-left::before {
  content: @nc-corner-up-left;
}

.nc-corner-up-right::before {
  content: @nc-corner-up-right;
}

.nc-corner-up-round::before {
  content: @nc-corner-up-round;
}

.nc-cornucopia::before {
  content: @nc-cornucopia;
}

.nc-corset::before {
  content: @nc-corset;
}

.nc-coughing::before {
  content: @nc-coughing;
}

.nc-countdown::before {
  content: @nc-countdown;
}

.nc-countdown-2::before {
  content: @nc-countdown-2;
}

.nc-couple-gay::before {
  content: @nc-couple-gay;
}

.nc-couple-lesbian::before {
  content: @nc-couple-lesbian;
}

.nc-coupon::before {
  content: @nc-coupon;
}

.nc-cow::before {
  content: @nc-cow;
}

.nc-cpu::before {
  content: @nc-cpu;
}

.nc-crab::before {
  content: @nc-crab;
}

.nc-cradle::before {
  content: @nc-cradle;
}

.nc-crane::before {
  content: @nc-crane;
}

.nc-creative-commons::before {
  content: @nc-creative-commons;
}

.nc-credit-card::before {
  content: @nc-credit-card;
}

.nc-credit-card-in::before {
  content: @nc-credit-card-in;
}

.nc-credit-locked::before {
  content: @nc-credit-locked;
}

.nc-crepe::before {
  content: @nc-crepe;
}

.nc-cricket-bat::before {
  content: @nc-cricket-bat;
}

.nc-croissant::before {
  content: @nc-croissant;
}

.nc-crop::before {
  content: @nc-crop;
}

.nc-cross::before {
  content: @nc-cross;
}

.nc-cross-down::before {
  content: @nc-cross-down;
}

.nc-cross-horizontal::before {
  content: @nc-cross-horizontal;
}

.nc-cross-left::before {
  content: @nc-cross-left;
}

.nc-cross-right::before {
  content: @nc-cross-right;
}

.nc-cross-up::before {
  content: @nc-cross-up;
}

.nc-cross-vertical::before {
  content: @nc-cross-vertical;
}

.nc-crosshair::before {
  content: @nc-crosshair;
}

.nc-crossing-directions::before {
  content: @nc-crossing-directions;
}

.nc-crossroad::before {
  content: @nc-crossroad;
}

.nc-croupier::before {
  content: @nc-croupier;
}

.nc-crown::before {
  content: @nc-crown;
}

.nc-crumpet::before {
  content: @nc-crumpet;
}

.nc-crunches::before {
  content: @nc-crunches;
}

.nc-cry-15::before {
  content: @nc-cry-15;
}

.nc-cry-57::before {
  content: @nc-cry-57;
}

.nc-crying-baby::before {
  content: @nc-crying-baby;
}

.nc-crypto-wallet::before {
  content: @nc-crypto-wallet;
}

.nc-cryptography::before {
  content: @nc-cryptography;
}

.nc-css3::before {
  content: @nc-css3;
}

.nc-ctrl-backward::before {
  content: @nc-ctrl-backward;
}

.nc-ctrl-down::before {
  content: @nc-ctrl-down;
}

.nc-ctrl-forward::before {
  content: @nc-ctrl-forward;
}

.nc-ctrl-left::before {
  content: @nc-ctrl-left;
}

.nc-ctrl-right::before {
  content: @nc-ctrl-right;
}

.nc-ctrl-up::before {
  content: @nc-ctrl-up;
}

.nc-cupcake::before {
  content: @nc-cupcake;
}

.nc-cure::before {
  content: @nc-cure;
}

.nc-curling::before {
  content: @nc-curling;
}

.nc-curling-stone::before {
  content: @nc-curling-stone;
}

.nc-currency-dollar::before {
  content: @nc-currency-dollar;
}

.nc-currency-euro::before {
  content: @nc-currency-euro;
}

.nc-currency-exchange::before {
  content: @nc-currency-exchange;
}

.nc-currency-exchange-2::before {
  content: @nc-currency-exchange-2;
}

.nc-currency-pound::before {
  content: @nc-currency-pound;
}

.nc-currency-yen::before {
  content: @nc-currency-yen;
}

.nc-cursor-48::before {
  content: @nc-cursor-48;
}

.nc-cursor-49::before {
  content: @nc-cursor-49;
}

.nc-cursor-add::before {
  content: @nc-cursor-add;
}

.nc-cursor-grab::before {
  content: @nc-cursor-grab;
}

.nc-cursor-load::before {
  content: @nc-cursor-load;
}

.nc-cursor-menu::before {
  content: @nc-cursor-menu;
}

.nc-cursor-not-allowed::before {
  content: @nc-cursor-not-allowed;
}

.nc-cursor-pointer::before {
  content: @nc-cursor-pointer;
}

.nc-cursor-text::before {
  content: @nc-cursor-text;
}

.nc-curtains::before {
  content: @nc-curtains;
}

.nc-curved-arrow-down::before {
  content: @nc-curved-arrow-down;
}

.nc-curved-arrow-left::before {
  content: @nc-curved-arrow-left;
}

.nc-curved-arrow-right::before {
  content: @nc-curved-arrow-right;
}

.nc-curved-circuit::before {
  content: @nc-curved-circuit;
}

.nc-customer-support::before {
  content: @nc-customer-support;
}

.nc-cut::before {
  content: @nc-cut;
}

.nc-cute::before {
  content: @nc-cute;
}

.nc-cutlery::before {
  content: @nc-cutlery;
}

.nc-cutlery-75::before {
  content: @nc-cutlery-75;
}

.nc-cutlery-76::before {
  content: @nc-cutlery-76;
}

.nc-cutlery-77::before {
  content: @nc-cutlery-77;
}

.nc-cyborg::before {
  content: @nc-cyborg;
}

.nc-cycle::before {
  content: @nc-cycle;
}

.nc-cycling::before {
  content: @nc-cycling;
}

.nc-cycling-track::before {
  content: @nc-cycling-track;
}

.nc-d-add::before {
  content: @nc-d-add;
}

.nc-d-chart::before {
  content: @nc-d-chart;
}

.nc-d-check::before {
  content: @nc-d-check;
}

.nc-d-delete::before {
  content: @nc-d-delete;
}

.nc-d-edit::before {
  content: @nc-d-edit;
}

.nc-d-remove::before {
  content: @nc-d-remove;
}

.nc-dancer::before {
  content: @nc-dancer;
}

.nc-dart::before {
  content: @nc-dart;
}

.nc-dashboard::before {
  content: @nc-dashboard;
}

.nc-data-download::before {
  content: @nc-data-download;
}

.nc-data-settings::before {
  content: @nc-data-settings;
}

.nc-data-table::before {
  content: @nc-data-table;
}

.nc-data-upload::before {
  content: @nc-data-upload;
}

.nc-database::before {
  content: @nc-database;
}

.nc-dead-hand::before {
  content: @nc-dead-hand;
}

.nc-deadlift::before {
  content: @nc-deadlift;
}

.nc-deaf::before {
  content: @nc-deaf;
}

.nc-debt::before {
  content: @nc-debt;
}

.nc-decentralize::before {
  content: @nc-decentralize;
}

.nc-decision-process::before {
  content: @nc-decision-process;
}

.nc-decoration::before {
  content: @nc-decoration;
}

.nc-decrease-font-size::before {
  content: @nc-decrease-font-size;
}

.nc-decrease-indent::before {
  content: @nc-decrease-indent;
}

.nc-deer::before {
  content: @nc-deer;
}

.nc-delete::before {
  content: @nc-delete;
}

.nc-delete-forever::before {
  content: @nc-delete-forever;
}

.nc-delete-key::before {
  content: @nc-delete-key;
}

.nc-delete-x::before {
  content: @nc-delete-x;
}

.nc-delivery::before {
  content: @nc-delivery;
}

.nc-delivery-2::before {
  content: @nc-delivery-2;
}

.nc-delivery-3::before {
  content: @nc-delivery-3;
}

.nc-delivery-fast::before {
  content: @nc-delivery-fast;
}

.nc-delivery-time::before {
  content: @nc-delivery-time;
}

.nc-delivery-track::before {
  content: @nc-delivery-track;
}

.nc-depth::before {
  content: @nc-depth;
}

.nc-design::before {
  content: @nc-design;
}

.nc-design-system::before {
  content: @nc-design-system;
}

.nc-desk::before {
  content: @nc-desk;
}

.nc-desk-drawer::before {
  content: @nc-desk-drawer;
}

.nc-desk-lamp::before {
  content: @nc-desk-lamp;
}

.nc-detached-property::before {
  content: @nc-detached-property;
}

.nc-detox::before {
  content: @nc-detox;
}

.nc-device-connection::before {
  content: @nc-device-connection;
}

.nc-devil::before {
  content: @nc-devil;
}

.nc-devto::before {
  content: @nc-devto;
}

.nc-diamond::before {
  content: @nc-diamond;
}

.nc-diamonds-suits::before {
  content: @nc-diamonds-suits;
}

.nc-diaper::before {
  content: @nc-diaper;
}

.nc-diaper-changing-area::before {
  content: @nc-diaper-changing-area;
}

.nc-dice::before {
  content: @nc-dice;
}

.nc-dice-2::before {
  content: @nc-dice-2;
}

.nc-diet::before {
  content: @nc-diet;
}

.nc-diet-food::before {
  content: @nc-diet-food;
}

.nc-diet-plan::before {
  content: @nc-diet-plan;
}

.nc-digital-image::before {
  content: @nc-digital-image;
}

.nc-digital-key::before {
  content: @nc-digital-key;
}

.nc-digital-piano::before {
  content: @nc-digital-piano;
}

.nc-direction::before {
  content: @nc-direction;
}

.nc-direction-down::before {
  content: @nc-direction-down;
}

.nc-direction-left::before {
  content: @nc-direction-left;
}

.nc-direction-right::before {
  content: @nc-direction-right;
}

.nc-direction-up::before {
  content: @nc-direction-up;
}

.nc-directions::before {
  content: @nc-directions;
}

.nc-discord::before {
  content: @nc-discord;
}

.nc-discount-2::before {
  content: @nc-discount-2;
}

.nc-disgusted::before {
  content: @nc-disgusted;
}

.nc-dish::before {
  content: @nc-dish;
}

.nc-dishwasher::before {
  content: @nc-dishwasher;
}

.nc-disinfectant::before {
  content: @nc-disinfectant;
}

.nc-disk::before {
  content: @nc-disk;
}

.nc-disk-reader::before {
  content: @nc-disk-reader;
}

.nc-disperse::before {
  content: @nc-disperse;
}

.nc-distance::before {
  content: @nc-distance;
}

.nc-distribute-horizontal::before {
  content: @nc-distribute-horizontal;
}

.nc-distribute-vertical::before {
  content: @nc-distribute-vertical;
}

.nc-divider::before {
  content: @nc-divider;
}

.nc-dizzy-face::before {
  content: @nc-dizzy-face;
}

.nc-dna-27::before {
  content: @nc-dna-27;
}

.nc-dna-38::before {
  content: @nc-dna-38;
}

.nc-doc-folder::before {
  content: @nc-doc-folder;
}

.nc-dock-bottom::before {
  content: @nc-dock-bottom;
}

.nc-dock-left::before {
  content: @nc-dock-left;
}

.nc-dock-right::before {
  content: @nc-dock-right;
}

.nc-dock-top::before {
  content: @nc-dock-top;
}

.nc-doctor::before {
  content: @nc-doctor;
}

.nc-document::before {
  content: @nc-document;
}

.nc-document-2::before {
  content: @nc-document-2;
}

.nc-document-copy::before {
  content: @nc-document-copy;
}

.nc-dog::before {
  content: @nc-dog;
}

.nc-dog-house::before {
  content: @nc-dog-house;
}

.nc-dog-leash::before {
  content: @nc-dog-leash;
}

.nc-dont-touch-eyes::before {
  content: @nc-dont-touch-eyes;
}

.nc-dont-touch-mouth::before {
  content: @nc-dont-touch-mouth;
}

.nc-donut::before {
  content: @nc-donut;
}

.nc-door::before {
  content: @nc-door;
}

.nc-door-2::before {
  content: @nc-door-2;
}

.nc-door-3::before {
  content: @nc-door-3;
}

.nc-door-handle::before {
  content: @nc-door-handle;
}

.nc-doorphone::before {
  content: @nc-doorphone;
}

.nc-dots::before {
  content: @nc-dots;
}

.nc-dots-anim::before {
  content: @nc-dots-anim;
}

.nc-dots-anim-2::before {
  content: @nc-dots-anim-2;
}

.nc-dots-anim-3::before {
  content: @nc-dots-anim-3;
}

.nc-dots-anim-4::before {
  content: @nc-dots-anim-4;
}

.nc-dots-anim-5::before {
  content: @nc-dots-anim-5;
}

.nc-dots-anim-6::before {
  content: @nc-dots-anim-6;
}

.nc-dots-anim-7::before {
  content: @nc-dots-anim-7;
}

.nc-double-arrow-left::before {
  content: @nc-double-arrow-left;
}

.nc-double-arrow-right::before {
  content: @nc-double-arrow-right;
}

.nc-double-bed::before {
  content: @nc-double-bed;
}

.nc-double-tap::before {
  content: @nc-double-tap;
}

.nc-down-arrow::before {
  content: @nc-down-arrow;
}

.nc-download::before {
  content: @nc-download;
}

.nc-download-data::before {
  content: @nc-download-data;
}

.nc-download-file::before {
  content: @nc-download-file;
}

.nc-drag::before {
  content: @nc-drag;
}

.nc-drag-21::before {
  content: @nc-drag-21;
}

.nc-drag-31::before {
  content: @nc-drag-31;
}

.nc-drag-down::before {
  content: @nc-drag-down;
}

.nc-drag-left::before {
  content: @nc-drag-left;
}

.nc-drag-right::before {
  content: @nc-drag-right;
}

.nc-drag-up::before {
  content: @nc-drag-up;
}

.nc-drawer::before {
  content: @nc-drawer;
}

.nc-drawer-2::before {
  content: @nc-drawer-2;
}

.nc-dress-man::before {
  content: @nc-dress-man;
}

.nc-dress-woman::before {
  content: @nc-dress-woman;
}

.nc-dresser::before {
  content: @nc-dresser;
}

.nc-dresser-2::before {
  content: @nc-dresser-2;
}

.nc-dresser-3::before {
  content: @nc-dresser-3;
}

.nc-drill::before {
  content: @nc-drill;
}

.nc-drink::before {
  content: @nc-drink;
}

.nc-drink-2::before {
  content: @nc-drink-2;
}

.nc-drink-list::before {
  content: @nc-drink-list;
}

.nc-drinking-bottle::before {
  content: @nc-drinking-bottle;
}

.nc-drone::before {
  content: @nc-drone;
}

.nc-drone-2::before {
  content: @nc-drone-2;
}

.nc-drop::before {
  content: @nc-drop;
}

.nc-drop-15::before {
  content: @nc-drop-15;
}

.nc-drop-cap::before {
  content: @nc-drop-cap;
}

.nc-drops::before {
  content: @nc-drops;
}

.nc-druidism::before {
  content: @nc-druidism;
}

.nc-drums::before {
  content: @nc-drums;
}

.nc-duck::before {
  content: @nc-duck;
}

.nc-dumbbell::before {
  content: @nc-dumbbell;
}

.nc-duplicate::before {
  content: @nc-duplicate;
}

.nc-e-add::before {
  content: @nc-e-add;
}

.nc-e-delete::before {
  content: @nc-e-delete;
}

.nc-e-reader::before {
  content: @nc-e-reader;
}

.nc-e-remove::before {
  content: @nc-e-remove;
}

.nc-earbuds::before {
  content: @nc-earbuds;
}

.nc-earth-science::before {
  content: @nc-earth-science;
}

.nc-eclipse::before {
  content: @nc-eclipse;
}

.nc-eco-home::before {
  content: @nc-eco-home;
}

.nc-ecology::before {
  content: @nc-ecology;
}

.nc-edge-razor::before {
  content: @nc-edge-razor;
}

.nc-edit::before {
  content: @nc-edit;
}

.nc-edit-color::before {
  content: @nc-edit-color;
}

.nc-edit-contrast::before {
  content: @nc-edit-contrast;
}

.nc-edit-curves::before {
  content: @nc-edit-curves;
}

.nc-edit-levels::before {
  content: @nc-edit-levels;
}

.nc-edit-note::before {
  content: @nc-edit-note;
}

.nc-edit-saturation::before {
  content: @nc-edit-saturation;
}

.nc-egg::before {
  content: @nc-egg;
}

.nc-egg-38::before {
  content: @nc-egg-38;
}

.nc-egg-39::before {
  content: @nc-egg-39;
}

.nc-eggs::before {
  content: @nc-eggs;
}

.nc-eight::before {
  content: @nc-eight;
}

.nc-eject::before {
  content: @nc-eject;
}

.nc-electronic-circuit::before {
  content: @nc-electronic-circuit;
}

.nc-elephant::before {
  content: @nc-elephant;
}

.nc-elliptical-cross-trainer::before {
  content: @nc-elliptical-cross-trainer;
}

.nc-email::before {
  content: @nc-email;
}

.nc-email-open::before {
  content: @nc-email-open;
}

.nc-embryo::before {
  content: @nc-embryo;
}

.nc-empty::before {
  content: @nc-empty;
}

.nc-energy::before {
  content: @nc-energy;
}

.nc-energy-drink::before {
  content: @nc-energy-drink;
}

.nc-energy-shaker::before {
  content: @nc-energy-shaker;
}

.nc-energy-supplement::before {
  content: @nc-energy-supplement;
}

.nc-engine::before {
  content: @nc-engine;
}

.nc-engine-start::before {
  content: @nc-engine-start;
}

.nc-enlarge::before {
  content: @nc-enlarge;
}

.nc-enlarge-diagonal::before {
  content: @nc-enlarge-diagonal;
}

.nc-enlarge-diagonal-2::before {
  content: @nc-enlarge-diagonal-2;
}

.nc-enlarge-h::before {
  content: @nc-enlarge-h;
}

.nc-enlarge-horizontal::before {
  content: @nc-enlarge-horizontal;
}

.nc-enlarge-vertical::before {
  content: @nc-enlarge-vertical;
}

.nc-enter::before {
  content: @nc-enter;
}

.nc-equation::before {
  content: @nc-equation;
}

.nc-equestrian-helmet::before {
  content: @nc-equestrian-helmet;
}

.nc-eraser-32::before {
  content: @nc-eraser-32;
}

.nc-eraser-33::before {
  content: @nc-eraser-33;
}

.nc-eraser-46::before {
  content: @nc-eraser-46;
}

.nc-escalator::before {
  content: @nc-escalator;
}

.nc-event-confirm::before {
  content: @nc-event-confirm;
}

.nc-event-create::before {
  content: @nc-event-create;
}

.nc-event-ticket::before {
  content: @nc-event-ticket;
}

.nc-exchange::before {
  content: @nc-exchange;
}

.nc-exclamation-mark::before {
  content: @nc-exclamation-mark;
}

.nc-exercise-bike::before {
  content: @nc-exercise-bike;
}

.nc-exhibition::before {
  content: @nc-exhibition;
}

.nc-exit-right::before {
  content: @nc-exit-right;
}

.nc-expand::before {
  content: @nc-expand;
}

.nc-expand-2::before {
  content: @nc-expand-2;
}

.nc-expand-h::before {
  content: @nc-expand-h;
}

.nc-expand-window::before {
  content: @nc-expand-window;
}

.nc-explore::before {
  content: @nc-explore;
}

.nc-explore-2::before {
  content: @nc-explore-2;
}

.nc-explore-user::before {
  content: @nc-explore-user;
}

.nc-export::before {
  content: @nc-export;
}

.nc-eye::before {
  content: @nc-eye;
}

.nc-eye-recognition::before {
  content: @nc-eye-recognition;
}

.nc-eyelash::before {
  content: @nc-eyelash;
}

.nc-eyeliner::before {
  content: @nc-eyeliner;
}

.nc-eyeshadow::before {
  content: @nc-eyeshadow;
}

.nc-ez-bar::before {
  content: @nc-ez-bar;
}

.nc-f-add::before {
  content: @nc-f-add;
}

.nc-f-chat::before {
  content: @nc-f-chat;
}

.nc-f-check::before {
  content: @nc-f-check;
}

.nc-f-comment::before {
  content: @nc-f-comment;
}

.nc-f-dashboard::before {
  content: @nc-f-dashboard;
}

.nc-f-delete::before {
  content: @nc-f-delete;
}

.nc-f-remove::before {
  content: @nc-f-remove;
}

.nc-face-man::before {
  content: @nc-face-man;
}

.nc-face-powder::before {
  content: @nc-face-powder;
}

.nc-face-recognition::before {
  content: @nc-face-recognition;
}

.nc-face-woman::before {
  content: @nc-face-woman;
}

.nc-factory::before {
  content: @nc-factory;
}

.nc-fahrenheit::before {
  content: @nc-fahrenheit;
}

.nc-fairy-wand::before {
  content: @nc-fairy-wand;
}

.nc-family::before {
  content: @nc-family;
}

.nc-family-roof::before {
  content: @nc-family-roof;
}

.nc-fan::before {
  content: @nc-fan;
}

.nc-farm-2::before {
  content: @nc-farm-2;
}

.nc-farm-rows::before {
  content: @nc-farm-rows;
}

.nc-farmer::before {
  content: @nc-farmer;
}

.nc-farmer-market::before {
  content: @nc-farmer-market;
}

.nc-fav-list::before {
  content: @nc-fav-list;
}

.nc-fav-property::before {
  content: @nc-fav-property;
}

.nc-fav-remove::before {
  content: @nc-fav-remove;
}

.nc-favorite::before {
  content: @nc-favorite;
}

.nc-feedback::before {
  content: @nc-feedback;
}

.nc-feeding-bottle::before {
  content: @nc-feeding-bottle;
}

.nc-female-figure::before {
  content: @nc-female-figure;
}

.nc-female-sign::before {
  content: @nc-female-sign;
}

.nc-fence::before {
  content: @nc-fence;
}

.nc-fencing::before {
  content: @nc-fencing;
}

.nc-fencing-swords::before {
  content: @nc-fencing-swords;
}

.nc-fertilizer::before {
  content: @nc-fertilizer;
}

.nc-file::before {
  content: @nc-file;
}

.nc-file-2::before {
  content: @nc-file-2;
}

.nc-file-add::before {
  content: @nc-file-add;
}

.nc-file-alert::before {
  content: @nc-file-alert;
}

.nc-file-archive::before {
  content: @nc-file-archive;
}

.nc-file-article::before {
  content: @nc-file-article;
}

.nc-file-audio::before {
  content: @nc-file-audio;
}

.nc-file-audio-2::before {
  content: @nc-file-audio-2;
}

.nc-file-bookmark::before {
  content: @nc-file-bookmark;
}

.nc-file-chart-bar::before {
  content: @nc-file-chart-bar;
}

.nc-file-chart-pie::before {
  content: @nc-file-chart-pie;
}

.nc-file-check::before {
  content: @nc-file-check;
}

.nc-file-cloud::before {
  content: @nc-file-cloud;
}

.nc-file-copies::before {
  content: @nc-file-copies;
}

.nc-file-copy::before {
  content: @nc-file-copy;
}

.nc-file-delete::before {
  content: @nc-file-delete;
}

.nc-file-dev::before {
  content: @nc-file-dev;
}

.nc-file-download::before {
  content: @nc-file-download;
}

.nc-file-download-3::before {
  content: @nc-file-download-3;
}

.nc-file-edit::before {
  content: @nc-file-edit;
}

.nc-file-export::before {
  content: @nc-file-export;
}

.nc-file-favorite::before {
  content: @nc-file-favorite;
}

.nc-file-folder::before {
  content: @nc-file-folder;
}

.nc-file-gallery::before {
  content: @nc-file-gallery;
}

.nc-file-history::before {
  content: @nc-file-history;
}

.nc-file-image::before {
  content: @nc-file-image;
}

.nc-file-import::before {
  content: @nc-file-import;
}

.nc-file-info::before {
  content: @nc-file-info;
}

.nc-file-link::before {
  content: @nc-file-link;
}

.nc-file-locked::before {
  content: @nc-file-locked;
}

.nc-file-money::before {
  content: @nc-file-money;
}

.nc-file-new::before {
  content: @nc-file-new;
}

.nc-file-no-access::before {
  content: @nc-file-no-access;
}

.nc-file-play::before {
  content: @nc-file-play;
}

.nc-file-preferences::before {
  content: @nc-file-preferences;
}

.nc-file-question::before {
  content: @nc-file-question;
}

.nc-file-remove::before {
  content: @nc-file-remove;
}

.nc-file-replace::before {
  content: @nc-file-replace;
}

.nc-file-search::before {
  content: @nc-file-search;
}

.nc-file-settings::before {
  content: @nc-file-settings;
}

.nc-file-shared::before {
  content: @nc-file-shared;
}

.nc-file-starred::before {
  content: @nc-file-starred;
}

.nc-file-sync::before {
  content: @nc-file-sync;
}

.nc-file-text::before {
  content: @nc-file-text;
}

.nc-file-upload::before {
  content: @nc-file-upload;
}

.nc-file-upload-2::before {
  content: @nc-file-upload-2;
}

.nc-file-upload-3::before {
  content: @nc-file-upload-3;
}

.nc-file-user::before {
  content: @nc-file-user;
}

.nc-file-vector::before {
  content: @nc-file-vector;
}

.nc-film::before {
  content: @nc-film;
}

.nc-filter::before {
  content: @nc-filter;
}

.nc-filter-check::before {
  content: @nc-filter-check;
}

.nc-filter-organization::before {
  content: @nc-filter-organization;
}

.nc-filter-remove::before {
  content: @nc-filter-remove;
}

.nc-filter-tool::before {
  content: @nc-filter-tool;
}

.nc-final-score::before {
  content: @nc-final-score;
}

.nc-find-baggage::before {
  content: @nc-find-baggage;
}

.nc-find-replace::before {
  content: @nc-find-replace;
}

.nc-finger-snap::before {
  content: @nc-finger-snap;
}

.nc-fire::before {
  content: @nc-fire;
}

.nc-firearm::before {
  content: @nc-firearm;
}

.nc-fireplace::before {
  content: @nc-fireplace;
}

.nc-firewall::before {
  content: @nc-firewall;
}

.nc-fireworks::before {
  content: @nc-fireworks;
}

.nc-fish::before {
  content: @nc-fish;
}

.nc-fishbone::before {
  content: @nc-fishbone;
}

.nc-fist::before {
  content: @nc-fist;
}

.nc-fit-horizontal::before {
  content: @nc-fit-horizontal;
}

.nc-fit-vertical::before {
  content: @nc-fit-vertical;
}

.nc-five::before {
  content: @nc-five;
}

.nc-flag::before {
  content: @nc-flag;
}

.nc-flag-complex::before {
  content: @nc-flag-complex;
}

.nc-flag-diagonal-33::before {
  content: @nc-flag-diagonal-33;
}

.nc-flag-diagonal-34::before {
  content: @nc-flag-diagonal-34;
}

.nc-flag-points-31::before {
  content: @nc-flag-points-31;
}

.nc-flag-points-32::before {
  content: @nc-flag-points-32;
}

.nc-flag-simple::before {
  content: @nc-flag-simple;
}

.nc-flame::before {
  content: @nc-flame;
}

.nc-flash-off::before {
  content: @nc-flash-off;
}

.nc-flash-off-2::before {
  content: @nc-flash-off-2;
}

.nc-flashlight::before {
  content: @nc-flashlight;
}

.nc-flask::before {
  content: @nc-flask;
}

.nc-flask-2::before {
  content: @nc-flask-2;
}

.nc-flick-down::before {
  content: @nc-flick-down;
}

.nc-flick-left::before {
  content: @nc-flick-left;
}

.nc-flick-right::before {
  content: @nc-flick-right;
}

.nc-flick-up::before {
  content: @nc-flick-up;
}

.nc-flight::before {
  content: @nc-flight;
}

.nc-flight-connection::before {
  content: @nc-flight-connection;
}

.nc-flip::before {
  content: @nc-flip;
}

.nc-flip-horizontal::before {
  content: @nc-flip-horizontal;
}

.nc-flip-up::before {
  content: @nc-flip-up;
}

.nc-flip-vertical::before {
  content: @nc-flip-vertical;
}

.nc-floor::before {
  content: @nc-floor;
}

.nc-floor-lamp::before {
  content: @nc-floor-lamp;
}

.nc-floors::before {
  content: @nc-floors;
}

.nc-floppy-disk::before {
  content: @nc-floppy-disk;
}

.nc-flower::before {
  content: @nc-flower;
}

.nc-flower-07::before {
  content: @nc-flower-07;
}

.nc-flower-2::before {
  content: @nc-flower-2;
}

.nc-flower-rose::before {
  content: @nc-flower-rose;
}

.nc-fog::before {
  content: @nc-fog;
}

.nc-folder::before {
  content: @nc-folder;
}

.nc-folder-2::before {
  content: @nc-folder-2;
}

.nc-folder-3::before {
  content: @nc-folder-3;
}

.nc-folder-add::before {
  content: @nc-folder-add;
}

.nc-folder-alert::before {
  content: @nc-folder-alert;
}

.nc-folder-audio::before {
  content: @nc-folder-audio;
}

.nc-folder-bookmark::before {
  content: @nc-folder-bookmark;
}

.nc-folder-chart-bar::before {
  content: @nc-folder-chart-bar;
}

.nc-folder-chart-pie::before {
  content: @nc-folder-chart-pie;
}

.nc-folder-check::before {
  content: @nc-folder-check;
}

.nc-folder-cloud::before {
  content: @nc-folder-cloud;
}

.nc-folder-dev::before {
  content: @nc-folder-dev;
}

.nc-folder-download::before {
  content: @nc-folder-download;
}

.nc-folder-edit::before {
  content: @nc-folder-edit;
}

.nc-folder-favorite::before {
  content: @nc-folder-favorite;
}

.nc-folder-gallery::before {
  content: @nc-folder-gallery;
}

.nc-folder-history::before {
  content: @nc-folder-history;
}

.nc-folder-image::before {
  content: @nc-folder-image;
}

.nc-folder-info::before {
  content: @nc-folder-info;
}

.nc-folder-link::before {
  content: @nc-folder-link;
}

.nc-folder-locked::before {
  content: @nc-folder-locked;
}

.nc-folder-money::before {
  content: @nc-folder-money;
}

.nc-folder-music::before {
  content: @nc-folder-music;
}

.nc-folder-no-access::before {
  content: @nc-folder-no-access;
}

.nc-folder-play::before {
  content: @nc-folder-play;
}

.nc-folder-preferences::before {
  content: @nc-folder-preferences;
}

.nc-folder-question::before {
  content: @nc-folder-question;
}

.nc-folder-remove::before {
  content: @nc-folder-remove;
}

.nc-folder-replace::before {
  content: @nc-folder-replace;
}

.nc-folder-search::before {
  content: @nc-folder-search;
}

.nc-folder-settings::before {
  content: @nc-folder-settings;
}

.nc-folder-shared::before {
  content: @nc-folder-shared;
}

.nc-folder-starred::before {
  content: @nc-folder-starred;
}

.nc-folder-sync::before {
  content: @nc-folder-sync;
}

.nc-folder-upload::before {
  content: @nc-folder-upload;
}

.nc-folder-user::before {
  content: @nc-folder-user;
}

.nc-folder-vector::before {
  content: @nc-folder-vector;
}

.nc-food-course::before {
  content: @nc-food-course;
}

.nc-food-dog::before {
  content: @nc-food-dog;
}

.nc-food-scale::before {
  content: @nc-food-scale;
}

.nc-food-supplement::before {
  content: @nc-food-supplement;
}

.nc-football-headguard::before {
  content: @nc-football-headguard;
}

.nc-forecast::before {
  content: @nc-forecast;
}

.nc-forest::before {
  content: @nc-forest;
}

.nc-fork::before {
  content: @nc-fork;
}

.nc-fork-2::before {
  content: @nc-fork-2;
}

.nc-forklift::before {
  content: @nc-forklift;
}

.nc-form::before {
  content: @nc-form;
}

.nc-format-left::before {
  content: @nc-format-left;
}

.nc-format-right::before {
  content: @nc-format-right;
}

.nc-forward::before {
  content: @nc-forward;
}

.nc-four::before {
  content: @nc-four;
}

.nc-frame::before {
  content: @nc-frame;
}

.nc-frame-effect::before {
  content: @nc-frame-effect;
}

.nc-frankenstein::before {
  content: @nc-frankenstein;
}

.nc-fridge::before {
  content: @nc-fridge;
}

.nc-fruit-crate::before {
  content: @nc-fruit-crate;
}

.nc-fruit-picking::before {
  content: @nc-fruit-picking;
}

.nc-fuel::before {
  content: @nc-fuel;
}

.nc-fuel-2::before {
  content: @nc-fuel-2;
}

.nc-fuel-electric::before {
  content: @nc-fuel-electric;
}

.nc-full-screen::before {
  content: @nc-full-screen;
}

.nc-fullscreen::before {
  content: @nc-fullscreen;
}

.nc-fullscreen-2::before {
  content: @nc-fullscreen-2;
}

.nc-fullsize::before {
  content: @nc-fullsize;
}

.nc-function::before {
  content: @nc-function;
}

.nc-funnel::before {
  content: @nc-funnel;
}

.nc-furnished-property::before {
  content: @nc-furnished-property;
}

.nc-g-chart::before {
  content: @nc-g-chart;
}

.nc-g-check::before {
  content: @nc-g-check;
}

.nc-gallery-layout::before {
  content: @nc-gallery-layout;
}

.nc-gallery-view::before {
  content: @nc-gallery-view;
}

.nc-gaming-console::before {
  content: @nc-gaming-console;
}

.nc-gaming-controller::before {
  content: @nc-gaming-controller;
}

.nc-gantt::before {
  content: @nc-gantt;
}

.nc-garden-fork::before {
  content: @nc-garden-fork;
}

.nc-garden-shears::before {
  content: @nc-garden-shears;
}

.nc-gardening-tools::before {
  content: @nc-gardening-tools;
}

.nc-garlic::before {
  content: @nc-garlic;
}

.nc-gas-mask::before {
  content: @nc-gas-mask;
}

.nc-gathering-restrictions::before {
  content: @nc-gathering-restrictions;
}

.nc-gear::before {
  content: @nc-gear;
}

.nc-geometry::before {
  content: @nc-geometry;
}

.nc-ghost::before {
  content: @nc-ghost;
}

.nc-ghost-2::before {
  content: @nc-ghost-2;
}

.nc-gift::before {
  content: @nc-gift;
}

.nc-gift-exchange::before {
  content: @nc-gift-exchange;
}

.nc-git-commit::before {
  content: @nc-git-commit;
}

.nc-git-merge::before {
  content: @nc-git-merge;
}

.nc-glass::before {
  content: @nc-glass;
}

.nc-glass-water::before {
  content: @nc-glass-water;
}

.nc-glasses::before {
  content: @nc-glasses;
}

.nc-glasses-2::before {
  content: @nc-glasses-2;
}

.nc-globe::before {
  content: @nc-globe;
}

.nc-globe-2::before {
  content: @nc-globe-2;
}

.nc-glove::before {
  content: @nc-glove;
}

.nc-gloves::before {
  content: @nc-gloves;
}

.nc-goal-65::before {
  content: @nc-goal-65;
}

.nc-gold::before {
  content: @nc-gold;
}

.nc-gold-coin::before {
  content: @nc-gold-coin;
}

.nc-golden-gate-bridge::before {
  content: @nc-golden-gate-bridge;
}

.nc-golf-ball::before {
  content: @nc-golf-ball;
}

.nc-golf-club::before {
  content: @nc-golf-club;
}

.nc-golf-course::before {
  content: @nc-golf-course;
}

.nc-golf-player::before {
  content: @nc-golf-player;
}

.nc-golf-strike::before {
  content: @nc-golf-strike;
}

.nc-gooey-anim::before {
  content: @nc-gooey-anim;
}

.nc-google::before {
  content: @nc-google;
}

.nc-goose::before {
  content: @nc-goose;
}

.nc-gps::before {
  content: @nc-gps;
}

.nc-grab::before {
  content: @nc-grab;
}

.nc-gradient::before {
  content: @nc-gradient;
}

.nc-grain::before {
  content: @nc-grain;
}

.nc-grain-2::before {
  content: @nc-grain-2;
}

.nc-grain-effect::before {
  content: @nc-grain-effect;
}

.nc-grammar-check::before {
  content: @nc-grammar-check;
}

.nc-grammar-check-2::before {
  content: @nc-grammar-check-2;
}

.nc-grandparent::before {
  content: @nc-grandparent;
}

.nc-grape::before {
  content: @nc-grape;
}

.nc-graphics-tablet::before {
  content: @nc-graphics-tablet;
}

.nc-grave::before {
  content: @nc-grave;
}

.nc-great-wall::before {
  content: @nc-great-wall;
}

.nc-greenhouse::before {
  content: @nc-greenhouse;
}

.nc-grenade::before {
  content: @nc-grenade;
}

.nc-grid::before {
  content: @nc-grid;
}

.nc-grid-interface::before {
  content: @nc-grid-interface;
}

.nc-grid-layout::before {
  content: @nc-grid-layout;
}

.nc-grid-system::before {
  content: @nc-grid-system;
}

.nc-grid-view::before {
  content: @nc-grid-view;
}

.nc-groom::before {
  content: @nc-groom;
}

.nc-group::before {
  content: @nc-group;
}

.nc-guitar::before {
  content: @nc-guitar;
}

.nc-gym::before {
  content: @nc-gym;
}

.nc-gym-class::before {
  content: @nc-gym-class;
}

.nc-gym-shoes::before {
  content: @nc-gym-shoes;
}

.nc-gymnastics::before {
  content: @nc-gymnastics;
}

.nc-hacker::before {
  content: @nc-hacker;
}

.nc-hair-clipper::before {
  content: @nc-hair-clipper;
}

.nc-hair-dryer::before {
  content: @nc-hair-dryer;
}

.nc-hair-gel::before {
  content: @nc-hair-gel;
}

.nc-hair-man::before {
  content: @nc-hair-man;
}

.nc-hair-straightener::before {
  content: @nc-hair-straightener;
}

.nc-hair-towel::before {
  content: @nc-hair-towel;
}

.nc-hair-woman::before {
  content: @nc-hair-woman;
}

.nc-hairdresser::before {
  content: @nc-hairdresser;
}

.nc-halloween-pumpkin::before {
  content: @nc-halloween-pumpkin;
}

.nc-hammer::before {
  content: @nc-hammer;
}

.nc-hand-card::before {
  content: @nc-hand-card;
}

.nc-hand-heart::before {
  content: @nc-hand-heart;
}

.nc-hand-mixer::before {
  content: @nc-hand-mixer;
}

.nc-handball::before {
  content: @nc-handball;
}

.nc-handheld-console::before {
  content: @nc-handheld-console;
}

.nc-handout::before {
  content: @nc-handout;
}

.nc-hands-heart::before {
  content: @nc-hands-heart;
}

.nc-handshake::before {
  content: @nc-handshake;
}

.nc-hanging-toys::before {
  content: @nc-hanging-toys;
}

.nc-happy-baby::before {
  content: @nc-happy-baby;
}

.nc-happy-sun::before {
  content: @nc-happy-sun;
}

.nc-hash-mark::before {
  content: @nc-hash-mark;
}

.nc-hat::before {
  content: @nc-hat;
}

.nc-hat-2::before {
  content: @nc-hat-2;
}

.nc-hat-3::before {
  content: @nc-hat-3;
}

.nc-hat-top::before {
  content: @nc-hat-top;
}

.nc-hazelnut::before {
  content: @nc-hazelnut;
}

.nc-hdmi::before {
  content: @nc-hdmi;
}

.nc-heading-1::before {
  content: @nc-heading-1;
}

.nc-heading-2::before {
  content: @nc-heading-2;
}

.nc-heading-3::before {
  content: @nc-heading-3;
}

.nc-heading-4::before {
  content: @nc-heading-4;
}

.nc-heading-5::before {
  content: @nc-heading-5;
}

.nc-heading-6::before {
  content: @nc-heading-6;
}

.nc-headphones::before {
  content: @nc-headphones;
}

.nc-headphones-2::before {
  content: @nc-headphones-2;
}

.nc-headphones-3::before {
  content: @nc-headphones-3;
}

.nc-headphones-mic::before {
  content: @nc-headphones-mic;
}

.nc-headset::before {
  content: @nc-headset;
}

.nc-heart::before {
  content: @nc-heart;
}

.nc-heart-anim::before {
  content: @nc-heart-anim;
}

.nc-heart-balloons::before {
  content: @nc-heart-balloons;
}

.nc-heart-lock::before {
  content: @nc-heart-lock;
}

.nc-heartbeat::before {
  content: @nc-heartbeat;
}

.nc-hearts-suit::before {
  content: @nc-hearts-suit;
}

.nc-heater::before {
  content: @nc-heater;
}

.nc-height::before {
  content: @nc-height;
}

.nc-height-2::before {
  content: @nc-height-2;
}

.nc-helicopter::before {
  content: @nc-helicopter;
}

.nc-helmet::before {
  content: @nc-helmet;
}

.nc-hide::before {
  content: @nc-hide;
}

.nc-hierarchy-53::before {
  content: @nc-hierarchy-53;
}

.nc-hierarchy-54::before {
  content: @nc-hierarchy-54;
}

.nc-hierarchy-55::before {
  content: @nc-hierarchy-55;
}

.nc-hierarchy-56::before {
  content: @nc-hierarchy-56;
}

.nc-high-priority::before {
  content: @nc-high-priority;
}

.nc-hinduism::before {
  content: @nc-hinduism;
}

.nc-hob::before {
  content: @nc-hob;
}

.nc-hockey::before {
  content: @nc-hockey;
}

.nc-hockey-stick::before {
  content: @nc-hockey-stick;
}

.nc-hold::before {
  content: @nc-hold;
}

.nc-home::before {
  content: @nc-home;
}

.nc-home-2::before {
  content: @nc-home-2;
}

.nc-home-3::before {
  content: @nc-home-3;
}

.nc-home-search::before {
  content: @nc-home-search;
}

.nc-honey::before {
  content: @nc-honey;
}

.nc-honeymoon::before {
  content: @nc-honeymoon;
}

.nc-hoodie::before {
  content: @nc-hoodie;
}

.nc-hook::before {
  content: @nc-hook;
}

.nc-horizontal-divider::before {
  content: @nc-horizontal-divider;
}

.nc-horse::before {
  content: @nc-horse;
}

.nc-horse-2::before {
  content: @nc-horse-2;
}

.nc-horse-hopper::before {
  content: @nc-horse-hopper;
}

.nc-horseshoe::before {
  content: @nc-horseshoe;
}

.nc-hose::before {
  content: @nc-hose;
}

.nc-hospital-32::before {
  content: @nc-hospital-32;
}

.nc-hospital-33::before {
  content: @nc-hospital-33;
}

.nc-hospital-34::before {
  content: @nc-hospital-34;
}

.nc-hospital-bed::before {
  content: @nc-hospital-bed;
}

.nc-hot-dog::before {
  content: @nc-hot-dog;
}

.nc-hot-key::before {
  content: @nc-hot-key;
}

.nc-hotel::before {
  content: @nc-hotel;
}

.nc-hotel-bell::before {
  content: @nc-hotel-bell;
}

.nc-hotel-symbol::before {
  content: @nc-hotel-symbol;
}

.nc-hotspot::before {
  content: @nc-hotspot;
}

.nc-hourglass::before {
  content: @nc-hourglass;
}

.nc-house::before {
  content: @nc-house;
}

.nc-house-pricing::before {
  content: @nc-house-pricing;
}

.nc-house-property::before {
  content: @nc-house-property;
}

.nc-house-search-engine::before {
  content: @nc-house-search-engine;
}

.nc-html5::before {
  content: @nc-html5;
}

.nc-humanoid::before {
  content: @nc-humanoid;
}

.nc-humidity-26::before {
  content: @nc-humidity-26;
}

.nc-humidity-52::before {
  content: @nc-humidity-52;
}

.nc-hurricane-44::before {
  content: @nc-hurricane-44;
}

.nc-hurricane-45::before {
  content: @nc-hurricane-45;
}

.nc-hut::before {
  content: @nc-hut;
}

.nc-hybrid-car::before {
  content: @nc-hybrid-car;
}

.nc-hyperlink::before {
  content: @nc-hyperlink;
}

.nc-hyperlink-broken::before {
  content: @nc-hyperlink-broken;
}

.nc-i-add::before {
  content: @nc-i-add;
}

.nc-i-check::before {
  content: @nc-i-check;
}

.nc-i-delete::before {
  content: @nc-i-delete;
}

.nc-i-edit::before {
  content: @nc-i-edit;
}

.nc-i-remove::before {
  content: @nc-i-remove;
}

.nc-ice-cream::before {
  content: @nc-ice-cream;
}

.nc-ice-cream-22::before {
  content: @nc-ice-cream-22;
}

.nc-ice-cream-72::before {
  content: @nc-ice-cream-72;
}

.nc-ice-skates::before {
  content: @nc-ice-skates;
}

.nc-igloo::before {
  content: @nc-igloo;
}

.nc-image::before {
  content: @nc-image;
}

.nc-image-2::before {
  content: @nc-image-2;
}

.nc-image-add::before {
  content: @nc-image-add;
}

.nc-image-delete::before {
  content: @nc-image-delete;
}

.nc-image-location::before {
  content: @nc-image-location;
}

.nc-img::before {
  content: @nc-img;
}

.nc-img-rotate-left::before {
  content: @nc-img-rotate-left;
}

.nc-img-rotate-right::before {
  content: @nc-img-rotate-right;
}

.nc-img-stack::before {
  content: @nc-img-stack;
}

.nc-incense::before {
  content: @nc-incense;
}

.nc-incognito::before {
  content: @nc-incognito;
}

.nc-increase::before {
  content: @nc-increase;
}

.nc-increase-font-size::before {
  content: @nc-increase-font-size;
}

.nc-increase-indent::before {
  content: @nc-increase-indent;
}

.nc-infinite::before {
  content: @nc-infinite;
}

.nc-infinite-loop::before {
  content: @nc-infinite-loop;
}

.nc-info::before {
  content: @nc-info;
}

.nc-info-point::before {
  content: @nc-info-point;
}

.nc-infrared-thermometer::before {
  content: @nc-infrared-thermometer;
}

.nc-input::before {
  content: @nc-input;
}

.nc-instant-camera::before {
  content: @nc-instant-camera;
}

.nc-instant-camera-2::before {
  content: @nc-instant-camera-2;
}

.nc-interview::before {
  content: @nc-interview;
}

.nc-intestine::before {
  content: @nc-intestine;
}

.nc-invert-direction::before {
  content: @nc-invert-direction;
}

.nc-invert-process::before {
  content: @nc-invert-process;
}

.nc-iron::before {
  content: @nc-iron;
}

.nc-iron-2::before {
  content: @nc-iron-2;
}

.nc-iron-dont::before {
  content: @nc-iron-dont;
}

.nc-islam::before {
  content: @nc-islam;
}

.nc-jacuzzi::before {
  content: @nc-jacuzzi;
}

.nc-jam::before {
  content: @nc-jam;
}

.nc-jeans-41::before {
  content: @nc-jeans-41;
}

.nc-jeans-43::before {
  content: @nc-jeans-43;
}

.nc-jeans-pocket::before {
  content: @nc-jeans-pocket;
}

.nc-jelly::before {
  content: @nc-jelly;
}

.nc-jellyfish::before {
  content: @nc-jellyfish;
}

.nc-jewel::before {
  content: @nc-jewel;
}

.nc-joint-account::before {
  content: @nc-joint-account;
}

.nc-journey::before {
  content: @nc-journey;
}

.nc-journey-06::before {
  content: @nc-journey-06;
}

.nc-journey-07::before {
  content: @nc-journey-07;
}

.nc-journey-08::before {
  content: @nc-journey-08;
}

.nc-js-console::before {
  content: @nc-js-console;
}

.nc-json-logo::before {
  content: @nc-json-logo;
}

.nc-judaism::before {
  content: @nc-judaism;
}

.nc-juice::before {
  content: @nc-juice;
}

.nc-jump-rope::before {
  content: @nc-jump-rope;
}

.nc-karate::before {
  content: @nc-karate;
}

.nc-ketchup::before {
  content: @nc-ketchup;
}

.nc-kettle::before {
  content: @nc-kettle;
}

.nc-kettlebell::before {
  content: @nc-kettlebell;
}

.nc-key::before {
  content: @nc-key;
}

.nc-keyboard::before {
  content: @nc-keyboard;
}

.nc-keyboard-hide::before {
  content: @nc-keyboard-hide;
}

.nc-keyboard-mouse::before {
  content: @nc-keyboard-mouse;
}

.nc-keyboard-wired::before {
  content: @nc-keyboard-wired;
}

.nc-keyboard-wireless::before {
  content: @nc-keyboard-wireless;
}

.nc-kid::before {
  content: @nc-kid;
}

.nc-kid-2::before {
  content: @nc-kid-2;
}

.nc-kiss::before {
  content: @nc-kiss;
}

.nc-kitchen-fan::before {
  content: @nc-kitchen-fan;
}

.nc-kiwi::before {
  content: @nc-kiwi;
}

.nc-knife::before {
  content: @nc-knife;
}

.nc-knob::before {
  content: @nc-knob;
}

.nc-l-add::before {
  content: @nc-l-add;
}

.nc-l-check::before {
  content: @nc-l-check;
}

.nc-l-circle::before {
  content: @nc-l-circle;
}

.nc-l-circles::before {
  content: @nc-l-circles;
}

.nc-l-location::before {
  content: @nc-l-location;
}

.nc-l-remove::before {
  content: @nc-l-remove;
}

.nc-l-search::before {
  content: @nc-l-search;
}

.nc-l-security::before {
  content: @nc-l-security;
}

.nc-l-settings::before {
  content: @nc-l-settings;
}

.nc-l-sync::before {
  content: @nc-l-sync;
}

.nc-l-system-update::before {
  content: @nc-l-system-update;
}

.nc-label::before {
  content: @nc-label;
}

.nc-ladder::before {
  content: @nc-ladder;
}

.nc-ladybug::before {
  content: @nc-ladybug;
}

.nc-land::before {
  content: @nc-land;
}

.nc-landing::before {
  content: @nc-landing;
}

.nc-landscape-orientation::before {
  content: @nc-landscape-orientation;
}

.nc-language::before {
  content: @nc-language;
}

.nc-laptop::before {
  content: @nc-laptop;
}

.nc-laptop-1::before {
  content: @nc-laptop-1;
}

.nc-laptop-2::before {
  content: @nc-laptop-2;
}

.nc-laptop-71::before {
  content: @nc-laptop-71;
}

.nc-laptop-72::before {
  content: @nc-laptop-72;
}

.nc-lat-station::before {
  content: @nc-lat-station;
}

.nc-laugh-17::before {
  content: @nc-laugh-17;
}

.nc-laugh-35::before {
  content: @nc-laugh-35;
}

.nc-launch::before {
  content: @nc-launch;
}

.nc-launch-app::before {
  content: @nc-launch-app;
}

.nc-laundry::before {
  content: @nc-laundry;
}

.nc-law::before {
  content: @nc-law;
}

.nc-layers::before {
  content: @nc-layers;
}

.nc-layers-2::before {
  content: @nc-layers-2;
}

.nc-layout-11::before {
  content: @nc-layout-11;
}

.nc-layout-25::before {
  content: @nc-layout-25;
}

.nc-layout-grid::before {
  content: @nc-layout-grid;
}

.nc-leaf::before {
  content: @nc-leaf;
}

.nc-leaf-36::before {
  content: @nc-leaf-36;
}

.nc-leaf-38::before {
  content: @nc-leaf-38;
}

.nc-leave::before {
  content: @nc-leave;
}

.nc-left-arrow::before {
  content: @nc-left-arrow;
}

.nc-leggins::before {
  content: @nc-leggins;
}

.nc-lemon::before {
  content: @nc-lemon;
}

.nc-lemon-slice::before {
  content: @nc-lemon-slice;
}

.nc-letter::before {
  content: @nc-letter;
}

.nc-letter-a::before {
  content: @nc-letter-a;
}

.nc-letter-b::before {
  content: @nc-letter-b;
}

.nc-letter-c::before {
  content: @nc-letter-c;
}

.nc-letter-d::before {
  content: @nc-letter-d;
}

.nc-letter-e::before {
  content: @nc-letter-e;
}

.nc-letter-f::before {
  content: @nc-letter-f;
}

.nc-letter-g::before {
  content: @nc-letter-g;
}

.nc-letter-h::before {
  content: @nc-letter-h;
}

.nc-letter-i::before {
  content: @nc-letter-i;
}

.nc-letter-j::before {
  content: @nc-letter-j;
}

.nc-letter-k::before {
  content: @nc-letter-k;
}

.nc-letter-l::before {
  content: @nc-letter-l;
}

.nc-letter-m::before {
  content: @nc-letter-m;
}

.nc-letter-n::before {
  content: @nc-letter-n;
}

.nc-letter-o::before {
  content: @nc-letter-o;
}

.nc-letter-p::before {
  content: @nc-letter-p;
}

.nc-letter-q::before {
  content: @nc-letter-q;
}

.nc-letter-r::before {
  content: @nc-letter-r;
}

.nc-letter-s::before {
  content: @nc-letter-s;
}

.nc-letter-t::before {
  content: @nc-letter-t;
}

.nc-letter-u::before {
  content: @nc-letter-u;
}

.nc-letter-v::before {
  content: @nc-letter-v;
}

.nc-letter-w::before {
  content: @nc-letter-w;
}

.nc-letter-x::before {
  content: @nc-letter-x;
}

.nc-letter-y::before {
  content: @nc-letter-y;
}

.nc-letter-z::before {
  content: @nc-letter-z;
}

.nc-library::before {
  content: @nc-library;
}

.nc-license-key::before {
  content: @nc-license-key;
}

.nc-lifering::before {
  content: @nc-lifering;
}

.nc-lift::before {
  content: @nc-lift;
}

.nc-light-2::before {
  content: @nc-light-2;
}

.nc-light-control::before {
  content: @nc-light-control;
}

.nc-light-switch::before {
  content: @nc-light-switch;
}

.nc-light-traffic::before {
  content: @nc-light-traffic;
}

.nc-lighter::before {
  content: @nc-lighter;
}

.nc-lighthouse::before {
  content: @nc-lighthouse;
}

.nc-lightning::before {
  content: @nc-lightning;
}

.nc-like::before {
  content: @nc-like;
}

.nc-line-chart::before {
  content: @nc-line-chart;
}

.nc-line-height::before {
  content: @nc-line-height;
}

.nc-link::before {
  content: @nc-link;
}

.nc-linux::before {
  content: @nc-linux;
}

.nc-lip-gloss::before {
  content: @nc-lip-gloss;
}

.nc-lips::before {
  content: @nc-lips;
}

.nc-lipstick::before {
  content: @nc-lipstick;
}

.nc-lipstick-2::before {
  content: @nc-lipstick-2;
}

.nc-liquid-soap-container::before {
  content: @nc-liquid-soap-container;
}

.nc-list::before {
  content: @nc-list;
}

.nc-live-streaming::before {
  content: @nc-live-streaming;
}

.nc-loader-bars::before {
  content: @nc-loader-bars;
}

.nc-loan::before {
  content: @nc-loan;
}

.nc-lobster::before {
  content: @nc-lobster;
}

.nc-lock::before {
  content: @nc-lock;
}

.nc-lock-landscape::before {
  content: @nc-lock-landscape;
}

.nc-lock-orientation::before {
  content: @nc-lock-orientation;
}

.nc-lock-portrait::before {
  content: @nc-lock-portrait;
}

.nc-log-in::before {
  content: @nc-log-in;
}

.nc-log-out::before {
  content: @nc-log-out;
}

.nc-logic::before {
  content: @nc-logic;
}

.nc-logo-500px::before {
  content: @nc-logo-500px;
}

.nc-logo-angellist::before {
  content: @nc-logo-angellist;
}

.nc-logo-behance::before {
  content: @nc-logo-behance;
}

.nc-logo-blogger::before {
  content: @nc-logo-blogger;
}

.nc-logo-buffer::before {
  content: @nc-logo-buffer;
}

.nc-logo-buysellads::before {
  content: @nc-logo-buysellads;
}

.nc-logo-codepen::before {
  content: @nc-logo-codepen;
}

.nc-logo-creative-market::before {
  content: @nc-logo-creative-market;
}

.nc-logo-crunchbase::before {
  content: @nc-logo-crunchbase;
}

.nc-logo-deviantart::before {
  content: @nc-logo-deviantart;
}

.nc-logo-dribbble::before {
  content: @nc-logo-dribbble;
}

.nc-logo-dropbox::before {
  content: @nc-logo-dropbox;
}

.nc-logo-envato::before {
  content: @nc-logo-envato;
}

.nc-logo-evernote::before {
  content: @nc-logo-evernote;
}

.nc-logo-facebook::before {
  content: @nc-logo-facebook;
}

.nc-logo-fb-simple::before {
  content: @nc-logo-fb-simple;
}

.nc-logo-feedly::before {
  content: @nc-logo-feedly;
}

.nc-logo-flickr::before {
  content: @nc-logo-flickr;
}

.nc-logo-github::before {
  content: @nc-logo-github;
}

.nc-logo-google-plus::before {
  content: @nc-logo-google-plus;
}

.nc-logo-instagram::before {
  content: @nc-logo-instagram;
}

.nc-logo-lastfm::before {
  content: @nc-logo-lastfm;
}

.nc-logo-linkedin::before {
  content: @nc-logo-linkedin;
}

.nc-logo-medium::before {
  content: @nc-logo-medium;
}

.nc-logo-meetup::before {
  content: @nc-logo-meetup;
}

.nc-logo-messenger::before {
  content: @nc-logo-messenger;
}

.nc-logo-mixer::before {
  content: @nc-logo-mixer;
}

.nc-logo-myspace::before {
  content: @nc-logo-myspace;
}

.nc-logo-paypal::before {
  content: @nc-logo-paypal;
}

.nc-logo-pinterest::before {
  content: @nc-logo-pinterest;
}

.nc-logo-product-hunt::before {
  content: @nc-logo-product-hunt;
}

.nc-logo-qq::before {
  content: @nc-logo-qq;
}

.nc-logo-reddit::before {
  content: @nc-logo-reddit;
}

.nc-logo-rss::before {
  content: @nc-logo-rss;
}

.nc-logo-shopify::before {
  content: @nc-logo-shopify;
}

.nc-logo-skype::before {
  content: @nc-logo-skype;
}

.nc-logo-slack::before {
  content: @nc-logo-slack;
}

.nc-logo-snapchat::before {
  content: @nc-logo-snapchat;
}

.nc-logo-soundcloud::before {
  content: @nc-logo-soundcloud;
}

.nc-logo-spotify::before {
  content: @nc-logo-spotify;
}

.nc-logo-squarespace::before {
  content: @nc-logo-squarespace;
}

.nc-logo-trello::before {
  content: @nc-logo-trello;
}

.nc-logo-tumblr::before {
  content: @nc-logo-tumblr;
}

.nc-logo-twitter::before {
  content: @nc-logo-twitter;
}

.nc-logo-unsplash::before {
  content: @nc-logo-unsplash;
}

.nc-logo-vimeo::before {
  content: @nc-logo-vimeo;
}

.nc-logo-vine::before {
  content: @nc-logo-vine;
}

.nc-logo-vk::before {
  content: @nc-logo-vk;
}

.nc-logo-wechat::before {
  content: @nc-logo-wechat;
}

.nc-logo-weibo::before {
  content: @nc-logo-weibo;
}

.nc-logo-whatsapp::before {
  content: @nc-logo-whatsapp;
}

.nc-logo-wikipedia::before {
  content: @nc-logo-wikipedia;
}

.nc-logo-wordpress::before {
  content: @nc-logo-wordpress;
}

.nc-logo-yelp::before {
  content: @nc-logo-yelp;
}

.nc-logo-youtube::before {
  content: @nc-logo-youtube;
}

.nc-logout::before {
  content: @nc-logout;
}

.nc-lollipop::before {
  content: @nc-lollipop;
}

.nc-long-sleeve::before {
  content: @nc-long-sleeve;
}

.nc-loop::before {
  content: @nc-loop;
}

.nc-loop-2::before {
  content: @nc-loop-2;
}

.nc-lotus-flower::before {
  content: @nc-lotus-flower;
}

.nc-loudspeaker::before {
  content: @nc-loudspeaker;
}

.nc-love::before {
  content: @nc-love;
}

.nc-love-camera::before {
  content: @nc-love-camera;
}

.nc-love-car::before {
  content: @nc-love-car;
}

.nc-love-card::before {
  content: @nc-love-card;
}

.nc-love-heart-pin::before {
  content: @nc-love-heart-pin;
}

.nc-love-letter::before {
  content: @nc-love-letter;
}

.nc-love-message::before {
  content: @nc-love-message;
}

.nc-love-movie::before {
  content: @nc-love-movie;
}

.nc-love-song::before {
  content: @nc-love-song;
}

.nc-low-priority::before {
  content: @nc-low-priority;
}

.nc-low-vision::before {
  content: @nc-low-vision;
}

.nc-lowercase::before {
  content: @nc-lowercase;
}

.nc-lucky-seven::before {
  content: @nc-lucky-seven;
}

.nc-luggage::before {
  content: @nc-luggage;
}

.nc-lungs::before {
  content: @nc-lungs;
}

.nc-lungs-infection::before {
  content: @nc-lungs-infection;
}

.nc-m-add::before {
  content: @nc-m-add;
}

.nc-m-check::before {
  content: @nc-m-check;
}

.nc-m-delete::before {
  content: @nc-m-delete;
}

.nc-m-edit::before {
  content: @nc-m-edit;
}

.nc-m-heart::before {
  content: @nc-m-heart;
}

.nc-m-location::before {
  content: @nc-m-location;
}

.nc-m-remove::before {
  content: @nc-m-remove;
}

.nc-m-search::before {
  content: @nc-m-search;
}

.nc-m-security::before {
  content: @nc-m-security;
}

.nc-m-settings::before {
  content: @nc-m-settings;
}

.nc-m-share::before {
  content: @nc-m-share;
}

.nc-m-star::before {
  content: @nc-m-star;
}

.nc-m-sync::before {
  content: @nc-m-sync;
}

.nc-m-time::before {
  content: @nc-m-time;
}

.nc-m-update::before {
  content: @nc-m-update;
}

.nc-machine-learning::before {
  content: @nc-machine-learning;
}

.nc-macro::before {
  content: @nc-macro;
}

.nc-mad-12::before {
  content: @nc-mad-12;
}

.nc-mad-58::before {
  content: @nc-mad-58;
}

.nc-magic-wand::before {
  content: @nc-magic-wand;
}

.nc-magnet::before {
  content: @nc-magnet;
}

.nc-magnifier::before {
  content: @nc-magnifier;
}

.nc-magnifier-zoom-in::before {
  content: @nc-magnifier-zoom-in;
}

.nc-magnifier-zoom-out::before {
  content: @nc-magnifier-zoom-out;
}

.nc-mail::before {
  content: @nc-mail;
}

.nc-makeup::before {
  content: @nc-makeup;
}

.nc-makeup-blush::before {
  content: @nc-makeup-blush;
}

.nc-makeup-brush::before {
  content: @nc-makeup-brush;
}

.nc-makeup-cream::before {
  content: @nc-makeup-cream;
}

.nc-makeup-foundation::before {
  content: @nc-makeup-foundation;
}

.nc-makeup-mirror::before {
  content: @nc-makeup-mirror;
}

.nc-makeup-palette::before {
  content: @nc-makeup-palette;
}

.nc-male-sign::before {
  content: @nc-male-sign;
}

.nc-malicious::before {
  content: @nc-malicious;
}

.nc-man::before {
  content: @nc-man;
}

.nc-man-arrow-down::before {
  content: @nc-man-arrow-down;
}

.nc-man-arrow-up::before {
  content: @nc-man-arrow-up;
}

.nc-man-profile::before {
  content: @nc-man-profile;
}

.nc-man-up-front::before {
  content: @nc-man-up-front;
}

.nc-manga-62::before {
  content: @nc-manga-62;
}

.nc-manga-63::before {
  content: @nc-manga-63;
}

.nc-map::before {
  content: @nc-map;
}

.nc-map-big::before {
  content: @nc-map-big;
}

.nc-map-compass::before {
  content: @nc-map-compass;
}

.nc-map-gps::before {
  content: @nc-map-gps;
}

.nc-map-marker::before {
  content: @nc-map-marker;
}

.nc-map-pin::before {
  content: @nc-map-pin;
}

.nc-maple-leaf::before {
  content: @nc-maple-leaf;
}

.nc-mario-mushroom::before {
  content: @nc-mario-mushroom;
}

.nc-markdown::before {
  content: @nc-markdown;
}

.nc-marker::before {
  content: @nc-marker;
}

.nc-marker-2::before {
  content: @nc-marker-2;
}

.nc-marker-3::before {
  content: @nc-marker-3;
}

.nc-market-music::before {
  content: @nc-market-music;
}

.nc-market-play::before {
  content: @nc-market-play;
}

.nc-mascara::before {
  content: @nc-mascara;
}

.nc-mask-face::before {
  content: @nc-mask-face;
}

.nc-mask-oval::before {
  content: @nc-mask-oval;
}

.nc-mask-rect::before {
  content: @nc-mask-rect;
}

.nc-massage::before {
  content: @nc-massage;
}

.nc-mat::before {
  content: @nc-mat;
}

.nc-matches::before {
  content: @nc-matches;
}

.nc-math::before {
  content: @nc-math;
}

.nc-maximize::before {
  content: @nc-maximize;
}

.nc-maximize-area::before {
  content: @nc-maximize-area;
}

.nc-mayo::before {
  content: @nc-mayo;
}

.nc-measure-02::before {
  content: @nc-measure-02;
}

.nc-measure-17::before {
  content: @nc-measure-17;
}

.nc-measure-big::before {
  content: @nc-measure-big;
}

.nc-measurement::before {
  content: @nc-measurement;
}

.nc-measuring-cup::before {
  content: @nc-measuring-cup;
}

.nc-meat-spit::before {
  content: @nc-meat-spit;
}

.nc-medal::before {
  content: @nc-medal;
}

.nc-media-player::before {
  content: @nc-media-player;
}

.nc-media-stream::before {
  content: @nc-media-stream;
}

.nc-medical-clipboard::before {
  content: @nc-medical-clipboard;
}

.nc-medical-mask::before {
  content: @nc-medical-mask;
}

.nc-medication::before {
  content: @nc-medication;
}

.nc-medicine::before {
  content: @nc-medicine;
}

.nc-medicine-ball::before {
  content: @nc-medicine-ball;
}

.nc-meeting::before {
  content: @nc-meeting;
}

.nc-megaphone::before {
  content: @nc-megaphone;
}

.nc-menu::before {
  content: @nc-menu;
}

.nc-menu-2::before {
  content: @nc-menu-2;
}

.nc-menu-3::before {
  content: @nc-menu-3;
}

.nc-menu-4::before {
  content: @nc-menu-4;
}

.nc-menu-6::before {
  content: @nc-menu-6;
}

.nc-menu-7::before {
  content: @nc-menu-7;
}

.nc-menu-8::before {
  content: @nc-menu-8;
}

.nc-menu-dots::before {
  content: @nc-menu-dots;
}

.nc-merge::before {
  content: @nc-merge;
}

.nc-merge-2::before {
  content: @nc-merge-2;
}

.nc-messaging::before {
  content: @nc-messaging;
}

.nc-metrics::before {
  content: @nc-metrics;
}

.nc-mic::before {
  content: @nc-mic;
}

.nc-mic-2::before {
  content: @nc-mic-2;
}

.nc-mickey-mouse::before {
  content: @nc-mickey-mouse;
}

.nc-microbiology::before {
  content: @nc-microbiology;
}

.nc-microphone::before {
  content: @nc-microphone;
}

.nc-microphone-2::before {
  content: @nc-microphone-2;
}

.nc-microphone-off::before {
  content: @nc-microphone-off;
}

.nc-microscope::before {
  content: @nc-microscope;
}

.nc-microsoft::before {
  content: @nc-microsoft;
}

.nc-microsoft-onenote::before {
  content: @nc-microsoft-onenote;
}

.nc-microwave::before {
  content: @nc-microwave;
}

.nc-migration::before {
  content: @nc-migration;
}

.nc-military-camp::before {
  content: @nc-military-camp;
}

.nc-military-knife::before {
  content: @nc-military-knife;
}

.nc-military-medal::before {
  content: @nc-military-medal;
}

.nc-military-tag::before {
  content: @nc-military-tag;
}

.nc-military-tank::before {
  content: @nc-military-tank;
}

.nc-military-vest::before {
  content: @nc-military-vest;
}

.nc-milk::before {
  content: @nc-milk;
}

.nc-miner::before {
  content: @nc-miner;
}

.nc-mirror::before {
  content: @nc-mirror;
}

.nc-mirror-2::before {
  content: @nc-mirror-2;
}

.nc-mirror-display::before {
  content: @nc-mirror-display;
}

.nc-mirror-tablet-phone::before {
  content: @nc-mirror-tablet-phone;
}

.nc-missile::before {
  content: @nc-missile;
}

.nc-mistletoe::before {
  content: @nc-mistletoe;
}

.nc-mobile-banking::before {
  content: @nc-mobile-banking;
}

.nc-mobile-card::before {
  content: @nc-mobile-card;
}

.nc-mobile-chat::before {
  content: @nc-mobile-chat;
}

.nc-mobile-contact::before {
  content: @nc-mobile-contact;
}

.nc-mobile-design::before {
  content: @nc-mobile-design;
}

.nc-mobile-dev::before {
  content: @nc-mobile-dev;
}

.nc-mobile-phone::before {
  content: @nc-mobile-phone;
}

.nc-moka::before {
  content: @nc-moka;
}

.nc-molecule::before {
  content: @nc-molecule;
}

.nc-molecule-39::before {
  content: @nc-molecule-39;
}

.nc-molecule-40::before {
  content: @nc-molecule-40;
}

.nc-money-11::before {
  content: @nc-money-11;
}

.nc-money-12::before {
  content: @nc-money-12;
}

.nc-money-13::before {
  content: @nc-money-13;
}

.nc-money-bag::before {
  content: @nc-money-bag;
}

.nc-money-coins::before {
  content: @nc-money-coins;
}

.nc-money-growth::before {
  content: @nc-money-growth;
}

.nc-money-time::before {
  content: @nc-money-time;
}

.nc-money-transfer::before {
  content: @nc-money-transfer;
}

.nc-monster::before {
  content: @nc-monster;
}

.nc-moon::before {
  content: @nc-moon;
}

.nc-moon-cloud-drop::before {
  content: @nc-moon-cloud-drop;
}

.nc-moon-cloud-fog::before {
  content: @nc-moon-cloud-fog;
}

.nc-moon-cloud-hail::before {
  content: @nc-moon-cloud-hail;
}

.nc-moon-cloud-light::before {
  content: @nc-moon-cloud-light;
}

.nc-moon-cloud-rain::before {
  content: @nc-moon-cloud-rain;
}

.nc-moon-cloud-snow-61::before {
  content: @nc-moon-cloud-snow-61;
}

.nc-moon-cloud-snow-62::before {
  content: @nc-moon-cloud-snow-62;
}

.nc-moon-fog::before {
  content: @nc-moon-fog;
}

.nc-moon-full::before {
  content: @nc-moon-full;
}

.nc-moon-stars::before {
  content: @nc-moon-stars;
}

.nc-mortar::before {
  content: @nc-mortar;
}

.nc-mortgage::before {
  content: @nc-mortgage;
}

.nc-mosque::before {
  content: @nc-mosque;
}

.nc-moto::before {
  content: @nc-moto;
}

.nc-mountain::before {
  content: @nc-mountain;
}

.nc-mouse::before {
  content: @nc-mouse;
}

.nc-mouse-2::before {
  content: @nc-mouse-2;
}

.nc-mouse-anim::before {
  content: @nc-mouse-anim;
}

.nc-move::before {
  content: @nc-move;
}

.nc-move-2::before {
  content: @nc-move-2;
}

.nc-move-3::before {
  content: @nc-move-3;
}

.nc-move-down::before {
  content: @nc-move-down;
}

.nc-move-down-2::before {
  content: @nc-move-down-2;
}

.nc-move-down-right::before {
  content: @nc-move-down-right;
}

.nc-move-layer-down::before {
  content: @nc-move-layer-down;
}

.nc-move-layer-left::before {
  content: @nc-move-layer-left;
}

.nc-move-layer-right::before {
  content: @nc-move-layer-right;
}

.nc-move-layer-up::before {
  content: @nc-move-layer-up;
}

.nc-move-left::before {
  content: @nc-move-left;
}

.nc-move-right::before {
  content: @nc-move-right;
}

.nc-move-up::before {
  content: @nc-move-up;
}

.nc-move-up-2::before {
  content: @nc-move-up-2;
}

.nc-move-up-left::before {
  content: @nc-move-up-left;
}

.nc-movie::before {
  content: @nc-movie;
}

.nc-movie-2::before {
  content: @nc-movie-2;
}

.nc-movie-3::before {
  content: @nc-movie-3;
}

.nc-movie-reel::before {
  content: @nc-movie-reel;
}

.nc-mower::before {
  content: @nc-mower;
}

.nc-muffin::before {
  content: @nc-muffin;
}

.nc-mug::before {
  content: @nc-mug;
}

.nc-mushroom::before {
  content: @nc-mushroom;
}

.nc-music::before {
  content: @nc-music;
}

.nc-music-album::before {
  content: @nc-music-album;
}

.nc-music-cloud::before {
  content: @nc-music-cloud;
}

.nc-music-note::before {
  content: @nc-music-note;
}

.nc-music-player::before {
  content: @nc-music-player;
}

.nc-music-playlist::before {
  content: @nc-music-playlist;
}

.nc-mustache::before {
  content: @nc-mustache;
}

.nc-n-check::before {
  content: @nc-n-check;
}

.nc-n-edit::before {
  content: @nc-n-edit;
}

.nc-nail-file::before {
  content: @nc-nail-file;
}

.nc-nail-polish::before {
  content: @nc-nail-polish;
}

.nc-nail-polish-2::before {
  content: @nc-nail-polish-2;
}

.nc-name-card::before {
  content: @nc-name-card;
}

.nc-nav-down::before {
  content: @nc-nav-down;
}

.nc-nav-left::before {
  content: @nc-nav-left;
}

.nc-nav-right::before {
  content: @nc-nav-right;
}

.nc-nav-up::before {
  content: @nc-nav-up;
}

.nc-navigation::before {
  content: @nc-navigation;
}

.nc-neck-duster::before {
  content: @nc-neck-duster;
}

.nc-needle::before {
  content: @nc-needle;
}

.nc-negative-judgement::before {
  content: @nc-negative-judgement;
}

.nc-nerd::before {
  content: @nc-nerd;
}

.nc-net::before {
  content: @nc-net;
}

.nc-network-communication::before {
  content: @nc-network-communication;
}

.nc-network-connection::before {
  content: @nc-network-connection;
}

.nc-networking::before {
  content: @nc-networking;
}

.nc-new::before {
  content: @nc-new;
}

.nc-new-construction::before {
  content: @nc-new-construction;
}

.nc-new-notification::before {
  content: @nc-new-notification;
}

.nc-news::before {
  content: @nc-news;
}

.nc-newsletter::before {
  content: @nc-newsletter;
}

.nc-newsletter-dev::before {
  content: @nc-newsletter-dev;
}

.nc-night::before {
  content: @nc-night;
}

.nc-night-table::before {
  content: @nc-night-table;
}

.nc-nine::before {
  content: @nc-nine;
}

.nc-ninja::before {
  content: @nc-ninja;
}

.nc-no-contact::before {
  content: @nc-no-contact;
}

.nc-no-guns::before {
  content: @nc-no-guns;
}

.nc-no-photo::before {
  content: @nc-no-photo;
}

.nc-no-results::before {
  content: @nc-no-results;
}

.nc-no-smoking::before {
  content: @nc-no-smoking;
}

.nc-no-words::before {
  content: @nc-no-words;
}

.nc-nodes::before {
  content: @nc-nodes;
}

.nc-noodles::before {
  content: @nc-noodles;
}

.nc-note::before {
  content: @nc-note;
}

.nc-note-code::before {
  content: @nc-note-code;
}

.nc-notebook::before {
  content: @nc-notebook;
}

.nc-notepad::before {
  content: @nc-notepad;
}

.nc-notes::before {
  content: @nc-notes;
}

.nc-notification::before {
  content: @nc-notification;
}

.nc-notification-2::before {
  content: @nc-notification-2;
}

.nc-nurse::before {
  content: @nc-nurse;
}

.nc-nutrition::before {
  content: @nc-nutrition;
}

.nc-ny-building::before {
  content: @nc-ny-building;
}

.nc-o-check::before {
  content: @nc-o-check;
}

.nc-o-warning::before {
  content: @nc-o-warning;
}

.nc-octagon::before {
  content: @nc-octagon;
}

.nc-octagon-m::before {
  content: @nc-octagon-m;
}

.nc-octopus::before {
  content: @nc-octopus;
}

.nc-office::before {
  content: @nc-office;
}

.nc-office-badge::before {
  content: @nc-office-badge;
}

.nc-office-chair::before {
  content: @nc-office-chair;
}

.nc-office-pass::before {
  content: @nc-office-pass;
}

.nc-offline::before {
  content: @nc-offline;
}

.nc-oil::before {
  content: @nc-oil;
}

.nc-oil-2::before {
  content: @nc-oil-2;
}

.nc-olympic-flame::before {
  content: @nc-olympic-flame;
}

.nc-one::before {
  content: @nc-one;
}

.nc-onion::before {
  content: @nc-onion;
}

.nc-online-banking::before {
  content: @nc-online-banking;
}

.nc-open-ai::before {
  content: @nc-open-ai;
}

.nc-open-book::before {
  content: @nc-open-book;
}

.nc-open-folder::before {
  content: @nc-open-folder;
}

.nc-open-in-browser::before {
  content: @nc-open-in-browser;
}

.nc-opening-times::before {
  content: @nc-opening-times;
}

.nc-opposite-directions::before {
  content: @nc-opposite-directions;
}

.nc-opposite-directions-2::before {
  content: @nc-opposite-directions-2;
}

.nc-options::before {
  content: @nc-options;
}

.nc-orange::before {
  content: @nc-orange;
}

.nc-ordered-list::before {
  content: @nc-ordered-list;
}

.nc-organic::before {
  content: @nc-organic;
}

.nc-organic-2::before {
  content: @nc-organic-2;
}

.nc-orientation::before {
  content: @nc-orientation;
}

.nc-oven::before {
  content: @nc-oven;
}

.nc-ovum-sperm::before {
  content: @nc-ovum-sperm;
}

.nc-owl::before {
  content: @nc-owl;
}

.nc-p-add::before {
  content: @nc-p-add;
}

.nc-p-chart::before {
  content: @nc-p-chart;
}

.nc-p-check::before {
  content: @nc-p-check;
}

.nc-p-edit::before {
  content: @nc-p-edit;
}

.nc-p-heart::before {
  content: @nc-p-heart;
}

.nc-p-location::before {
  content: @nc-p-location;
}

.nc-p-remove::before {
  content: @nc-p-remove;
}

.nc-p-search::before {
  content: @nc-p-search;
}

.nc-p-settings::before {
  content: @nc-p-settings;
}

.nc-p-share::before {
  content: @nc-p-share;
}

.nc-p-sync::before {
  content: @nc-p-sync;
}

.nc-p-system-update::before {
  content: @nc-p-system-update;
}

.nc-p-time::before {
  content: @nc-p-time;
}

.nc-pacifier::before {
  content: @nc-pacifier;
}

.nc-pacman::before {
  content: @nc-pacman;
}

.nc-padlock::before {
  content: @nc-padlock;
}

.nc-padlock-unlocked::before {
  content: @nc-padlock-unlocked;
}

.nc-pagoda::before {
  content: @nc-pagoda;
}

.nc-paint-16::before {
  content: @nc-paint-16;
}

.nc-paint-37::before {
  content: @nc-paint-37;
}

.nc-paint-38::before {
  content: @nc-paint-38;
}

.nc-paint-brush::before {
  content: @nc-paint-brush;
}

.nc-paint-bucket-39::before {
  content: @nc-paint-bucket-39;
}

.nc-paint-bucket-40::before {
  content: @nc-paint-bucket-40;
}

.nc-pajamas::before {
  content: @nc-pajamas;
}

.nc-palette::before {
  content: @nc-palette;
}

.nc-pallet::before {
  content: @nc-pallet;
}

.nc-pallet-stacked-boxes::before {
  content: @nc-pallet-stacked-boxes;
}

.nc-palm-tree::before {
  content: @nc-palm-tree;
}

.nc-pan::before {
  content: @nc-pan;
}

.nc-pancake::before {
  content: @nc-pancake;
}

.nc-panda::before {
  content: @nc-panda;
}

.nc-panel::before {
  content: @nc-panel;
}

.nc-pantone::before {
  content: @nc-pantone;
}

.nc-paper::before {
  content: @nc-paper;
}

.nc-paper-design::before {
  content: @nc-paper-design;
}

.nc-paper-dev::before {
  content: @nc-paper-dev;
}

.nc-paper-diploma::before {
  content: @nc-paper-diploma;
}

.nc-parachute::before {
  content: @nc-parachute;
}

.nc-paragraph::before {
  content: @nc-paragraph;
}

.nc-paragraph-2::before {
  content: @nc-paragraph-2;
}

.nc-paralympic-games::before {
  content: @nc-paralympic-games;
}

.nc-parenting::before {
  content: @nc-parenting;
}

.nc-paris-tower::before {
  content: @nc-paris-tower;
}

.nc-park::before {
  content: @nc-park;
}

.nc-parking::before {
  content: @nc-parking;
}

.nc-parking-sensors::before {
  content: @nc-parking-sensors;
}

.nc-parrot::before {
  content: @nc-parrot;
}

.nc-party::before {
  content: @nc-party;
}

.nc-passenger::before {
  content: @nc-passenger;
}

.nc-passport::before {
  content: @nc-passport;
}

.nc-password::before {
  content: @nc-password;
}

.nc-pasta::before {
  content: @nc-pasta;
}

.nc-patch::before {
  content: @nc-patch;
}

.nc-patch-19::before {
  content: @nc-patch-19;
}

.nc-patch-34::before {
  content: @nc-patch-34;
}

.nc-path-exclude::before {
  content: @nc-path-exclude;
}

.nc-path-intersect::before {
  content: @nc-path-intersect;
}

.nc-path-minus::before {
  content: @nc-path-minus;
}

.nc-path-unite::before {
  content: @nc-path-unite;
}

.nc-pattern-recognition::before {
  content: @nc-pattern-recognition;
}

.nc-paw::before {
  content: @nc-paw;
}

.nc-payee::before {
  content: @nc-payee;
}

.nc-payment::before {
  content: @nc-payment;
}

.nc-payment-method::before {
  content: @nc-payment-method;
}

.nc-payor::before {
  content: @nc-payor;
}

.nc-pc::before {
  content: @nc-pc;
}

.nc-pc-monitor::before {
  content: @nc-pc-monitor;
}

.nc-pc-mouse::before {
  content: @nc-pc-mouse;
}

.nc-pc-play-media::before {
  content: @nc-pc-play-media;
}

.nc-pci-card::before {
  content: @nc-pci-card;
}

.nc-peanut::before {
  content: @nc-peanut;
}

.nc-pear::before {
  content: @nc-pear;
}

.nc-peas::before {
  content: @nc-peas;
}

.nc-pectoral-machine::before {
  content: @nc-pectoral-machine;
}

.nc-pen::before {
  content: @nc-pen;
}

.nc-pen-01::before {
  content: @nc-pen-01;
}

.nc-pen-2::before {
  content: @nc-pen-2;
}

.nc-pen-23::before {
  content: @nc-pen-23;
}

.nc-pen-tool::before {
  content: @nc-pen-tool;
}

.nc-pencil::before {
  content: @nc-pencil;
}

.nc-pencil-47::before {
  content: @nc-pencil-47;
}

.nc-pendant-lighting::before {
  content: @nc-pendant-lighting;
}

.nc-pendulum::before {
  content: @nc-pendulum;
}

.nc-penguin::before {
  content: @nc-penguin;
}

.nc-pennant::before {
  content: @nc-pennant;
}

.nc-people-network::before {
  content: @nc-people-network;
}

.nc-pepper::before {
  content: @nc-pepper;
}

.nc-percent-sign::before {
  content: @nc-percent-sign;
}

.nc-percentage-38::before {
  content: @nc-percentage-38;
}

.nc-percentage-39::before {
  content: @nc-percentage-39;
}

.nc-perfume::before {
  content: @nc-perfume;
}

.nc-personal-trainer::before {
  content: @nc-personal-trainer;
}

.nc-pesticide::before {
  content: @nc-pesticide;
}

.nc-pet-food::before {
  content: @nc-pet-food;
}

.nc-pharmacy::before {
  content: @nc-pharmacy;
}

.nc-phone::before {
  content: @nc-phone;
}

.nc-phone-button::before {
  content: @nc-phone-button;
}

.nc-phone-call::before {
  content: @nc-phone-call;
}

.nc-phone-call-end::before {
  content: @nc-phone-call-end;
}

.nc-phone-camera-back::before {
  content: @nc-phone-camera-back;
}

.nc-phone-camera-front::before {
  content: @nc-phone-camera-front;
}

.nc-phone-charging::before {
  content: @nc-phone-charging;
}

.nc-phone-charging-2::before {
  content: @nc-phone-charging-2;
}

.nc-phone-charging-3::before {
  content: @nc-phone-charging-3;
}

.nc-phone-dock::before {
  content: @nc-phone-dock;
}

.nc-phone-heart::before {
  content: @nc-phone-heart;
}

.nc-phone-heartbeat::before {
  content: @nc-phone-heartbeat;
}

.nc-phone-music::before {
  content: @nc-phone-music;
}

.nc-phone-toolbar::before {
  content: @nc-phone-toolbar;
}

.nc-photo::before {
  content: @nc-photo;
}

.nc-photo-album::before {
  content: @nc-photo-album;
}

.nc-photo-editor::before {
  content: @nc-photo-editor;
}

.nc-photo-frame::before {
  content: @nc-photo-frame;
}

.nc-photo-not-allowed::before {
  content: @nc-photo-not-allowed;
}

.nc-piano::before {
  content: @nc-piano;
}

.nc-piano-2::before {
  content: @nc-piano-2;
}

.nc-pickaxe::before {
  content: @nc-pickaxe;
}

.nc-pickle::before {
  content: @nc-pickle;
}

.nc-picnic-basket::before {
  content: @nc-picnic-basket;
}

.nc-picture::before {
  content: @nc-picture;
}

.nc-pie::before {
  content: @nc-pie;
}

.nc-pig::before {
  content: @nc-pig;
}

.nc-pig-2::before {
  content: @nc-pig-2;
}

.nc-pilcrow::before {
  content: @nc-pilcrow;
}

.nc-pilgrim-hat::before {
  content: @nc-pilgrim-hat;
}

.nc-pill-42::before {
  content: @nc-pill-42;
}

.nc-pill-43::before {
  content: @nc-pill-43;
}

.nc-pill-bottle::before {
  content: @nc-pill-bottle;
}

.nc-pin::before {
  content: @nc-pin;
}

.nc-pin-2::before {
  content: @nc-pin-2;
}

.nc-pin-3::before {
  content: @nc-pin-3;
}

.nc-pin-add::before {
  content: @nc-pin-add;
}

.nc-pin-add-2::before {
  content: @nc-pin-add-2;
}

.nc-pin-check::before {
  content: @nc-pin-check;
}

.nc-pin-copy::before {
  content: @nc-pin-copy;
}

.nc-pin-delete::before {
  content: @nc-pin-delete;
}

.nc-pin-edit::before {
  content: @nc-pin-edit;
}

.nc-pin-heart::before {
  content: @nc-pin-heart;
}

.nc-pin-remove::before {
  content: @nc-pin-remove;
}

.nc-pin-remove-2::before {
  content: @nc-pin-remove-2;
}

.nc-pin-search::before {
  content: @nc-pin-search;
}

.nc-pin-security::before {
  content: @nc-pin-security;
}

.nc-pin-settings::before {
  content: @nc-pin-settings;
}

.nc-pin-share::before {
  content: @nc-pin-share;
}

.nc-pin-star::before {
  content: @nc-pin-star;
}

.nc-pin-sync::before {
  content: @nc-pin-sync;
}

.nc-pin-time::before {
  content: @nc-pin-time;
}

.nc-pin-user::before {
  content: @nc-pin-user;
}

.nc-pinch::before {
  content: @nc-pinch;
}

.nc-pineapple::before {
  content: @nc-pineapple;
}

.nc-pins::before {
  content: @nc-pins;
}

.nc-pipe::before {
  content: @nc-pipe;
}

.nc-pirate::before {
  content: @nc-pirate;
}

.nc-pisa-tower::before {
  content: @nc-pisa-tower;
}

.nc-pizza::before {
  content: @nc-pizza;
}

.nc-pizza-slice::before {
  content: @nc-pizza-slice;
}

.nc-plane::before {
  content: @nc-plane;
}

.nc-planet::before {
  content: @nc-planet;
}

.nc-plant-leaf::before {
  content: @nc-plant-leaf;
}

.nc-plant-soil::before {
  content: @nc-plant-soil;
}

.nc-plant-vase::before {
  content: @nc-plant-vase;
}

.nc-plate::before {
  content: @nc-plate;
}

.nc-play-media::before {
  content: @nc-play-media;
}

.nc-play-movie::before {
  content: @nc-play-movie;
}

.nc-player::before {
  content: @nc-player;
}

.nc-playground::before {
  content: @nc-playground;
}

.nc-playing-cards::before {
  content: @nc-playing-cards;
}

.nc-playlist::before {
  content: @nc-playlist;
}

.nc-plug::before {
  content: @nc-plug;
}

.nc-plug-2::before {
  content: @nc-plug-2;
}

.nc-podcast::before {
  content: @nc-podcast;
}

.nc-podcast-mic::before {
  content: @nc-podcast-mic;
}

.nc-podium::before {
  content: @nc-podium;
}

.nc-podium-trophy::before {
  content: @nc-podium-trophy;
}

.nc-point-a::before {
  content: @nc-point-a;
}

.nc-point-b::before {
  content: @nc-point-b;
}

.nc-pointing-down::before {
  content: @nc-pointing-down;
}

.nc-pointing-left::before {
  content: @nc-pointing-left;
}

.nc-pointing-right::before {
  content: @nc-pointing-right;
}

.nc-pointing-up::before {
  content: @nc-pointing-up;
}

.nc-polaroid::before {
  content: @nc-polaroid;
}

.nc-polaroid-photo::before {
  content: @nc-polaroid-photo;
}

.nc-polaroid-portrait::before {
  content: @nc-polaroid-portrait;
}

.nc-polaroid-shot-delete::before {
  content: @nc-polaroid-shot-delete;
}

.nc-polaroid-shot-new::before {
  content: @nc-polaroid-shot-new;
}

.nc-polaroid-shots::before {
  content: @nc-polaroid-shots;
}

.nc-police-officer::before {
  content: @nc-police-officer;
}

.nc-poop::before {
  content: @nc-poop;
}

.nc-popcorn::before {
  content: @nc-popcorn;
}

.nc-pos::before {
  content: @nc-pos;
}

.nc-position::before {
  content: @nc-position;
}

.nc-position-marker::before {
  content: @nc-position-marker;
}

.nc-position-pin::before {
  content: @nc-position-pin;
}

.nc-position-user::before {
  content: @nc-position-user;
}

.nc-positive-judgement::before {
  content: @nc-positive-judgement;
}

.nc-pot::before {
  content: @nc-pot;
}

.nc-potato::before {
  content: @nc-potato;
}

.nc-potion::before {
  content: @nc-potion;
}

.nc-power-level::before {
  content: @nc-power-level;
}

.nc-power-lifting::before {
  content: @nc-power-lifting;
}

.nc-power-rack::before {
  content: @nc-power-rack;
}

.nc-pram::before {
  content: @nc-pram;
}

.nc-preferences::before {
  content: @nc-preferences;
}

.nc-pregnancy-test::before {
  content: @nc-pregnancy-test;
}

.nc-pregnant-woman::before {
  content: @nc-pregnant-woman;
}

.nc-present::before {
  content: @nc-present;
}

.nc-presentation::before {
  content: @nc-presentation;
}

.nc-print::before {
  content: @nc-print;
}

.nc-printer::before {
  content: @nc-printer;
}

.nc-priority-high::before {
  content: @nc-priority-high;
}

.nc-priority-highest::before {
  content: @nc-priority-highest;
}

.nc-priority-low::before {
  content: @nc-priority-low;
}

.nc-priority-lowest::before {
  content: @nc-priority-lowest;
}

.nc-priority-normal::before {
  content: @nc-priority-normal;
}

.nc-privacy::before {
  content: @nc-privacy;
}

.nc-privacy-policy::before {
  content: @nc-privacy-policy;
}

.nc-privacy-settings::before {
  content: @nc-privacy-settings;
}

.nc-profile::before {
  content: @nc-profile;
}

.nc-progress::before {
  content: @nc-progress;
}

.nc-progress-2::before {
  content: @nc-progress-2;
}

.nc-progress-indicator::before {
  content: @nc-progress-indicator;
}

.nc-projector::before {
  content: @nc-projector;
}

.nc-property::before {
  content: @nc-property;
}

.nc-property-agreement::before {
  content: @nc-property-agreement;
}

.nc-property-app::before {
  content: @nc-property-app;
}

.nc-property-for-sale::before {
  content: @nc-property-for-sale;
}

.nc-property-location::before {
  content: @nc-property-location;
}

.nc-property-sold::before {
  content: @nc-property-sold;
}

.nc-property-to-rent::before {
  content: @nc-property-to-rent;
}

.nc-prosciutto::before {
  content: @nc-prosciutto;
}

.nc-prototype::before {
  content: @nc-prototype;
}

.nc-pruning-saws::before {
  content: @nc-pruning-saws;
}

.nc-pulse::before {
  content: @nc-pulse;
}

.nc-pulse-chart::before {
  content: @nc-pulse-chart;
}

.nc-pulse-sleep::before {
  content: @nc-pulse-sleep;
}

.nc-pumpkin::before {
  content: @nc-pumpkin;
}

.nc-puzzle-09::before {
  content: @nc-puzzle-09;
}

.nc-puzzle-10::before {
  content: @nc-puzzle-10;
}

.nc-puzzle-toy::before {
  content: @nc-puzzle-toy;
}

.nc-puzzled::before {
  content: @nc-puzzled;
}

.nc-pyramid::before {
  content: @nc-pyramid;
}

.nc-question-mark::before {
  content: @nc-question-mark;
}

.nc-questionnaire::before {
  content: @nc-questionnaire;
}

.nc-quite-happy::before {
  content: @nc-quite-happy;
}

.nc-quote::before {
  content: @nc-quote;
}

.nc-r-chat::before {
  content: @nc-r-chat;
}

.nc-r-down-left-arrows::before {
  content: @nc-r-down-left-arrows;
}

.nc-r-down-right-arrows::before {
  content: @nc-r-down-right-arrows;
}

.nc-r-up-left-arrows::before {
  content: @nc-r-up-left-arrows;
}

.nc-r-up-right-arrows::before {
  content: @nc-r-up-right-arrows;
}

.nc-rabbit::before {
  content: @nc-rabbit;
}

.nc-radar::before {
  content: @nc-radar;
}

.nc-radiation::before {
  content: @nc-radiation;
}

.nc-radio::before {
  content: @nc-radio;
}

.nc-radio-btn::before {
  content: @nc-radio-btn;
}

.nc-radio-btn-checked::before {
  content: @nc-radio-btn-checked;
}

.nc-rain::before {
  content: @nc-rain;
}

.nc-rain-hail::before {
  content: @nc-rain-hail;
}

.nc-rainbow::before {
  content: @nc-rainbow;
}

.nc-rake::before {
  content: @nc-rake;
}

.nc-ram::before {
  content: @nc-ram;
}

.nc-ram-2::before {
  content: @nc-ram-2;
}

.nc-random::before {
  content: @nc-random;
}

.nc-ranking::before {
  content: @nc-ranking;
}

.nc-rat::before {
  content: @nc-rat;
}

.nc-rat-head::before {
  content: @nc-rat-head;
}

.nc-rate-down::before {
  content: @nc-rate-down;
}

.nc-rate-up::before {
  content: @nc-rate-up;
}

.nc-raw-image::before {
  content: @nc-raw-image;
}

.nc-razor::before {
  content: @nc-razor;
}

.nc-read::before {
  content: @nc-read;
}

.nc-reading::before {
  content: @nc-reading;
}

.nc-reading-tablet::before {
  content: @nc-reading-tablet;
}

.nc-real-estate::before {
  content: @nc-real-estate;
}

.nc-receipt::before {
  content: @nc-receipt;
}

.nc-receipt-list-42::before {
  content: @nc-receipt-list-42;
}

.nc-receipt-list-43::before {
  content: @nc-receipt-list-43;
}

.nc-recipe::before {
  content: @nc-recipe;
}

.nc-recipe-book-46::before {
  content: @nc-recipe-book-46;
}

.nc-recipe-book-47::before {
  content: @nc-recipe-book-47;
}

.nc-recipe-create::before {
  content: @nc-recipe-create;
}

.nc-record-player::before {
  content: @nc-record-player;
}

.nc-recycling::before {
  content: @nc-recycling;
}

.nc-redo::before {
  content: @nc-redo;
}

.nc-referee::before {
  content: @nc-referee;
}

.nc-refresh::before {
  content: @nc-refresh;
}

.nc-refresh-01::before {
  content: @nc-refresh-01;
}

.nc-refresh-02::before {
  content: @nc-refresh-02;
}

.nc-refund::before {
  content: @nc-refund;
}

.nc-reload::before {
  content: @nc-reload;
}

.nc-remote-control::before {
  content: @nc-remote-control;
}

.nc-remove::before {
  content: @nc-remove;
}

.nc-remove-fav::before {
  content: @nc-remove-fav;
}

.nc-remove-favorite::before {
  content: @nc-remove-favorite;
}

.nc-remove-like::before {
  content: @nc-remove-like;
}

.nc-repeat::before {
  content: @nc-repeat;
}

.nc-repeat-cycle::before {
  content: @nc-repeat-cycle;
}

.nc-replay::before {
  content: @nc-replay;
}

.nc-reply::before {
  content: @nc-reply;
}

.nc-reply-all::before {
  content: @nc-reply-all;
}

.nc-reply-arrow::before {
  content: @nc-reply-arrow;
}

.nc-research::before {
  content: @nc-research;
}

.nc-reservation::before {
  content: @nc-reservation;
}

.nc-resistance-band::before {
  content: @nc-resistance-band;
}

.nc-resize-x::before {
  content: @nc-resize-x;
}

.nc-resize-y::before {
  content: @nc-resize-y;
}

.nc-respond-arrow::before {
  content: @nc-respond-arrow;
}

.nc-restaurant-menu::before {
  content: @nc-restaurant-menu;
}

.nc-restore::before {
  content: @nc-restore;
}

.nc-rice::before {
  content: @nc-rice;
}

.nc-right-arrow::before {
  content: @nc-right-arrow;
}

.nc-rim::before {
  content: @nc-rim;
}

.nc-ring::before {
  content: @nc-ring;
}

.nc-rings::before {
  content: @nc-rings;
}

.nc-rio-statue::before {
  content: @nc-rio-statue;
}

.nc-ripple-anim::before {
  content: @nc-ripple-anim;
}

.nc-road::before {
  content: @nc-road;
}

.nc-road-2::before {
  content: @nc-road-2;
}

.nc-road-sign-left::before {
  content: @nc-road-sign-left;
}

.nc-road-sign-right::before {
  content: @nc-road-sign-right;
}

.nc-roadmap::before {
  content: @nc-roadmap;
}

.nc-roast-chicken::before {
  content: @nc-roast-chicken;
}

.nc-roast-turkey::before {
  content: @nc-roast-turkey;
}

.nc-robot::before {
  content: @nc-robot;
}

.nc-robot-cleaner::before {
  content: @nc-robot-cleaner;
}

.nc-robotic-arm::before {
  content: @nc-robotic-arm;
}

.nc-rock::before {
  content: @nc-rock;
}

.nc-rolling-pin::before {
  content: @nc-rolling-pin;
}

.nc-romantic-dinner::before {
  content: @nc-romantic-dinner;
}

.nc-romantic-restaurant::before {
  content: @nc-romantic-restaurant;
}

.nc-rotate-22::before {
  content: @nc-rotate-22;
}

.nc-rotate-23::before {
  content: @nc-rotate-23;
}

.nc-rotate-camera::before {
  content: @nc-rotate-camera;
}

.nc-rotate-left::before {
  content: @nc-rotate-left;
}

.nc-rotate-right::before {
  content: @nc-rotate-right;
}

.nc-rotating-bars-anim::before {
  content: @nc-rotating-bars-anim;
}

.nc-roulette::before {
  content: @nc-roulette;
}

.nc-round-dollar::before {
  content: @nc-round-dollar;
}

.nc-round-euro::before {
  content: @nc-round-euro;
}

.nc-round-pound::before {
  content: @nc-round-pound;
}

.nc-round-yen::before {
  content: @nc-round-yen;
}

.nc-route::before {
  content: @nc-route;
}

.nc-route-alert::before {
  content: @nc-route-alert;
}

.nc-route-close::before {
  content: @nc-route-close;
}

.nc-route-open::before {
  content: @nc-route-open;
}

.nc-router::before {
  content: @nc-router;
}

.nc-row-machine::before {
  content: @nc-row-machine;
}

.nc-row-table::before {
  content: @nc-row-table;
}

.nc-rowing::before {
  content: @nc-rowing;
}

.nc-rowing-oars::before {
  content: @nc-rowing-oars;
}

.nc-rugby::before {
  content: @nc-rugby;
}

.nc-rugby-ball::before {
  content: @nc-rugby-ball;
}

.nc-ruler-pencil::before {
  content: @nc-ruler-pencil;
}

.nc-run-shoes::before {
  content: @nc-run-shoes;
}

.nc-runny-nose::before {
  content: @nc-runny-nose;
}

.nc-s-add::before {
  content: @nc-s-add;
}

.nc-s-ban::before {
  content: @nc-s-ban;
}

.nc-s-check::before {
  content: @nc-s-check;
}

.nc-s-delete::before {
  content: @nc-s-delete;
}

.nc-s-edit::before {
  content: @nc-s-edit;
}

.nc-s-info::before {
  content: @nc-s-info;
}

.nc-s-pulse::before {
  content: @nc-s-pulse;
}

.nc-s-question::before {
  content: @nc-s-question;
}

.nc-s-remove::before {
  content: @nc-s-remove;
}

.nc-s-warning::before {
  content: @nc-s-warning;
}

.nc-sad::before {
  content: @nc-sad;
}

.nc-safe::before {
  content: @nc-safe;
}

.nc-sagrada-familia::before {
  content: @nc-sagrada-familia;
}

.nc-saint-basil-cathedral::before {
  content: @nc-saint-basil-cathedral;
}

.nc-salad::before {
  content: @nc-salad;
}

.nc-sale::before {
  content: @nc-sale;
}

.nc-salt::before {
  content: @nc-salt;
}

.nc-santa-hat::before {
  content: @nc-santa-hat;
}

.nc-satellite::before {
  content: @nc-satellite;
}

.nc-satellite-dish::before {
  content: @nc-satellite-dish;
}

.nc-satisfied::before {
  content: @nc-satisfied;
}

.nc-sauna::before {
  content: @nc-sauna;
}

.nc-sausage::before {
  content: @nc-sausage;
}

.nc-save-for-later::before {
  content: @nc-save-for-later;
}

.nc-save-planet::before {
  content: @nc-save-planet;
}

.nc-save-the-date::before {
  content: @nc-save-the-date;
}

.nc-save-to-list::before {
  content: @nc-save-to-list;
}

.nc-saved-items::before {
  content: @nc-saved-items;
}

.nc-savings::before {
  content: @nc-savings;
}

.nc-saxophone::before {
  content: @nc-saxophone;
}

.nc-scale::before {
  content: @nc-scale;
}

.nc-scale-2::before {
  content: @nc-scale-2;
}

.nc-scale-3::before {
  content: @nc-scale-3;
}

.nc-scale-4::before {
  content: @nc-scale-4;
}

.nc-scale-down::before {
  content: @nc-scale-down;
}

.nc-scale-up::before {
  content: @nc-scale-up;
}

.nc-scan::before {
  content: @nc-scan;
}

.nc-scarecrow::before {
  content: @nc-scarecrow;
}

.nc-scarf::before {
  content: @nc-scarf;
}

.nc-scented-candle::before {
  content: @nc-scented-candle;
}

.nc-school::before {
  content: @nc-school;
}

.nc-scissors::before {
  content: @nc-scissors;
}

.nc-scissors-2::before {
  content: @nc-scissors-2;
}

.nc-scissors-dashed::before {
  content: @nc-scissors-dashed;
}

.nc-scooter::before {
  content: @nc-scooter;
}

.nc-scotch::before {
  content: @nc-scotch;
}

.nc-screen-enlarge::before {
  content: @nc-screen-enlarge;
}

.nc-screen-expand::before {
  content: @nc-screen-expand;
}

.nc-screen-maximize::before {
  content: @nc-screen-maximize;
}

.nc-screen-reader::before {
  content: @nc-screen-reader;
}

.nc-screen-rotation::before {
  content: @nc-screen-rotation;
}

.nc-screen-sharing-2::before {
  content: @nc-screen-sharing-2;
}

.nc-screen-sharing-off-2::before {
  content: @nc-screen-sharing-off-2;
}

.nc-screen-touch::before {
  content: @nc-screen-touch;
}

.nc-scroll-horizontal::before {
  content: @nc-scroll-horizontal;
}

.nc-scroll-vertical::before {
  content: @nc-scroll-vertical;
}

.nc-sd-card::before {
  content: @nc-sd-card;
}

.nc-search::before {
  content: @nc-search;
}

.nc-search-content::before {
  content: @nc-search-content;
}

.nc-search-property::before {
  content: @nc-search-property;
}

.nc-search-user::before {
  content: @nc-search-user;
}

.nc-search-zoom-in::before {
  content: @nc-search-zoom-in;
}

.nc-search-zoom-out::before {
  content: @nc-search-zoom-out;
}

.nc-seat::before {
  content: @nc-seat;
}

.nc-seatbelt::before {
  content: @nc-seatbelt;
}

.nc-security::before {
  content: @nc-security;
}

.nc-security-gate::before {
  content: @nc-security-gate;
}

.nc-security-officer::before {
  content: @nc-security-officer;
}

.nc-segmentation::before {
  content: @nc-segmentation;
}

.nc-select::before {
  content: @nc-select;
}

.nc-selection::before {
  content: @nc-selection;
}

.nc-selfie::before {
  content: @nc-selfie;
}

.nc-selfie-2::before {
  content: @nc-selfie-2;
}

.nc-send::before {
  content: @nc-send;
}

.nc-send-message::before {
  content: @nc-send-message;
}

.nc-send-to-phone::before {
  content: @nc-send-to-phone;
}

.nc-sensor::before {
  content: @nc-sensor;
}

.nc-separate::before {
  content: @nc-separate;
}

.nc-separate-branch::before {
  content: @nc-separate-branch;
}

.nc-separate-directions::before {
  content: @nc-separate-directions;
}

.nc-server::before {
  content: @nc-server;
}

.nc-server-rack::before {
  content: @nc-server-rack;
}

.nc-settings::before {
  content: @nc-settings;
}

.nc-settings-gear::before {
  content: @nc-settings-gear;
}

.nc-settings-wheel::before {
  content: @nc-settings-wheel;
}

.nc-setup-options::before {
  content: @nc-setup-options;
}

.nc-setup-preferences::before {
  content: @nc-setup-preferences;
}

.nc-setup-tools::before {
  content: @nc-setup-tools;
}

.nc-seven::before {
  content: @nc-seven;
}

.nc-shaker::before {
  content: @nc-shaker;
}

.nc-shape-adjust::before {
  content: @nc-shape-adjust;
}

.nc-shape-arrow::before {
  content: @nc-shape-arrow;
}

.nc-shape-circle::before {
  content: @nc-shape-circle;
}

.nc-shape-custom::before {
  content: @nc-shape-custom;
}

.nc-shape-line::before {
  content: @nc-shape-line;
}

.nc-shape-oval::before {
  content: @nc-shape-oval;
}

.nc-shape-polygon::before {
  content: @nc-shape-polygon;
}

.nc-shape-polygon-2::before {
  content: @nc-shape-polygon-2;
}

.nc-shape-rectangle::before {
  content: @nc-shape-rectangle;
}

.nc-shape-square::before {
  content: @nc-shape-square;
}

.nc-shape-star::before {
  content: @nc-shape-star;
}

.nc-shape-triangle::before {
  content: @nc-shape-triangle;
}

.nc-shape-triangle-2::before {
  content: @nc-shape-triangle-2;
}

.nc-shapes::before {
  content: @nc-shapes;
}

.nc-share::before {
  content: @nc-share;
}

.nc-share-2::before {
  content: @nc-share-2;
}

.nc-share-3::before {
  content: @nc-share-3;
}

.nc-sharing::before {
  content: @nc-sharing;
}

.nc-shark::before {
  content: @nc-shark;
}

.nc-shark-2::before {
  content: @nc-shark-2;
}

.nc-sharpen::before {
  content: @nc-sharpen;
}

.nc-sharpener::before {
  content: @nc-sharpener;
}

.nc-sheep::before {
  content: @nc-sheep;
}

.nc-shell::before {
  content: @nc-shell;
}

.nc-shield::before {
  content: @nc-shield;
}

.nc-shinto::before {
  content: @nc-shinto;
}

.nc-shipping-container::before {
  content: @nc-shipping-container;
}

.nc-shirt::before {
  content: @nc-shirt;
}

.nc-shirt-business::before {
  content: @nc-shirt-business;
}

.nc-shirt-buttons::before {
  content: @nc-shirt-buttons;
}

.nc-shirt-neck::before {
  content: @nc-shirt-neck;
}

.nc-shoe-man::before {
  content: @nc-shoe-man;
}

.nc-shoe-woman::before {
  content: @nc-shoe-woman;
}

.nc-shop::before {
  content: @nc-shop;
}

.nc-shop-location::before {
  content: @nc-shop-location;
}

.nc-shopping-bag::before {
  content: @nc-shopping-bag;
}

.nc-shopping-cart::before {
  content: @nc-shopping-cart;
}

.nc-shopping-cart-2::before {
  content: @nc-shopping-cart-2;
}

.nc-shopping-label::before {
  content: @nc-shopping-label;
}

.nc-shopping-tag::before {
  content: @nc-shopping-tag;
}

.nc-shorts::before {
  content: @nc-shorts;
}

.nc-shotgun::before {
  content: @nc-shotgun;
}

.nc-shovel::before {
  content: @nc-shovel;
}

.nc-show::before {
  content: @nc-show;
}

.nc-shower::before {
  content: @nc-shower;
}

.nc-shrimp::before {
  content: @nc-shrimp;
}

.nc-shuffle::before {
  content: @nc-shuffle;
}

.nc-shuffle-2::before {
  content: @nc-shuffle-2;
}

.nc-shuttle::before {
  content: @nc-shuttle;
}

.nc-shuttlecock::before {
  content: @nc-shuttlecock;
}

.nc-shy::before {
  content: @nc-shy;
}

.nc-sick::before {
  content: @nc-sick;
}

.nc-sickle::before {
  content: @nc-sickle;
}

.nc-sickle-2::before {
  content: @nc-sickle-2;
}

.nc-sidebar::before {
  content: @nc-sidebar;
}

.nc-sign::before {
  content: @nc-sign;
}

.nc-sign-board::before {
  content: @nc-sign-board;
}

.nc-sign-down::before {
  content: @nc-sign-down;
}

.nc-sign-left::before {
  content: @nc-sign-left;
}

.nc-sign-right::before {
  content: @nc-sign-right;
}

.nc-sign-up::before {
  content: @nc-sign-up;
}

.nc-signal::before {
  content: @nc-signal;
}

.nc-signature::before {
  content: @nc-signature;
}

.nc-silly::before {
  content: @nc-silly;
}

.nc-sim-card::before {
  content: @nc-sim-card;
}

.nc-single-bed::before {
  content: @nc-single-bed;
}

.nc-sink::before {
  content: @nc-sink;
}

.nc-sink-faucet::before {
  content: @nc-sink-faucet;
}

.nc-six::before {
  content: @nc-six;
}

.nc-size-large::before {
  content: @nc-size-large;
}

.nc-size-medium::before {
  content: @nc-size-medium;
}

.nc-size-small::before {
  content: @nc-size-small;
}

.nc-skateboard::before {
  content: @nc-skateboard;
}

.nc-skateboard-2::before {
  content: @nc-skateboard-2;
}

.nc-skateboarding::before {
  content: @nc-skateboarding;
}

.nc-skating::before {
  content: @nc-skating;
}

.nc-skiing::before {
  content: @nc-skiing;
}

.nc-skipping-rope::before {
  content: @nc-skipping-rope;
}

.nc-skirt::before {
  content: @nc-skirt;
}

.nc-skull::before {
  content: @nc-skull;
}

.nc-skull-2::before {
  content: @nc-skull-2;
}

.nc-slacks-12::before {
  content: @nc-slacks-12;
}

.nc-slacks-13::before {
  content: @nc-slacks-13;
}

.nc-sleep::before {
  content: @nc-sleep;
}

.nc-sleep-2::before {
  content: @nc-sleep-2;
}

.nc-sleeping-baby::before {
  content: @nc-sleeping-baby;
}

.nc-slice::before {
  content: @nc-slice;
}

.nc-slide-left::before {
  content: @nc-slide-left;
}

.nc-slide-right::before {
  content: @nc-slide-right;
}

.nc-slider::before {
  content: @nc-slider;
}

.nc-slideshow-anim::before {
  content: @nc-slideshow-anim;
}

.nc-slippers::before {
  content: @nc-slippers;
}

.nc-slot-machine::before {
  content: @nc-slot-machine;
}

.nc-sloth::before {
  content: @nc-sloth;
}

.nc-smart::before {
  content: @nc-smart;
}

.nc-smart-house::before {
  content: @nc-smart-house;
}

.nc-smartphone::before {
  content: @nc-smartphone;
}

.nc-smartwatch::before {
  content: @nc-smartwatch;
}

.nc-smile::before {
  content: @nc-smile;
}

.nc-smiling-face-glasses::before {
  content: @nc-smiling-face-glasses;
}

.nc-smiling-face-sunglasses::before {
  content: @nc-smiling-face-sunglasses;
}

.nc-smoking::before {
  content: @nc-smoking;
}

.nc-smoothie::before {
  content: @nc-smoothie;
}

.nc-snack::before {
  content: @nc-snack;
}

.nc-snake::before {
  content: @nc-snake;
}

.nc-sneeze::before {
  content: @nc-sneeze;
}

.nc-sniper-rifle::before {
  content: @nc-sniper-rifle;
}

.nc-snorkel-mask::before {
  content: @nc-snorkel-mask;
}

.nc-snow::before {
  content: @nc-snow;
}

.nc-snow-ball::before {
  content: @nc-snow-ball;
}

.nc-snowboard::before {
  content: @nc-snowboard;
}

.nc-snowboarding::before {
  content: @nc-snowboarding;
}

.nc-snowman::before {
  content: @nc-snowman;
}

.nc-snowman-head::before {
  content: @nc-snowman-head;
}

.nc-soap::before {
  content: @nc-soap;
}

.nc-soccer::before {
  content: @nc-soccer;
}

.nc-soccer-ball::before {
  content: @nc-soccer-ball;
}

.nc-soccer-field::before {
  content: @nc-soccer-field;
}

.nc-social-distancing::before {
  content: @nc-social-distancing;
}

.nc-social-sharing::before {
  content: @nc-social-sharing;
}

.nc-sock::before {
  content: @nc-sock;
}

.nc-socket::before {
  content: @nc-socket;
}

.nc-socket-europe-1::before {
  content: @nc-socket-europe-1;
}

.nc-socket-europe-2::before {
  content: @nc-socket-europe-2;
}

.nc-socket-uk::before {
  content: @nc-socket-uk;
}

.nc-sofa::before {
  content: @nc-sofa;
}

.nc-soft-drink::before {
  content: @nc-soft-drink;
}

.nc-soldier::before {
  content: @nc-soldier;
}

.nc-solider-helmet::before {
  content: @nc-solider-helmet;
}

.nc-sort-tool::before {
  content: @nc-sort-tool;
}

.nc-sound::before {
  content: @nc-sound;
}

.nc-sound-wave::before {
  content: @nc-sound-wave;
}

.nc-soundwave::before {
  content: @nc-soundwave;
}

.nc-soup::before {
  content: @nc-soup;
}

.nc-soy-sauce::before {
  content: @nc-soy-sauce;
}

.nc-spa::before {
  content: @nc-spa;
}

.nc-spa-rocks::before {
  content: @nc-spa-rocks;
}

.nc-space-divider::before {
  content: @nc-space-divider;
}

.nc-spaceship::before {
  content: @nc-spaceship;
}

.nc-spade::before {
  content: @nc-spade;
}

.nc-spades-suit::before {
  content: @nc-spades-suit;
}

.nc-sparks::before {
  content: @nc-sparks;
}

.nc-speaker::before {
  content: @nc-speaker;
}

.nc-speaker-2::before {
  content: @nc-speaker-2;
}

.nc-spectrum::before {
  content: @nc-spectrum;
}

.nc-speechless::before {
  content: @nc-speechless;
}

.nc-speedometer::before {
  content: @nc-speedometer;
}

.nc-sperm::before {
  content: @nc-sperm;
}

.nc-sphinx::before {
  content: @nc-sphinx;
}

.nc-spider::before {
  content: @nc-spider;
}

.nc-spinning-bike::before {
  content: @nc-spinning-bike;
}

.nc-spiteful::before {
  content: @nc-spiteful;
}

.nc-split::before {
  content: @nc-split;
}

.nc-split-branch::before {
  content: @nc-split-branch;
}

.nc-sport-bag::before {
  content: @nc-sport-bag;
}

.nc-sport-mode::before {
  content: @nc-sport-mode;
}

.nc-sports-bra::before {
  content: @nc-sports-bra;
}

.nc-sports-fan::before {
  content: @nc-sports-fan;
}

.nc-sports-tank::before {
  content: @nc-sports-tank;
}

.nc-spray-bottle::before {
  content: @nc-spray-bottle;
}

.nc-spray-can::before {
  content: @nc-spray-can;
}

.nc-square-marker::before {
  content: @nc-square-marker;
}

.nc-square-pin::before {
  content: @nc-square-pin;
}

.nc-squares-anim::before {
  content: @nc-squares-anim;
}

.nc-squares-anim-2::before {
  content: @nc-squares-anim-2;
}

.nc-ssd::before {
  content: @nc-ssd;
}

.nc-stack::before {
  content: @nc-stack;
}

.nc-stadium::before {
  content: @nc-stadium;
}

.nc-stair-climber::before {
  content: @nc-stair-climber;
}

.nc-stairs::before {
  content: @nc-stairs;
}

.nc-stamp::before {
  content: @nc-stamp;
}

.nc-standing-man::before {
  content: @nc-standing-man;
}

.nc-standing-woman::before {
  content: @nc-standing-woman;
}

.nc-star::before {
  content: @nc-star;
}

.nc-star-rate::before {
  content: @nc-star-rate;
}

.nc-statistics::before {
  content: @nc-statistics;
}

.nc-statue-of-liberty::before {
  content: @nc-statue-of-liberty;
}

.nc-stay-home::before {
  content: @nc-stay-home;
}

.nc-steak::before {
  content: @nc-steak;
}

.nc-steak-2::before {
  content: @nc-steak-2;
}

.nc-steam-iron::before {
  content: @nc-steam-iron;
}

.nc-steering-wheel::before {
  content: @nc-steering-wheel;
}

.nc-steps::before {
  content: @nc-steps;
}

.nc-stethoscope::before {
  content: @nc-stethoscope;
}

.nc-sticker::before {
  content: @nc-sticker;
}

.nc-stock-2::before {
  content: @nc-stock-2;
}

.nc-stock-market::before {
  content: @nc-stock-market;
}

.nc-stop-sign::before {
  content: @nc-stop-sign;
}

.nc-stopwatch::before {
  content: @nc-stopwatch;
}

.nc-storage-hanger::before {
  content: @nc-storage-hanger;
}

.nc-storage-shelves::before {
  content: @nc-storage-shelves;
}

.nc-storage-unit::before {
  content: @nc-storage-unit;
}

.nc-store::before {
  content: @nc-store;
}

.nc-strawberry::before {
  content: @nc-strawberry;
}

.nc-stretch::before {
  content: @nc-stretch;
}

.nc-stretching::before {
  content: @nc-stretching;
}

.nc-strikethrough::before {
  content: @nc-strikethrough;
}

.nc-style::before {
  content: @nc-style;
}

.nc-submachine-gun::before {
  content: @nc-submachine-gun;
}

.nc-submarine::before {
  content: @nc-submarine;
}

.nc-subscript::before {
  content: @nc-subscript;
}

.nc-subtitles::before {
  content: @nc-subtitles;
}

.nc-sugar::before {
  content: @nc-sugar;
}

.nc-sun::before {
  content: @nc-sun;
}

.nc-sun-cloud::before {
  content: @nc-sun-cloud;
}

.nc-sun-cloud-drop::before {
  content: @nc-sun-cloud-drop;
}

.nc-sun-cloud-fog::before {
  content: @nc-sun-cloud-fog;
}

.nc-sun-cloud-hail::before {
  content: @nc-sun-cloud-hail;
}

.nc-sun-cloud-light::before {
  content: @nc-sun-cloud-light;
}

.nc-sun-cloud-rain::before {
  content: @nc-sun-cloud-rain;
}

.nc-sun-cloud-snow-54::before {
  content: @nc-sun-cloud-snow-54;
}

.nc-sun-cloud-snow-55::before {
  content: @nc-sun-cloud-snow-55;
}

.nc-sun-fog-29::before {
  content: @nc-sun-fog-29;
}

.nc-sun-fog-30::before {
  content: @nc-sun-fog-30;
}

.nc-sun-fog-43::before {
  content: @nc-sun-fog-43;
}

.nc-sunglasses::before {
  content: @nc-sunglasses;
}

.nc-sunglasses-48::before {
  content: @nc-sunglasses-48;
}

.nc-superscript::before {
  content: @nc-superscript;
}

.nc-support::before {
  content: @nc-support;
}

.nc-surfboard::before {
  content: @nc-surfboard;
}

.nc-surprise::before {
  content: @nc-surprise;
}

.nc-survey::before {
  content: @nc-survey;
}

.nc-sushi::before {
  content: @nc-sushi;
}

.nc-swap-horizontal::before {
  content: @nc-swap-horizontal;
}

.nc-swap-vertical::before {
  content: @nc-swap-vertical;
}

.nc-swimming::before {
  content: @nc-swimming;
}

.nc-swimming-pool::before {
  content: @nc-swimming-pool;
}

.nc-swimsuit::before {
  content: @nc-swimsuit;
}

.nc-swipe-bottom::before {
  content: @nc-swipe-bottom;
}

.nc-swipe-left::before {
  content: @nc-swipe-left;
}

.nc-swipe-right::before {
  content: @nc-swipe-right;
}

.nc-swipe-up::before {
  content: @nc-swipe-up;
}

.nc-swiss-knife::before {
  content: @nc-swiss-knife;
}

.nc-switches::before {
  content: @nc-switches;
}

.nc-sword::before {
  content: @nc-sword;
}

.nc-sydney-opera-house::before {
  content: @nc-sydney-opera-house;
}

.nc-sync-devices::before {
  content: @nc-sync-devices;
}

.nc-syringe::before {
  content: @nc-syringe;
}

.nc-system-configuration::before {
  content: @nc-system-configuration;
}

.nc-system-preferences::before {
  content: @nc-system-preferences;
}

.nc-system-update::before {
  content: @nc-system-update;
}

.nc-t-add::before {
  content: @nc-t-add;
}

.nc-t-delete::before {
  content: @nc-t-delete;
}

.nc-t-remove::before {
  content: @nc-t-remove;
}

.nc-table::before {
  content: @nc-table;
}

.nc-table-lamp::before {
  content: @nc-table-lamp;
}

.nc-table-layout::before {
  content: @nc-table-layout;
}

.nc-table-move::before {
  content: @nc-table-move;
}

.nc-table-slide::before {
  content: @nc-table-slide;
}

.nc-table-tennis-bat::before {
  content: @nc-table-tennis-bat;
}

.nc-tablet::before {
  content: @nc-tablet;
}

.nc-tablet-2::before {
  content: @nc-tablet-2;
}

.nc-tablet-charging::before {
  content: @nc-tablet-charging;
}

.nc-tablet-mobile::before {
  content: @nc-tablet-mobile;
}

.nc-tablet-toolbar::before {
  content: @nc-tablet-toolbar;
}

.nc-tacos::before {
  content: @nc-tacos;
}

.nc-tactic::before {
  content: @nc-tactic;
}

.nc-tag::before {
  content: @nc-tag;
}

.nc-tag-add::before {
  content: @nc-tag-add;
}

.nc-tag-check::before {
  content: @nc-tag-check;
}

.nc-tag-cut::before {
  content: @nc-tag-cut;
}

.nc-tag-loyalty::before {
  content: @nc-tag-loyalty;
}

.nc-tag-remove::before {
  content: @nc-tag-remove;
}

.nc-tag-sale::before {
  content: @nc-tag-sale;
}

.nc-tags-stack::before {
  content: @nc-tags-stack;
}

.nc-take-off::before {
  content: @nc-take-off;
}

.nc-takeaway::before {
  content: @nc-takeaway;
}

.nc-taoism::before {
  content: @nc-taoism;
}

.nc-tap-01::before {
  content: @nc-tap-01;
}

.nc-tap-02::before {
  content: @nc-tap-02;
}

.nc-tape::before {
  content: @nc-tape;
}

.nc-target::before {
  content: @nc-target;
}

.nc-taxi::before {
  content: @nc-taxi;
}

.nc-tea::before {
  content: @nc-tea;
}

.nc-tea-bag::before {
  content: @nc-tea-bag;
}

.nc-team::before {
  content: @nc-team;
}

.nc-teddy-bear::before {
  content: @nc-teddy-bear;
}

.nc-telegram::before {
  content: @nc-telegram;
}

.nc-telephone::before {
  content: @nc-telephone;
}

.nc-telescope::before {
  content: @nc-telescope;
}

.nc-temperature::before {
  content: @nc-temperature;
}

.nc-temperature-2::before {
  content: @nc-temperature-2;
}

.nc-temple::before {
  content: @nc-temple;
}

.nc-temple-2::before {
  content: @nc-temple-2;
}

.nc-tennis::before {
  content: @nc-tennis;
}

.nc-tennis-ball::before {
  content: @nc-tennis-ball;
}

.nc-tennis-racket::before {
  content: @nc-tennis-racket;
}

.nc-terrace::before {
  content: @nc-terrace;
}

.nc-text::before {
  content: @nc-text;
}

.nc-text-align-center::before {
  content: @nc-text-align-center;
}

.nc-text-align-justify::before {
  content: @nc-text-align-justify;
}

.nc-text-align-left::before {
  content: @nc-text-align-left;
}

.nc-text-align-right::before {
  content: @nc-text-align-right;
}

.nc-text-bg-color::before {
  content: @nc-text-bg-color;
}

.nc-text-color::before {
  content: @nc-text-color;
}

.nc-text-horizontal-scale::before {
  content: @nc-text-horizontal-scale;
}

.nc-text-italic::before {
  content: @nc-text-italic;
}

.nc-text-size::before {
  content: @nc-text-size;
}

.nc-text-tracking::before {
  content: @nc-text-tracking;
}

.nc-text-vertical-scale::before {
  content: @nc-text-vertical-scale;
}

.nc-texture::before {
  content: @nc-texture;
}

.nc-theater::before {
  content: @nc-theater;
}

.nc-theater-curtains::before {
  content: @nc-theater-curtains;
}

.nc-thermometer::before {
  content: @nc-thermometer;
}

.nc-three::before {
  content: @nc-three;
}

.nc-three-dimensional-object::before {
  content: @nc-three-dimensional-object;
}

.nc-three-dimensional-world::before {
  content: @nc-three-dimensional-world;
}

.nc-three-way-direction::before {
  content: @nc-three-way-direction;
}

.nc-thumb-down::before {
  content: @nc-thumb-down;
}

.nc-thumb-up::before {
  content: @nc-thumb-up;
}

.nc-ticket::before {
  content: @nc-ticket;
}

.nc-tie-01::before {
  content: @nc-tie-01;
}

.nc-tie-02::before {
  content: @nc-tie-02;
}

.nc-tie-bow::before {
  content: @nc-tie-bow;
}

.nc-tiktok::before {
  content: @nc-tiktok;
}

.nc-time-alarm::before {
  content: @nc-time-alarm;
}

.nc-time-clock::before {
  content: @nc-time-clock;
}

.nc-time-machine::before {
  content: @nc-time-machine;
}

.nc-timeline::before {
  content: @nc-timeline;
}

.nc-timer::before {
  content: @nc-timer;
}

.nc-title-case::before {
  content: @nc-title-case;
}

.nc-tnt-explosives::before {
  content: @nc-tnt-explosives;
}

.nc-toast::before {
  content: @nc-toast;
}

.nc-toaster::before {
  content: @nc-toaster;
}

.nc-todo::before {
  content: @nc-todo;
}

.nc-toggle::before {
  content: @nc-toggle;
}

.nc-toilet::before {
  content: @nc-toilet;
}

.nc-toilet-paper::before {
  content: @nc-toilet-paper;
}

.nc-toilette::before {
  content: @nc-toilette;
}

.nc-tomato::before {
  content: @nc-tomato;
}

.nc-tool-blur::before {
  content: @nc-tool-blur;
}

.nc-tool-hand::before {
  content: @nc-tool-hand;
}

.nc-tool-select::before {
  content: @nc-tool-select;
}

.nc-tooth::before {
  content: @nc-tooth;
}

.nc-towel::before {
  content: @nc-towel;
}

.nc-towel-hanger::before {
  content: @nc-towel-hanger;
}

.nc-track-delivery::before {
  content: @nc-track-delivery;
}

.nc-tractor::before {
  content: @nc-tractor;
}

.nc-traffic::before {
  content: @nc-traffic;
}

.nc-train::before {
  content: @nc-train;
}

.nc-train-speed::before {
  content: @nc-train-speed;
}

.nc-tram::before {
  content: @nc-tram;
}

.nc-transaction::before {
  content: @nc-transaction;
}

.nc-transactions::before {
  content: @nc-transactions;
}

.nc-transform::before {
  content: @nc-transform;
}

.nc-transform-2d::before {
  content: @nc-transform-2d;
}

.nc-transform-origin::before {
  content: @nc-transform-origin;
}

.nc-translation::before {
  content: @nc-translation;
}

.nc-transparent::before {
  content: @nc-transparent;
}

.nc-trash::before {
  content: @nc-trash;
}

.nc-trash-can::before {
  content: @nc-trash-can;
}

.nc-travel-makeup-mirror::before {
  content: @nc-travel-makeup-mirror;
}

.nc-treadmill::before {
  content: @nc-treadmill;
}

.nc-treasure-map-21::before {
  content: @nc-treasure-map-21;
}

.nc-treasure-map-40::before {
  content: @nc-treasure-map-40;
}

.nc-tree::before {
  content: @nc-tree;
}

.nc-tree-01::before {
  content: @nc-tree-01;
}

.nc-tree-03::before {
  content: @nc-tree-03;
}

.nc-tree-2::before {
  content: @nc-tree-2;
}

.nc-tree-ball::before {
  content: @nc-tree-ball;
}

.nc-trend-down::before {
  content: @nc-trend-down;
}

.nc-trend-up::before {
  content: @nc-trend-up;
}

.nc-triangle-down::before {
  content: @nc-triangle-down;
}

.nc-triangle-left::before {
  content: @nc-triangle-left;
}

.nc-triangle-line-down::before {
  content: @nc-triangle-line-down;
}

.nc-triangle-line-left::before {
  content: @nc-triangle-line-left;
}

.nc-triangle-line-right::before {
  content: @nc-triangle-line-right;
}

.nc-triangle-line-up::before {
  content: @nc-triangle-line-up;
}

.nc-triangle-right::before {
  content: @nc-triangle-right;
}

.nc-triangle-sm-down::before {
  content: @nc-triangle-sm-down;
}

.nc-triangle-sm-left::before {
  content: @nc-triangle-sm-left;
}

.nc-triangle-sm-right::before {
  content: @nc-triangle-sm-right;
}

.nc-triangle-sm-up::before {
  content: @nc-triangle-sm-up;
}

.nc-triangle-up::before {
  content: @nc-triangle-up;
}

.nc-tripod::before {
  content: @nc-tripod;
}

.nc-trophy::before {
  content: @nc-trophy;
}

.nc-trowel::before {
  content: @nc-trowel;
}

.nc-truck-front::before {
  content: @nc-truck-front;
}

.nc-trumpet::before {
  content: @nc-trumpet;
}

.nc-trunk::before {
  content: @nc-trunk;
}

.nc-tshirt-53::before {
  content: @nc-tshirt-53;
}

.nc-tshirt-54::before {
  content: @nc-tshirt-54;
}

.nc-tshirt-sport::before {
  content: @nc-tshirt-sport;
}

.nc-tty::before {
  content: @nc-tty;
}

.nc-turkey-head::before {
  content: @nc-turkey-head;
}

.nc-turn-e::before {
  content: @nc-turn-e;
}

.nc-turn-n::before {
  content: @nc-turn-n;
}

.nc-turn-s::before {
  content: @nc-turn-s;
}

.nc-turn-w::before {
  content: @nc-turn-w;
}

.nc-turtle::before {
  content: @nc-turtle;
}

.nc-tv::before {
  content: @nc-tv;
}

.nc-tv-stand::before {
  content: @nc-tv-stand;
}

.nc-twitch::before {
  content: @nc-twitch;
}

.nc-two::before {
  content: @nc-two;
}

.nc-two-way-direction::before {
  content: @nc-two-way-direction;
}

.nc-type-tool::before {
  content: @nc-type-tool;
}

.nc-typography::before {
  content: @nc-typography;
}

.nc-umbrella-13::before {
  content: @nc-umbrella-13;
}

.nc-underline::before {
  content: @nc-underline;
}

.nc-underwear::before {
  content: @nc-underwear;
}

.nc-underwear-man::before {
  content: @nc-underwear-man;
}

.nc-undo::before {
  content: @nc-undo;
}

.nc-ungroup::before {
  content: @nc-ungroup;
}

.nc-unite::before {
  content: @nc-unite;
}

.nc-unite-2::before {
  content: @nc-unite-2;
}

.nc-unlink::before {
  content: @nc-unlink;
}

.nc-unlocked::before {
  content: @nc-unlocked;
}

.nc-unordered-list::before {
  content: @nc-unordered-list;
}

.nc-up-arrow::before {
  content: @nc-up-arrow;
}

.nc-upload::before {
  content: @nc-upload;
}

.nc-upload-data::before {
  content: @nc-upload-data;
}

.nc-upload-file::before {
  content: @nc-upload-file;
}

.nc-uppercase::before {
  content: @nc-uppercase;
}

.nc-upset-13::before {
  content: @nc-upset-13;
}

.nc-upset-14::before {
  content: @nc-upset-14;
}

.nc-url::before {
  content: @nc-url;
}

.nc-usb::before {
  content: @nc-usb;
}

.nc-user::before {
  content: @nc-user;
}

.nc-user-2::before {
  content: @nc-user-2;
}

.nc-user-add::before {
  content: @nc-user-add;
}

.nc-user-bubble::before {
  content: @nc-user-bubble;
}

.nc-user-c-frame::before {
  content: @nc-user-c-frame;
}

.nc-user-c-frame-2::before {
  content: @nc-user-c-frame-2;
}

.nc-user-c-frame-3::before {
  content: @nc-user-c-frame-3;
}

.nc-user-check::before {
  content: @nc-user-check;
}

.nc-user-create::before {
  content: @nc-user-create;
}

.nc-user-delete::before {
  content: @nc-user-delete;
}

.nc-user-delete-cross::before {
  content: @nc-user-delete-cross;
}

.nc-user-delete-line::before {
  content: @nc-user-delete-line;
}

.nc-user-edit::before {
  content: @nc-user-edit;
}

.nc-user-focus::before {
  content: @nc-user-focus;
}

.nc-user-group::before {
  content: @nc-user-group;
}

.nc-user-heart::before {
  content: @nc-user-heart;
}

.nc-user-list::before {
  content: @nc-user-list;
}

.nc-user-location::before {
  content: @nc-user-location;
}

.nc-user-location-2::before {
  content: @nc-user-location-2;
}

.nc-user-lock::before {
  content: @nc-user-lock;
}

.nc-user-new::before {
  content: @nc-user-new;
}

.nc-user-remove::before {
  content: @nc-user-remove;
}

.nc-user-s-frame::before {
  content: @nc-user-s-frame;
}

.nc-user-s-frame-2::before {
  content: @nc-user-s-frame-2;
}

.nc-user-s-frame-3::before {
  content: @nc-user-s-frame-3;
}

.nc-user-search::before {
  content: @nc-user-search;
}

.nc-user-share::before {
  content: @nc-user-share;
}

.nc-user-star::before {
  content: @nc-user-star;
}

.nc-user-sync::before {
  content: @nc-user-sync;
}

.nc-user-time::before {
  content: @nc-user-time;
}

.nc-user-wearing-glasses::before {
  content: @nc-user-wearing-glasses;
}

.nc-users::before {
  content: @nc-users;
}

.nc-users-add::before {
  content: @nc-users-add;
}

.nc-users-check::before {
  content: @nc-users-check;
}

.nc-users-heart::before {
  content: @nc-users-heart;
}

.nc-users-location::before {
  content: @nc-users-location;
}

.nc-users-lock::before {
  content: @nc-users-lock;
}

.nc-users-meeting::before {
  content: @nc-users-meeting;
}

.nc-users-mm::before {
  content: @nc-users-mm;
}

.nc-users-remove::before {
  content: @nc-users-remove;
}

.nc-users-wm::before {
  content: @nc-users-wm;
}

.nc-users-ww::before {
  content: @nc-users-ww;
}

.nc-utility-bench::before {
  content: @nc-utility-bench;
}

.nc-vacuum-cleaner::before {
  content: @nc-vacuum-cleaner;
}

.nc-vampire::before {
  content: @nc-vampire;
}

.nc-vector::before {
  content: @nc-vector;
}

.nc-vegan::before {
  content: @nc-vegan;
}

.nc-ventilation::before {
  content: @nc-ventilation;
}

.nc-verified::before {
  content: @nc-verified;
}

.nc-vertical-divider::before {
  content: @nc-vertical-divider;
}

.nc-vespa::before {
  content: @nc-vespa;
}

.nc-vespa-front::before {
  content: @nc-vespa-front;
}

.nc-vest::before {
  content: @nc-vest;
}

.nc-vest-31::before {
  content: @nc-vest-31;
}

.nc-vibrance::before {
  content: @nc-vibrance;
}

.nc-video::before {
  content: @nc-video;
}

.nc-video-camera::before {
  content: @nc-video-camera;
}

.nc-video-gallery::before {
  content: @nc-video-gallery;
}

.nc-video-gallery-2::before {
  content: @nc-video-gallery-2;
}

.nc-video-off::before {
  content: @nc-video-off;
}

.nc-video-player::before {
  content: @nc-video-player;
}

.nc-video-playlist::before {
  content: @nc-video-playlist;
}

.nc-view::before {
  content: @nc-view;
}

.nc-vignette::before {
  content: @nc-vignette;
}

.nc-vintage-computer::before {
  content: @nc-vintage-computer;
}

.nc-vintage-tv::before {
  content: @nc-vintage-tv;
}

.nc-violin::before {
  content: @nc-violin;
}

.nc-virtual-assistant::before {
  content: @nc-virtual-assistant;
}

.nc-virtual-assistant-2::before {
  content: @nc-virtual-assistant-2;
}

.nc-virtual-environment::before {
  content: @nc-virtual-environment;
}

.nc-virtual-reality::before {
  content: @nc-virtual-reality;
}

.nc-virus::before {
  content: @nc-virus;
}

.nc-voice-recognition::before {
  content: @nc-voice-recognition;
}

.nc-voice-record::before {
  content: @nc-voice-record;
}

.nc-volleyball::before {
  content: @nc-volleyball;
}

.nc-volleyball-player::before {
  content: @nc-volleyball-player;
}

.nc-volume::before {
  content: @nc-volume;
}

.nc-volume-2::before {
  content: @nc-volume-2;
}

.nc-volume-down::before {
  content: @nc-volume-down;
}

.nc-volume-mute::before {
  content: @nc-volume-mute;
}

.nc-volume-off::before {
  content: @nc-volume-off;
}

.nc-volume-up::before {
  content: @nc-volume-up;
}

.nc-vpn::before {
  content: @nc-vpn;
}

.nc-vr-controller::before {
  content: @nc-vr-controller;
}

.nc-vr-headset::before {
  content: @nc-vr-headset;
}

.nc-waffle::before {
  content: @nc-waffle;
}

.nc-walk::before {
  content: @nc-walk;
}

.nc-walking-aid::before {
  content: @nc-walking-aid;
}

.nc-walking-support::before {
  content: @nc-walking-support;
}

.nc-wallet::before {
  content: @nc-wallet;
}

.nc-wallet-43::before {
  content: @nc-wallet-43;
}

.nc-wallet-44::before {
  content: @nc-wallet-44;
}

.nc-wallet-90::before {
  content: @nc-wallet-90;
}

.nc-wand::before {
  content: @nc-wand;
}

.nc-wardrobe::before {
  content: @nc-wardrobe;
}

.nc-wardrobe-2::before {
  content: @nc-wardrobe-2;
}

.nc-wardrobe-3::before {
  content: @nc-wardrobe-3;
}

.nc-wardrobe-4::before {
  content: @nc-wardrobe-4;
}

.nc-warning-sign::before {
  content: @nc-warning-sign;
}

.nc-wash-30::before {
  content: @nc-wash-30;
}

.nc-wash-60::before {
  content: @nc-wash-60;
}

.nc-wash-90::before {
  content: @nc-wash-90;
}

.nc-wash-hand::before {
  content: @nc-wash-hand;
}

.nc-wash-hands::before {
  content: @nc-wash-hands;
}

.nc-washing-fluid::before {
  content: @nc-washing-fluid;
}

.nc-washing-machine::before {
  content: @nc-washing-machine;
}

.nc-waste::before {
  content: @nc-waste;
}

.nc-waste-danger::before {
  content: @nc-waste-danger;
}

.nc-waste-recycling::before {
  content: @nc-waste-recycling;
}

.nc-watch::before {
  content: @nc-watch;
}

.nc-watch-2::before {
  content: @nc-watch-2;
}

.nc-watch-dev::before {
  content: @nc-watch-dev;
}

.nc-watch-heart::before {
  content: @nc-watch-heart;
}

.nc-watch-heartbeat::before {
  content: @nc-watch-heartbeat;
}

.nc-water::before {
  content: @nc-water;
}

.nc-water-aerobics::before {
  content: @nc-water-aerobics;
}

.nc-water-hand::before {
  content: @nc-water-hand;
}

.nc-water-polo::before {
  content: @nc-water-polo;
}

.nc-water-polo-ball::before {
  content: @nc-water-polo-ball;
}

.nc-water-sink::before {
  content: @nc-water-sink;
}

.nc-water-surface::before {
  content: @nc-water-surface;
}

.nc-water-wave::before {
  content: @nc-water-wave;
}

.nc-watering-can::before {
  content: @nc-watering-can;
}

.nc-watering-plants::before {
  content: @nc-watering-plants;
}

.nc-watermelon::before {
  content: @nc-watermelon;
}

.nc-wc-sign::before {
  content: @nc-wc-sign;
}

.nc-web-design::before {
  content: @nc-web-design;
}

.nc-web-hyperlink::before {
  content: @nc-web-hyperlink;
}

.nc-web-link::before {
  content: @nc-web-link;
}

.nc-web-url::before {
  content: @nc-web-url;
}

.nc-webcam::before {
  content: @nc-webcam;
}

.nc-webcam-2::before {
  content: @nc-webcam-2;
}

.nc-webpage::before {
  content: @nc-webpage;
}

.nc-wedding-arch::before {
  content: @nc-wedding-arch;
}

.nc-wedding-cake::before {
  content: @nc-wedding-cake;
}

.nc-wedding-ring::before {
  content: @nc-wedding-ring;
}

.nc-wedding-rings::before {
  content: @nc-wedding-rings;
}

.nc-weed::before {
  content: @nc-weed;
}

.nc-weight-bench::before {
  content: @nc-weight-bench;
}

.nc-weight-gain::before {
  content: @nc-weight-gain;
}

.nc-weight-loss::before {
  content: @nc-weight-loss;
}

.nc-weight-plate::before {
  content: @nc-weight-plate;
}

.nc-weight-scale::before {
  content: @nc-weight-scale;
}

.nc-what::before {
  content: @nc-what;
}

.nc-wheel::before {
  content: @nc-wheel;
}

.nc-wheel-2::before {
  content: @nc-wheel-2;
}

.nc-wheelbarrow::before {
  content: @nc-wheelbarrow;
}

.nc-wheelchair::before {
  content: @nc-wheelchair;
}

.nc-wheelchair-2::before {
  content: @nc-wheelchair-2;
}

.nc-wheelchair-ramp::before {
  content: @nc-wheelchair-ramp;
}

.nc-whisk::before {
  content: @nc-whisk;
}

.nc-whiskers::before {
  content: @nc-whiskers;
}

.nc-whistle::before {
  content: @nc-whistle;
}

.nc-white-balance::before {
  content: @nc-white-balance;
}

.nc-white-house::before {
  content: @nc-white-house;
}

.nc-widget::before {
  content: @nc-widget;
}

.nc-width::before {
  content: @nc-width;
}

.nc-wifi::before {
  content: @nc-wifi;
}

.nc-wifi-2::before {
  content: @nc-wifi-2;
}

.nc-wifi-off::before {
  content: @nc-wifi-off;
}

.nc-wifi-protected::before {
  content: @nc-wifi-protected;
}

.nc-wifi-router::before {
  content: @nc-wifi-router;
}

.nc-wind::before {
  content: @nc-wind;
}

.nc-wind-2::before {
  content: @nc-wind-2;
}

.nc-windmill::before {
  content: @nc-windmill;
}

.nc-windmill-2::before {
  content: @nc-windmill-2;
}

.nc-window::before {
  content: @nc-window;
}

.nc-window-add::before {
  content: @nc-window-add;
}

.nc-window-code::before {
  content: @nc-window-code;
}

.nc-window-delete::before {
  content: @nc-window-delete;
}

.nc-window-dev::before {
  content: @nc-window-dev;
}

.nc-window-maximize::before {
  content: @nc-window-maximize;
}

.nc-window-minimize::before {
  content: @nc-window-minimize;
}

.nc-window-paragraph::before {
  content: @nc-window-paragraph;
}

.nc-window-responsive::before {
  content: @nc-window-responsive;
}

.nc-windsurfing::before {
  content: @nc-windsurfing;
}

.nc-wine-list::before {
  content: @nc-wine-list;
}

.nc-wink-06::before {
  content: @nc-wink-06;
}

.nc-wink-11::before {
  content: @nc-wink-11;
}

.nc-wink-69::before {
  content: @nc-wink-69;
}

.nc-winner::before {
  content: @nc-winner;
}

.nc-wireframe::before {
  content: @nc-wireframe;
}

.nc-wireless-charging::before {
  content: @nc-wireless-charging;
}

.nc-witch-hat::before {
  content: @nc-witch-hat;
}

.nc-wolf::before {
  content: @nc-wolf;
}

.nc-woman::before {
  content: @nc-woman;
}

.nc-woman-arrow-down::before {
  content: @nc-woman-arrow-down;
}

.nc-woman-arrow-up::before {
  content: @nc-woman-arrow-up;
}

.nc-woman-man::before {
  content: @nc-woman-man;
}

.nc-woman-profile::before {
  content: @nc-woman-profile;
}

.nc-woman-up-front::before {
  content: @nc-woman-up-front;
}

.nc-wood::before {
  content: @nc-wood;
}

.nc-wool-ball::before {
  content: @nc-wool-ball;
}

.nc-work-badge::before {
  content: @nc-work-badge;
}

.nc-workout-plan::before {
  content: @nc-workout-plan;
}

.nc-world::before {
  content: @nc-world;
}

.nc-world-2::before {
  content: @nc-world-2;
}

.nc-world-marker::before {
  content: @nc-world-marker;
}

.nc-world-pin::before {
  content: @nc-world-pin;
}

.nc-wrench::before {
  content: @nc-wrench;
}

.nc-wrench-tool::before {
  content: @nc-wrench-tool;
}

.nc-xmas-sock::before {
  content: @nc-xmas-sock;
}

.nc-yoga::before {
  content: @nc-yoga;
}

.nc-yogurt::before {
  content: @nc-yogurt;
}

.nc-zero::before {
  content: @nc-zero;
}

.nc-zipped-file::before {
  content: @nc-zipped-file;
}

.nc-zombie::before {
  content: @nc-zombie;
}

.nc-zoom::before {
  content: @nc-zoom;
}

.nc-zoom-in::before {
  content: @nc-zoom-in;
}

.nc-zoom-out::before {
  content: @nc-zoom-out;
}

