name: vnc/infobox
title: Infobox
description: 'Box for displaying short and breaking news in the staging area'
group: vnccontent
prefixFields: true
prefixType: full
fields:
  - identifier: icon
    label: 'Icon'
    required: false
    type: Text
    renderType: selectIcon
    fieldControl:
      0:
        iconset-type: nucleo
        iconset-path: EXT:vnc_sitepackage/Resources/Public/Libraries/Nucleo-Solid_24px
  -
    identifier: header
    useExistingField: true
  -
    identifier: header_layout
    useExistingField: true
  -
    identifier: bodytext
    useExistingField: true
    richtextConfiguration: VncDefault
  -
    identifier: vnc_content_link
    type: Link
    useExistingField: true
  -
    identifier: vnc_content_linktext
    type: Text
    useExistingField: true
  -
    identifier: space_after_class
    type: Select
    useExistingField: true
