<f:comment>
    <cb:asset.css identifier="content-block-css-vnc-vnc/counter" file="Frontend.css"/>
    <cb:asset.script identifier="content-block-js-vnc-vnc/counter" file="Frontend.js"/>
</f:comment>

<f:layout name="Default"/>
<f:section name="Main">
    <!-- <section class="vnccounters page-section mb-5 mb-lg-8 px-0" id="c373"> -->
        <div class="bg-primary py-5 px-1">
            <div class="row">
                <div class="col-12">
                    <div class="keyfacts-counter text-white">
                        <div class="row">
                            <f:alias map="{l: '{', r: '}'}">
                            <f:for each="{data.counters}" as="counter" >
                            <div class="col-12 col-md-6 col-lg text-center mb-5 mb-lg-0">
                                <div class="animated-counter fst-italic text-center">
                                    <div class="{counter.icon}"></div>
                                    <p  class="headline display-3 mb-3">
                                        {counter.prefix}
                                        <span data-animated-counter='{l}"from":{counter.number_from},"to":{counter.number_to},"duration":{counter.duration},"locale":"de","suffix":"{counter.suffix}"{r}'>
                                        {counter.prefix}{counter.number_to}{counter.suffix}
                                        </span>
                                    </p>
                                    <p>
                                        {counter.description}
                                    </p></div>
                            </div>
                            </f:for>
                            </f:alias>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</f:section>

