<?xml version="1.0"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file datatype="plaintext" original="Labels.xlf" source-language="en" date="2024-11-08T08:45:27+00:00" product-name="vnc/counter">
		<header/>
		<body>
			<trans-unit id="title" resname="title">
				<source>Animated Counter</source>
			</trans-unit>
			<trans-unit id="description" resname="description">
				<source>Content Element with animated Numbers</source>
			</trans-unit>
			<trans-unit id="header.label" resname="header.label">
				<source>Description (not displayed in Frontend)</source>
			</trans-unit>
			<trans-unit id="counters.label" resname="counters.label">
				<source>Counters</source>
			</trans-unit>
			<trans-unit id="counters.icon.label" resname="counters.icon.label">
				<source>Icon</source>
			</trans-unit>
			<trans-unit id="counters.description.label" resname="counters.description.label">
				<source>Description</source>
			</trans-unit>
			<trans-unit id="counters.duration.label" resname="counters.duration.label">
				<source>Duration</source>
			</trans-unit>
			<trans-unit id="counters.duration.description" resname="counters.duration.description">
				<source>Duration in milliseconds (seconds * 1000), default is 2 seconds</source>
			</trans-unit>
			<trans-unit id="counters.palettes.counter_palette.label" resname="counters.palettes.counter_palette.label">
				<source>Counter</source>
			</trans-unit>
			<trans-unit id="counters.prefix.label" resname="counters.prefix.label">
				<source>Prefix</source>
			</trans-unit>
			<trans-unit id="counters.number_from.label" resname="counters.number_from.label">
				<source>Start value</source>
			</trans-unit>
			<trans-unit id="counters.number_to.label" resname="counters.number_to.label">
				<source>End value</source>
			</trans-unit>
			<trans-unit id="counters.suffix.label" resname="counters.suffix.label">
				<source>Suffix</source>
			</trans-unit>
			<trans-unit id="header_layout.label" resname="header_layout.label">
				<source>Header Type</source>
			</trans-unit>
			<trans-unit id="bodytext.label" resname="bodytext.label">
				<source>Intro Text</source>
			</trans-unit>
		</body>
	</file>
</xliff>
