<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file source-language="en" target-language="de" original="" datatype="plaintext" date="2024-09-11T11:53:58+02:00">
		<body>
			<trans-unit id="title" resname="title">
				<source>Animated Counter</source>
				<target>Animier<PERSON> Z<PERSON>hler</target>
			</trans-unit>
			<trans-unit id="description" resname="description">
				<source>Content Element with animated Numbers</source>
				<target>Inhaltselement mit animierten Zahlen</target>
			</trans-unit>
			<trans-unit id="header.label" resname="header.label">
				<source>Description (not displayed in Frontend)</source>
				<target>Beschreibung (wird nicht auf der Seite dargestellt)</target>
			</trans-unit>
			<trans-unit id="counters.label" resname="counters.label">
				<source>Counters</source>
				<target><PERSON><PERSON><PERSON></target>
			</trans-unit>
			<trans-unit id="counters.icon.label" resname="counters.icon.label">
				<source>Icon</source>
				<target>Icon</target>
			</trans-unit>
			<trans-unit id="counters.description.label" resname="counters.description.label">
				<source>Description</source>
				<target>Beschreibung</target>
			</trans-unit>
			<trans-unit id="counters.duration.label" resname="counters.duration.label">
				<source>Duration</source>
				<target>Dauer</target>
			</trans-unit>
			<trans-unit id="counters.duration.description" resname="counters.duration.description">
				<source>Duration in milliseconds (seconds * 1000), default is 2 seconds</source>
				<target>Dauer in Millisekunden (Sekunden * 1000), Standardwert ist 2 Sekunden</target>
			</trans-unit>
			<trans-unit id="counters.palettes.counter_palette.label" resname="counters.palettes.counter_palette.label">
				<source>Counter</source>
				<target>Zähler</target>
			</trans-unit>
			<trans-unit id="counters.prefix.label" resname="counters.prefix.label">
				<source>Prefix</source>
				<target>Präfix</target>
			</trans-unit>
			<trans-unit id="counters.number_from.label" resname="counters.number_from.label">
				<source>Start value</source>
				<target>Startwert</target>
			</trans-unit>
			<trans-unit id="counters.number_to.label" resname="counters.number_to.label">
				<source>End value</source>
				<target>Endwert</target>
			</trans-unit>
			<trans-unit id="counters.suffix.label" resname="counters.suffix.label">
				<source>Suffix</source>
				<target>Suffix</target>
			</trans-unit>
			<trans-unit id="header_layout.label" resname="header_layout.label">
				<source>Header Type</source>
				<target>Überschriftstyp</target>
			</trans-unit>
			<trans-unit id="bodytext.label" resname="bodytext.label">
				<source>Intro Text</source>
				<target>Intro Text</target>
			</trans-unit>
		</body>
	</file>
</xliff>
