<f:comment><!--
<cb:asset.css identifier="content-block-css-vnc-vnc/maps" file="Frontend.css"/>
<cb:asset.script identifier="content-block-js-vnc-vnc/maps" file="Frontend.js"/>
--></f:comment>

<f:layout name="Default"/>
<f:section name="Main">
    <div class="container">
        <h2 class="headline h2 mb-3 mb-lg-4 mt-2 mt-md-3 mb-3 mb-lg-4 mt-lg-0">
            {data.header}
        </h2>
        <div class="map-container position-relative">
            <div
                id="map"
                class="map"
                data-locations="{data.marker -> f:format.json()}"
            ></div>
            <div id="map__info" class="map__info map__info--hidden"></div>
            <div class="map__links">
                <f:for each="{data.marker}" as="marker">
                    <f:link.typolink parameter="{marker.buttonUrl}">
                        {marker.buttonText}
                    </f:link.typolink>
                </f:for>
            </div>
        </div>
    </div>
</f:section>
