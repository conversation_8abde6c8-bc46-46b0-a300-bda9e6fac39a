name: vnc/maps
typeName: vnc_maps
group: vnccontent
prefixFields: true
prefixType: full
fields:
  -
    identifier: header
    useExistingField: true
  - identifier: marker
    type: Collection
    label: "Marker"
    labelField: title
    minitems: 1
    maxitems: 99
    appearance:
      collapseAll: true
      levelLinksPosition: both
    fields:
      - identifier: title
        type: Text
        label: "Titel"
        required: true
      - identifier: type
        type: Select
        renderType: selectSingle
        label: "Typ"
        default: 'biogasanlage'
        items:
          - label: 'Biogasanlage'
            value: biogasanlage
          - label: 'Photovoltaikanlage'
            value: photovoltaikanlage
          - label: 'Wasserkraftwerk'
            value: wasserkraftwerk
          - label: 'Windkraftanlage'
            value: windkraftanlage
        required: true
      - identifier: inOperationSince
        type: Text
        label: "In Betrieb seit"
        required: true
      - identifier: power
        type: Text
        label: "Leistung"
        required: true
      - identifier: energyGenerated
        type: Text
        label: "Erzeugte Energie"
        required: true
      - identifier: householdsSupplied
        type: Text
        label: "Versorgung"
        required: true
      - identifier: lat
        type: Text
        label: "Latitude"
        required: true
      - identifier: lng
        type: Text
        label: "Longitude"
        required: true
      - identifier: buttonUrl
        type: Link
        label: "Button URL"
        required: false
      - identifier: buttonText
        type: Text
        label: "Button Text"
        required: false
  - identifier: space_after_class
    type: Select
    useExistingField: true
