name: vnc/accordion
title: Accordion
description: 'Collapsing Accordion Element'
group: vnccontent
prefixFields: true
prefixType: full
fields:
  -
    identifier: header
    useExistingField: true
    label: 'Header'
  -
    identifier: header_layout
    useExistingField: true
    label: "Header Type"
  -
    identifier: bodytext
    useExistingField: true
    label: "Intro Text"
    richtextConfiguration: 'VncContentBasic'
  -
    identifier: layout
    type: Select
    label: "Layout"
    renderType: selectSingle
    items:
      - label: 'Without Image'
        value: 1
      #- label: 'With Image square left'
      #  value: 2
      - label: 'With Image portrait left'
        value: 3
    onChange: reload
  -
    identifier: image
    useExistingField: true
    label: "Image"
    displayCond: FIELD:layout:>=:2
    allowed: jpg, jpeg, png
    minitems: 1
    maxitems: 1
    cropVariants:
      default:
        disabled: true
      widescreen:
        disabled: true
      ultrawide:
        disabled: true
      classicscreen:
        disabled: true
      univisium:
        disabled: true
      wide:
        disabled: true
      television:
        disabled: true
      square:
        disabled: true
      upright:
        disabled: false
  - identifier: items
    type: Collection
    labelField: header
    minitems: 1
    maxitems: 99
    appearance:
      collapseAll: true
      levelLinksPosition: both
    fields:
      - identifier: header
        required: true
        type: Text
        label: "Header"
      - identifier: bodytext
        type: Textarea
        label: "Text"
        enableRichtext: true
        richtextConfiguration: 'VncDefault'
      - identifier: image
        type: File
        allowed: jpg, jpeg, png, youtube
        minitems: 0
        maxitems: 1
        cropVariants:
          default:
            disabled: false
          widescreen:
            disabled: true
          ultrawide:
            disabled: true
          classicscreen:
            disabled: true
          univisium:
            disabled: true
          wide:
            disabled: true
          television:
            disabled: true
          upright:
            disabled: true
  - identifier: space_after_class
    type: Select
    useExistingField: true
