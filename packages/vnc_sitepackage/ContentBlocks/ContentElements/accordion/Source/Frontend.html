<f:comment><!--
<cb:asset.css identifier="content-block-css-vnc-vnc/accordion" file="Frontend.css"/>
<cb:asset.script identifier="content-block-js-vnc-vnc/accordion" file="Frontend.js"/>
--></f:comment>
<f:layout name="Default"/>
<f:section name="Main">
<div class="container">
    <f:if condition="{data.layout}>1">
        <f:then>
            <f:comment><!-- Renders accordion with image left --></f:comment>
            <f:render section="one-image-left" arguments="{_all}"/>
        </f:then>
        <f:else>
            <f:comment><!-- Renders accordion w/o image --></f:comment>
            <f:render section="no-image" arguments="{_all}"/>
        </f:else>
    </f:if>
</div>
</f:section>

<f:section name="no-image">
    <f:comment><!--

        ELEMENT AKKORDION - wrapper

    --></f:comment>
    <div id="c{data.uid}-{data.pid}" class="row {f:if(condition:'{data.space_after_class}', then: '{data.space_after_class}')}">
        <div class="col-12 col-md-10 offset-md-1 col-lg-8 offset-lg-2">
            <f:if condition="{data.header || data.bodytext}">
                <article class="vnc_accordion__intro">
                    <f:if condition="{data.header}">
                        <f:render partial="Header" arguments="{_all}"/>
                    </f:if>
                    <f:if condition="{data.bodytext}">
                        <f:format.html>{data.bodytext}</f:format.html>
                    </f:if>
                </article>
            </f:if>
            <f:if condition="{data.items}">
                <f:render partial="Accordion" arguments="{_all}" />
            </f:if>
        </div>
    </div>
</f:section>

<f:section name="one-image-left">
    <f:comment><!--

        ELEMENT AKKORDION with IMAGE - wrapper

        HINWEIS EMERGY: es gilt hier immer: data.layout == 3 Bild im Hochformat 3:4 (upright).
        Quadratisch (square, data.layout == 2) ist nicht vorgesehen


    --></f:comment>
    <div class="row">
        <div class="col-md-12">
            <div class="row">
                <div
                    class="col-12 col-md-{f:if(condition:'{data.layout}==2', then: '6', else: '4')} mb-4 mb-md-0">
                    <f:for each="{data.image}" as="image" iteration="imageIteration">
                        <f:if condition="{data.layout}==2">
                            <f:then>
                                <f:alias map="{cropVariant: 'square'}">
                                    <f:render partial="Media/Type/Image" arguments="{_all}" />
                                </f:alias>
                            </f:then>
                            <f:else>
                                <f:alias map="{cropVariant: 'upright'}">
                                    <f:render partial="Media/Type/Image" arguments="{_all}" />
                                </f:alias>
                            </f:else>
                        </f:if>
                    </f:for>
                </div>
                <div class="col-12 col-md-{f:if(condition:'{data.layout}==2', then: '6', else: '8')}">
                    <f:if condition="{data.header}">
                        <h2 class="headline h3">
                            <f:format.raw>{data.header}</f:format.raw>
                        </h2>
                    </f:if>
                    <f:if condition="{data.bodytext}">
                        <div class="py-4">
                            <f:format.html>{data.bodytext}</f:format.html>
                        </div>
                    </f:if>
                    <f:if condition="{data.items}">
                        <f:render partial="Accordion" arguments="{_all}" />
                    </f:if>
                </div>
            </div>
        </div>
    </div>
</f:section>
