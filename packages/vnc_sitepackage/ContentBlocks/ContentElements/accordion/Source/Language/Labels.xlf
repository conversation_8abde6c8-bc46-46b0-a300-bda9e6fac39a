<?xml version="1.0"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file datatype="plaintext" original="Labels.xlf" source-language="en" date="2024-11-08T08:45:27+00:00" product-name="vnc/accordion">
		<header/>
		<body>
			<trans-unit id="title" resname="title">
				<source>Accordion</source>
			</trans-unit>
			<trans-unit id="description" resname="description">
				<source>An Element with collapsing text/images</source>
			</trans-unit>
			<trans-unit id="header.label" resname="header.label">
				<source>Header</source>
			</trans-unit>
			<trans-unit id="header_layout.label" resname="header_layout.label">
				<source>Header Type</source>
			</trans-unit>
			<trans-unit id="bodytext.label" resname="bodytext.label">
				<source>Intro Text</source>
			</trans-unit>
			<trans-unit id="layout.label" resname="layout.label">
				<source>Layout</source>
			</trans-unit>
			<trans-unit id="layout.items.1.label" resname="layout.items.1.label">
				<source>Without Image</source>
			</trans-unit>
			<trans-unit id="layout.items.3.label" resname="layout.items.3.label">
				<source>With Image portrait left</source>
			</trans-unit>
			<trans-unit id="image.label" resname="image.label">
				<source>Image</source>
			</trans-unit>
			<trans-unit id="items.label" resname="items.label">
				<source>items</source>
			</trans-unit>
			<trans-unit id="items.header.label" resname="items.header.label">
				<source>Header</source>
			</trans-unit>
			<trans-unit id="items.bodytext.label" resname="items.bodytext.label">
				<source>Text</source>
			</trans-unit>
			<trans-unit id="items.image.label" resname="items.image.label">
				<source>image/Video</source>
			</trans-unit>
			<trans-unit id="layout.items.2.label" resname="layout.items.2.label">
				<source>With Image square left</source>
			</trans-unit>
		</body>
	</file>
</xliff>
