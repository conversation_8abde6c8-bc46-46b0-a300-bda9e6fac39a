<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns:t3="http://typo3.org/schemas/xliff" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file source-language="en" target-language="de" original="" datatype="plaintext" date="2024-03-13T15:14:12+01:00">
		<body>
			<trans-unit id="title" resname="title">
				<source>Accordion</source>
				<target>Akkordeon</target>
			</trans-unit>
			<trans-unit id="description" resname="description">
				<source>An Element with collapsing text/images</source>
				<target>Ein Element mit ausklappbarem Text/Bildern</target>
			</trans-unit>
			<trans-unit id="header.label" resname="header.label">
				<source>Header</source>
				<target>Überschrift</target>
			</trans-unit>
			<trans-unit id="header_layout.label" resname="header_layout.label">
				<source>Header Type</source>
				<target>Typ der Überschrift</target>
			</trans-unit>
			<trans-unit id="bodytext.label" resname="bodytext.label">
				<source>Intro Text</source>
				<target>Intro Text</target>
			</trans-unit>
			<trans-unit id="layout.label" resname="layout.label">
				<source>Layout</source>
				<target>Layout</target>
			</trans-unit>
			<trans-unit id="layout.items.1.label" resname="layout.items.1.label">
				<source>Without Image</source>
				<target>Ohne Bild</target>
			</trans-unit>
			<trans-unit id="layout.items.2.label" resname="layout.items.2.label">
				<source>With Image square left</source>
				<target>Mit Bild quadratisch links</target>
			</trans-unit>
			<trans-unit id="layout.items.3.label" resname="layout.items.3.label">
				<source>With Image portrait left</source>
				<target>Mit Bild hochformtig links</target>
			</trans-unit>
			<trans-unit id="image.label" resname="image.label">
				<source>Image</source>
				<target>Bild</target>
			</trans-unit>
			<trans-unit id="items.label" resname="items.label">
				<source>items</source>
				<target>Elemente</target>
			</trans-unit>
			<trans-unit id="items.header.label" resname="items.header.label">
				<source>Header</source>
				<target>Überschrift</target>
			</trans-unit>
			<trans-unit id="items.bodytext.label" resname="items.bodytext.label">
				<source>Text</source>
				<target>Text</target>
			</trans-unit>
			<trans-unit id="items.image.label" resname="items.image.label">
				<source>image/Video</source>
				<target>Bild/Video</target>
			</trans-unit>
		</body>
	</file>
</xliff>
