const e={title:"<PERSON>ergie<PERSON><PERSON><PERSON><PERSON><PERSON>",tab:{power:"<PERSON>kos<PERSON>",gas:"Gas",heat:"Wärm<PERSON>"},rate:{zip:{label:"Postleitzahl",placeholder:"<PERSON>hr<PERSON> Postleitzahl"},household:{power:{label:"Personen im Haushalt"},gas:{label:"Wohnfl<PERSON>che in qm"}},consumption:{label:"Verbrauch",placeholder:"Ihr Verbrauch"},consumptionDefault:{label:"Ihr Jahresverbrauch im Hochtarif (HT)",placeholder:"Ihr Jahresverbrauch"},consumptionAlt:{label:"Ihr Jahresverbrauch im Niedertarif (NT)",placeholder:"Ihr Jahresverbrauch"},heating:{label:"Womit heizen Si<PERSON>?",heatpump:{label:"Wärmepumpe"},storage:{label:"Nachspeicherheizung"}},meter:{label:"Welchen Zählertyp nutzen Sie?",single:{label:"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (nur Wärmestrom)"},double:{label:"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (nur Wärmestrom)"},household:{label:"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (inkl. <PERSON><PERSON><PERSON><PERSON>)"}}},submit:{caption:"<PERSON>rif berechnen"}};export{e as default,e as messages};
