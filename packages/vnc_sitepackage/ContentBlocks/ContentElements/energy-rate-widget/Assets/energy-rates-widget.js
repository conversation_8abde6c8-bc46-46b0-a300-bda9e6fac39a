(function(){"use strict";var Xr=document.createElement("style");Xr.textContent=`.vnc-col-auto{grid-column:auto!important}.vnc-mx-auto{margin-left:auto!important;margin-right:auto!important}.vnc-mb-0{margin-bottom:0!important}.vnc-mb-1{margin-bottom:.25rem!important}.vnc-mb-3{margin-bottom:.75rem!important}.vnc-ms-auto{margin-inline-start:auto!important}.vnc-block{display:block!important}.vnc-flex{display:flex!important}.vnc-grid{display:grid!important}.vnc-w-1\\/3{width:33.333333%!important}.vnc-w-full{width:100%!important}.vnc-grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))!important}.vnc-grid-rows-1{grid-template-rows:repeat(1,minmax(0,1fr))!important}.vnc-gap-12{gap:3rem!important}.vnc-gap-2{gap:.5rem!important}.vnc-gap-4{gap:1rem!important}.vnc-overflow-hidden{overflow:hidden!important}.vnc-p-4{padding:1rem!important}.vnc-py-3{padding-top:.75rem!important;padding-bottom:.75rem!important}.vnc-pb-5{padding-bottom:1.25rem!important}.vnc-text-black{--tw-text-opacity: 1 !important;color:rgb(0 0 0 / var(--tw-text-opacity, 1))!important}.vnc-opacity-50{opacity:.5!important}:root,*:before,*:after{--vnc-brand-color-primary-dark: #5A7800;--vnc-brand-color-primary-light: #789314;--vnc-brand-color-secondary-dark: #49083a;--vnc-brand-color-secondary-light: #7D1464;--vnc-brand-color-tertiary-dark: #000000;--vnc-brand-color-tertiary-light: #575756;--vnc-brand-color-primary-variant-dark: #B0B108;--vnc-brand-color-primary-variant-light: #C8C920;--vnc-brand-color-primary: var(--vnc-brand-color-primary-dark);--vnc-brand-color-secondary: var(--vnc-brand-color-secondary-dark);--vnc-brand-color-tertiary: var(--vnc-brand-color-tertiary-dark);--vnc-brand-color-signal-success: #5A7800;--vnc-brand-color-signal-info: #0073AF;--vnc-brand-color-signal-warning: #E16400;--vnc-brand-color-signal-danger: #c82314;--vnc-brand-color-dark: #4e4e4e;--vnc-brand-color-light: #fafafa;--vnc-brand-color-black: #000;--vnc-brand-color-white: #fff;--vnc-body-color: #4e4e4e;--vnc-body-background-color: var(--vnc-brand-color-white);--vnc-link-color: var(--vnc-brand-color-secondary);--vnc-headline-font-family: "Ropa-Sans";--vnc-border-color: var(--bs-border-color);--vnc-border-width: var(--bs-border-width);--vnc-border-radius: var(--bs-border-radius);--vnc-brand-gradient-primary-start: var(--vnc-brand-color-primary-light);--vnc-brand-gradient-primary-stop: var(--vnc-brand-color-primary-dark);--vnc-brand-gradient-primary: linear-gradient(180deg, var(--vnc-brand-gradient-primary-start) 0%, var(--vnc-brand-gradient-primary-stop) 100%);--vnc-brand-gradient-primary-variant-start: var(--vnc-brand-color-primary-variant-light);--vnc-brand-gradient-primary-variant-stop: var(--vnc-brand-color-primary-variant-dark);--vnc-brand-gradient-primary-variant: linear-gradient(180deg, var(--vnc-brand-gradient-primary-variant-start) 0%, var(--vnc-brand-gradient-primary-variant-stop) 100%);--vnc-brand-gradient-secondary-start: var(--vnc-brand-color-secondary-light);--vnc-brand-gradient-secondary-stop: var(--vnc-brand-color-secondary-dark);--vnc-brand-gradient-secondary: linear-gradient(180deg, var(--vnc-brand-gradient-secondary-start) 0%, var(--vnc-brand-gradient-secondary-stop) 100%);--vnc-widget-primary: var(--vnc-brand-color-primary);--vnc-widget-secondary: var(--vnc-brand-color-secondary);--vnc-widget-tertiary: var(--vnc-brand-color-teriary);--vnc-widget-white: var(--vnc-brand-color-white);--vnc-widget-red: var(--vnc-brand-color-red);--vnc-widget-orange: var(--vnc-brand-color-orange);--vnc-widget-color: var(--vnc-widget-white);--vnc-widget-bg-color: #49083A;--vnc-widget-active-color: var(--vnc-widget-color);--vnc-widget-active-bg-color: #7D1464;--vnc-widget-button-color: var(--vnc-widget-color);--vnc-widget-button-bg-color: var(--vnc-widget-bg-color);--vnc-widget-input-color: var(--vnc-brand-color-black);--vnc-widget-input-height: 4rem}.theme--light{--vnc-body-color: var(--vnc-brand-color-primary);--vnc-body-background-color: var(--vnc-brand-color-white)}.theme--dark{--vnc-body-color: var(--vnc-brand-color-light);--vnc-body-background-color: var(--vnc-brand-color-primary)}:root{font-synthesis:none;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;font-family:sans-serif}.vnc-bg-gradient-primary{background:var(--vnc-brand-gradient-primary)}.vnc-bg-gradient-secondary{background:var(--vnc-brand-gradient-secondary)}input[type=text],input[type=number]{color:var(--vnc-widget-input-color)}.vnc-visually-hidden{position:absolute!important;width:1px!important;height:1px!important;padding:0!important;margin:-1px!important;overflow:hidden!important;clip:rect(0,0,0,0)!important;white-space:nowrap!important;border:0!important}.vnc-energy-rate--power{--vnc-thumb-background: var(--vnc-thumb-background-avatar)}.vnc-energy-rate--gas{--vnc-thumb-background: var(--vnc-thumb-background-house)}.vnc-input{position:relative;display:flex;gap:.3125rem;flex-direction:column;text-align:start}.vnc-input>input,.vnc-input>textarea,.vnc-input>p,.vnc-input>span{order:1}.vnc-input>label{order:0}.vnc-input__label{font-weight:700;min-height:3rem;display:inline-flex;align-items:end;line-height:1}.vnc-input__error{order:2}.vnc-input input,.vnc-input textarea{display:block;width:100%;height:var(--vnc-widget-input-height);outline:none;border:thin solid var(--border-color);border-radius:var(--border-radius);color:var(--vnc-widget-input-color);padding:0 1rem;transition:all .12s ease}.vnc-input ::-moz-placeholder{color:var(--vnc-widget-input-color);opacity:1}.vnc-input ::placeholder{color:var(--vnc-widget-input-color);opacity:1}.vnc-input ::-ms-input-placeholder{color:var(--vnc-widget-input-color)}.vnc-input textarea{height:auto}.vnc-input input[type=checkbox],.vnc-input input[type=radio]{position:relative;display:grid;place-content:center;width:1.5em;height:1.5em;margin:0;padding:0;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:.125em solid currentColor;font:inherit;color:currentColor}.vnc-input input:disabled~label{opacity:.62}.vnc-input input[type=file]{opacity:0;z-index:-1;position:absolute;top:-1px;left:0;width:0;height:0}.vnc-input:has(input:invalid)+.input__error{display:block}.vnc-input:has(input:invalid)+.input__error.hide{display:none}.input--light .caption__count,.button--light .caption__count{border:.125rem solid var(--brand-color-white);background-color:transparent}.input__error{display:none;color:#fff}form[data-validate] .is-invalid,form[data-validate] :invalid{border:.125em solid var(--vnc-widget-input-invalid-color);border-color:var(--vnc-widget-input-invalid-color)}form[data-validate] .is-invalid::-moz-placeholder,form[data-validate] :invalid::-moz-placeholder{color:var(--vnc-widget-input-invalid-color);opacity:1}form[data-validate] .is-invalid::placeholder,form[data-validate] :invalid::placeholder{color:var(--vnc-widget-input-invalid-color);opacity:1}form[data-validate] .is-invalid::-ms-input-placeholder,form[data-validate] :invalid::-ms-input-placeholder{color:var(--vnc-widget-input-invalid-color)}form[data-validate] .is-invalid~label,form[data-validate] :invalid~label{color:var(--widget-white)}form[data-validate] .is-invalid~label.vnc-btn,form[data-validate] :invalid~label.vnc-btn{--button-text-color: var(--brand-color-white);--button-accent-color: var(--brand-color-error)}form[data-validate] .is-invalid~.input__error,form[data-validate] :invalid~.input__error{display:block}form[data-validate] [data-append-text]:has(:invalid):after{color:var(--vnc-widget-input-invalid-color)}form[data-validate]:has(:invalid) .button[type=submit]{--button-text-color: var(--brand-color-white);--button-background-color: var(--brand-color-error);--button-border-color: var(--brand-color-error);--button-hover-background-color: var(--brand-color-error);pointer-events:none}.vnc-btn{--vnc-widget-input-height: var(--vnc-widget-input-height);-webkit-appearance:none;-moz-appearance:none;appearance:none;border:none;font-weight:700;height:var(--vnc-widget-input-height);line-height:1;display:inline-flex;align-items:center;gap:.3125rem}input[type=number]{-moz-appearance:textfield}[data-append-text]:not([data-append-text=""]){position:relative}[data-append-text]:not([data-append-text=""]) input[type=text]{padding-inline-end:6rem}[data-append-text]:not([data-append-text=""]):after{content:attr(data-append-text);position:absolute;bottom:0;right:0;width:6rem;height:var(--vnc-widget-input-height);display:block;display:grid;place-content:center;color:var(--vnc-brand-color-secondary-dark)}.vnc-input input[type=checkbox],.vnc-input input[type=radio]{background-color:transparent;position:relative;display:grid;place-content:center;width:1.5em;height:1.5em;margin:0;padding:0;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:.125em solid currentColor;font:inherit;color:currentColor}.vnc-input input[type=checkbox]:before,.vnc-input input[type=radio]:before{content:"";width:.5em;height:.5em;transform:scale(0);transition:.18s transform ease-in-out;box-shadow:inset .5em .5em currentColor}.vnc-input input[type=checkbox]:after,.vnc-input input[type=radio]:after{content:"";width:.5em;height:.5em;position:absolute;left:50%;top:50%;transform:scale(0) translate(-50%) translateY(-50%);transform-origin:0 0;box-shadow:0 0 0 .5em currentColor;opacity:.32;border-radius:99999px;transition:all .32s ease-in-out}.vnc-input input[type=radio]{border-radius:1em}.vnc-input input[type=radio]:before{border-radius:1em}.vnc-input input:checked:before{transform:scale(1)}.vnc-input input:checked:after{opacity:0;transform:scale(2) translate(-50%) translateY(-50%)}.vnc-input input::-webkit-outer-spin-button,.vnc-input input::-webkit-inner-spin-button{-webkit-appearance:none;margin:0}.vnc-input input[type=number]{-moz-appearance:textfield}.vnc-input--radio,.vnc-input--checkbox{display:inline-flex;flex-direction:row;flex-wrap:wrap;gap:.5rem;line-height:1.1;display:flex;align-items:center;gap:.5em;margin-block-end:.5rem;min-height:2rem}.vnc-input--radio input,.vnc-input--radio label,.vnc-input--checkbox input,.vnc-input--checkbox label{order:unset}.vnc-input--radio label,.vnc-input--checkbox label{flex:1 1 auto;cursor:pointer;align-items:center}.vnc-input--radio .vnc-input__error,.vnc-input--checkbox .vnc-input__error{flex-basis:100%}.vnc-container--grid{container:container-grid/inline-size}@container container-grid (max-width: 992px){.vnc-grid .vnc-g-col-lg-4{grid-column:auto/span 4}}.bullet-list{margin:0 0 1rem!important}:root,.svelte-1j9upyo:before,.svelte-1j9upyo:after{--vnc-range-track-height: .3125rem;--vnc-range-thumb-width: 2rem;--vnc-range-thumb-height: 2rem;--vnc-range-thumb-radius: 50%;--vnc-range-step-width: var(--vnc-range-thumb-width);--vnc-range-track-color: var(--vnc-widget-white);--vnc-range-thumb-color: var(--vnc-widget-white);--vnc-range-highlight-color: var(--vnc-widget-primary);--vnc-thumb-background-avatar: "";--vnc-thumb-background-house: "";--vnc-thumb-background: var(--vnc-thumb-background-house)}.vnc-input.svelte-1j9upyo{position:relative;display:flex;gap:.3125rem;flex-direction:column;text-align:start}.vnc-input--vnc-range.svelte-1j9upyo input[type=range]:where(.svelte-1j9upyo){-webkit-appearance:none;-moz-appearance:none;appearance:none;background:transparent;border:none;padding:0;cursor:pointer}.vnc-input--vnc-range.svelte-1j9upyo input[type=range]:where(.svelte-1j9upyo)::-webkit-slider-runnable-track{background:var(--vnc-range-track-color);height:var(--vnc-range-track-height)}.vnc-input--vnc-range.svelte-1j9upyo input[type=range]:where(.svelte-1j9upyo)::-moz-range-track{background:var(--vnc-range-track-color);height:var(--vnc-range-track-height)}.vnc-input--vnc-range.svelte-1j9upyo input[type=range]:where(.svelte-1j9upyo)::-webkit-slider-thumb{-webkit-appearance:none;-moz-appearance:none;appearance:none;transform:translateY(calc((var(--vnc-range-thumb-height) / 2 - var(--vnc-range-track-height) / 2) * -1));background-color:var(--vnc-range-thumb-color);width:var(--vnc-range-thumb-width);height:var(--vnc-range-thumb-height);border-radius:var(--vnc-range-thumb-radius)}.vnc-input--vnc-range.svelte-1j9upyo input[type=range]:where(.svelte-1j9upyo)::-moz-range-thumb{border:none;background-color:var(--vnc-range-thumb-color);width:var(--vnc-range-thumb-width);height:var(--vnc-range-thumb-height);border-radius:var(--vnc-range-thumb-radius)}.vnc-input--vnc-range__track.svelte-1j9upyo{position:relative;background:var(--vnc-range-track-color);height:var(--vnc-range-track-height);background-clip:content-box}.vnc-input--vnc-range__thumb.svelte-1j9upyo{position:absolute;top:50%;transform:translateY(-50%) translate(-50%);background-color:var(--vnc-range-thumb-color);width:var(--vnc-range-thumb-width);height:var(--vnc-range-thumb-height);border-radius:var(--vnc-range-thumb-radius);transition:all .12s ease}.vnc-input--vnc-range__steps.svelte-1j9upyo{order:2;display:flex;justify-content:space-between;align-items:center}.vnc-input--vnc-range__steps.svelte-1j9upyo .vnc-input__label:where(.svelte-1j9upyo){width:var(--vnc-range-step-width);text-align:center;transition:opacity .12s ease;display:block;min-height:0}.vnc-input--vnc-range__steps.svelte-1j9upyo .vnc-input__label:where(.svelte-1j9upyo):hover{color:var(--brand-color-primary)}.vnc-input--vnc-range__track.svelte-1j9upyo{width:100%;top:-24px}.vnc-input--vnc-range__thumb.svelte-1j9upyo{position:absolute;top:1px;left:15px;z-index:1;width:30px;height:30px;display:grid;place-content:center}.vnc-input--vnc-range.svelte-1j9upyo input[type=range]:where(.svelte-1j9upyo)::-webkit-slider-thumb{z-index:2;background-image:var(--vnc-thumb-background);background-size:var(thumb-background-size);background-position:center center;background-repeat:no-repeat}.vnc-input--vnc-range.svelte-1j9upyo input[type=range]:where(.svelte-1j9upyo)::-moz-range-thumb{z-index:2;background-image:var(--vnc-thumb-background);background-size:var(thumb-background-size);background-position:center center;background-repeat:no-repeat}.vnc-btn.svelte-uprp2f{color:var(--vnc-brand-color-secondary);background-color:var(--vnc-brand-color-light);border-radius:5rem;font-weight:700;min-height:3rem;padding-inline:1.5rem}.vnc-svg-icon.svelte-1hywxi2{display:inline-grid;place-content:center;font-size:1.4em;aspect-ratio:1/1}.vnc-svg-icon svg{display:inline-block;width:1em;height:1em;stroke-width:0;stroke:currentColor;fill:currentColor}.vnc-tabs.svelte-v5rx30{--vnc-tab-color: var(--vnc-widget-color);--vnc-tab-bg-color: var(--vnc-widget-bg-color);--vnc-tab-active-color: var(--vnc-widget-active-color);--vnc-tab-active-bg-color: var(--vnc-widget-active-bg-color)}.vnc-tab-nav.svelte-v5rx30 .vnc-btn:where(.svelte-v5rx30){display:flex;color:var(--vnc-tab-color);background-color:var(--vnc-tab-bg-color);transition:all .32s ease-in-out;border:none;justify-content:start;font-size:1.25rem;gap:.625rem;height:4rem;font-weight:700;flex-direction:column;align-items:center;justify-content:center}.vnc-tab-nav.svelte-v5rx30 .vnc-btn:where(.svelte-v5rx30):active{color:var(--vnc-tab-color);background-color:var(--vnc-tab-bg-color)}.vnc-tab-nav.svelte-v5rx30 .vnc-btn.vnc-is-active:where(.svelte-v5rx30){color:var(--vnc-tab-active-color);background-color:var(--vnc-tab-active-bg-color)}@container container-grid (min-width: 576px){.vnc-tab-nav.svelte-v5rx30 .vnc-btn:where(.svelte-v5rx30){font-size:1.75rem;justify-content:start;flex-direction:row}.vnc-tab-nav.svelte-v5rx30 .vnc-btn:where(.svelte-v5rx30) .vnc-caption:where(.svelte-v5rx30){display:inline}}.vnc-tab-group.svelte-v5rx30{display:grid}.vnc-tab-group__item.svelte-v5rx30{grid-column-start:1;grid-column-end:2;grid-row-start:1;grid-row-end:2}.vnc-svelte-app.svelte-mi0n5p{color:#fff}
/*$vite$:1*/`,document.head.appendChild(Xr);const Ci=!1;var sr=Array.isArray,lr=Array.from,ki=Object.defineProperty,Le=Object.getOwnPropertyDescriptor,Wr=Object.getOwnPropertyDescriptors,Mi=Object.prototype,Ri=Array.prototype,wt=Object.getPrototypeOf;function Di(e){return typeof e=="function"}const Q=()=>{};function Ui(e){return e()}function Tt(e){for(var t=0;t<e.length;t++)e[t]()}const oe=2,Zr=4,at=8,At=16,se=32,ot=64,ur=128,Ie=256,St=512,q=1024,we=2048,De=4096,de=8192,Ue=16384,Jr=32768,st=65536,Gi=1<<17,Fi=1<<19,Yr=1<<20,ye=Symbol("$state"),ji=Symbol("legacy props"),zi=Symbol("");function Qr(e){return e===this.v}function Kr(e,t){return e!=e?t==t:e!==t||e!==null&&typeof e=="object"||typeof e=="function"}function cr(e){return!Kr(e,this.v)}function Vi(e){throw new Error("effect_in_teardown")}function $i(){throw new Error("effect_in_unowned_derived")}function qi(e){throw new Error("effect_orphan")}function Xi(){throw new Error("effect_update_depth_exceeded")}function Wi(e){throw new Error("props_invalid_value")}function Zi(){throw new Error("state_descriptors_fixed")}function Ji(){throw new Error("state_prototype_fixed")}function Yi(){throw new Error("state_unsafe_local_read")}function Qi(){throw new Error("state_unsafe_mutation")}let Ge=!1;function Ki(){Ge=!0}function ie(e){return{f:0,v:e,reactions:null,equals:Qr,version:0}}function Ht(e,t=!1){var n;const r=ie(e);return t||(r.equals=cr),Ge&&k!==null&&k.l!==null&&((n=k.l).s??(n.s=[])).push(r),r}function le(e,t=!1){return ea(Ht(e,t))}function ea(e){return L!==null&&L.f&oe&&(ge===null?Ea([e]):ge.push(e)),e}function en(e,t){return G(e,xe(()=>y(e))),t}function G(e,t){return L!==null&&Gt()&&L.f&(oe|At)&&(ge===null||!ge.includes(e))&&Qi(),tn(e,t)}function tn(e,t){return e.equals(t)||(e.v=t,e.version=wn(),rn(e,we),Gt()&&A!==null&&A.f&q&&!(A.f&se)&&(z!==null&&z.includes(e)?(ce(A,we),jt(A)):Te===null?wa([e]):Te.push(e))),t}function rn(e,t){var r=e.reactions;if(r!==null)for(var n=Gt(),i=r.length,a=0;a<i;a++){var s=r[a],c=s.f;c&we||!n&&s===A||(ce(s,t),c&(q|Ie)&&(c&oe?rn(s,De):jt(s)))}}const ta=1,ra=2,na=16,ia=1,aa=2,oa=4,sa=8,la=16,ua=4,ca=1,fa=2,K=Symbol();let nn=!1;function Fe(e,t=null,r){if(typeof e!="object"||e===null||ye in e)return e;const n=wt(e);if(n!==Mi&&n!==Ri)return e;var i=new Map,a=sr(e),s=ie(0);a&&i.set("length",ie(e.length));var c;return new Proxy(e,{defineProperty(u,l,o){(!("value"in o)||o.configurable===!1||o.enumerable===!1||o.writable===!1)&&Zi();var h=i.get(l);return h===void 0?(h=ie(o.value),i.set(l,h)):G(h,Fe(o.value,c)),!0},deleteProperty(u,l){var o=i.get(l);if(o===void 0)l in u&&i.set(l,ie(K));else{if(a&&typeof l=="string"){var h=i.get("length"),f=Number(l);Number.isInteger(f)&&f<h.v&&G(h,f)}G(o,K),an(s)}return!0},get(u,l,o){var p;if(l===ye)return e;var h=i.get(l),f=l in u;if(h===void 0&&(!f||(p=Le(u,l))!=null&&p.writable)&&(h=ie(Fe(f?u[l]:K,c)),i.set(l,h)),h!==void 0){var v=y(h);return v===K?void 0:v}return Reflect.get(u,l,o)},getOwnPropertyDescriptor(u,l){var o=Reflect.getOwnPropertyDescriptor(u,l);if(o&&"value"in o){var h=i.get(l);h&&(o.value=y(h))}else if(o===void 0){var f=i.get(l),v=f==null?void 0:f.v;if(f!==void 0&&v!==K)return{enumerable:!0,configurable:!0,value:v,writable:!0}}return o},has(u,l){var v;if(l===ye)return!0;var o=i.get(l),h=o!==void 0&&o.v!==K||Reflect.has(u,l);if(o!==void 0||A!==null&&(!h||(v=Le(u,l))!=null&&v.writable)){o===void 0&&(o=ie(h?Fe(u[l],c):K),i.set(l,o));var f=y(o);if(f===K)return!1}return h},set(u,l,o,h){var b;var f=i.get(l),v=l in u;if(a&&l==="length")for(var p=o;p<f.v;p+=1){var g=i.get(p+"");g!==void 0?G(g,K):p in u&&(g=ie(K),i.set(p+"",g))}f===void 0?(!v||(b=Le(u,l))!=null&&b.writable)&&(f=ie(void 0),G(f,Fe(o,c)),i.set(l,f)):(v=f.v!==K,G(f,Fe(o,c)));var _=Reflect.getOwnPropertyDescriptor(u,l);if(_!=null&&_.set&&_.set.call(h,o),!v){if(a&&typeof l=="string"){var d=i.get("length"),m=Number(l);Number.isInteger(m)&&m>=d.v&&G(d,m+1)}an(s)}return!0},ownKeys(u){y(s);var l=Reflect.ownKeys(u).filter(f=>{var v=i.get(f);return v===void 0||v.v!==K});for(var[o,h]of i)h.v!==K&&!(o in u)&&l.push(o);return l},setPrototypeOf(){Ji()}})}function an(e,t=1){G(e,e.v+t)}function on(e){return e!==null&&typeof e=="object"&&ye in e?e[ye]:e}function ha(e,t){return Object.is(on(e),on(t))}var sn,ln,un;function va(){if(sn===void 0){sn=window;var e=Element.prototype,t=Node.prototype;ln=Le(t,"firstChild").get,un=Le(t,"nextSibling").get,e.__click=void 0,e.__className="",e.__attributes=null,e.__styles=null,e.__e=void 0,Text.prototype.__t=void 0}}function Ot(e=""){return document.createTextNode(e)}function je(e){return ln.call(e)}function Pt(e){return un.call(e)}function T(e,t){return je(e)}function ze(e,t){{var r=je(e);return r instanceof Comment&&r.data===""?Pt(r):r}}function F(e,t=1,r=!1){let n=e;for(;t--;)n=Pt(n);return n}function da(e){e.textContent=""}function Bt(e){var t=oe|we;A===null?t|=Ie:A.f|=Yr;var r=L!==null&&L.f&oe?L:null;const n={children:null,ctx:k,deps:null,equals:Qr,f:t,fn:e,reactions:null,v:null,version:0,parent:r??A};return r!==null&&(r.children??(r.children=[])).push(n),n}function pe(e){const t=Bt(e);return t.equals=cr,t}function cn(e){var t=e.children;if(t!==null){e.children=null;for(var r=0;r<t.length;r+=1){var n=t[r];n.f&oe?fr(n):Ce(n)}}}function pa(e){for(var t=e.parent;t!==null;){if(!(t.f&oe))return t;t=t.parent}return null}function fn(e){var t,r=A;ae(pa(e));try{cn(e),t=Tn(e)}finally{ae(r)}return t}function hn(e){var t=fn(e),r=(We||e.f&Ie)&&e.deps!==null?De:q;ce(e,r),e.equals(t)||(e.v=t,e.version=wn())}function fr(e){cn(e),ct(e,0),ce(e,Ue),e.v=e.children=e.deps=e.ctx=e.reactions=null}function vn(e){A===null&&L===null&&qi(),L!==null&&L.f&Ie&&$i(),mr&&Vi()}function ma(e,t){var r=t.last;r===null?t.last=t.first=e:(r.next=e,e.prev=r,t.last=e)}function Ve(e,t,r,n=!0){var i=(e&ot)!==0,a=A,s={ctx:k,deps:null,deriveds:null,nodes_start:null,nodes_end:null,f:e|we,first:null,fn:t,last:null,next:null,parent:i?null:a,prev:null,teardown:null,transitions:null,version:0};if(r){var c=Xe;try{yn(!0),ft(s),s.f|=Jr}catch(o){throw Ce(s),o}finally{yn(c)}}else t!==null&&jt(s);var u=r&&s.deps===null&&s.first===null&&s.nodes_start===null&&s.teardown===null&&(s.f&Yr)===0;if(!u&&!i&&n&&(a!==null&&ma(s,a),L!==null&&L.f&oe)){var l=L;(l.children??(l.children=[])).push(s)}return s}function Nt(e){const t=Ve(at,null,!1);return ce(t,q),t.teardown=e,t}function hr(e){vn();var t=A!==null&&(A.f&se)!==0&&k!==null&&!k.m;if(t){var r=k;(r.e??(r.e=[])).push({fn:e,effect:A,reaction:L})}else{var n=Lt(e);return n}}function ga(e){return vn(),$e(e)}function ba(e){const t=Ve(ot,e,!0);return()=>{Ce(t)}}function Lt(e){return Ve(Zr,e,!1)}function ue(e,t,r,n){var i=k,a={effect:null,ran:!1};i.l.r1.push(a),a.effect=$e(()=>{e(),!a.ran&&(a.ran=!0,G(i.l.r2,!0),xe(t))})}function It(){var e=k;$e(()=>{if(y(e.l.r2)){for(var t of e.l.r1){var r=t.effect;r.f&q&&ce(r,De),Ze(r)&&ft(r),t.ran=!1}e.l.r2.v=!1}})}function $e(e){return Ve(at,e,!0)}function C(e){return Ct(e)}function Ct(e,t=0){return Ve(at|At|t,e,!0)}function qe(e,t=!0){return Ve(at|se,e,!0,t)}function dn(e){var t=e.teardown;if(t!==null){const r=mr,n=L;xn(!0),me(null);try{t.call(null)}finally{xn(r),me(n)}}}function pn(e){var t=e.deriveds;if(t!==null){e.deriveds=null;for(var r=0;r<t.length;r+=1)fr(t[r])}}function mn(e,t=!1){var r=e.first;for(e.first=e.last=null;r!==null;){var n=r.next;Ce(r,t),r=n}}function _a(e){for(var t=e.first;t!==null;){var r=t.next;t.f&se||Ce(t),t=r}}function Ce(e,t=!0){var r=!1;if((t||e.f&Fi)&&e.nodes_start!==null){for(var n=e.nodes_start,i=e.nodes_end;n!==null;){var a=n===i?null:Pt(n);n.remove(),n=a}r=!0}mn(e,t&&!r),pn(e),ct(e,0),ce(e,Ue);var s=e.transitions;if(s!==null)for(const u of s)u.stop();dn(e);var c=e.parent;c!==null&&c.first!==null&&gn(e),e.next=e.prev=e.teardown=e.ctx=e.deps=e.fn=e.nodes_start=e.nodes_end=null}function gn(e){var t=e.parent,r=e.prev,n=e.next;r!==null&&(r.next=n),n!==null&&(n.prev=r),t!==null&&(t.first===e&&(t.first=n),t.last===e&&(t.last=r))}function kt(e,t){var r=[];vr(e,r,!0),bn(r,()=>{Ce(e),t&&t()})}function bn(e,t){var r=e.length;if(r>0){var n=()=>--r||t();for(var i of e)i.out(n)}else t()}function vr(e,t,r){if(!(e.f&de)){if(e.f^=de,e.transitions!==null)for(const s of e.transitions)(s.is_global||r)&&t.push(s);for(var n=e.first;n!==null;){var i=n.next,a=(n.f&st)!==0||(n.f&se)!==0;vr(n,t,a?r:!1),n=i}}}function Mt(e){_n(e,!0)}function _n(e,t){if(e.f&de){Ze(e)&&ft(e),e.f^=de;for(var r=e.first;r!==null;){var n=r.next,i=(r.f&st)!==0||(r.f&se)!==0;_n(r,i?t:!1),r=n}if(e.transitions!==null)for(const a of e.transitions)(a.is_global||t)&&a.in()}}let dr=!1,pr=[];function ya(){dr=!1;const e=pr.slice();pr=[],Tt(e)}function lt(e){dr||(dr=!0,queueMicrotask(ya)),pr.push(e)}function xa(e){throw new Error("lifecycle_outside_component")}let Rt=!1,Dt=!1,Ut=null,Xe=!1,mr=!1;function yn(e){Xe=e}function xn(e){mr=e}let gr=[],ut=0,Bl=[],L=null;function me(e){L=e}let A=null;function ae(e){A=e}let ge=null;function Ea(e){ge=e}let z=null,ee=0,Te=null;function wa(e){Te=e}let En=0,We=!1,k=null;function wn(){return++En}function Gt(){return!Ge||k!==null&&k.l===null}function Ze(e){var s,c;var t=e.f;if(t&we)return!0;if(t&De){var r=e.deps,n=(t&Ie)!==0;if(r!==null){var i;if(t&St){for(i=0;i<r.length;i++)((s=r[i]).reactions??(s.reactions=[])).push(e);e.f^=St}for(i=0;i<r.length;i++){var a=r[i];if(Ze(a)&&hn(a),n&&A!==null&&!We&&!((c=a==null?void 0:a.reactions)!=null&&c.includes(e))&&(a.reactions??(a.reactions=[])).push(e),a.version>e.version)return!0}}n||ce(e,q)}return!1}function Ta(e,t){for(var r=t;r!==null;){if(r.f&ur)try{r.fn(e);return}catch{r.f^=ur}r=r.parent}throw Rt=!1,e}function Aa(e){return(e.f&Ue)===0&&(e.parent===null||(e.parent.f&ur)===0)}function Ft(e,t,r,n){if(Rt){if(r===null&&(Rt=!1),Aa(t))throw e;return}r!==null&&(Rt=!0);{Ta(e,t);return}}function Tn(e){var f;var t=z,r=ee,n=Te,i=L,a=We,s=ge,c=k,u=e.f;z=null,ee=0,Te=null,L=u&(se|ot)?null:e,We=!Xe&&(u&Ie)!==0,ge=null,k=e.ctx;try{var l=(0,e.fn)(),o=e.deps;if(z!==null){var h;if(ct(e,ee),o!==null&&ee>0)for(o.length=ee+z.length,h=0;h<z.length;h++)o[ee+h]=z[h];else e.deps=o=z;if(!We)for(h=ee;h<o.length;h++)((f=o[h]).reactions??(f.reactions=[])).push(e)}else o!==null&&ee<o.length&&(ct(e,ee),o.length=ee);return l}finally{z=t,ee=r,Te=n,L=i,We=a,ge=s,k=c}}function Sa(e,t){let r=t.reactions;if(r!==null){var n=r.indexOf(e);if(n!==-1){var i=r.length-1;i===0?r=t.reactions=null:(r[n]=r[i],r.pop())}}r===null&&t.f&oe&&(z===null||!z.includes(t))&&(ce(t,De),t.f&(Ie|St)||(t.f^=St),ct(t,0))}function ct(e,t){var r=e.deps;if(r!==null)for(var n=t;n<r.length;n++)Sa(e,r[n])}function ft(e){var t=e.f;if(!(t&Ue)){ce(e,q);var r=A,n=k;A=e;try{t&At?_a(e):mn(e),pn(e),dn(e);var i=Tn(e);e.teardown=typeof i=="function"?i:null,e.version=En}catch(a){Ft(a,e,r,n||e.ctx)}finally{A=r}}}function Ha(){if(ut>1e3){ut=0;try{Xi()}catch(e){if(Ut!==null)Ft(e,Ut,null);else throw e}}ut++}function Oa(e){var t=e.length;if(t!==0){Ha();var r=Xe;Xe=!0;try{for(var n=0;n<t;n++){var i=e[n];i.f&q||(i.f^=q);var a=[];An(i,a),Pa(a)}}finally{Xe=r}}}function Pa(e){var t=e.length;if(t!==0)for(var r=0;r<t;r++){var n=e[r];if(!(n.f&(Ue|de)))try{Ze(n)&&(ft(n),n.deps===null&&n.first===null&&n.nodes_start===null&&(n.teardown===null?gn(n):n.fn=null))}catch(i){Ft(i,n,null,n.ctx)}}}function Ba(){if(Dt=!1,ut>1001)return;const e=gr;gr=[],Oa(e),Dt||(ut=0,Ut=null)}function jt(e){Dt||(Dt=!0,queueMicrotask(Ba)),Ut=e;for(var t=e;t.parent!==null;){t=t.parent;var r=t.f;if(r&(ot|se)){if(!(r&q))return;t.f^=q}}gr.push(t)}function An(e,t){var r=e.first,n=[];e:for(;r!==null;){var i=r.f,a=(i&se)!==0,s=a&&(i&q)!==0,c=r.next;if(!s&&!(i&de))if(i&at){if(a)r.f^=q;else try{Ze(r)&&ft(r)}catch(h){Ft(h,r,null,r.ctx)}var u=r.first;if(u!==null){r=u;continue}}else i&Zr&&n.push(r);if(c===null){let h=r.parent;for(;h!==null;){if(e===h)break e;var l=h.next;if(l!==null){r=l;continue e}h=h.parent}}r=c}for(var o=0;o<n.length;o++)u=n[o],t.push(u),An(u,t)}function y(e){var o;var t=e.f,r=(t&oe)!==0;if(r&&t&Ue){var n=fn(e);return fr(e),n}if(L!==null){ge!==null&&ge.includes(e)&&Yi();var i=L.deps;z===null&&i!==null&&i[ee]===e?ee++:z===null?z=[e]:z.push(e),Te!==null&&A!==null&&A.f&q&&!(A.f&se)&&Te.includes(e)&&(ce(A,we),jt(A))}else if(r&&e.deps===null)for(var a=e,s=a.parent,c=a;s!==null;)if(s.f&oe){var u=s;c=u,s=u.parent}else{var l=s;(o=l.deriveds)!=null&&o.includes(c)||(l.deriveds??(l.deriveds=[])).push(c);break}return r&&(a=e,Ze(a)&&hn(a)),e.v}function xe(e){const t=L;try{return L=null,e()}finally{L=t}}const Na=~(we|De|q);function ce(e,t){e.f=e.f&Na|t}function Ae(e,t=!1,r){k={p:k,c:null,e:null,m:!1,s:e,x:null,l:null},Ge&&!t&&(k.l={s:null,u:null,r1:[],r2:ie(!1)})}function Se(e){const t=k;if(t!==null){e!==void 0&&(t.x=e);const s=t.e;if(s!==null){var r=A,n=L;t.e=null;try{for(var i=0;i<s.length;i++){var a=s[i];ae(a.effect),me(a.reaction),Lt(a.fn)}}finally{ae(r),me(n)}}k=t.p,t.m=!0}return e||{}}function fe(e){if(!(typeof e!="object"||!e||e instanceof EventTarget)){if(ye in e)br(e);else if(!Array.isArray(e))for(let t in e){const r=e[t];typeof r=="object"&&r&&ye in r&&br(r)}}}function br(e,t=new Set){if(typeof e=="object"&&e!==null&&!(e instanceof EventTarget)&&!t.has(e)){t.add(e),e instanceof Date&&e.getTime();for(let n in e)try{br(e[n],t)}catch{}const r=wt(e);if(r!==Object.prototype&&r!==Array.prototype&&r!==Map.prototype&&r!==Set.prototype&&r!==Date.prototype){const n=Wr(r);for(let i in n){const a=n[i].get;if(a)try{a.call(e)}catch{}}}}}let Sn=!1;function La(){Sn||(Sn=!0,document.addEventListener("reset",e=>{Promise.resolve().then(()=>{var t;if(!e.defaultPrevented)for(const r of e.target.elements)(t=r.__on_r)==null||t.call(r)})},{capture:!0}))}function Hn(e){var t=L,r=A;me(null),ae(null);try{return e()}finally{me(t),ae(r)}}function On(e,t,r,n=r){e.addEventListener(t,()=>Hn(r));const i=e.__on_r;i?e.__on_r=()=>{i(),n()}:e.__on_r=n,La()}const Ia=new Set,Pn=new Set;function Ca(e,t,r,n){function i(a){if(n.capture||ht.call(t,a),!a.cancelBubble)return Hn(()=>r.call(this,a))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?lt(()=>{t.addEventListener(e,i,n)}):t.addEventListener(e,i,n),i}function Ee(e,t,r,n,i){var a={capture:n,passive:i},s=Ca(e,t,r,a);(t===document.body||t===window||t===document)&&Nt(()=>{t.removeEventListener(e,s,a)})}function ht(e){var m;var t=this,r=t.ownerDocument,n=e.type,i=((m=e.composedPath)==null?void 0:m.call(e))||[],a=i[0]||e.target,s=0,c=e.__root;if(c){var u=i.indexOf(c);if(u!==-1&&(t===document||t===window)){e.__root=t;return}var l=i.indexOf(t);if(l===-1)return;u<=l&&(s=u)}if(a=i[s]||e.target,a!==t){ki(e,"currentTarget",{configurable:!0,get(){return a||r}});var o=L,h=A;me(null),ae(null);try{for(var f,v=[];a!==null;){var p=a.assignedSlot||a.parentNode||a.host||null;try{var g=a["__"+n];if(g!==void 0&&!a.disabled)if(sr(g)){var[_,...d]=g;_.apply(a,[e,...d])}else g.call(a,e)}catch(b){f?v.push(b):f=b}if(e.cancelBubble||p===t||p===null)break;a=p}if(f){for(let b of v)queueMicrotask(()=>{throw b});throw f}}finally{e.__root=t,delete e.currentTarget,me(o),ae(h)}}}function Bn(e){var t=document.createElement("template");return t.innerHTML=e,t.content}function vt(e,t){var r=A;r.nodes_start===null&&(r.nodes_start=e,r.nodes_end=t)}function R(e,t){var r=(t&ca)!==0,n=(t&fa)!==0,i,a=!e.startsWith("<!>");return()=>{i===void 0&&(i=Bn(a?e:"<!>"+e),r||(i=je(i)));var s=n?document.importNode(i,!0):i.cloneNode(!0);if(r){var c=je(s),u=s.lastChild;vt(c,u)}else vt(s,s);return s}}function _r(e,t,r="svg"){var n=!e.startsWith("<!>"),i=`<${r}>${n?e:"<!>"+e}</${r}>`,a;return()=>{if(!a){var s=Bn(i),c=je(s);a=je(c)}var u=a.cloneNode(!0);return vt(u,u),u}}function yr(e=""){{var t=Ot(e+"");return vt(t,t),t}}function Nn(){var e=document.createDocumentFragment(),t=document.createComment(""),r=Ot();return e.append(t,r),vt(t,r),e}function P(e,t){e!==null&&e.before(t)}const ka=["touchstart","touchmove"];function Ma(e){return ka.includes(e)}let xr=!0;function be(e,t){var r=t==null?"":typeof t=="object"?t+"":t;r!==(e.__t??(e.__t=e.nodeValue))&&(e.__t=r,e.nodeValue=r==null?"":r+"")}function Ra(e,t){return Da(e,t)}const Je=new Map;function Da(e,{target:t,anchor:r,props:n={},events:i,context:a,intro:s=!0}){va();var c=new Set,u=h=>{for(var f=0;f<h.length;f++){var v=h[f];if(!c.has(v)){c.add(v);var p=Ma(v);t.addEventListener(v,ht,{passive:p});var g=Je.get(v);g===void 0?(document.addEventListener(v,ht,{passive:p}),Je.set(v,1)):Je.set(v,g+1)}}};u(lr(Ia)),Pn.add(u);var l=void 0,o=ba(()=>{var h=r??t.appendChild(Ot());return qe(()=>{if(a){Ae({});var f=k;f.c=a}i&&(n.$$events=i),xr=s,l=e(h,n)||{},xr=!0,a&&Se()}),()=>{var p;for(var f of c){t.removeEventListener(f,ht);var v=Je.get(f);--v===0?(document.removeEventListener(f,ht),Je.delete(f)):Je.set(f,v)}Pn.delete(u),Ln.delete(l),h!==r&&((p=h.parentNode)==null||p.removeChild(h))}});return Ln.set(l,o),l}let Ln=new WeakMap;function X(e,t,r,n=null,i=!1){var a=e,s=null,c=null,u=null,l=i?st:0;Ct(()=>{u!==(u=!!t())&&(u?(s?Mt(s):s=qe(()=>r(a)),c&&kt(c,()=>{c=null})):(c?Mt(c):n&&(c=qe(()=>n(a))),s&&kt(s,()=>{s=null})))},l)}function zt(e,t){return t}function Ua(e,t,r,n){for(var i=[],a=t.length,s=0;s<a;s++)vr(t[s].e,i,!0);var c=a>0&&i.length===0&&r!==null;if(c){var u=r.parentNode;da(u),u.append(r),n.clear(),He(e,t[0].prev,t[a-1].next)}bn(i,()=>{for(var l=0;l<a;l++){var o=t[l];c||(n.delete(o.k),He(e,o.prev,o.next)),Ce(o.e,!c)}})}function Vt(e,t,r,n,i,a=null){var s=e,c={flags:t,items:new Map,first:null};{var u=e;s=u.appendChild(Ot())}var l=null,o=!1;Ct(()=>{var h=r(),f=sr(h)?h:h==null?[]:lr(h),v=f.length;if(!(o&&v===0)){o=v===0;{var p=L;Ga(f,c,s,i,t,(p.f&de)!==0,n)}a!==null&&(v===0?l?Mt(l):l=qe(()=>a(s)):l!==null&&kt(l,()=>{l=null})),r()}})}function Ga(e,t,r,n,i,a,s){var c=e.length,u=t.items,l=t.first,o=l,h,f=null,v=[],p=[],g,_,d,m;for(m=0;m<c;m+=1){if(g=e[m],_=s(g,m),d=u.get(_),d===void 0){var b=o?o.e.nodes_start:r;f=ja(b,t,f,f===null?t.first:f.next,g,_,m,n,i),u.set(_,f),v=[],p=[],o=f.next;continue}if(Fa(d,g,m),d.e.f&de&&Mt(d.e),d!==o){if(h!==void 0&&h.has(d)){if(v.length<p.length){var E=p[0],x;f=E.prev;var I=v[0],U=v[v.length-1];for(x=0;x<v.length;x+=1)In(v[x],E,r);for(x=0;x<p.length;x+=1)h.delete(p[x]);He(t,I.prev,U.next),He(t,f,I),He(t,U,E),o=E,f=U,m-=1,v=[],p=[]}else h.delete(d),In(d,o,r),He(t,d.prev,d.next),He(t,d,f===null?t.first:f.next),He(t,f,d),f=d;continue}for(v=[],p=[];o!==null&&o.k!==_;)(a||!(o.e.f&de))&&(h??(h=new Set)).add(o),p.push(o),o=o.next;if(o===null)continue;d=o}v.push(d),f=d,o=d.next}if(o!==null||h!==void 0){for(var O=h===void 0?[]:lr(h);o!==null;)(a||!(o.e.f&de))&&O.push(o),o=o.next;var Z=O.length;if(Z>0){var te=c===0?r:null;Ua(t,O,te,u)}}A.first=t.first&&t.first.e,A.last=f&&f.e}function Fa(e,t,r,n){tn(e.v,t),e.i=r}function ja(e,t,r,n,i,a,s,c,u){var l=(u&ta)!==0,o=(u&na)===0,h=l?o?Ht(i):ie(i):i,f=u&ra?ie(s):s,v={i:f,v:h,k:a,a:null,e:null,prev:r,next:n};try{return v.e=qe(()=>c(e,h,f),nn),v.e.prev=r&&r.e,v.e.next=n&&n.e,r===null?t.first=v:(r.next=v,r.e.next=v.e),n!==null&&(n.prev=v,n.e.prev=v.e),v}finally{}}function In(e,t,r){for(var n=e.next?e.next.e.nodes_start:r,i=t?t.e.nodes_start:r,a=e.e.nodes_start;a!==n;){var s=Pt(a);i.before(a),a=s}}function He(e,t,r){t===null?e.first=r:(t.next=r,t.e.next=r&&r.e),r!==null&&(r.prev=t,r.e.prev=t&&t.e)}function $t(e,t,r,n,i){var c;var a=(c=t.$$slots)==null?void 0:c[r],s=!1;a===!0&&(a=t[r==="default"?"children":r],s=!0),a===void 0?i!==null&&i(e):a(e,s?()=>n:n)}function za(e,t,r){var n=e,i,a;Ct(()=>{i!==(i=t())&&(a&&(kt(a),a=null),i&&(a=qe(()=>r(n,i))))},st)}function Va(e,t){var r=e.__attributes??(e.__attributes={});r.checked!==(r.checked=t)&&(e.checked=t)}function w(e,t,r,n){var i=e.__attributes??(e.__attributes={});i[t]!==(i[t]=r)&&(t==="style"&&"__styles"in e&&(e.__styles={}),t==="loading"&&(e[zi]=r),r==null?e.removeAttribute(t):typeof r!="string"&&$a(e).includes(t)?e[t]=r:e.setAttribute(t,r))}var Cn=new Map;function $a(e){var t=Cn.get(e.nodeName);if(t)return t;Cn.set(e.nodeName,t=[]);for(var r,n=wt(e),i=Element.prototype;i!==n;){r=Wr(n);for(var a in r)r[a].set&&t.push(a);n=wt(n)}return t}function qt(e,t){var r=e.__className,n=qa(t);(r!==n||nn)&&(t==null?e.removeAttribute("class"):e.className=n,e.__className=n)}function qa(e){return e??""}function Xa(e,t,r){if(r){if(e.classList.contains(t))return;e.classList.add(t)}else{if(!e.classList.contains(t))return;e.classList.remove(t)}}const Wa=()=>performance.now(),Oe={tick:e=>requestAnimationFrame(e),now:()=>Wa(),tasks:new Set};function kn(e){Oe.tasks.forEach(t=>{t.c(e)||(Oe.tasks.delete(t),t.f())}),Oe.tasks.size!==0&&Oe.tick(kn)}function Za(e){let t;return Oe.tasks.size===0&&Oe.tick(kn),{promise:new Promise(r=>{Oe.tasks.add(t={c:e,f:r})}),abort(){Oe.tasks.delete(t)}}}function Xt(e,t){e.dispatchEvent(new CustomEvent(t))}function Ja(e){if(e==="float")return"cssFloat";if(e==="offset")return"cssOffset";if(e.startsWith("--"))return e;const t=e.split("-");return t.length===1?t[0]:t[0]+t.slice(1).map(r=>r[0].toUpperCase()+r.slice(1)).join("")}function Mn(e){const t={},r=e.split(";");for(const n of r){const[i,a]=n.split(":");if(!i||a===void 0)break;const s=Ja(i.trim());t[s]=a.trim()}return t}const Ya=e=>e;function Er(e,t,r,n){var i=(e&ua)!==0,a="both",s,c=t.inert,u,l;function o(){var g=L,_=A;me(null),ae(null);try{return s??(s=r()(t,(n==null?void 0:n())??{},{direction:a}))}finally{me(g),ae(_)}}var h={is_global:i,in(){t.inert=c,Xt(t,"introstart"),u=wr(t,o(),l,1,()=>{Xt(t,"introend"),u==null||u.abort(),u=s=void 0})},out(g){t.inert=!0,Xt(t,"outrostart"),l=wr(t,o(),u,0,()=>{Xt(t,"outroend"),g==null||g()})},stop:()=>{u==null||u.abort(),l==null||l.abort()}},f=A;if((f.transitions??(f.transitions=[])).push(h),xr){var v=i;if(!v){for(var p=f.parent;p&&p.f&st;)for(;(p=p.parent)&&!(p.f&At););v=!p||(p.f&Jr)!==0}v&&Lt(()=>{xe(()=>h.in())})}}function wr(e,t,r,n,i){var a=n===1;if(Di(t)){var s,c=!1;return lt(()=>{if(!c){var _=t({direction:a?"in":"out"});s=wr(e,_,r,n,i)}}),{abort:()=>{c=!0,s==null||s.abort()},deactivate:()=>s.deactivate(),reset:()=>s.reset(),t:()=>s.t()}}if(r==null||r.deactivate(),!(t!=null&&t.duration))return i(),{abort:Q,deactivate:Q,reset:Q,t:()=>n};const{delay:u=0,css:l,tick:o,easing:h=Ya}=t;var f=[];if(a&&r===void 0&&(o&&o(0,1),l)){var v=Mn(l(0,1));f.push(v,v)}var p=()=>1-n,g=e.animate(f,{duration:u});return g.onfinish=()=>{var _=(r==null?void 0:r.t())??1-n;r==null||r.abort();var d=n-_,m=t.duration*Math.abs(d),b=[];if(m>0){if(l)for(var E=Math.ceil(m/16.666666666666668),x=0;x<=E;x+=1){var I=_+d*h(x/E),U=l(I,1-I);b.push(Mn(U))}p=()=>{var O=g.currentTime;return _+d*h(O/m)},o&&Za(()=>{if(g.playState!=="running")return!1;var O=p();return o(O,1-O),!0})}g=e.animate(b,{duration:m,fill:"forwards"}),g.onfinish=()=>{p=()=>n,o==null||o(n,1-n),i()}},{abort:()=>{g&&(g.cancel(),g.effect=null,g.onfinish=Q)},deactivate:()=>{i=Q},reset:()=>{n===0&&(o==null||o(1,0))},t:()=>p()}}function dt(e,t,r=t){var n=Gt();On(e,"input",()=>{var i=Dn(e)?Un(e.value):e.value;r(i),n&&i!==(i=t())&&(e.value=i??"")}),$e(()=>{var i=t();Dn(e)&&i===Un(e.value)||e.type==="date"&&!i&&!e.value||i!==e.value&&(e.value=i??"")})}const Tr=new Set;function Rn(e,t,r,n,i=n){var a=r.getAttribute("type")==="checkbox",s=e;if(t!==null)for(var c of t)s=s[c]??(s[c]=[]);s.push(r),On(r,"change",()=>{var u=r.__value;a&&(u=Qa(s,u,r.checked)),i(u)},()=>i(a?[]:null)),$e(()=>{var u=n();a?(u=u||[],r.checked=u.includes(r.__value)):r.checked=ha(r.__value,u)}),Nt(()=>{var u=s.indexOf(r);u!==-1&&s.splice(u,1)}),Tr.has(s)||(Tr.add(s),lt(()=>{s.sort((u,l)=>u.compareDocumentPosition(l)===4?-1:1),Tr.delete(s)})),lt(()=>{})}function Qa(e,t,r){for(var n=new Set,i=0;i<e.length;i+=1)e[i].checked&&n.add(e[i].__value);return r||n.delete(t),Array.from(n)}function Dn(e){var t=e.type;return t==="number"||t==="range"}function Un(e){return e===""?null:+e}function Gn(e,t,r){var n=Le(e,t);n&&n.set&&(e[t]=r,Nt(()=>{e[t]=null}))}function Fn(e,t){return e===t||(e==null?void 0:e[ye])===t}function Ka(e={},t,r,n){return Lt(()=>{var i,a;return $e(()=>{i=a,a=[],xe(()=>{e!==r(...a)&&(t(e,...a),i&&Fn(r(...i),e)&&t(null,...i))})}),()=>{lt(()=>{a&&Fn(r(...a),e)&&t(null,...a)})}}),e}function ke(e=!1){const t=k,r=t.l.u;if(!r)return;let n=()=>fe(t.s);if(e){let i=0,a={};const s=Bt(()=>{let c=!1;const u=t.s;for(const l in u)u[l]!==a[l]&&(a[l]=u[l],c=!0);return c&&i++,i});n=()=>y(s)}r.b.length&&ga(()=>{jn(t,n),Tt(r.b)}),hr(()=>{const i=xe(()=>r.m.map(Ui));return()=>{for(const a of i)typeof a=="function"&&a()}}),r.a.length&&hr(()=>{jn(t,n),Tt(r.a)})}function jn(e,t){if(e.l.s)for(const r of e.l.s)y(r);t()}function zn(e,t,r){if(e==null)return t(void 0),r&&r(void 0),Q;const n=xe(()=>e.subscribe(t,r));return n.unsubscribe?()=>n.unsubscribe():n}let Wt=!1;function Ar(e,t,r){const n=r[t]??(r[t]={store:null,source:Ht(void 0),unsubscribe:Q});if(n.store!==e)if(n.unsubscribe(),n.store=e??null,e==null)n.source.v=void 0,n.unsubscribe=Q;else{var i=!0;n.unsubscribe=zn(e,a=>{i?n.source.v=a:G(n.source,a)}),i=!1}return y(n.source)}function Sr(){const e={};return Nt(()=>{for(var t in e)e[t].unsubscribe()}),e}function eo(e){var t=Wt;try{return Wt=!1,[e(),Wt]}finally{Wt=t}}function Vn(e){for(var t=A,r=A;t!==null&&!(t.f&(se|ot));)t=t.parent;try{return ae(t),e()}finally{ae(r)}}function N(e,t,r,n){var U;var i=(r&ia)!==0,a=!Ge||(r&aa)!==0,s=(r&sa)!==0,c=(r&la)!==0,u=!1,l;s?[l,u]=eo(()=>e[t]):l=e[t];var o=ye in e||ji in e,h=((U=Le(e,t))==null?void 0:U.set)??(o&&s&&t in e?O=>e[t]=O:void 0),f=n,v=!0,p=!1,g=()=>(p=!0,v&&(v=!1,c?f=xe(n):f=n),f);l===void 0&&n!==void 0&&(h&&a&&Wi(),l=g(),h&&h(l));var _;if(a)_=()=>{var O=e[t];return O===void 0?g():(v=!0,p=!1,O)};else{var d=Vn(()=>(i?Bt:pe)(()=>e[t]));d.f|=Gi,_=()=>{var O=y(d);return O!==void 0&&(f=void 0),O===void 0?f:O}}if(!(r&oa))return _;if(h){var m=e.$$legacy;return function(O,Z){return arguments.length>0?((!a||!Z||m||u)&&h(Z?_():O),O):_()}}var b=!1,E=!1,x=Ht(l),I=Vn(()=>Bt(()=>{var O=_(),Z=y(x);return b?(b=!1,E=!0,Z):(E=!1,x.v=O)}));return i||(I.equals=cr),function(O,Z){if(arguments.length>0){const te=Z?y(I):a&&s?Fe(O):O;return I.equals(te)||(b=!0,G(x,te),p&&f!==void 0&&(f=te),xe(()=>y(I))),O}return y(I)}}function to(e){k===null&&xa(),Ge&&k.l!==null?ro(k).m.push(e):hr(()=>{const t=xe(e);if(typeof t=="function")return t})}function ro(e){var t=e.l;return t.u??(t.u={a:[],b:[],m:[]})}const no="5";typeof window<"u"&&(window.__svelte||(window.__svelte={v:new Set})).v.add(no),Ki();const Ye=[];function io(e,t){return{subscribe:Zt(e,t).subscribe}}function Zt(e,t=Q){let r=null;const n=new Set;function i(c){if(Kr(e,c)&&(e=c,r)){const u=!Ye.length;for(const l of n)l[1](),Ye.push(l,e);if(u){for(let l=0;l<Ye.length;l+=2)Ye[l][0](Ye[l+1]);Ye.length=0}}}function a(c){i(c(e))}function s(c,u=Q){const l=[c,u];return n.add(l),n.size===1&&(r=t(i,a)||Q),c(e),()=>{n.delete(l),n.size===0&&r&&(r(),r=null)}}return{set:i,update:a,subscribe:s}}function Qe(e,t,r){const n=!Array.isArray(e),i=n?[e]:e;if(!i.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const a=t.length<2;return io(r,(s,c)=>{let u=!1;const l=[];let o=0,h=Q;const f=()=>{if(o)return;h();const p=t(n?l[0]:l,s,c);a?s(p):h=typeof p=="function"?p:Q},v=i.map((p,g)=>zn(p,_=>{l[g]=_,o&=~(1<<g),u&&f()},()=>{o|=1<<g}));return u=!0,f(),function(){Tt(v),h(),u=!1}})}function ao(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Hr,$n;function oo(){if($n)return Hr;$n=1;var e=function(m){return t(m)&&!r(m)};function t(d){return!!d&&typeof d=="object"}function r(d){var m=Object.prototype.toString.call(d);return m==="[object RegExp]"||m==="[object Date]"||a(d)}var n=typeof Symbol=="function"&&Symbol.for,i=n?Symbol.for("react.element"):60103;function a(d){return d.$$typeof===i}function s(d){return Array.isArray(d)?[]:{}}function c(d,m){return m.clone!==!1&&m.isMergeableObject(d)?g(s(d),d,m):d}function u(d,m,b){return d.concat(m).map(function(E){return c(E,b)})}function l(d,m){if(!m.customMerge)return g;var b=m.customMerge(d);return typeof b=="function"?b:g}function o(d){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(d).filter(function(m){return Object.propertyIsEnumerable.call(d,m)}):[]}function h(d){return Object.keys(d).concat(o(d))}function f(d,m){try{return m in d}catch{return!1}}function v(d,m){return f(d,m)&&!(Object.hasOwnProperty.call(d,m)&&Object.propertyIsEnumerable.call(d,m))}function p(d,m,b){var E={};return b.isMergeableObject(d)&&h(d).forEach(function(x){E[x]=c(d[x],b)}),h(m).forEach(function(x){v(d,x)||(f(d,x)&&b.isMergeableObject(m[x])?E[x]=l(x,b)(d[x],m[x],b):E[x]=c(m[x],b))}),E}function g(d,m,b){b=b||{},b.arrayMerge=b.arrayMerge||u,b.isMergeableObject=b.isMergeableObject||e,b.cloneUnlessOtherwiseSpecified=c;var E=Array.isArray(m),x=Array.isArray(d),I=E===x;return I?E?b.arrayMerge(d,m,b):p(d,m,b):c(m,b)}g.all=function(m,b){if(!Array.isArray(m))throw new Error("first argument should be an array");return m.reduce(function(E,x){return g(E,x,b)},{})};var _=g;return Hr=_,Hr}var so=oo();const lo=ao(so);var Or=function(e,t){return Or=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(r[i]=n[i])},Or(e,t)};function Jt(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Or(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}var B=function(){return B=Object.assign||function(t){for(var r,n=1,i=arguments.length;n<i;n++){r=arguments[n];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(t[a]=r[a])}return t},B.apply(this,arguments)};function uo(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)t.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}function Pr(e,t,r){if(r||arguments.length===2)for(var n=0,i=t.length,a;n<i;n++)(a||!(n in t))&&(a||(a=Array.prototype.slice.call(t,0,n)),a[n]=t[n]);return e.concat(a||Array.prototype.slice.call(t))}typeof SuppressedError=="function"&&SuppressedError;function Br(e,t){var r=t&&t.cache?t.cache:mo,n=t&&t.serializer?t.serializer:po,i=t&&t.strategy?t.strategy:fo;return i(e,{cache:r,serializer:n})}function co(e){return e==null||typeof e=="number"||typeof e=="boolean"}function qn(e,t,r,n){var i=co(n)?n:r(n),a=t.get(i);return typeof a>"u"&&(a=e.call(this,n),t.set(i,a)),a}function Xn(e,t,r){var n=Array.prototype.slice.call(arguments,3),i=r(n),a=t.get(i);return typeof a>"u"&&(a=e.apply(this,n),t.set(i,a)),a}function Nr(e,t,r,n,i){return r.bind(t,e,n,i)}function fo(e,t){var r=e.length===1?qn:Xn;return Nr(e,this,r,t.cache.create(),t.serializer)}function ho(e,t){return Nr(e,this,Xn,t.cache.create(),t.serializer)}function vo(e,t){return Nr(e,this,qn,t.cache.create(),t.serializer)}var po=function(){return JSON.stringify(arguments)};function Lr(){this.cache=Object.create(null)}Lr.prototype.get=function(e){return this.cache[e]},Lr.prototype.set=function(e,t){this.cache[e]=t};var mo={create:function(){return new Lr}},Ir={variadic:ho,monadic:vo},S;(function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(S||(S={}));var D;(function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"})(D||(D={}));var Ke;(function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"})(Ke||(Ke={}));function Wn(e){return e.type===D.literal}function go(e){return e.type===D.argument}function Zn(e){return e.type===D.number}function Jn(e){return e.type===D.date}function Yn(e){return e.type===D.time}function Qn(e){return e.type===D.select}function Kn(e){return e.type===D.plural}function bo(e){return e.type===D.pound}function ei(e){return e.type===D.tag}function ti(e){return!!(e&&typeof e=="object"&&e.type===Ke.number)}function Cr(e){return!!(e&&typeof e=="object"&&e.type===Ke.dateTime)}var ri=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,_o=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;function yo(e){var t={};return e.replace(_o,function(r){var n=r.length;switch(r[0]){case"G":t.era=n===4?"long":n===5?"narrow":"short";break;case"y":t.year=n===2?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw new RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":t.month=["numeric","2-digit","short","long","narrow"][n-1];break;case"w":case"W":throw new RangeError("`w/W` (week) patterns are not supported");case"d":t.day=["numeric","2-digit"][n-1];break;case"D":case"F":case"g":throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":t.weekday=n===4?"long":n===5?"narrow":"short";break;case"e":if(n<4)throw new RangeError("`e..eee` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][n-4];break;case"c":if(n<4)throw new RangeError("`c..ccc` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][n-4];break;case"a":t.hour12=!0;break;case"b":case"B":throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":t.hourCycle="h12",t.hour=["numeric","2-digit"][n-1];break;case"H":t.hourCycle="h23",t.hour=["numeric","2-digit"][n-1];break;case"K":t.hourCycle="h11",t.hour=["numeric","2-digit"][n-1];break;case"k":t.hourCycle="h24",t.hour=["numeric","2-digit"][n-1];break;case"j":case"J":case"C":throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":t.minute=["numeric","2-digit"][n-1];break;case"s":t.second=["numeric","2-digit"][n-1];break;case"S":case"A":throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":t.timeZoneName=n<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),t}var xo=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i;function Eo(e){if(e.length===0)throw new Error("Number skeleton cannot be empty");for(var t=e.split(xo).filter(function(f){return f.length>0}),r=[],n=0,i=t;n<i.length;n++){var a=i[n],s=a.split("/");if(s.length===0)throw new Error("Invalid number skeleton");for(var c=s[0],u=s.slice(1),l=0,o=u;l<o.length;l++){var h=o[l];if(h.length===0)throw new Error("Invalid number skeleton")}r.push({stem:c,options:u})}return r}function wo(e){return e.replace(/^(.*?)-/,"")}var ni=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,ii=/^(@+)?(\+|#+)?[rs]?$/g,To=/(\*)(0+)|(#+)(0+)|(0+)/g,ai=/^(0+)$/;function oi(e){var t={};return e[e.length-1]==="r"?t.roundingPriority="morePrecision":e[e.length-1]==="s"&&(t.roundingPriority="lessPrecision"),e.replace(ii,function(r,n,i){return typeof i!="string"?(t.minimumSignificantDigits=n.length,t.maximumSignificantDigits=n.length):i==="+"?t.minimumSignificantDigits=n.length:n[0]==="#"?t.maximumSignificantDigits=n.length:(t.minimumSignificantDigits=n.length,t.maximumSignificantDigits=n.length+(typeof i=="string"?i.length:0)),""}),t}function si(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function Ao(e){var t;if(e[0]==="E"&&e[1]==="E"?(t={notation:"engineering"},e=e.slice(2)):e[0]==="E"&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if(r==="+!"?(t.signDisplay="always",e=e.slice(2)):r==="+?"&&(t.signDisplay="exceptZero",e=e.slice(2)),!ai.test(e))throw new Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}function li(e){var t={},r=si(e);return r||t}function So(e){for(var t={},r=0,n=e;r<n.length;r++){var i=n[r];switch(i.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=i.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=wo(i.options[0]);continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=B(B(B({},t),{notation:"scientific"}),i.options.reduce(function(u,l){return B(B({},u),li(l))},{}));continue;case"engineering":t=B(B(B({},t),{notation:"engineering"}),i.options.reduce(function(u,l){return B(B({},u),li(l))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(i.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(i.options.length>1)throw new RangeError("integer-width stems only accept a single optional option");i.options[0].replace(To,function(u,l,o,h,f,v){if(l)t.minimumIntegerDigits=o.length;else{if(h&&f)throw new Error("We currently do not support maximum integer digits");if(v)throw new Error("We currently do not support exact integer digits")}return""});continue}if(ai.test(i.stem)){t.minimumIntegerDigits=i.stem.length;continue}if(ni.test(i.stem)){if(i.options.length>1)throw new RangeError("Fraction-precision stems only accept a single optional option");i.stem.replace(ni,function(u,l,o,h,f,v){return o==="*"?t.minimumFractionDigits=l.length:h&&h[0]==="#"?t.maximumFractionDigits=h.length:f&&v?(t.minimumFractionDigits=f.length,t.maximumFractionDigits=f.length+v.length):(t.minimumFractionDigits=l.length,t.maximumFractionDigits=l.length),""});var a=i.options[0];a==="w"?t=B(B({},t),{trailingZeroDisplay:"stripIfInteger"}):a&&(t=B(B({},t),oi(a)));continue}if(ii.test(i.stem)){t=B(B({},t),oi(i.stem));continue}var s=si(i.stem);s&&(t=B(B({},t),s));var c=Ao(i.stem);c&&(t=B(B({},t),c))}return t}var Yt={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]};function Ho(e,t){for(var r="",n=0;n<e.length;n++){var i=e.charAt(n);if(i==="j"){for(var a=0;n+1<e.length&&e.charAt(n+1)===i;)a++,n++;var s=1+(a&1),c=a<2?1:3+(a>>1),u="a",l=Oo(t);for((l=="H"||l=="k")&&(c=0);c-- >0;)r+=u;for(;s-- >0;)r=l+r}else i==="J"?r+="H":r+=i}return r}function Oo(e){var t=e.hourCycle;if(t===void 0&&e.hourCycles&&e.hourCycles.length&&(t=e.hourCycles[0]),t)switch(t){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw new Error("Invalid hourCycle")}var r=e.language,n;r!=="root"&&(n=e.maximize().region);var i=Yt[n||""]||Yt[r||""]||Yt["".concat(r,"-001")]||Yt["001"];return i[0]}var kr,Po=new RegExp("^".concat(ri.source,"*")),Bo=new RegExp("".concat(ri.source,"*$"));function H(e,t){return{start:e,end:t}}var No=!!String.prototype.startsWith&&"_a".startsWith("a",1),Lo=!!String.fromCodePoint,Io=!!Object.fromEntries,Co=!!String.prototype.codePointAt,ko=!!String.prototype.trimStart,Mo=!!String.prototype.trimEnd,Ro=!!Number.isSafeInteger,Do=Ro?Number.isSafeInteger:function(e){return typeof e=="number"&&isFinite(e)&&Math.floor(e)===e&&Math.abs(e)<=9007199254740991},Mr=!0;try{var Uo=hi("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");Mr=((kr=Uo.exec("a"))===null||kr===void 0?void 0:kr[0])==="a"}catch{Mr=!1}var ui=No?function(t,r,n){return t.startsWith(r,n)}:function(t,r,n){return t.slice(n,n+r.length)===r},Rr=Lo?String.fromCodePoint:function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",i=t.length,a=0,s;i>a;){if(s=t[a++],s>1114111)throw RangeError(s+" is not a valid code point");n+=s<65536?String.fromCharCode(s):String.fromCharCode(((s-=65536)>>10)+55296,s%1024+56320)}return n},ci=Io?Object.fromEntries:function(t){for(var r={},n=0,i=t;n<i.length;n++){var a=i[n],s=a[0],c=a[1];r[s]=c}return r},fi=Co?function(t,r){return t.codePointAt(r)}:function(t,r){var n=t.length;if(!(r<0||r>=n)){var i=t.charCodeAt(r),a;return i<55296||i>56319||r+1===n||(a=t.charCodeAt(r+1))<56320||a>57343?i:(i-55296<<10)+(a-56320)+65536}},Go=ko?function(t){return t.trimStart()}:function(t){return t.replace(Po,"")},Fo=Mo?function(t){return t.trimEnd()}:function(t){return t.replace(Bo,"")};function hi(e,t){return new RegExp(e,t)}var Dr;if(Mr){var vi=hi("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");Dr=function(t,r){var n;vi.lastIndex=r;var i=vi.exec(t);return(n=i[1])!==null&&n!==void 0?n:""}}else Dr=function(t,r){for(var n=[];;){var i=fi(t,r);if(i===void 0||di(i)||$o(i))break;n.push(i),r+=i>=65536?2:1}return Rr.apply(void 0,n)};var jo=function(){function e(t,r){r===void 0&&(r={}),this.message=t,this.position={offset:0,line:1,column:1},this.ignoreTag=!!r.ignoreTag,this.locale=r.locale,this.requiresOtherClause=!!r.requiresOtherClause,this.shouldParseSkeletons=!!r.shouldParseSkeletons}return e.prototype.parse=function(){if(this.offset()!==0)throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(t,r,n){for(var i=[];!this.isEOF();){var a=this.char();if(a===123){var s=this.parseArgument(t,n);if(s.err)return s;i.push(s.val)}else{if(a===125&&t>0)break;if(a===35&&(r==="plural"||r==="selectordinal")){var c=this.clonePosition();this.bump(),i.push({type:D.pound,location:H(c,this.clonePosition())})}else if(a===60&&!this.ignoreTag&&this.peek()===47){if(n)break;return this.error(S.UNMATCHED_CLOSING_TAG,H(this.clonePosition(),this.clonePosition()))}else if(a===60&&!this.ignoreTag&&Ur(this.peek()||0)){var s=this.parseTag(t,r);if(s.err)return s;i.push(s.val)}else{var s=this.parseLiteral(t,r);if(s.err)return s;i.push(s.val)}}}return{val:i,err:null}},e.prototype.parseTag=function(t,r){var n=this.clonePosition();this.bump();var i=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:D.literal,value:"<".concat(i,"/>"),location:H(n,this.clonePosition())},err:null};if(this.bumpIf(">")){var a=this.parseMessage(t+1,r,!0);if(a.err)return a;var s=a.val,c=this.clonePosition();if(this.bumpIf("</")){if(this.isEOF()||!Ur(this.char()))return this.error(S.INVALID_TAG,H(c,this.clonePosition()));var u=this.clonePosition(),l=this.parseTagName();return i!==l?this.error(S.UNMATCHED_CLOSING_TAG,H(u,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">")?{val:{type:D.tag,value:i,children:s,location:H(n,this.clonePosition())},err:null}:this.error(S.INVALID_TAG,H(c,this.clonePosition())))}else return this.error(S.UNCLOSED_TAG,H(n,this.clonePosition()))}else return this.error(S.INVALID_TAG,H(n,this.clonePosition()))},e.prototype.parseTagName=function(){var t=this.offset();for(this.bump();!this.isEOF()&&Vo(this.char());)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(t,r){for(var n=this.clonePosition(),i="";;){var a=this.tryParseQuote(r);if(a){i+=a;continue}var s=this.tryParseUnquoted(t,r);if(s){i+=s;continue}var c=this.tryParseLeftAngleBracket();if(c){i+=c;continue}break}var u=H(n,this.clonePosition());return{val:{type:D.literal,value:i,location:u},err:null}},e.prototype.tryParseLeftAngleBracket=function(){return!this.isEOF()&&this.char()===60&&(this.ignoreTag||!zo(this.peek()||0))?(this.bump(),"<"):null},e.prototype.tryParseQuote=function(t){if(this.isEOF()||this.char()!==39)return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if(t==="plural"||t==="selectordinal")break;return null;default:return null}this.bump();var r=[this.char()];for(this.bump();!this.isEOF();){var n=this.char();if(n===39)if(this.peek()===39)r.push(39),this.bump();else{this.bump();break}else r.push(n);this.bump()}return Rr.apply(void 0,r)},e.prototype.tryParseUnquoted=function(t,r){if(this.isEOF())return null;var n=this.char();return n===60||n===123||n===35&&(r==="plural"||r==="selectordinal")||n===125&&t>0?null:(this.bump(),Rr(n))},e.prototype.parseArgument=function(t,r){var n=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(S.EXPECT_ARGUMENT_CLOSING_BRACE,H(n,this.clonePosition()));if(this.char()===125)return this.bump(),this.error(S.EMPTY_ARGUMENT,H(n,this.clonePosition()));var i=this.parseIdentifierIfPossible().value;if(!i)return this.error(S.MALFORMED_ARGUMENT,H(n,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(S.EXPECT_ARGUMENT_CLOSING_BRACE,H(n,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:D.argument,value:i,location:H(n,this.clonePosition())},err:null};case 44:return this.bump(),this.bumpSpace(),this.isEOF()?this.error(S.EXPECT_ARGUMENT_CLOSING_BRACE,H(n,this.clonePosition())):this.parseArgumentOptions(t,r,i,n);default:return this.error(S.MALFORMED_ARGUMENT,H(n,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var t=this.clonePosition(),r=this.offset(),n=Dr(this.message,r),i=r+n.length;this.bumpTo(i);var a=this.clonePosition(),s=H(t,a);return{value:n,location:s}},e.prototype.parseArgumentOptions=function(t,r,n,i){var a,s=this.clonePosition(),c=this.parseIdentifierIfPossible().value,u=this.clonePosition();switch(c){case"":return this.error(S.EXPECT_ARGUMENT_TYPE,H(s,u));case"number":case"date":case"time":{this.bumpSpace();var l=null;if(this.bumpIf(",")){this.bumpSpace();var o=this.clonePosition(),h=this.parseSimpleArgStyleIfPossible();if(h.err)return h;var f=Fo(h.val);if(f.length===0)return this.error(S.EXPECT_ARGUMENT_STYLE,H(this.clonePosition(),this.clonePosition()));var v=H(o,this.clonePosition());l={style:f,styleLocation:v}}var p=this.tryParseArgumentClose(i);if(p.err)return p;var g=H(i,this.clonePosition());if(l&&ui(l==null?void 0:l.style,"::",0)){var _=Go(l.style.slice(2));if(c==="number"){var h=this.parseNumberSkeletonFromString(_,l.styleLocation);return h.err?h:{val:{type:D.number,value:n,location:g,style:h.val},err:null}}else{if(_.length===0)return this.error(S.EXPECT_DATE_TIME_SKELETON,g);var d=_;this.locale&&(d=Ho(_,this.locale));var f={type:Ke.dateTime,pattern:d,location:l.styleLocation,parsedOptions:this.shouldParseSkeletons?yo(d):{}},m=c==="date"?D.date:D.time;return{val:{type:m,value:n,location:g,style:f},err:null}}}return{val:{type:c==="number"?D.number:c==="date"?D.date:D.time,value:n,location:g,style:(a=l==null?void 0:l.style)!==null&&a!==void 0?a:null},err:null}}case"plural":case"selectordinal":case"select":{var b=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(S.EXPECT_SELECT_ARGUMENT_OPTIONS,H(b,B({},b)));this.bumpSpace();var E=this.parseIdentifierIfPossible(),x=0;if(c!=="select"&&E.value==="offset"){if(!this.bumpIf(":"))return this.error(S.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,H(this.clonePosition(),this.clonePosition()));this.bumpSpace();var h=this.tryParseDecimalInteger(S.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,S.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(h.err)return h;this.bumpSpace(),E=this.parseIdentifierIfPossible(),x=h.val}var I=this.tryParsePluralOrSelectOptions(t,c,r,E);if(I.err)return I;var p=this.tryParseArgumentClose(i);if(p.err)return p;var U=H(i,this.clonePosition());return c==="select"?{val:{type:D.select,value:n,options:ci(I.val),location:U},err:null}:{val:{type:D.plural,value:n,options:ci(I.val),offset:x,pluralType:c==="plural"?"cardinal":"ordinal",location:U},err:null}}default:return this.error(S.INVALID_ARGUMENT_TYPE,H(s,u))}},e.prototype.tryParseArgumentClose=function(t){return this.isEOF()||this.char()!==125?this.error(S.EXPECT_ARGUMENT_CLOSING_BRACE,H(t,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var t=0,r=this.clonePosition();!this.isEOF();){var n=this.char();switch(n){case 39:{this.bump();var i=this.clonePosition();if(!this.bumpUntil("'"))return this.error(S.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,H(i,this.clonePosition()));this.bump();break}case 123:{t+=1,this.bump();break}case 125:{if(t>0)t-=1;else return{val:this.message.slice(r.offset,this.offset()),err:null};break}default:this.bump();break}}return{val:this.message.slice(r.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(t,r){var n=[];try{n=Eo(t)}catch{return this.error(S.INVALID_NUMBER_SKELETON,r)}return{val:{type:Ke.number,tokens:n,location:r,parsedOptions:this.shouldParseSkeletons?So(n):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(t,r,n,i){for(var a,s=!1,c=[],u=new Set,l=i.value,o=i.location;;){if(l.length===0){var h=this.clonePosition();if(r!=="select"&&this.bumpIf("=")){var f=this.tryParseDecimalInteger(S.EXPECT_PLURAL_ARGUMENT_SELECTOR,S.INVALID_PLURAL_ARGUMENT_SELECTOR);if(f.err)return f;o=H(h,this.clonePosition()),l=this.message.slice(h.offset,this.offset())}else break}if(u.has(l))return this.error(r==="select"?S.DUPLICATE_SELECT_ARGUMENT_SELECTOR:S.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,o);l==="other"&&(s=!0),this.bumpSpace();var v=this.clonePosition();if(!this.bumpIf("{"))return this.error(r==="select"?S.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:S.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,H(this.clonePosition(),this.clonePosition()));var p=this.parseMessage(t+1,r,n);if(p.err)return p;var g=this.tryParseArgumentClose(v);if(g.err)return g;c.push([l,{value:p.val,location:H(v,this.clonePosition())}]),u.add(l),this.bumpSpace(),a=this.parseIdentifierIfPossible(),l=a.value,o=a.location}return c.length===0?this.error(r==="select"?S.EXPECT_SELECT_ARGUMENT_SELECTOR:S.EXPECT_PLURAL_ARGUMENT_SELECTOR,H(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!s?this.error(S.MISSING_OTHER_CLAUSE,H(this.clonePosition(),this.clonePosition())):{val:c,err:null}},e.prototype.tryParseDecimalInteger=function(t,r){var n=1,i=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(n=-1);for(var a=!1,s=0;!this.isEOF();){var c=this.char();if(c>=48&&c<=57)a=!0,s=s*10+(c-48),this.bump();else break}var u=H(i,this.clonePosition());return a?(s*=n,Do(s)?{val:s,err:null}:this.error(r,u)):this.error(t,u)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var t=this.position.offset;if(t>=this.message.length)throw Error("out of bound");var r=fi(this.message,t);if(r===void 0)throw Error("Offset ".concat(t," is at invalid UTF-16 code unit boundary"));return r},e.prototype.error=function(t,r){return{val:null,err:{kind:t,message:this.message,location:r}}},e.prototype.bump=function(){if(!this.isEOF()){var t=this.char();t===10?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=t<65536?1:2)}},e.prototype.bumpIf=function(t){if(ui(this.message,t,this.offset())){for(var r=0;r<t.length;r++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(t){var r=this.offset(),n=this.message.indexOf(t,r);return n>=0?(this.bumpTo(n),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(t){if(this.offset()>t)throw Error("targetOffset ".concat(t," must be greater than or equal to the current offset ").concat(this.offset()));for(t=Math.min(t,this.message.length);;){var r=this.offset();if(r===t)break;if(r>t)throw Error("targetOffset ".concat(t," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&di(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var t=this.char(),r=this.offset(),n=this.message.charCodeAt(r+(t>=65536?2:1));return n??null},e}();function Ur(e){return e>=97&&e<=122||e>=65&&e<=90}function zo(e){return Ur(e)||e===47}function Vo(e){return e===45||e===46||e>=48&&e<=57||e===95||e>=97&&e<=122||e>=65&&e<=90||e==183||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039}function di(e){return e>=9&&e<=13||e===32||e===133||e>=8206&&e<=8207||e===8232||e===8233}function $o(e){return e>=33&&e<=35||e===36||e>=37&&e<=39||e===40||e===41||e===42||e===43||e===44||e===45||e>=46&&e<=47||e>=58&&e<=59||e>=60&&e<=62||e>=63&&e<=64||e===91||e===92||e===93||e===94||e===96||e===123||e===124||e===125||e===126||e===161||e>=162&&e<=165||e===166||e===167||e===169||e===171||e===172||e===174||e===176||e===177||e===182||e===187||e===191||e===215||e===247||e>=8208&&e<=8213||e>=8214&&e<=8215||e===8216||e===8217||e===8218||e>=8219&&e<=8220||e===8221||e===8222||e===8223||e>=8224&&e<=8231||e>=8240&&e<=8248||e===8249||e===8250||e>=8251&&e<=8254||e>=8257&&e<=8259||e===8260||e===8261||e===8262||e>=8263&&e<=8273||e===8274||e===8275||e>=8277&&e<=8286||e>=8592&&e<=8596||e>=8597&&e<=8601||e>=8602&&e<=8603||e>=8604&&e<=8607||e===8608||e>=8609&&e<=8610||e===8611||e>=8612&&e<=8613||e===8614||e>=8615&&e<=8621||e===8622||e>=8623&&e<=8653||e>=8654&&e<=8655||e>=8656&&e<=8657||e===8658||e===8659||e===8660||e>=8661&&e<=8691||e>=8692&&e<=8959||e>=8960&&e<=8967||e===8968||e===8969||e===8970||e===8971||e>=8972&&e<=8991||e>=8992&&e<=8993||e>=8994&&e<=9e3||e===9001||e===9002||e>=9003&&e<=9083||e===9084||e>=9085&&e<=9114||e>=9115&&e<=9139||e>=9140&&e<=9179||e>=9180&&e<=9185||e>=9186&&e<=9254||e>=9255&&e<=9279||e>=9280&&e<=9290||e>=9291&&e<=9311||e>=9472&&e<=9654||e===9655||e>=9656&&e<=9664||e===9665||e>=9666&&e<=9719||e>=9720&&e<=9727||e>=9728&&e<=9838||e===9839||e>=9840&&e<=10087||e===10088||e===10089||e===10090||e===10091||e===10092||e===10093||e===10094||e===10095||e===10096||e===10097||e===10098||e===10099||e===10100||e===10101||e>=10132&&e<=10175||e>=10176&&e<=10180||e===10181||e===10182||e>=10183&&e<=10213||e===10214||e===10215||e===10216||e===10217||e===10218||e===10219||e===10220||e===10221||e===10222||e===10223||e>=10224&&e<=10239||e>=10240&&e<=10495||e>=10496&&e<=10626||e===10627||e===10628||e===10629||e===10630||e===10631||e===10632||e===10633||e===10634||e===10635||e===10636||e===10637||e===10638||e===10639||e===10640||e===10641||e===10642||e===10643||e===10644||e===10645||e===10646||e===10647||e===10648||e>=10649&&e<=10711||e===10712||e===10713||e===10714||e===10715||e>=10716&&e<=10747||e===10748||e===10749||e>=10750&&e<=11007||e>=11008&&e<=11055||e>=11056&&e<=11076||e>=11077&&e<=11078||e>=11079&&e<=11084||e>=11085&&e<=11123||e>=11124&&e<=11125||e>=11126&&e<=11157||e===11158||e>=11159&&e<=11263||e>=11776&&e<=11777||e===11778||e===11779||e===11780||e===11781||e>=11782&&e<=11784||e===11785||e===11786||e===11787||e===11788||e===11789||e>=11790&&e<=11798||e===11799||e>=11800&&e<=11801||e===11802||e===11803||e===11804||e===11805||e>=11806&&e<=11807||e===11808||e===11809||e===11810||e===11811||e===11812||e===11813||e===11814||e===11815||e===11816||e===11817||e>=11818&&e<=11822||e===11823||e>=11824&&e<=11833||e>=11834&&e<=11835||e>=11836&&e<=11839||e===11840||e===11841||e===11842||e>=11843&&e<=11855||e>=11856&&e<=11857||e===11858||e>=11859&&e<=11903||e>=12289&&e<=12291||e===12296||e===12297||e===12298||e===12299||e===12300||e===12301||e===12302||e===12303||e===12304||e===12305||e>=12306&&e<=12307||e===12308||e===12309||e===12310||e===12311||e===12312||e===12313||e===12314||e===12315||e===12316||e===12317||e>=12318&&e<=12319||e===12320||e===12336||e===64830||e===64831||e>=65093&&e<=65094}function Gr(e){e.forEach(function(t){if(delete t.location,Qn(t)||Kn(t))for(var r in t.options)delete t.options[r].location,Gr(t.options[r].value);else Zn(t)&&ti(t.style)||(Jn(t)||Yn(t))&&Cr(t.style)?delete t.style.location:ei(t)&&Gr(t.children)})}function qo(e,t){t===void 0&&(t={}),t=B({shouldParseSkeletons:!0,requiresOtherClause:!0},t);var r=new jo(e,t).parse();if(r.err){var n=SyntaxError(S[r.err.kind]);throw n.location=r.err.location,n.originalMessage=r.err.message,n}return t!=null&&t.captureLocation||Gr(r.val),r.val}var et;(function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"})(et||(et={}));var Qt=function(e){Jt(t,e);function t(r,n,i){var a=e.call(this,r)||this;return a.code=n,a.originalMessage=i,a}return t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),pi=function(e){Jt(t,e);function t(r,n,i,a){return e.call(this,'Invalid values for "'.concat(r,'": "').concat(n,'". Options are "').concat(Object.keys(i).join('", "'),'"'),et.INVALID_VALUE,a)||this}return t}(Qt),Xo=function(e){Jt(t,e);function t(r,n,i){return e.call(this,'Value for "'.concat(r,'" must be of type ').concat(n),et.INVALID_VALUE,i)||this}return t}(Qt),Wo=function(e){Jt(t,e);function t(r,n){return e.call(this,'The intl string context variable "'.concat(r,'" was not provided to the string "').concat(n,'"'),et.MISSING_VALUE,n)||this}return t}(Qt),W;(function(e){e[e.literal=0]="literal",e[e.object=1]="object"})(W||(W={}));function Zo(e){return e.length<2?e:e.reduce(function(t,r){var n=t[t.length-1];return!n||n.type!==W.literal||r.type!==W.literal?t.push(r):n.value+=r.value,t},[])}function Jo(e){return typeof e=="function"}function Kt(e,t,r,n,i,a,s){if(e.length===1&&Wn(e[0]))return[{type:W.literal,value:e[0].value}];for(var c=[],u=0,l=e;u<l.length;u++){var o=l[u];if(Wn(o)){c.push({type:W.literal,value:o.value});continue}if(bo(o)){typeof a=="number"&&c.push({type:W.literal,value:r.getNumberFormat(t).format(a)});continue}var h=o.value;if(!(i&&h in i))throw new Wo(h,s);var f=i[h];if(go(o)){(!f||typeof f=="string"||typeof f=="number")&&(f=typeof f=="string"||typeof f=="number"?String(f):""),c.push({type:typeof f=="string"?W.literal:W.object,value:f});continue}if(Jn(o)){var v=typeof o.style=="string"?n.date[o.style]:Cr(o.style)?o.style.parsedOptions:void 0;c.push({type:W.literal,value:r.getDateTimeFormat(t,v).format(f)});continue}if(Yn(o)){var v=typeof o.style=="string"?n.time[o.style]:Cr(o.style)?o.style.parsedOptions:n.time.medium;c.push({type:W.literal,value:r.getDateTimeFormat(t,v).format(f)});continue}if(Zn(o)){var v=typeof o.style=="string"?n.number[o.style]:ti(o.style)?o.style.parsedOptions:void 0;v&&v.scale&&(f=f*(v.scale||1)),c.push({type:W.literal,value:r.getNumberFormat(t,v).format(f)});continue}if(ei(o)){var p=o.children,g=o.value,_=i[g];if(!Jo(_))throw new Xo(g,"function",s);var d=Kt(p,t,r,n,i,a),m=_(d.map(function(x){return x.value}));Array.isArray(m)||(m=[m]),c.push.apply(c,m.map(function(x){return{type:typeof x=="string"?W.literal:W.object,value:x}}))}if(Qn(o)){var b=o.options[f]||o.options.other;if(!b)throw new pi(o.value,f,Object.keys(o.options),s);c.push.apply(c,Kt(b.value,t,r,n,i));continue}if(Kn(o)){var b=o.options["=".concat(f)];if(!b){if(!Intl.PluralRules)throw new Qt(`Intl.PluralRules is not available in this environment.
Try polyfilling it using "@formatjs/intl-pluralrules"
`,et.MISSING_INTL_API,s);var E=r.getPluralRules(t,{type:o.pluralType}).select(f-(o.offset||0));b=o.options[E]||o.options.other}if(!b)throw new pi(o.value,f,Object.keys(o.options),s);c.push.apply(c,Kt(b.value,t,r,n,i,f-(o.offset||0)));continue}}return Zo(c)}function Yo(e,t){return t?B(B(B({},e||{}),t||{}),Object.keys(e).reduce(function(r,n){return r[n]=B(B({},e[n]),t[n]||{}),r},{})):e}function Qo(e,t){return t?Object.keys(e).reduce(function(r,n){return r[n]=Yo(e[n],t[n]),r},B({},e)):e}function Fr(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}function Ko(e){return e===void 0&&(e={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:Br(function(){for(var t,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return new((t=Intl.NumberFormat).bind.apply(t,Pr([void 0],r,!1)))},{cache:Fr(e.number),strategy:Ir.variadic}),getDateTimeFormat:Br(function(){for(var t,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return new((t=Intl.DateTimeFormat).bind.apply(t,Pr([void 0],r,!1)))},{cache:Fr(e.dateTime),strategy:Ir.variadic}),getPluralRules:Br(function(){for(var t,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return new((t=Intl.PluralRules).bind.apply(t,Pr([void 0],r,!1)))},{cache:Fr(e.pluralRules),strategy:Ir.variadic})}}var mi=function(){function e(t,r,n,i){r===void 0&&(r=e.defaultLocale);var a=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(u){var l=a.formatToParts(u);if(l.length===1)return l[0].value;var o=l.reduce(function(h,f){return!h.length||f.type!==W.literal||typeof h[h.length-1]!="string"?h.push(f.value):h[h.length-1]+=f.value,h},[]);return o.length<=1?o[0]||"":o},this.formatToParts=function(u){return Kt(a.ast,a.locales,a.formatters,a.formats,u,void 0,a.message)},this.resolvedOptions=function(){var u;return{locale:((u=a.resolvedLocale)===null||u===void 0?void 0:u.toString())||Intl.NumberFormat.supportedLocalesOf(a.locales)[0]}},this.getAst=function(){return a.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),typeof t=="string"){if(this.message=t,!e.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var s=i||{};s.formatters;var c=uo(s,["formatters"]);this.ast=e.__parse(t,B(B({},c),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw new TypeError("A message must be provided as a String or AST.");this.formats=Qo(e.formats,n),this.formatters=i&&i.formatters||Ko(this.formatterCache)}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(t){if(!(typeof Intl.Locale>"u")){var r=Intl.NumberFormat.supportedLocalesOf(t);return r.length>0?new Intl.Locale(r[0]):new Intl.Locale(typeof t=="string"?t:t[0])}},e.__parse=qo,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();function es(e,t){if(t==null)return;if(t in e)return e[t];const r=t.split(".");let n=e;for(let i=0;i<r.length;i++)if(typeof n=="object"){if(i>0){const a=r.slice(i,r.length).join(".");if(a in n){n=n[a];break}}n=n[r[i]]}else n=void 0;return n}const Pe={},ts=(e,t,r)=>r&&(t in Pe||(Pe[t]={}),e in Pe[t]||(Pe[t][e]=r),r),gi=(e,t)=>{if(t==null)return;if(t in Pe&&e in Pe[t])return Pe[t][e];const r=_t(t);for(let n=0;n<r.length;n++){const i=r[n],a=ns(i,e);if(a)return ts(e,t,a)}};let jr;const tt=Zt({});function rs(e){return jr[e]||null}function zr(e){return e in jr}function ns(e,t){if(!zr(e))return null;const r=rs(e);return es(r,t)}function is(e){if(e==null)return;const t=_t(e);for(let r=0;r<t.length;r++){const n=t[r];if(zr(n))return n}}function as(e,...t){delete Pe[e],tt.update(r=>(r[e]=lo.all([r[e]||{},...t]),r))}Qe([tt],([e])=>Object.keys(e)),tt.subscribe(e=>jr=e);const pt={};function os(e){pt[e]=new Set}function ss(e,t){pt[e].delete(t),pt[e].size===0&&delete pt[e]}function mt(e){return pt[e]}function ls(e){return _t(e).map(t=>{const r=mt(t);return[t,r?[...r]:[]]}).filter(([,t])=>t.length>0)}function er(e){return e==null?!1:_t(e).some(t=>{var r;return(r=mt(t))==null?void 0:r.size})}function us(e,t){return Promise.all(t.map(n=>(ss(e,n),n().then(i=>i.default||i)))).then(n=>as(e,...n))}const gt={};function bi(e){if(!er(e))return e in gt?gt[e]:Promise.resolve();const t=ls(e);return gt[e]=Promise.all(t.map(([r,n])=>us(r,n))).then(()=>{if(er(e))return bi(e);delete gt[e]}),gt[e]}function _i(e,t){mt(e)||os(e);const r=mt(e);mt(e).has(t)||(zr(e)||tt.update(n=>(n[e]={},n)),r.add(t))}var yi=Object.getOwnPropertySymbols,cs=Object.prototype.hasOwnProperty,fs=Object.prototype.propertyIsEnumerable,hs=(e,t)=>{var r={};for(var n in e)cs.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&yi)for(var n of yi(e))t.indexOf(n)<0&&fs.call(e,n)&&(r[n]=e[n]);return r};const vs={number:{scientific:{notation:"scientific"},engineering:{notation:"engineering"},compactLong:{notation:"compact",compactDisplay:"long"},compactShort:{notation:"compact",compactDisplay:"short"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}};function ds({locale:e,id:t}){console.warn(`[svelte-i18n] The message "${t}" was not found in "${_t(e).join('", "')}".${er(Be())?`

Note: there are at least one loader still registered to this locale that wasn't executed.`:""}`)}const bt={fallbackLocale:null,loadingDelay:200,formats:vs,warnOnMissingMessages:!0,handleMissingMessage:void 0,ignoreTag:!0};function rt(){return bt}function ps(e){const t=e,{formats:r}=t,n=hs(t,["formats"]);let i=e.fallbackLocale;if(e.initialLocale)try{mi.resolveLocale(e.initialLocale)&&(i=e.initialLocale)}catch{console.warn(`[svelte-i18n] The initial locale "${e.initialLocale}" is not a valid locale.`)}return n.warnOnMissingMessages&&(delete n.warnOnMissingMessages,n.handleMissingMessage==null?n.handleMissingMessage=ds:console.warn('[svelte-i18n] The "warnOnMissingMessages" option is deprecated. Please use the "handleMissingMessage" option instead.')),Object.assign(bt,n,{initialLocale:i}),r&&("number"in r&&Object.assign(bt.formats.number,r.number),"date"in r&&Object.assign(bt.formats.date,r.date),"time"in r&&Object.assign(bt.formats.time,r.time)),nt.set(i)}const tr=Zt(!1);var ms=Object.defineProperty,gs=Object.defineProperties,bs=Object.getOwnPropertyDescriptors,xi=Object.getOwnPropertySymbols,_s=Object.prototype.hasOwnProperty,ys=Object.prototype.propertyIsEnumerable,Ei=(e,t,r)=>t in e?ms(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,xs=(e,t)=>{for(var r in t||(t={}))_s.call(t,r)&&Ei(e,r,t[r]);if(xi)for(var r of xi(t))ys.call(t,r)&&Ei(e,r,t[r]);return e},Es=(e,t)=>gs(e,bs(t));let Vr;const rr=Zt(null);function wi(e){return e.split("-").map((t,r,n)=>n.slice(0,r+1).join("-")).reverse()}function _t(e,t=rt().fallbackLocale){const r=wi(e);return t?[...new Set([...r,...wi(t)])]:r}function Be(){return Vr??void 0}rr.subscribe(e=>{Vr=e??void 0,typeof window<"u"&&e!=null&&document.documentElement.setAttribute("lang",e)});const ws=e=>{if(e&&is(e)&&er(e)){const{loadingDelay:t}=rt();let r;return typeof window<"u"&&Be()!=null&&t?r=window.setTimeout(()=>tr.set(!0),t):tr.set(!0),bi(e).then(()=>{rr.set(e)}).finally(()=>{clearTimeout(r),tr.set(!1)})}return rr.set(e)},nt=Es(xs({},rr),{set:ws}),nr=e=>{const t=Object.create(null);return n=>{const i=JSON.stringify(n);return i in t?t[i]:t[i]=e(n)}};var Ts=Object.defineProperty,ir=Object.getOwnPropertySymbols,Ti=Object.prototype.hasOwnProperty,Ai=Object.prototype.propertyIsEnumerable,Si=(e,t,r)=>t in e?Ts(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,$r=(e,t)=>{for(var r in t||(t={}))Ti.call(t,r)&&Si(e,r,t[r]);if(ir)for(var r of ir(t))Ai.call(t,r)&&Si(e,r,t[r]);return e},it=(e,t)=>{var r={};for(var n in e)Ti.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&ir)for(var n of ir(e))t.indexOf(n)<0&&Ai.call(e,n)&&(r[n]=e[n]);return r};const yt=(e,t)=>{const{formats:r}=rt();if(e in r&&t in r[e])return r[e][t];throw new Error(`[svelte-i18n] Unknown "${t}" ${e} format.`)},As=nr(e=>{var t=e,{locale:r,format:n}=t,i=it(t,["locale","format"]);if(r==null)throw new Error('[svelte-i18n] A "locale" must be set to format numbers');return n&&(i=yt("number",n)),new Intl.NumberFormat(r,i)}),Ss=nr(e=>{var t=e,{locale:r,format:n}=t,i=it(t,["locale","format"]);if(r==null)throw new Error('[svelte-i18n] A "locale" must be set to format dates');return n?i=yt("date",n):Object.keys(i).length===0&&(i=yt("date","short")),new Intl.DateTimeFormat(r,i)}),Hs=nr(e=>{var t=e,{locale:r,format:n}=t,i=it(t,["locale","format"]);if(r==null)throw new Error('[svelte-i18n] A "locale" must be set to format time values');return n?i=yt("time",n):Object.keys(i).length===0&&(i=yt("time","short")),new Intl.DateTimeFormat(r,i)}),Os=(e={})=>{var t=e,{locale:r=Be()}=t,n=it(t,["locale"]);return As($r({locale:r},n))},Ps=(e={})=>{var t=e,{locale:r=Be()}=t,n=it(t,["locale"]);return Ss($r({locale:r},n))},Bs=(e={})=>{var t=e,{locale:r=Be()}=t,n=it(t,["locale"]);return Hs($r({locale:r},n))},Ns=nr((e,t=Be())=>new mi(e,t,rt().formats,{ignoreTag:rt().ignoreTag})),Ls=(e,t={})=>{var r,n,i,a;let s=t;typeof e=="object"&&(s=e,e=s.id);const{values:c,locale:u=Be(),default:l}=s;if(u==null)throw new Error("[svelte-i18n] Cannot format a message without first setting the initial locale.");let o=gi(e,u);if(!o)o=(a=(i=(n=(r=rt()).handleMissingMessage)==null?void 0:n.call(r,{locale:u,id:e,defaultValue:l}))!=null?i:l)!=null?a:e;else if(typeof o!="string")return console.warn(`[svelte-i18n] Message with id "${e}" must be of type "string", found: "${typeof o}". Gettin its value through the "$format" method is deprecated; use the "json" method instead.`),o;if(!c)return o;let h=o;try{h=Ns(o,u).format(c)}catch(f){f instanceof Error&&console.warn(`[svelte-i18n] Message "${e}" has syntax error:`,f.message)}return h},Is=(e,t)=>Bs(t).format(e),Cs=(e,t)=>Ps(t).format(e),ks=(e,t)=>Os(t).format(e),Ms=(e,t=Be())=>gi(e,t),Hi=Qe([nt,tt],()=>Ls);Qe([nt],()=>Is),Qe([nt],()=>Cs),Qe([nt],()=>ks),Qe([nt,tt],()=>Ms);var he=(e=>(e.power="power",e.gas="gas",e.heat="heat",e))(he||{}),Me=(e=>(e.heatpump="heatpump",e.storage="storage",e))(Me||{}),Re=(e=>(e.single="single",e.double="double",e.household="household",e))(Re||{}),Rs=R('<div><form style="display: contents" action="javascript:void(0);"><!></form></div>');function Ds(e,t){Ae(t,!1);const r=le(),n=le(),i={type:{energyType:{[he.power]:"electricity",[he.gas]:"natural_gas",[he.heat]:"electric_heating"}},zip:"zipCode",heating:{category:{[Me.heatpump]:"heat_pump",[Me.storage]:"night_storage_heater"}},meter:{separateMeterForHeating:{heating:Me.storage},filter_counterTypes:{[Re.single]:"SINGLE",[Re.double]:"DUAL",[Re.household]:"SINGLE"}},consumptionAlt:"consumptionNT"},a={endpoint:endpointUrl,method:"get",target:endpointTarget,verbose:!1};let s=le(),c=N(t,"config",8),u=N(t,"rate",8);function l(){y(s).checkValidity()?a.target==="_blank"?window.open(`${a.endpoint}?${y(n).toString()}`,"_blank").focus():location.href=`${a.endpoint}?${y(n).toString()}`:en(s,y(s).value.dataset.validate=!0)}ue(()=>fe(u()),()=>{G(r,Object.keys(u()).reduce((v,p)=>{let g,_,d;return i[p]instanceof Object?d=Object.keys(i[p]).reduce((m,b)=>{let E,x;const I=u()[p];return i[p][b][I]?(E=I,x=i[p][b][E]):(E=Object.keys(i[p][b])[0],x=u()[E]===i[p][b][E]),{...m,[b]:x}},{}):(g=i[p]||p,_=u()[p],d={[g]:_}),{...v,...d||{}}},{}))}),ue(()=>y(r),()=>{G(n,new URLSearchParams(y(r)))}),It(),ke();var o=Rs(),h=T(o),f=T(h);$t(f,t,"default",{},null),Ka(h,v=>G(s,v),()=>y(s)),C(()=>qt(o,`vnc-energy-rate__form vnc-energy-rate--${c().type}__form`)),Ee("submit",h,l),P(e,o),Se()}var Us=R('<label class="vnc-input__label vnc-mb-1"><!></label>'),Gs=R('<div class="vnc-input vnc-input--radio vnc-mb-0 vnc-g-col-12 vnc-g-col-lg-4 vnc-d-inline-flex vnc-align-items-center vnc-gap-2"><input type="radio"> <label class="vnc-input__label vnc-mb-0"><!></label></div>'),Fs=R('<div class="vnc-input--radio-group"><!> <div class="vnc-grid vnc-grid-cols-3 vnc-grid-rows-1 vnc-gap-4 vnc-gap-lg-5 vnc-row-gap-2"></div></div>');function Oi(e,t){Ae(t,!1);const r=le(),n=[];let i=N(t,"id",8,""),a=N(t,"name",8,""),s=N(t,"label",8,""),c=N(t,"required",8,!1),u=N(t,"disabled",8,!1),l=N(t,"readOnly",8,!1),o=N(t,"options",24,()=>[]),h=N(t,"value",12,"");ue(()=>fe(o()),()=>{G(r,o().map(g=>typeof g=="string"||typeof g=="number"?{text:g,value:g}:g))}),It(),ke();var f=Fs(),v=T(f);X(v,s,g=>{var _=Us(),d=T(_);$t(d,t,"label",{},m=>{var b=yr();C(()=>be(b,s())),P(m,b)}),C(()=>w(_,"for",`${i()}-0`)),P(g,_)});var p=F(v,2);Vt(p,5,()=>y(r),zt,(g,_,d)=>{var m=Gs(),b=T(m),E,x=F(b,2),I=T(x);$t(I,t,"item-label",{},U=>{var O=yr();C(()=>be(O,y(_).text)),P(U,O)}),C(()=>{w(b,"name",a()),w(b,"id",`${i()}-${d}`),E!==(E=y(_).value)&&(b.value=(b.__value=y(_).value)==null?"":y(_).value),Va(b,y(_).value===h()),b.disabled=u(),b.readOnly=l(),b.required=c(),w(x,"for",`${i()}-${d}`)}),Rn(n,[],b,()=>(y(_).value,h()),h),P(g,m)}),P(e,f),Se()}const js=`<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0" width="34" height="32" viewBox="0, 0, 34, 32">
  <g id="Ebene_1_1747405490">
    <path d="M2.178,4.142 C-3.24,11.12 1.847,29.432 13.054,31.756 C24.228,34.073 35.973,19.356 33.72,10.769 C31.085,0.727 8.496,-3.996 2.178,4.142" fill="#FFFFFF"/>
    <g>
      <path d="M21.386,9.554 C21.386,12.01 19.422,14 17,14 C14.578,14 12.614,12.01 12.614,9.554 C12.614,7.099 14.578,5.109 17,5.109 C19.422,5.109 21.386,7.099 21.386,9.554 z" fill="#49083A"/>
      <path d="M25.772,19.779 C25.776,18.91 25.277,18.12 24.497,17.759 C22.133,16.716 19.578,16.193 17,16.223 C14.422,16.193 11.867,16.716 9.503,17.759 C8.723,18.12 8.224,18.91 8.228,19.779 L8.228,22.891 L25.772,22.891 L25.772,19.779 z" fill="#49083A"/>
    </g>
  </g>
</svg>
`,zs=`<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0" width="32" height="32" viewBox="0, 0, 32, 32">
  <g id="Ebene_1_1747405500">
    <path d="M7.008,17.245 L7.008,26.005 C7.008,26.555 7.458,27.005 8.008,27.005 L14.008,27.005 L14.008,21.505 C14.008,21.225 14.228,21.005 14.508,21.005 L17.508,21.005 C17.788,21.005 18.008,21.225 18.008,21.505 L18.008,27.005 L24.008,27.005 C24.558,27.005 25.008,26.555 25.008,26.005 L25.008,17.245 L16.008,9.885 L7.008,17.245 z" fill="#49083A"/>
    <path d="M27.637,14.225 L16.637,5.225 C16.267,4.925 15.737,4.925 15.367,5.225 L4.367,14.225 C3.937,14.575 3.877,15.205 4.227,15.635 C4.577,16.065 5.207,16.125 5.637,15.775 L16.007,7.295 L26.377,15.775 C26.557,15.935 26.787,16.005 27.007,16.005 C27.297,16.005 27.587,15.875 27.777,15.635 C28.127,15.205 28.067,14.575 27.637,14.225 z" fill="#49083A"/>
  </g>
</svg>
`;var Vs=R('<label class="vnc-input__label svelte-1j9upyo"> </label>'),$s=R('<input type="radio" class="vnc-visually-hidden svelte-1j9upyo"> <label> </label>',1),qs=R('<div class="vnc-input--vnc-range__steps svelte-1j9upyo"></div>'),Xs=R('<div class="vnc-input vnc-input--vnc-range svelte-1j9upyo" data-form-input=""><!> <input type="range" data-input-range="" class="svelte-1j9upyo"> <!></div>');function Ws(e,t){Ae(t,!1);const r=[],n={avatar:js,house:zs};let i=N(t,"id",8,""),a=N(t,"name",8,""),s=N(t,"label",8,""),c=N(t,"required",8,!1),u=N(t,"disabled",8,!1),l=N(t,"readOnly",8,!1),o=N(t,"min",8,0),h=N(t,"max",8,0),f=N(t,"step",8,"any"),v=N(t,"steps",24,()=>[]),p=N(t,"value",12,0);to(()=>{document.documentElement.style.setProperty("--vnc-thumb-background-avatar",`url("data:image/svg+xml;base64,${btoa(n.avatar)}")`),document.documentElement.style.setProperty("--vnc-thumb-background-house",`url("data:image/svg+xml;base64,${btoa(n.house)}")`)}),ke();var g=Xs(),_=T(g);X(_,s,b=>{var E=Vs(),x=T(E);C(()=>{w(E,"for",`${i()}__range`),be(x,s())}),P(b,E)});var d=F(_,2),m=F(d,2);X(m,v,b=>{var E=qs();Vt(E,5,()=>[...Array(v().length).keys()],zt,(I,U,O)=>{var Z=$s(),te=ze(Z),Ne,xt=F(te,2),ar=T(xt);C(()=>{w(te,"id",`${i()}__radio-${y(U)}`),Ne!==(Ne=y(U))&&(te.value=(te.__value=y(U))==null?"":y(U)),w(xt,"for",`${i()}__radio-${y(U)}`),qt(xt,`${`vnc-input__label ${O===p()?"vnc-is-active":"vnc-opacity-50"}`??""} svelte-1j9upyo`),be(ar,v()[y(U)])}),Rn(r,[],te,()=>(y(U),p()),p),P(I,Z)}),P(b,E)}),C(()=>{w(d,"id",`${i()}__range`),w(d,"name",a()),w(d,"min",o()),w(d,"max",h()),w(d,"step",f()),d.required=c(),d.disabled=u(),d.readOnly=l()}),dt(d,p),P(e,g),Se()}const Zs=e=>e;function qr(e,{delay:t=0,duration:r=400,easing:n=Zs}={}){const i=+getComputedStyle(e).opacity;return{delay:t,duration:r,easing:n,css:a=>`opacity: ${a*i}`}}var Js=R('<div class="vnc-energy-rate__heating"><div class="vnc-energy-rate__heating-type vnc-mb-3"><!></div> <div class="vnc-energy-rate__heating-type"><!></div></div>'),Ys=R('<div class="input__error">Bitte geben Sie eine gültige Postleitzahl ein.</div>'),Qs=R('<div class="input__error">Bitte geben Sie Ihren Jahresverbrauch im Hochtarif (HT) ein.</div>'),Ks=R('<div class="vnc-input"><label class="vnc-input__label"> </label> <input></div> <!>',1),el=R('<div class="input__error">Bitte geben Sie Ihren Verbrauch ein.</div>'),tl=R('<div class="vnc-input"><label class="vnc-input__label"> </label> <input></div> <!>',1),rl=R('<div class="input__error">Bitte geben Sie Ihren Jahresverbrauch im Niedertarif (NT) ein.</div>'),nl=R('<div class="vnc-input"><label class="vnc-input__label"> </label> <input></div> <!>',1),il=R('<div class="vnc-mx-auto vnc-grid vnc-gap-4"><!> <div class="vnc-energy-rate__main vnc-grid vnc-grid-cols-3 vnc-grid-rows-1 vnc-gap-12"><div class="vnc-energy-rate__zip vnc-g-col-12 vnc-g-col-lg-4"><div class="vnc-input"><label class="vnc-input__label"> </label> <input type="text" minlength="5" maxlength="5"></div> <!></div> <div class="vnc-energy-rate__household vnc-g-col-12 vnc-g-col-lg-4"><!></div> <div class="vnc-energy-rate__consumption vnc-g-col-12 vnc-g-col-lg-4"><transition name="vnc-fade"><!></transition></div></div> <div class="vnc-energy-rate__footer vnc-py-3"><div class="vnc-energy-rate__submit"><button type="submit" class="vnc-btn vnc-bg-light vnc-block vnc-mx-auto svelte-uprp2f">Tarif berechnen</button></div></div></div>'),al=R('<div><div class="vnc-container vnc-mx-auto vnc-p-4 vnc-pb-5"><!></div></div>');function ol(e,t){Ae(t,!1);const r=Sr(),n=()=>Ar(Hi,"$_",r),i=le(),a=le(),s=le(),c=le(),u=le();let l=N(t,"config",8),o=N(t,"rate",12),h=N(t,"household",12,0);function f(m){return m.key&&(m.key.length===1&&isNaN(Number(m.key))||m.key===" ")&&m.preventDefault()}function v(m){return!m.target.value&&m.key==="0"&&m.preventDefault()}function p(m){Number(m.target.value)===0&&(m.target.value="")}ue(()=>fe(l()),()=>{G(i,l().household&&Object.keys(l().household.steps).map(Number))}),ue(()=>(fe(o()),Me),()=>{G(a,o().heating===Me.storage)}),ue(()=>(fe(o()),Re),()=>{G(s,o().meter!==Re.single)}),ue(()=>(fe(l()),y(a)),()=>{var m;G(c,(m=l().heating)==null?void 0:m.meters.filter(b=>b!=="household"||y(a)))}),ue(()=>(fe(l()),fe(h())),()=>{o(o().consumption=l().household&&Object.values(l().household.steps).map(Number)[h()]||0,!0)}),ue(()=>fe(o()),()=>{G(u,o().zip)}),It(),ke();var g=al(),_=T(g),d=T(_);Ds(d,{get config(){return l()},get rate(){return o()},children:(m,b)=>{var E=il(),x=T(E);X(x,()=>l().heating,V=>{var J=Js(),Y=T(J),re=T(Y);X(re,()=>o().heating,ve=>{var ne=pe(()=>`${l().type}-heating`),j=pe(()=>l().heating.types.map($=>({text:n()(`rate.heating.${$}.label`),value:$}))),Et=pe(()=>n()("rate.heating.label"));Oi(ve,{get id(){return y(ne)},name:"heatingType",get value(){return o().heating},set value($){o(o().heating=$,!0)},get options(){return y(j)},get label(){return y(Et)},$$legacy:!0})});var _e=F(Y,2),M=T(_e);X(M,()=>o().heating&&y(c),ve=>{var ne=pe(()=>`${l().type}-meter`),j=pe(()=>y(c).map($=>({text:n()(`rate.meter.${$}.label`),value:$}))),Et=pe(()=>n()("rate.meter.label"));Oi(ve,{get id(){return y(ne)},name:"heatingMeter",get value(){return o().meter},set value($){o(o().meter=$,!0)},get options(){return y(j)},get label(){return y(Et)},$$legacy:!0})}),P(V,J)});var I=F(x,2),U=T(I),O=T(U),Z=T(O),te=T(Z);C(()=>be(te,n()("rate.zip.label")));var Ne=F(Z,2);C(()=>w(Ne,"placeholder",n()("rate.zip.placeholder")));var xt=F(O,2);X(xt,()=>o().zip!=="",V=>{var J=Ys();P(V,J)});var ar=F(U,2),Sl=T(ar);X(Sl,()=>y(i),V=>{var J=pe(()=>`${l().type}-consumption`),Y=pe(()=>y(i).length-1),re=pe(()=>n()(`rate.household.${l().type}.label`));Ws(V,{get id(){return y(J)},get value(){return h()},set value(_e){h(_e)},min:0,get max(){return y(Y)},step:1,get steps(){return y(i)},get label(){return y(re)},$$legacy:!0})},V=>{var J=Ks(),Y=ze(J),re=T(Y),_e=T(re);C(()=>be(_e,n()("rate.consumptionDefault.label")));var M=F(re,2);w(M,"placeholder","Ihr Jahresverbrauch");var ve=F(Y,2);X(ve,()=>!o().consumption,ne=>{var j=Qs();P(ne,j)}),C(()=>{w(Y,"data-append-text",l().consumption.unit),w(re,"for",`${o().type}-consumptionDefault`),w(M,"id",`${o().type}-consumptionDefault`),w(M,"type",l().consumption.type),M.required=l().consumption.required,w(M,"min",l().consumption.min),w(M,"max",l().consumption.max)}),dt(M,()=>o().consumption,ne=>o(o().consumption=ne,!0)),Ee("keydown",M,v),Ee("blur",M,p),P(V,J)});var Hl=F(ar,2),Ol=T(Hl),Pl=T(Ol);X(Pl,()=>l().household,V=>{var J=tl(),Y=ze(J),re=T(Y),_e=T(re);C(()=>be(_e,n()("rate.consumption.label")));var M=F(re,2);C(()=>w(M,"placeholder",n()("rate.consumption.placeholder")));var ve=F(Y,2);X(ve,()=>!o().consumption,ne=>{var j=el();P(ne,j)}),C(()=>{w(Y,"data-append-text",l().consumption.unit),w(re,"for",`${o().type}-consumption`),w(M,"id",`${o().type}-consumption`),w(M,"type",l().consumption.type),M.required=l().consumption.required,w(M,"min",l().consumption.min),w(M,"max",l().consumption.max)}),dt(M,()=>o().consumption,ne=>o(o().consumption=ne,!0)),Ee("keydown",M,v),Ee("blur",M,p),Er(3,Y,()=>qr),P(V,J)},V=>{var J=Nn(),Y=ze(J);X(Y,()=>y(s),re=>{var _e=nl(),M=ze(_e),ve=T(M),ne=T(ve);C(()=>be(ne,n()("rate.consumptionAlt.label")));var j=F(ve,2);C(()=>w(j,"placeholder",n()("rate.consumptionAlt.placeholder")));var Et=F(M,2);X(Et,()=>!o().consumptionAlt,$=>{var or=rl();P($,or)}),C(()=>{var $,or,Li,Ii;w(M,"data-append-text",l().consumption.unit),w(ve,"for",`${o().type}-consumptionAlt`),w(j,"id",`${o().type}-consumptionAlt`),w(j,"type",($=l().consumptionAlt)==null?void 0:$.type),j.required=(or=l().consumptionAlt)==null?void 0:or.required,w(j,"min",(Li=l().consumptionAlt)==null?void 0:Li.min),w(j,"max",(Ii=l().consumptionAlt)==null?void 0:Ii.max)}),dt(j,()=>o().consumptionAlt,$=>o(o().consumptionAlt=$,!0)),Ee("keydown",j,v),Ee("blur",j,p),Er(3,M,()=>qr),P(re,_e)},null,!0),P(V,J)}),C(()=>{var V;w(Z,"for",`${o().type}-zip`),w(Ne,"id",`${o().type}-zip`),Ne.required=(V=l().zip)==null?void 0:V.required}),dt(Ne,()=>o().zip,V=>o(o().zip=V,!0)),Ee("keydown",Ne,f),P(m,E)},$$slots:{default:!0}}),C(()=>qt(g,`${`vnc-energy-rate vnc-energy-rate--${l().type} vnc-bg-gradient-secondary`??""} svelte-uprp2f`)),P(e,g),Se()}var sl=_r('<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0" width="32" height="32" viewBox="0, 0, 32, 32"><path d="M16.41 20l2-2-1.41-1.41-2 2-1.59-1.59 2-2-1.41-1.41-2 2-1.33-1.33c-0.39-0.39-1.020-0.39-1.41 0l-1.5 1.5c-2.070 2.070-2.34 5.33-0.64 7.71l-2.82 2.82c-0.4 0.38-0.41 1.020-0.020 1.41 0.38 0.4 1.020 0.41 1.41 0.020 0 0 0.020-0.020 0.020-0.020l2.82-2.82c2.38 1.7 5.65 1.43 7.72-0.65l1.5-1.5c0.39-0.39 0.39-1.020 0-1.41l-1.33-1.33z"></path><path d="M21.33 17.74c0.39 0.39 1.020 0.39 1.41 0l1.5-1.5c2.070-2.070 2.34-5.33 0.64-7.72l2.82-2.82c0.4-0.38 0.41-1.020 0.020-1.41-0.38-0.4-1.020-0.41-1.41-0.020 0 0-0.020 0.020-0.020 0.020l-2.82 2.82c-2.38-1.7-5.65-1.43-7.72 0.64l-1.5 1.5c-0.39 0.39-0.39 1.020 0 1.41l7.070 7.070z"></path></svg>');function ll(e){var t=sl();P(e,t)}var ul=_r('<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0" width="32" height="32" viewBox="0, 0, 32, 32"><path d="M18.59 13.44s1.11-6.58-3.45-9.44c-0.14 2.29-1.24 4.41-3.040 5.83-1.95 1.72-5.62 5.57-5.58 9.68-0.030 3.58 1.98 6.87 5.18 8.49 0.11-1.6 0.87-3.090 2.090-4.13 1.040-0.8 1.71-1.98 1.87-3.28 2.73 1.45 4.5 4.23 4.65 7.32v0.020c3.020-1.38 5-4.34 5.14-7.66 0.32-3.86-1.79-9.11-3.67-10.83-0.71 1.58-1.81 2.96-3.2 3.99z"></path></svg>');function cl(e){var t=ul();P(e,t)}var fl=_r('<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0" width="32" height="32" viewBox="0, 0, 32, 32"><path d="M23 15.24v-7.24c0-2.21-1.79-4-4-4s-4 1.79-4 4v7.24c-3.17 2.21-3.95 6.57-1.74 9.74s6.57 3.95 9.74 1.74c3.17-2.21 3.95-6.57 1.74-9.74-0.47-0.68-1.060-1.27-1.74-1.74zM19 25c-2.21 0-4-1.79-4-4h8c0 2.21-1.79 4-4 4z"></path><path d="M6 6h6v2h-6v-2z"></path><path d="M6 11h6v2h-6v-2z"></path><path d="M6 16h4v2h-4v-2z"></path></svg>');function hl(e){var t=fl();P(e,t)}var vl=R("<i><!></i>");function dl(e,t){const r={power:ll,gas:cl,heat:hl};let n=N(t,"title",8,""),i=N(t,"symbol",8,"");var a=vl(),s=T(a);za(s,()=>r[i()],(c,u)=>{u(c,{class:"vnc-icon"})}),C(()=>{qt(a,`vnc-svg-icon vnc-svg-icon--${i()??""} svelte-1hywxi2`),w(a,"title",n())}),P(e,a)}var pl=R('<div class="vnc-w-1/3"><button class="vnc-btn vnc-w-full svelte-v5rx30"><!> <span class="vnc-caption svelte-v5rx30"> </span></button></div>'),ml=R('<div class="vnc-tab-group__item vnc-col-12 svelte-v5rx30"><div class="tab-group__item__inner"><!></div></div>'),gl=R('<div class="vnc-tabs svelte-v5rx30"><div class="vnc-tab-nav vnc-flex middle vnc-gap-2 vnc-gap-md-4 vnc-gap-lg-5 svelte-v5rx30"></div> <div class="vnc-tab-group vnc-overflow-hidden svelte-v5rx30"></div></div>');function bl(e,t){Ae(t,!1);const r=Sr(),n=()=>Ar(Hi,"$_",r);let i=N(t,"active",12,0),a=N(t,"items",24,()=>[]);ke();var s=gl(),c=T(s);Vt(c,5,a,zt,(l,o,h)=>{var f=pl(),v=T(f);C(()=>w(v,"aria-label",n()(`tab.${y(o)}`)));var p=T(v);dl(p,{get symbol(){return y(o)}});var g=F(p,2),_=T(g);C(()=>be(_,n()(`tab.${y(o)}`))),C(()=>Xa(v,"vnc-is-active",i()===h)),Ee("click",v,()=>i(h)),P(l,f)});var u=F(c,2);Vt(u,5,a,zt,(l,o,h)=>{var f=Nn(),v=ze(f);X(v,()=>i()===h,p=>{var g=ml();w(g,"id",`tab-group__item-${h}`);var _=T(g),d=T(_);$t(d,t,"item",{":item":"item",":index":"index"},m=>{var b=yr();C(()=>be(b,`id: ${y(o)??""}`)),P(m,b)}),Er(3,g,()=>qr),P(p,g)}),P(l,f)}),C(()=>w(s,"data-selected-tab",i()+1)),P(e,s),Se()}var _l=R('<div class="energy-rates-widget" data-mode="dark"><div class="vnc-container--grid vnc-mx-auto"><!></div></div>');function yl(e,t){Ae(t,!1);const r=le(),n=le(),i=o=>({type:"",zip:"",consumption:void 0,...o===he.heat?{heating:Me.heatpump,meter:Re.single}:{}});let a=N(t,"configs",24,()=>[]),s=N(t,"active",12,0);ue(()=>fe(a()),()=>{G(r,a().map(o=>o.type))}),ue(()=>y(r),()=>{G(n,y(r).map(o=>({...i(o),type:o})))}),It(),ke();var c=_l(),u=T(c),l=T(u);return bl(l,{get active(){return s()},set active(o){s(o)},get items(){return y(r)},$$slots:{item:(o,h)=>{ol(o,{get rate(){return y(n)[s()]},set rate(f){en(n,y(n)[s()]=f)},get config(){return a()[s()]},$$legacy:!0})}},$$legacy:!0}),P(e,c),Gn(t,"baseRate",i),Se({baseRate:i})}var xl=R("<p>Loading...</p>"),El=R('<div class="vnc-svelte-app svelte-mi0n5p"><!></div>');function wl(e,t){Ae(t,!1);const r=Sr(),n=()=>Ar(tr,"$isLoading",r),i=[{id:he.power,type:he.power,zip:{minLength:5,maxLength:5,required:!0},household:{steps:{1:1500,2:2500,3:3e3,4:4e3,5:5e3,6:6e3}},consumption:{type:"number",unit:"kWh/Jahr",required:!0,min:0,max:999999}},{id:he.gas,type:he.gas,household:{steps:{25:4300,50:6700,75:9800,100:12800,125:16400,150:22600}},consumption:{type:"number",unit:"kWh/Jahr",required:!0,min:0,max:999999}},{id:he.heat,type:he.heat,zip:{required:!0},consumption:{type:"number",unit:"kWh/Jahr",required:!0,min:0,max:999999},consumptionAlt:{type:"number",unit:"kWh/Jahr",required:!0,min:0,max:999999},heating:{types:["heatpump","storage"],meters:["single","double","household"]}}],a=i.filter(u=>defaultRates.includes(u.id));ke();var s=El(),c=T(s);return X(c,n,u=>{var l=xl();P(u,l)},u=>{yl(u,{configs:a})}),P(e,s),Gn(t,"rates",i),Se({rates:i})}const Ll="modulepreload",Il=function(e){return"/"+e},Cl={},Pi=function(t,r,n){let i=Promise.resolve();function a(s){const c=new Event("vite:preloadError",{cancelable:!0});if(c.payload=s,window.dispatchEvent(c),!c.defaultPrevented)throw s}return i.then(s=>{for(const c of s||[])c.status==="rejected"&&a(c.reason);return t().catch(a)})};_i("en",()=>Pi(()=>Promise.resolve().then(()=>Tl),void 0)),_i("de",()=>Pi(()=>Promise.resolve().then(()=>Al),void 0)),ps({fallbackLocale:"en",initialLocale:"de"}),Ra(wl,{target:document.querySelector("[data-energy-rates-widget]")});const Bi={title:"Energy price widget",rate:{zip:{label:"Zip code"},consumption:{label:"Consumption"},area:{label:"Area in square meters"},consumers:{label:"Consumers"}}},Tl=Object.freeze(Object.defineProperty({__proto__:null,default:Bi,messages:Bi},Symbol.toStringTag,{value:"Module"})),Ni={title:"Energiepreiswidget",tab:{power:"Ökostrom",gas:"Gas",heat:"Wärme"},rate:{zip:{label:"Postleitzahl",placeholder:"Ihre Postleitzahl"},household:{power:{label:"Personen im Haushalt"},gas:{label:"Wohnfläche in qm"}},consumption:{label:"Verbrauch",placeholder:"Ihr Verbrauch"},consumptionDefault:{label:"Ihr Jahresverbrauch im Hochtarif (HT)",placeholder:"Ihr Jahresverbrauch"},consumptionAlt:{label:"Ihr Jahresverbrauch im Niedertarif (NT)",placeholder:"Ihr Jahresverbrauch"},heating:{label:"Womit heizen Sie?",heatpump:{label:"Wärmepumpe"},storage:{label:"Nachspeicherheizung"}},meter:{label:"Welchen Zählertyp nutzen Sie?",single:{label:"Eintarifzähler (nur Wärmestrom)"},double:{label:"Zweitarifzähler (nur Wärmestrom)"},household:{label:"Zweitarifzähler (inkl. Haushaltsstrom)"}}},submit:{caption:"Tarif berechnen"}},Al=Object.freeze(Object.defineProperty({__proto__:null,default:Ni,messages:Ni},Symbol.toStringTag,{value:"Module"}))})();
