<f:layout name="Default"/>
<f:section name="Main">
<cb:asset.script
    identifier="content-block-js-vnc-vnc/energy-rate-widget"
    file="energy-rates-widget.js"
/>


<script>
     const defaultRates = [ '{f:if(condition: '{data.checkbox}==0 || {data.checkbox} == 1 || {data.checkbox}==3 || {data.checkbox}==5 || {data.checkbox}==7 ', then: 'power')}',
                            '{f:if(condition: '{data.checkbox}==0 || {data.checkbox} == 2 || {data.checkbox}==3 || {data.checkbox}==6 || {data.checkbox}==7 ', then: 'gas')}',
                             '{f:if(condition: '{data.checkbox}==0 || {data.checkbox} == 4 || {data.checkbox}==5 || {data.checkbox}==6 || {data.checkbox}==7 ', then: 'heat')}',
     ];
     const endpointUrl = '{data.url}';
     const endpointTarget = '_blank';
</script>
<div class="container">
    <div class="row">
        <div class="col-12">
            <f:if condition="{data.header}">
                <f:then>
                    <div class="container">
                        <h2 class="headline h2 text-center mb-4">{data.header}</h2>
                    </div>
                </f:then>
            </f:if>
            <div data-energy-rates-widget></div>
        </div>
    </div>
</div>

</f:section>

