
<f:layout name="Default"/>

<f:section name="Main">
    <section class="page-section">
        <div class="container">
            <div class="row">
                <div class="col-12 col-lg-10 offset-lg-1 text-center">
                    <f:variable name="sliderContent">
                        <f:for each="{data.items}" as="data">
                            <f:render section="cite" arguments="{_all}" />
                        </f:for>
                    </f:variable>
                    <f:variable name="carouselType" value="tiles"></f:variable>
                    <f:variable name="carouselOptions" value="{
                        &quot;arrows&quot;: true
                        }">
                    </f:variable>
                    <f:render partial="Carousel" arguments="{_all}"></f:render>
                </div>
            </div>
        </div>
    </section>
</f:section>
</html>

<f:section name="cite">
    <div class="carousel__item splide__slide cite">
        <f:variable name="file" value="{data.image.0}"/>
        <f:variable name="files" value="{data.images.0}"/>
        <f:if condition="{file}">
            <f:variable name="cropVariant">square</f:variable>
            <f:variable name="sizes" value="{0:'sm'}" />
            <f:variable name="className">cite__visual rounded-circle mx-auto bodytext--l</f:variable>
            <f:render partial="Media/Media" arguments="{_all}"></f:render>
        </f:if>
        <blockquote cite="{data.author}">{data.bodytext -> f:format.nl2br()}</blockquote>
        <address class="author">
            <f:if condition="{href}">
                <f:then>
                    <a class="link bodytext--xs" rel="author" {href}>{data.author}</a>
                </f:then>
                <f:else>
                    <span class="bodytext--xs" rel="author">{data.author}</span>
                </f:else>
            </f:if>
        </address>
    </div>
</f:section>

