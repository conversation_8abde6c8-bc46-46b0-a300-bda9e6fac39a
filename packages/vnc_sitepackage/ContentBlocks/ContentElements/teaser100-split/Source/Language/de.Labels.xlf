<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file source-language="en" target-language="de" original="" datatype="plaintext" date="2024-11-07T16:49:03+01:00">
		<body>
			<trans-unit id="title" resname="title">
				<source>Teaser Split</source>
				<target>Fullsize-Teaser Split</target>
			</trans-unit>
			<trans-unit id="description" resname="description">
				<source>Fullsize Split-Teaser Slider with Image and Text</source>
				<target>Ein Teaser mit zwei Teilen mit Bild und Text links/rechts</target>
			</trans-unit>
			<trans-unit id="header.label" resname="header.label">
				<source>Identifier (for information only)</source>
				<target>Bezeichner (auf der Webseite nicht sichtbar)</target>
			</trans-unit>
			<trans-unit id="vnc_content_layout.label" resname="vnc_content_layout.label">
				<source>Layout</source>
				<target>Darstellung</target>
			</trans-unit>
			<trans-unit id="vnc_content_layout.items.left.label" resname="vnc_content_layout.items.left.label">
				<source>Image Left</source>
				<target>Bild links</target>
			</trans-unit>
			<trans-unit id="vnc_content_layout.items.right.label" resname="vnc_content_layout.items.right.label">
				<source>Image Right</source>
				<target>Bild rechts</target>
			</trans-unit>
			<trans-unit id="format.label" resname="format.label">
				<source>Image Format</source>
				<target>Seitenverhältnis der Bilder</target>
			</trans-unit>
			<trans-unit id="format.items.upright.label" resname="format.items.upright.label">
				<source>Upright (3:4)</source>
				<target>Hochkant (3:4)</target>
			</trans-unit>
			<trans-unit id="format.items.square.label" resname="format.items.square.label">
				<source>Square (1:1)</source>
				<target>Quadratisch (1:1)</target>
			</trans-unit>
			<trans-unit id="format.items.classicscreen.label" resname="format.items.classicscreen.label">
				<source>Classicscreen (5:4)</source>
				<target>Klassischer Monitor (5:4)</target>
			</trans-unit>
			<trans-unit id="format.items.television.label" resname="format.items.television.label">
				<source>Television (4:3)</source>
				<target>Klassischer Fernseher (4:3)</target>
			</trans-unit>
			<trans-unit id="format.items.default.label" resname="format.items.default.label">
				<source>Photo (3:2)</source>
				<target>Foto (3:2)</target>
			</trans-unit>
			<trans-unit id="items.label" resname="items.label">
				<source>Teaser</source>
				<target>Teaser</target>
			</trans-unit>
			<trans-unit id="items.header.label" resname="items.header.label">
				<source>Headline</source>
				<target>Überschrift</target>
			</trans-unit>
			<trans-unit id="items.subheader.label" resname="items.subheader.label">
				<source>Sub Headline</source>
				<target>Unter-Überschrift</target>
			</trans-unit>
			<trans-unit id="items.bodytext.label" resname="items.bodytext.label">
				<source>Text</source>
				<target>Text</target>
			</trans-unit>
			<trans-unit id="items.link.label" resname="items.link.label">
				<source>Link</source>
				<target>Link</target>
			</trans-unit>
			<trans-unit id="items.linktext.label" resname="items.linktext.label">
				<source>Linktext</source>
				<target>Linktext</target>
			</trans-unit>
			<trans-unit id="items.image.label" resname="items.image.label">
				<source>image</source>
				<target>Bild</target>
			</trans-unit>
			<trans-unit id="items.headline.label" resname="items.headline.label">
				<source>Headline</source>
				<target>Überschrift</target>
			</trans-unit>
			<trans-unit id="items.subheadline.label" resname="items.subheadline.label">
				<source>Sub Headline</source>
				<target>Unter-Überschrift</target>
			</trans-unit>
			<trans-unit id="format.items.widescreen.label" resname="format.items.widescreen.label">
				<source>Widescreen (16:9)</source>
				<target>Breitbild (16:9)</target>
			</trans-unit>
			<trans-unit id="format.items.ultrawide.label" resname="format.items.ultrawide.label">
				<source>Ultrawide (3:1)</source>
				<target>Ultrawide (3:1)</target>
			</trans-unit>
			<trans-unit id="format.items.univisium.label" resname="format.items.univisium.label">
				<source>Univisium (2:1)</source>
				<target>Univisium (2:1)</target>
			</trans-unit>
			<trans-unit id="format.items.wide.label" resname="format.items.wide.label">
				<source>Wide (5:2)</source>
				<target>Wide (5:2)</target>
			</trans-unit>
		</body>
	</file>
</xliff>
