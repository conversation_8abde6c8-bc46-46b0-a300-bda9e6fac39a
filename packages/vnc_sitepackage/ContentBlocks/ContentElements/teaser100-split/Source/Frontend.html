
<f:layout name="Default"/>

<f:section name="Main">
    <f:variable name="carouselType" value="tiles"></f:variable>
    <f:variable name="showStones" value="true"></f:variable>
    <f:variable name="sliderContent">
        <f:for each="{data.items}" as="teaser">
            <f:render section="teaser" arguments="{_all}" />
        </f:for>
    </f:variable>

    <section class="page-section">
        <div class="container-xxxl">
            <div class="row">
                <div class="col-12">
                    <f:render partial="Carousel" arguments="{_all}"></f:render>
                </div>
            </div>
        </div>
    </section>
</f:section>

<f:section name="teaser">
    <div class="carousel__item splide__slide">

        <div class="split-stage split-stage--reverse has-gutter container-xxxl">
            <div class="split-stage__item{f:if(condition:'{data.vnc_content_overlay}', then: ' bg-{data.vnc_content_overlay}')}">
                <div class="aspect-ratio">
                    <div class="aspect-ratio__canvas">
                        <div class="split-stage__container">
                            <div class="container teaser p-md-0">
                                <div class="teaser__text">
                                    <f:render partial="Text" arguments="{data:teaser,linkType:'button'}"></f:render>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="split-stage__item">
                <div class="split-stage__image h-100">
                    <f:variable name="file" value="{teaser.image.0}"/>
                    <f:if condition="{file}">
                        <f:variable name="cropVariant">default</f:variable>
                        <f:variable name="sizes" value="{0:'xxl'}" />
                        <f:variable name="className">mx-auto</f:variable>
                        <f:render partial="Media/Media" arguments="{_all}"></f:render>
                    </f:if>
                </div>
            </div>
        </div>

    </div>
</f:section>

