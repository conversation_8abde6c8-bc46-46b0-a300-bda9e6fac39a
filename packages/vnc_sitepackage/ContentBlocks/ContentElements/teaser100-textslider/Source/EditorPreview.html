<cb:asset.css file="EditorPreview.css" identifier="content-block-vnc-vnc/teaser100-textslider-be"/>

<section>
    <div class="container">
        <div class="row">
            <div class="col-3">
    <f:if condition="{data.image.0}">
        <img
            alt="{f:if(condition: '{decorative}', then: '', else: data.image.0.alternative)}"
            src="{f:uri.image(image:data.image.0, maxWidth: '150',  cropVariant: '{data.format}', treatIdAsReference: '1')}"
            title="{data.image.0.title}"
        />
    </f:if>
            </div>
            <div class="col-9">
        <f:for as="teaser" each="{data.items}" iteration="recordIterator">
                    <f:sanitize.html>{teaser.header}</f:sanitize.html><br>
        </f:for>
            </div>
        </div>
    </div>
</section>

