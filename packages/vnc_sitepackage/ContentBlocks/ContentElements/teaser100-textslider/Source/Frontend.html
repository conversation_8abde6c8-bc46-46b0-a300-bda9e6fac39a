
<f:layout name="Default"/>

<f:section name="Main">
    <div class="container-xxxl">
        <f:variable name="cropVariants" value="{
                        default:'{data.format}',
                        tablet:'default',
                        mobile:'default'
                    }"/>
        <f:variable name="sizes" value="{0:'xxl', 1:'xl', 2:'lg', 3:'sm'}"></f:variable>
        <f:variable name="image" value="{data.image.0}"></f:variable>
        <f:variable name="className" value="img-fluid w-100"></f:variable>
        <f:if condition="image">
             <f:render partial="Media/Type/Image" arguments="{_all}" />
        </f:if>

        <f:variable name="carouselType" value="stage"></f:variable>
        <f:variable name="sliderContent">
            <f:for each="{data.items}" as="slide" key="label">
                <div class="carousel__item splide__slide">
                    <f:render partial="Text" arguments="{data: slide, linkType: 'button', headerClass: 'h1'}"></f:render>
                </div>
            </f:for>
        </f:variable>

        <div class="teaser teaser--nested {className}">
            <div class="teaser__canvas">
                <div class="teaser__body">
                    <div class="teaser__text">
                        <f:render partial="Carousel" arguments="{_all}"></f:render>

                        <div class="teaser__stones teaser__stones-introtext">
                            <svg width="104" height="79" viewBox="0 0 104 79" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <g>
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M17.022 17.9694C28.6318 7.58285 35.1065 0 49.8279 0C64.5493 0 76.9372 5.59786 85.172 14.6927C93.4067 23.7875 95.9693 32.0028 86.5343 47.6258C77.0992 63.2488 42.002 72.6658 24.4187 68.7635C6.83535 64.8611 0 58.7291 0 47.6258C0 36.5225 5.41229 28.356 17.022 17.9694Z"
                                        fill="url(#def-brand-gradient-primary)" />
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M59.3887 42.9848C61.6061 40.562 75.5205 25.4992 93.6305 29.9078C111.741 34.3165 101.269 62.1471 98.1846 67.5749C95.0998 73.0027 85.8109 82.1487 67.3173 77.9128C59.4687 76.115 53.8007 67.4168 54.0907 58.3697C54.2563 53.2051 57.1713 45.4077 59.3887 42.9848Z"
                                        fill="url(#def-brand-gradient-primary-variant)" />
                                </g>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="teaser__visual">
                </div>
            </div>
        </div>
    </div>
</f:section>


