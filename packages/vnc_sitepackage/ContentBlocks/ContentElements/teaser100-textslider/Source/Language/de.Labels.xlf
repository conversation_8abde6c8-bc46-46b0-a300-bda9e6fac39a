<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file source-language="en" target-language="de" original="" datatype="plaintext" date="2024-11-08T09:48:28+01:00">
		<body>
			<trans-unit id="title" resname="title">
				<source>Teaser Text Slider</source>
				<target>Fullsize-Teaser mit Textbox Slider</target>
			</trans-unit>
			<trans-unit id="description" resname="description">
				<source>Teaser with Background Image and a Slider for Texts</source>
				<target>Ein Teaser mit Hintergrund-Bild und einem Slider für Texte</target>
			</trans-unit>
			<trans-unit id="header.label" resname="header.label">
				<source>Identifier (for information only)</source>
				<target>Bezeichner (auf der Webseite nicht sichtbar)</target>
			</trans-unit>
			<trans-unit id="image.label" resname="image.label">
				<source>image</source>
				<target>Bild</target>
			</trans-unit>
			<trans-unit id="format.label" resname="format.label">
				<source>Image Format</source>
				<target>Bildformat</target>
			</trans-unit>
			<trans-unit id="format.items.widescreen.label" resname="format.items.widescreen.label">
				<source>Widescreen (16:9)</source>
				<target>Breitbild (16:9)</target>
			</trans-unit>
			<trans-unit id="format.items.univisium.label" resname="format.items.univisium.label">
				<source>Univisium (2:1)</source>
				<target>Univisium (2:1)</target>
			</trans-unit>
			<trans-unit id="format.items.wide.label" resname="format.items.wide.label">
				<source>Wide (5:2)</source>
				<target>Wide (5:2)</target>
			</trans-unit>
			<trans-unit id="format.items.ultrawide.label" resname="format.items.ultrawide.label">
				<source>Ultrawide (3:1)</source>
				<target>Ultrabreit (3:1)</target>
			</trans-unit>
			<trans-unit id="items.label" resname="items.label">
				<source>Teaser</source>
				<target>Teaser</target>
			</trans-unit>
			<trans-unit id="items.header.label" resname="items.header.label">
				<source>Headline</source>
				<target>Überschrift</target>
			</trans-unit>
			<trans-unit id="items.subheader.label" resname="items.subheader.label">
				<source>Sub Headline</source>
				<target>Unter-Überschrift</target>
			</trans-unit>
			<trans-unit id="items.bodytext.label" resname="items.bodytext.label">
				<source>Text</source>
				<target>Text</target>
			</trans-unit>
			<trans-unit id="items.link.label" resname="items.link.label">
				<source>Link</source>
				<target>Link</target>
			</trans-unit>
			<trans-unit id="items.linktext.label" resname="items.linktext.label">
				<source>Linktext</source>
				<target>Linktext</target>
			</trans-unit>
		</body>
	</file>
</xliff>
