<?xml version="1.0"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file datatype="plaintext" original="Labels.xlf" source-language="en" date="2025-03-06T16:09:33+00:00" product-name="vnc/teaser100-textslider">
		<header/>
		<body>
			<trans-unit id="title" resname="title">
				<source>Teaser Text Slider</source>
			</trans-unit>
			<trans-unit id="description" resname="description">
				<source>Teaser with Background Image and a Slider for Texts</source>
			</trans-unit>
			<trans-unit id="header.label" resname="header.label">
				<source>Identifier (for information only)</source>
			</trans-unit>
			<trans-unit id="image.label" resname="image.label">
				<source>image</source>
			</trans-unit>
			<trans-unit id="format.label" resname="format.label">
				<source>Image Format</source>
			</trans-unit>
			<trans-unit id="format.items.widescreen.label" resname="format.items.widescreen.label">
				<source>Widescreen (16:9)</source>
			</trans-unit>
			<trans-unit id="format.items.univisium.label" resname="format.items.univisium.label">
				<source>Univisium (2:1)</source>
			</trans-unit>
			<trans-unit id="format.items.wide.label" resname="format.items.wide.label">
				<source>Wide (5:2)</source>
			</trans-unit>
			<trans-unit id="format.items.ultrawide.label" resname="format.items.ultrawide.label">
				<source>Ultrawide (3:1)</source>
			</trans-unit>
			<trans-unit id="items.label" resname="items.label">
				<source>Teaser</source>
			</trans-unit>
			<trans-unit id="items.header.label" resname="items.header.label">
				<source>Headline</source>
			</trans-unit>
			<trans-unit id="items.bodytext.label" resname="items.bodytext.label">
				<source>Text</source>
			</trans-unit>
			<trans-unit id="items.link.label" resname="items.link.label">
				<source>Link</source>
			</trans-unit>
			<trans-unit id="items.linktext.label" resname="items.linktext.label">
				<source>Linktext</source>
			</trans-unit>
			<trans-unit id="items.subheader.label" resname="items.subheader.label">
				<source>Sub Headline</source>
			</trans-unit>
		</body>
	</file>
</xliff>
