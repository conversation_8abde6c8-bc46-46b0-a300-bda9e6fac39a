name: vnc/teaser100-textimage
title: Fullsize Teaser
description: 'Fullsize Teaser Slider with Image and Text'
group: vnccontent
prefixFields: true
prefixType: full
fields:
  - identifier: header
    label: 'Identifier (for information only)'
    useExistingField: true
  - identifier: format
    label: 'Image Format'
    type: Select
    renderType: selectSingle
    default: widescreen
    items:
#      - value: upright
#        label: 'Upright (9:16)'
#      - value: square
#        label: 'Square (1:1)'
#      - value: classicscreen
#        label: 'Classicscreen (5:4)'
#      - value: television
#        label: 'Television (4:3)'
#      - value: default
#        label: 'Photo (3:2)'
      - value: widescreen
        label: 'Widescreen (16:9)'
      - value: univisium
        label: 'Univisium (2:1)'
      - value: wide
        label: 'Wide (5:2)'
      - value: ultrawide
        label: 'Ultrawide (3:1)'
  - identifier: items
    label: 'Teaser'
    type: Collection
    labelField: header
    minitems: 1
    maxitems: 99
    appearance:
      collapseAll: true
      levelLinksPosition: both
    fields:
      - identifier: header
        type: Text
        label: "Headline"
        required: true
#      - identifier: subheader
#        type: Text
#        label: "Sub Headline"
#        required: false
      - identifier: bodytext
        type: Textarea
        label: "Text"
        enableRichtext: false
        required: true
      - identifier: link
        type: Link
        label: "Link"
      - identifier: linktext
        type: Text
        label: "Linktext"
      - identifier: image
        type: File
        allowed: jpg, jpeg, png
        minitems: 1
        maxitems: 1
        cropVariants:
          upright:
            disabled: true
          square:
            disabled: true
          classicscreen:
            disabled: true
          television:
            disabled: true
          default:
            disabled: false
          widescreen:
            disabled: false
          univisium:
            disabled: false
          wide:
            disabled: false
          ultrawide:
            disabled: false
  - identifier: space_after_class
    type: Select
    useExistingField: true
