<cb:asset.css identifier="content-block-vnc-vnc/teaser100-textimage-be" file="EditorPreview.css"/>

<section>
    <div class="container">
        <f:for each="{data.items}" as="teaser" iteration="recordIterator">
            <div class="row">
                <div class="col-3">
                        <f:sanitize.html>{teaser.header}</f:sanitize.html>
                </div>
                <div class="col-9">
                    <f:if condition="{teaser.image.0}">
                        <img
                            src="{f:uri.image(image:teaser.image.0, maxWidth: '150',  cropVariant: '{data.format}', treatIdAsReference: '1')}"
                            title="{teaser.image.0.title}"
                            alt="{f:if(condition: '{decorative}', then: '', else: teaser.image.0.alternative)}"
                        />
                    </f:if>
                </div>
            </div>
        </f:for>
    </div>
</section>
