
<f:layout name="Default"/>

<f:section name="Main">
        <div class="container-xxxl">

            <f:variable name="carouselType" value="stage"></f:variable>
            <f:variable name="carouselOptions" value="{
                &quot;arrows&quot;: true
            }"></f:variable>
            <f:variable name="sliderContent">
                <f:for each="{data.items}" as="slide" key="label">
                    <div class="carousel__item splide__slide">
                        <f:variable name="record" value="{ data: slide, files: slide.image }"></f:variable>
                        <f:variable name="teaserType" value="nested"></f:variable>
                        <f:variable name="headerClass" value="h1"></f:variable>
                        <f:variable name="linkType" value="button"></f:variable>
                        <f:variable name="sizes" value="{0:'xxl', 1:'xl', 2:'lg', 3:'xs'}"/>
                        <f:variable name="cropVariants" value="{
                        default:'{data.format}',
                        tablet:'default',
                        mobile:'default'
                    }"/>
                        <f:render partial="Teaser" arguments="{_all}"></f:render>
                    </div>
                </f:for>
            </f:variable>
            <f:render partial="Carousel" arguments="{_all}"></f:render>
        </div>
</f:section>


