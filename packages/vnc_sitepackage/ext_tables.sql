CREATE TABLE tt_content
(
	vnc_content_layout              varchar(24)   DEFAULT '' NOT NULL,
	vnc_content_overlay             varchar(24)   DEFAULT '' NOT NULL,
	vnc_content_columns             varchar(32)   DEFAULT '' NOT NULL,
	vnc_content_type                varchar(32)   DEFAULT '' NOT NULL,
	vnc_content_overline            varchar(1024) DEFAULT '' NOT NULL,
	vnc_content_introtext           mediumtext,
	vnc_content_bodytextsecondcol   mediumtext,
	vnc_content_link                varchar(1040) DEFAULT '' NOT NULL,
	vnc_content_linktext            varchar(124)  DEFAULT '' NOT NULL,
	vnc_content_video               int(11) unsigned DEFAULT '0',
	vnc_content_textimagetiles_item int(11) unsigned DEFAULT '0',
	vnc_content_texttiles_item      int(11) unsigned DEFAULT '0',
	vnc_content_stageslider_item    int(11) unsigned DEFAULT '0',
	vnc_content_download_icon       int(11) unsigned DEFAULT '0',
	vnc_content_enlarge_icon        int(11) unsigned DEFAULT '0',
	vnc_content_layout_stone        int(11) unsigned DEFAULT '0',
);

#
# Table structure for table 'vnc_content_texttiles_item'
#
CREATE TABLE vnc_content_texttiles_item
(
	uid              int(11) NOT NULL auto_increment,
	pid              int(11) DEFAULT '0' NOT NULL,

	tt_content       int(11) unsigned DEFAULT '0',
	header           varchar(255)  DEFAULT ''  NOT NULL,

	bodytext         text,
	link             varchar(255)  DEFAULT ''  NOT NULL,
	linktext         varchar(1024) DEFAULT ''  NOT NULL,
	icon             varchar(64)   DEFAULT '0' NOT NULL,

	tstamp           int(11) unsigned DEFAULT '0' NOT NULL,
	crdate           int(11) unsigned DEFAULT '0' NOT NULL,
	cruser_id        int(11) unsigned DEFAULT '0' NOT NULL,
	deleted          tinyint(4) unsigned DEFAULT '0' NOT NULL,
	hidden           tinyint(4) unsigned DEFAULT '0' NOT NULL,
	starttime        int(11) unsigned DEFAULT '0' NOT NULL,
	endtime          int(11) unsigned DEFAULT '0' NOT NULL,

	t3ver_oid        int(11) DEFAULT '0' NOT NULL,
	t3ver_id         int(11) DEFAULT '0' NOT NULL,
	t3ver_wsid       int(11) DEFAULT '0' NOT NULL,
	t3ver_label      varchar(255)  DEFAULT ''  NOT NULL,
	t3ver_state      tinyint(4) DEFAULT '0' NOT NULL,
	t3ver_stage      int(11) DEFAULT '0' NOT NULL,
	t3ver_count      int(11) DEFAULT '0' NOT NULL,
	t3ver_tstamp     int(11) DEFAULT '0' NOT NULL,
	t3ver_move_id    int(11) DEFAULT '0' NOT NULL,
	sorting          int(11) DEFAULT '0' NOT NULL,
	t3_origuid       int(11) DEFAULT '0' NOT NULL,
	sys_language_uid int(11) DEFAULT '0' NOT NULL,
	l10n_parent      int(11) DEFAULT '0' NOT NULL,
	l10n_diffsource  mediumblob                NOT NULL,

	PRIMARY KEY (uid),
	KEY              parent (pid),
	KEY              t3ver_oid (t3ver_oid, t3ver_wsid),
	KEY language (l10n_parent, sys_language_uid)
);

#
# Table structure for table 'vnc_content_textimagetiles_item'
#
CREATE TABLE vnc_content_textimagetiles_item
(
	uid              int(11) NOT NULL auto_increment,
	pid              int(11) DEFAULT '0' NOT NULL,

	tt_content       int(11) unsigned DEFAULT '0',
	header           varchar(255)  DEFAULT ''  NOT NULL,
	overlineicon     varchar(64)   DEFAULT ''  NOT NULL,
	overline         varchar(255)  DEFAULT ''  NOT NULL,
	bodytext         text,
	bodytextorient   varchar(12)   DEFAULT '0' NOT NULL,
	teaserorient     varchar(64)   DEFAULT '0' NOT NULL,
	link             varchar(255)  DEFAULT ''  NOT NULL,
	linktext         varchar(1024) DEFAULT ''  NOT NULL,

	image            int(11) unsigned DEFAULT '0',
	imageorient      varchar(12)   DEFAULT '0' NOT NULL,

	tstamp           int(11) unsigned DEFAULT '0' NOT NULL,
	crdate           int(11) unsigned DEFAULT '0' NOT NULL,
	cruser_id        int(11) unsigned DEFAULT '0' NOT NULL,
	deleted          tinyint(4) unsigned DEFAULT '0' NOT NULL,
	hidden           tinyint(4) unsigned DEFAULT '0' NOT NULL,
	starttime        int(11) unsigned DEFAULT '0' NOT NULL,
	endtime          int(11) unsigned DEFAULT '0' NOT NULL,

	t3ver_oid        int(11) DEFAULT '0' NOT NULL,
	t3ver_id         int(11) DEFAULT '0' NOT NULL,
	t3ver_wsid       int(11) DEFAULT '0' NOT NULL,
	t3ver_label      varchar(255)  DEFAULT ''  NOT NULL,
	t3ver_state      tinyint(4) DEFAULT '0' NOT NULL,
	t3ver_stage      int(11) DEFAULT '0' NOT NULL,
	t3ver_count      int(11) DEFAULT '0' NOT NULL,
	t3ver_tstamp     int(11) DEFAULT '0' NOT NULL,
	t3ver_move_id    int(11) DEFAULT '0' NOT NULL,
	sorting          int(11) DEFAULT '0' NOT NULL,
	t3_origuid       int(11) DEFAULT '0' NOT NULL,
	sys_language_uid int(11) DEFAULT '0' NOT NULL,
	l10n_parent      int(11) DEFAULT '0' NOT NULL,
	l10n_diffsource  mediumblob                NOT NULL,

	PRIMARY KEY (uid),
	KEY              parent (pid),
	KEY              t3ver_oid (t3ver_oid, t3ver_wsid),
	KEY language (l10n_parent, sys_language_uid)
);

#
# Table structure for table 'vnc_content_stageslider_item'
#
CREATE TABLE vnc_content_stageslider_item
(
	uid              int(11) NOT NULL auto_increment,
	pid              int(11) DEFAULT '0' NOT NULL,

	tt_content       int(11) unsigned DEFAULT '0',
	header           varchar(255)  DEFAULT '' NOT NULL,
	header_layout		 varchar(64)   DEFAULT '0' NOT NULL,
	bodytext         text,
	link             varchar(255)  DEFAULT '' NOT NULL,
	linktext         varchar(1024) DEFAULT '' NOT NULL,
	image            int(11) unsigned DEFAULT '0',

	tstamp           int(11) unsigned DEFAULT '0' NOT NULL,
	crdate           int(11) unsigned DEFAULT '0' NOT NULL,
	cruser_id        int(11) unsigned DEFAULT '0' NOT NULL,
	deleted          tinyint(4) unsigned DEFAULT '0' NOT NULL,
	hidden           tinyint(4) unsigned DEFAULT '0' NOT NULL,
	starttime        int(11) unsigned DEFAULT '0' NOT NULL,
	endtime          int(11) unsigned DEFAULT '0' NOT NULL,

	t3ver_oid        int(11) DEFAULT '0' NOT NULL,
	t3ver_id         int(11) DEFAULT '0' NOT NULL,
	t3ver_wsid       int(11) DEFAULT '0' NOT NULL,
	t3ver_label      varchar(255)  DEFAULT '' NOT NULL,
	t3ver_state      tinyint(4) DEFAULT '0' NOT NULL,
	t3ver_stage      int(11) DEFAULT '0' NOT NULL,
	t3ver_count      int(11) DEFAULT '0' NOT NULL,
	t3ver_tstamp     int(11) DEFAULT '0' NOT NULL,
	t3ver_move_id    int(11) DEFAULT '0' NOT NULL,
	sorting          int(11) DEFAULT '0' NOT NULL,
	t3_origuid       int(11) DEFAULT '0' NOT NULL,
	sys_language_uid int(11) DEFAULT '0' NOT NULL,
	l10n_parent      int(11) DEFAULT '0' NOT NULL,
	l10n_diffsource  mediumblob               NOT NULL,

	PRIMARY KEY (uid),
	KEY              parent (pid),
	KEY              t3ver_oid (t3ver_oid, t3ver_wsid),
	KEY language (l10n_parent, sys_language_uid)
);
