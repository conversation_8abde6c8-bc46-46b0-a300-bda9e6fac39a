mod {
  web_layout {
    BackendLayouts {
      startpage {
        title = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:backendlayout.startpage
        config {
          backend_layout {
            colCount = 1
            rowCount = 2
            rows {
              1 {
                columns {
                  1 {
                    name = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:backendlayout.startpage.stage
                    colPos = 1
                    allowed {
                      CType = html, vncstage, vncstageslider, vncstagesplit, vncintrotext
                    }
                  }
                }
              }

              2 {
                columns {
                  1 {
                    name = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:backendlayout.startpage.content
                    colPos = 0
                    allowed {
                      CType = felogin_login, html, shortcut, list, 2cols-container, vnctextimagetiles, vnctextimage, vnctextmedia, vncimage, vncgallery, vnc_quotes,  vnc_accordion, vnctexttiles, vnc_teaser100split, vnc_teaser100textimage, vnc_energyratewidget, vnc_teaser100textslider, powermail_pi1,  news_pi1, news_detailview, vnctext, vnc_infobox, vnc_maps, vnc_events
                      list_type = ttaddress_listview, indexedsearch_pi2, felogin_login
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
