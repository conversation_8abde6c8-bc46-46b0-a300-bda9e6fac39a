# ****************************************************
# Sets the proc options for all default configurations
# ****************************************************

processing:
  mode: default
  # Tags that are allowed in the content in general
  # Note that some tags like <mark> require you to style them via custom CSS directives in your frontend.
  allowTags:
    - a
    - abbr
    - acronym
    - address
    - article
    - big
    - blockquote
    - br
    - caption
    - cite
    - code
    - col
    - colgroup
    - dd
    - del
    - dfn
    - dl
    - div
    - dt
    - em
    - iframe
    - figure
    - figcaption
    - footer
    - header
    - h1
    - h2
    - h3
    - h4
    - h5
    - h6
    - hr
    - i
    - img
    - ins
    - kbd
    - label
    - li
    - mark
    - nav
    - ol
    - p
    - pre
    - q
    - s
    - samp
    - script
    - section
    - small
    - span
    - strong
    - sub
    - sup
    - table
    - thead
    - tbody
    - tfoot
    - td
    - th
    - tr
    - tt
    - u
    - ul
    - var

  ## Tags that are allowed outside of paragraphs
  allowTagsOutside: [address, article, aside, blockquote, figure, figcaption, footer, header, hr, mark, nav, section, div, script]

  ## allowed default attributes
  allowAttributes: [class, id, title, dir, lang, xml:lang, itemscope, itemtype, itemprop]

  ## CONTENT TO DATABASE
  HTMLparser_db:
    ## STRIP ALL ATTRIBUTES FROM THESE TAGS
    ## If this list of tags is not set, it will default to: b,i,u,br,center,hr,sub,sup,strong,em,li,ul,ol,blockquote,strike,mark,s.
    ## However, we want to keep xml:lang attribute on most tags and tags from the default list were cleaned on entry.
    noAttrib: br
    # Can be disabled if you trust ckeditor (If Automatic Content Formatting is enabled, this should be OK)
    # allowTags: %default%
    denyTags: img
    tags:
      hr:
        allowedAttribs:
          - class

    ## REMOVE OPEN OFFICE META DATA TAGS, WORD 2003 TAGS, LINK, META, STYLE AND TITLE TAGS, AND DEPRECATED HTML TAGS
    ## We use this rule instead of the denyTags rule so that we can protect custom tags without protecting these unwanted tags.
    ## @todo In v14, insert "center, font" here again (breaking change, see #104839)
    removeTags: [link, meta, o:p, sdfield, style, title]

    ## PROTECT CUSTOM TAGS
    keepNonMatchedTags: protect

    # HTML Sanitizer
    # `htmlSanitize = false | null` to disable individually
    htmlSanitize:
      # either preset name as declared in `$GLOBALS['TYPO3_CONF_VARS']['SYS']['htmlSanitizer']`
      # or class-name implementing interface `\TYPO3\HtmlSanitizer\Builder\BuilderInterface`
      build: default
