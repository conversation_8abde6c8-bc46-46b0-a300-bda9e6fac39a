imports:
  - { resource: "EXT:vnc_sitepackage/Configuration/RTE/Processing.yaml" }
  - { resource: "EXT:rte_ckeditor/Configuration/RTE/Editor/Base.yaml" }
  - { resource: "EXT:rte_ckeditor/Configuration/RTE/Editor/Plugins.yaml" }
  - { resource: "EXT:vnc_sitepackage/Configuration/RTE/Links.yaml" }

editor:
  config:
    contentsCss:
      - 'EXT:rte_ckeditor/Resources/Public/Css/contents.css'
      - 'EXT:vnc_sitepackage/Resources/Public/css/Backend/VncSitepackage.css'
    extraAllowedContent: "*(*)[data-*]; span(#shy)"
    stylesSet:
      - { name: "S Überschrift (H4) - Anthrazit", element: "h4", attributes: { class: "hl-s"}}
      - { name: "S Überschrift (H4) - Blau", element: "h4", attributes: { class: "color-blue-l hl-s"}}
      - { name: "XS Überschrift (H5) - Anthrazit", element: "h5", attributes: { class: "hl-xs"}}
      - { name: "XS Überschrift (H5) - Blau", element: "h5", attributes: { class: "color-blue-l hl-xs"}}
      - { name: "XSS Überschrift (H6) - Anthrazit", element: "h6", attributes: { class: "hl-xxs"}}
      - { name: "XSS Überschrift (H6) - Blau", element: "h6", attributes: { class: "color-blue-l hl-xxs"}}
      - { name: "Accordion Tabelle", element: "table", attributes: { class: "transform-to-accordion"}}
    toolbarGroups:
      - { name: styles, groups: [ format, styles] }
      - { name: basicstyles, groups: [ basicstyles ] }
      - { name: paragraph, groups: [ list, indent, blocks, align ] }
      - { name: colors }
      - "/"
      - { name: links, groups: [ links ] }
      - { name: editing, groups: [ spellchecker ] }
      - { name: insert, groups: [ insert ] }
      - { name: tools, groups: [ table, specialchar ] }
      - { name: document, groups: [ mode ] }
    table:
      defaultHeadings: { rows: 1 }
      contentToolbar:
        - tableColumn
        - tableRow
        # - mergeTableCells
        - tableProperties
        - tableCellProperties
        - toggleTableCaption

    format_tags: "p;h3;h4;h5;h6;pre"

    justifyClasses:
      - text-left
      - text-center
      - text-right
      - text-justify

    extraPlugins:
      - justify
      - autolink
      - colorbutton
      - button
      - panelbutton
      - panel
      - floatpanel
      - linebreak

    removePlugins:
      - image

    removeButtons:
      - Anchor
      - Underline
      - Strike
      - BGColor

    colorButton_enableAutomatic: true
    colorButton_enableMore: false
    colorButton_colors: 4a4a4a,0092bc
