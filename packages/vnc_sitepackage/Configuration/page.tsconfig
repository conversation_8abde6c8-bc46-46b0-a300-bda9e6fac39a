@import 'EXT:vnc_sitepackage/Configuration/TsConfig/Page/BackendLayouts/Startpage.tsconfig'
@import 'EXT:vnc_sitepackage/Configuration/TsConfig/Page/BackendLayouts/Subpage.tsconfig'
@import 'EXT:vnc_sitepackage/Configuration/TsConfig/Page/BackendLayouts/Footer.tsconfig'

@import 'EXT:vnc_sitepackage/Configuration/TsConfig/Page/Mod/Wizards/NewContentElement.tsconfig'

@import 'EXT:vnc_sitepackage/Configuration/TsConfig/Page/TCFORM/CropVariants.tsconfig'
@import 'EXT:vnc_sitepackage/Configuration/TsConfig/Page/TCFORM/tt_content.header_layout.tsconfig'
@import 'EXT:vnc_sitepackage/Configuration/TsConfig/Page/TCFORM/tt_content.layout.tsconfig'
@import 'EXT:vnc_sitepackage/Configuration/TsConfig/Page/TCFORM/tt_content.pi_flexform.tt_address.tsconfig'
@import 'EXT:vnc_sitepackage/Configuration/TsConfig/Page/TCFORM/tt_content.space_after_class.tsconfig'
@import 'EXT:vnc_sitepackage/Configuration/TsConfig/Page/TCFORM/tx_news.tsconfig'

@import 'EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Configuration/TsConfig/NewContentElementWizard.tsconfig'
@import 'EXT:vnc_sitepackage/SubPackages/vnc_powermail/Configuration/TsConfig/Tx_powermail_domain_model_field.tsconfig'
@import 'EXT:vnc_sitepackage/SubPackages/news/Configuration/TsConfig/page.tsconfig'


tx_powermail.flexForm.formSelection = {$rootPageId}
