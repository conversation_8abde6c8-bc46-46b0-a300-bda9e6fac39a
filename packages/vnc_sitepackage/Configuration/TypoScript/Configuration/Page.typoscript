page = PAGE
page {
  config {
    absRefPrefix = /
    htmlTag_langKey = de
    doctype = html5
    metaCharset = utf-8
    renderCharset = utf-8
    removeDefaultJS = 1w
    removePageCss = 0
    removeDefaultCss = 1
    removeDefaultJS = external
    sendCacheHeaders = 1
    disablePrefixComment = 1
    meaningfulTempFilePrefix = 100
    typolinkCheckRootline = 1
    typolinkEnableLinksAcrossDomains = 1
    spamProtectEmailAddresses = 1
    spamProtectEmailAddresses_atSubst = <span>@</span>
    language = de
    locale_all = de_DE.utf-8
    #bodyTag >
    #disableBodyTag = 1
    headerComment (
<vancado>

     VVVVVVVVVVVVV                     V VAVAVAVAVAV A
      VVVVVVVVVVVVV                   AVA VAVAVAVAV AAA
       VVVVVVVVVVVVV                 AVAVA VAVAVAV AAAAA
        VVVVVVVVVVVVV               AVAVAVA VAVAV AAAAAAA
         VVVVVVVVVVVVV             AVAVAVAVA VAV AAAAAAAAA
          VVVVVVVVVVVVV           AVAVAVAVAVA V AAAAAAAAAAA
           VVVVVVVVVVVVV         AVAVAVAVAVAVA AAAAAAAAAAAAA
            VVVVVVVVVVVVV       AVAVAVAVAVAVA   AAAAAAAAAAAAA
             VVVVVVVVVVVVV     AVAVAVAVAVAVA     AAAAAAAAAAAAA
              VVVVVVVVVVVVV   AVAVAVAVAVAVA       AAAAAAAAAAAAA
               VVVVVVVVVVVVV AVAVAVAVAVAVA         AAAAAAAAAAAAA
                VVVVVVVVVVV A AVAVAVAVAVA           AAAAAAAAAAAAA
                 VVVVVVVVV AVA AVAVAVAVA             AAAAAAAAAAAAA
                  VVVVVVV AVAVA AVAVAVA               AAAAAAAAAAAAA
                   VVVVV AVAVAVA AVAVA                 AAAAAAAAAAAAA
                    VVV AVAVAVAVA AVA                   AAAAAAAAAAAAA
                     V AVAVAVAVAVA A                     AAAAAAAAAAAAA

 	Diese Website wurde mit viel Know-how und Leidenschaft von VANCADO entwickelt.
 	Erfahren Sie mehr zu unserer substanziellen Arbeitsweise.
 	Sprechen Sie uns gerne an:
	+49 221 888 245-55
	<EMAIL>
	www.vancado.de

</vancado>
    )
  }
}

page {
  config {
    htmlTag_stdWrap {
      setContentToCurrent = 1
      cObject = COA
      cObject {
        10 = TEXT
        10 {
          addParams {
            class = no-js
          }

          append = TEXT
          append {
            char = 10
          }

          current = 1
        }
      }
    }
  }
}

page {
  meta {
    viewport = width=device-width, initial-scale=1, user-scalable=yes, shrink-to-fit=no
    language = de
  }

  headerData {
    30 = TEXT
    30 {
      value (
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <meta name="format-detection" content="telephone=no">
      )
    }
  }
}

page.10 = FLUIDTEMPLATE
page.10 {
  file.stdWrap.cObject = CASE
  file.stdWrap.cObject {
    key.data = levelfield:-2,backend_layout_next_level,slide
    key.override.field = backend_layout

    pagets__startpage = TEXT
    pagets__startpage {
      value = {$plugin.tx_vncsitepackage.view.templateRootPath}Startpage.html
    }

    pagets__subpage = TEXT
    pagets__subpage {
      value = {$plugin.tx_vncsitepackage.view.templateRootPath}Subpage.html
    }

  }

  # ---------------------------------------------------
  # Auto replace || to &shy;
  # ---------------------------------------------------
  stdWrap.replacement {
    10 {
      search = ||
      replace = &shy;
    }
  }

  format = html
  partialRootPath = {$plugin.tx_vncsitepackage.view.partialRootPath}
  layoutRootPath = {$plugin.tx_vncsitepackage.view.layoutRootPath}

  variables {

  }

  dataProcessing {
    10 = TYPO3\CMS\Frontend\DataProcessing\MenuProcessor
    10 {
      levels = 3
      titleField = nav_title // title
      expandAll = 1
      includeSpacer = 1
      as = mainNavigation
      dataProcessing {
        10 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
        10 {
          references.fieldName = media
          as = images
        }
      }
    }
    20 = TYPO3\CMS\Frontend\DataProcessing\MenuProcessor
    20 {
      special = directory
      special.value = {$plugin.tx_vncsitepackage.settings.metaNavigationPid}
      as = metaNavigation
      dataProcessing {
        10 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
        10 {
          references.fieldName = media
        }
      }
    }
    30 = TYPO3\CMS\Frontend\DataProcessing\MenuProcessor
    30 {
      special = directory
      special.value = {$plugin.tx_vncsitepackage.settings.footerNavigationPid}
      as = footerNavigation
    }
  }
  settings {
    startPagePid = {$plugin.tx_vncsitepackage.settings.startPagePid}
    svgSpriteFile = {$plugin.tx_vncsitepackage.settings.svgSpriteFile}
    siteLogo = {$plugin.tx_vncsitepackage.settings.siteLogo}
    footerNavigationPid = {$plugin.tx_vncsitepackage.settings.footerNavigationPid}
    linkInstagram = {$plugin.tx_vncsitepackage.settings.linkInstagram}
    linkFacebook = {$plugin.tx_vncsitepackage.settings.linkFacebook}
    linkLinkedin = {$plugin.tx_vncsitepackage.settings.linkLinkedin}
    linkXing = {$plugin.tx_vncsitepackage.settings.linkXing}
    linkYoutube = {$plugin.tx_vncsitepackage.settings.linkYoutube}
    linkX = {$plugin.tx_vncsitepackage.settings.linkX}
  }
}

#Cookie Banner
page.headerData.40 = TEXT
page.headerData.40.value (

)

page {
  includeCSS {
    style = EXT:vnc_sitepackage/Resources/Public/Css/style.css
  }

  includeJS {
  }

  includeJSFooterlibs {
    scripts = EXT:vnc_sitepackage/Resources/Public/Js/scripts.js
    // widget = EXT:vnc_sitepackage/Resources/Public/Js/energy-rates-widget.js
  }
}

# Favicon
page {
  headerData.50 = FLUIDTEMPLATE
  headerData.50 {
    file = EXT:vnc_sitepackage/Resources/Private/Partials/Page/Favicons.html
    settings {
      faviconPath = {$plugin.tx_vncsitepackage.settings.faviconPath}
    }
  }
}

[applicationContext == "Testing" || applicationContext == "Development"]
  config {
    no_cache = 1
    contentObjectExceptionHandler = 0
    concatenateJs = 0
    compressJs = 0
    concatenateCss = 0
    compressCss = 0
    minifyCSS = 0
  }
  # no Cookie Banner
  page.headerData.40 >
[global]

lib.inlineSvg = SVG
lib.inlineSvg {
  renderMode = inline
  src.field = src
}
seo_sitemap.config.index_enable = 0