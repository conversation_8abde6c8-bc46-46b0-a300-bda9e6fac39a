<?php

$LOCALLANG = 'vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf';

return [
    'ctrl' => [
        'label' => 'header',
        'sortby' => 'sorting',
        'tstamp' => 'tstamp',
        'crdate' => 'crdate',
        'title' => 'Text-Bild-<PERSON>chel <PERSON>',
        'delete' => 'deleted',
        'versioningWS' => true,
        'origUid' => 't3_origuid',
        'hideAtCopy' => true,
        'prependAtCopy' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.prependAtCopy',
        'transOrigPointerField' => 'l10n_parent',
        'transOrigDiffSourceField' => 'l10n_diffsource',
        'languageField' => 'sys_language_uid',
        'enablecolumns' => [
            'disabled' => 'hidden',
            'starttime' => 'starttime',
            'endtime' => 'endtime',
        ],
        'typeicon_classes' => [
            'default' => 'vnctextimagetiles-icon'
        ],
    ],
    'types' => [
        '1' => [
            'showitem' => '
            --palette--;LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:content.element.vnctextimagetiles.item,
            overlineicon,
            overline,
            header,
            bodytext,
            linktext,
            link,
            image,
            --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:tabs.access,
            --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:palette.visibility;visibility,
            --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:palette.access;access,
            --palette--;;hiddenLanguagePalette
            '
        ],
    ],
    'palettes' => [
        '1' => [
            'showitem' => ''
        ],
        'access' => [
            'showitem' => '
                starttime;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:starttime_formlabel,
                endtime;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:endtime_formlabel
            '
        ],
        'general' => [
            'showitem' => '
                tt_content
            '
        ],
        'visibility' => [
            'showitem' => '
                hidden;LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:content.element.vnctextimagetiles.item
            '
        ],
        // hidden but needs to be included all the time, so sys_language_uid is set correctly
        'hiddenLanguagePalette' => [
            'showitem' => 'sys_language_uid, l10n_parent',
            'isHiddenPalette' => true,
        ],
    ],
    'columns' => [
        'tt_content' => [
            'exclude' => true,
            'label' => 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:content.element.vnctextimagetiles.item',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'foreign_table' => 'tt_content',
                'foreign_table_where' => 'AND tt_content.pid=###CURRENT_PID### AND tt_content.CType="vnctextimagetiles"',
                'maxitems' => 1,
                'default' => 0,
            ],
        ],
        'hidden' => [
            'exclude' => true,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.hidden',
            'config' => [
                'type' => 'check'
            ]
        ],
        'starttime' => [
            'exclude' => true,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.starttime',
            'config' => [
                'type' => 'datetime',
                'default' => 0
            ],
            'l10n_mode' => 'exclude',
            'l10n_display' => 'defaultAsReadonly'
        ],
        'endtime' => [
            'exclude' => true,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.endtime',
            'config' => [
                'type' => 'datetime',
                'default' => 0,
                'range' => [
                    'upper' => mktime(0, 0, 0, 1, 1, 2038)
                ]
            ],
            'l10n_mode' => 'exclude',
            'l10n_display' => 'defaultAsReadonly'
        ],
        'sys_language_uid' => [
            'exclude' => 1,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.language',
            'config' => ['type' => 'language']
        ],
        'l10n_parent' => [
            'displayCond' => 'FIELD:sys_language_uid:>:0',
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.l18n_parent',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'items' => [
                    [
                        'label' => '',
                        'value' => 0
                    ]
                ],
                'foreign_table' => 'vnc_content_textimagetiles_item',
                'foreign_table_where' => 'AND vnc_content_textimagetiles_item.pid=###CURRENT_PID### AND vnc_content_textimagetiles_item.sys_language_uid IN (-1,0)',
                'default' => 0
            ]
        ],
        'l10n_diffsource' => [
            'config' => [
                'type' => 'passthrough'
            ]
        ],

        'overline' => [
            'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.overline',
            'exclude' => true,
            'config' => [
                'type' => 'input',
                'size' => 50,
                'eval' => 'trim'
            ],
        ],

        'overlineicon' => [
            'label' => 'Icon',
            'config' => [
                'eval' => 'trim',
                'type' => 'input',
                'renderType' => 'selectIcon',
                'iconset-type' => 'nucleo',
                'iconset-path' => 'EXT:vnc_sitepackage/Resources/Public/Libraries/Nucleo-Solid_24px',
            ],
        ],
        'header' => [
            'label' => 'LLL:EXT:' . $LOCALLANG . ':content.item.field.header',
            'exclude' => true,
            'config' => [
                'type' => 'input',
                'size' => 50,
                'eval' => 'trim'
            ],
        ],

        'bodytext' => [
            'label' => 'LLL:EXT:' . $LOCALLANG . ':content.item.field.bodytext',
            'l10n_mode' => 'prefixLangTitle',
            'l10n_cat' => 'text',
            'config' => [
                'type' => 'text',
                'cols' => '80',
                'rows' => '5',
                'enableRichtext' => true,
                'richtextConfiguration' => 'VncContentBasic',
                'eval' => 'trim'
            ],
        ],
        'image' => [
            'label' => 'LLL:EXT:' . $LOCALLANG . ':content.item.field.image',
            'config' => [
                'type' => 'file',
                'appearance' => [
                    'createNewRelationLinkTitle' => 'LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:images.addFileReference'
                ],
                // custom configuration for displaying fields in the overlay/reference table
                // to use the imageoverlayPalette instead of the basicoverlayPalette
                'overrideChildTca' => [

                    'types' => [
                        '0' => [
                            'showitem' => '
                                --palette--;;imageoverlayPalette,
                                --palette--;;filePalette'
                        ],
                        \TYPO3\CMS\Core\Resource\File::FILETYPE_IMAGE => [
                            'showitem' => 'crop, --palette--;;filePalette'
                        ],

                    ],
                    'columns' => [
                        'crop' => [
                            'config' =>
                                [
                                    'cropVariants' => [
                                        'default' => [
                                            'disabled' => false,
                                        ],
                                        'ultrawide' => [
                                            'disabled' => true,
                                        ],
                                        'univisium' => [
                                            'disabled' => true,
                                        ],
                                        'wide' => [
                                            'disabled' => true,
                                        ],
                                        'widescreen' => [
                                            'disabled' => true,
                                        ],
                                        'classicscreen' => [
                                            'disabled' => true,
                                        ],
                                        'upright' => [
                                            'disabled' => true,
                                        ],
                                        'television' => [
                                            'disabled' => true,
                                        ],
                                        'square' => [
                                            'disabled' => true,
                                        ],
                                    ],
                                ],
                        ],
                    ],
                ],
                'minitems' => 0,
                'maxitems' => 1,
                'allowed' => 'jpg,jpeg,png',
            ],
        ],
        'linktext' => [
            'label' => 'LLL:EXT:' . $LOCALLANG . ':content.item.field.linktext',
            'config' => [
                'type' => 'input',
                'eval' => 'trim'
            ],
        ],
        'link' => [
            'label' => 'LLL:EXT:' . $LOCALLANG . ':content.item.field.link',
            'config' => [
                'type' => 'input',
                'renderType' => 'link',
                'size' => 1020,
                'eval' => 'trim'
            ],
        ],


    ],
];
