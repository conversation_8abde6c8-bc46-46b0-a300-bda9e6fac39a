<?php

defined('TYPO3') || die();

$CTYPE = 'vnctextimagetiles';
$EXTKEY = 'vnc_sitepackage';
$LOCALLANG = 'vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf';

/***************
 * Add Content Element
 */
if (!isset($GLOBALS['TCA']['tt_content']['types'][$CTYPE])) {
    $GLOBALS['TCA']['tt_content']['types'][$CTYPE] = [];
}

/***************
 * Add content element to selector list
 */
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTcaSelectItem(
    'tt_content',
    'CType',
    [
        'LLL:EXT:' . $LOCALLANG . ':content.element.' . $CTYPE,
        $CTYPE,
        $CTYPE . '-icon'
    ],
    'text',
    'after'
);

/***************
 * Assign Icon
 */
$GLOBALS['TCA']['tt_content']['ctrl']['typeicon_classes'][$CTYPE] = $CTYPE . '-icon';

/***************
 * Configure element type
 */
$GLOBALS['TCA']['tt_content']['types'][$CTYPE] = array_replace_recursive(
    $GLOBALS['TCA']['tt_content']['types'][$CTYPE],
    [
        'showitem' => '--div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:general,
                --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:palette.general;general,
                header,
                header_layout,
                vnc_content_introtext,
                vnc_content_link,
                vnc_content_linktext,
                vnc_content_columns,
                vnc_content_textimagetiles_item,
                space_after_class,
                --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:language,
                --palette--;;language,
                --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:access,
                --palette--;;hidden,
                --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:palette.access;access,',
        'columnsOverrides' => [
            'image' => [
                'config' => [
                    'maxitems' => 1,
                    'minitems' => 1
                ]
            ],
        ]
    ]
);

/***************
 * Register fields
 */
$GLOBALS['TCA']['tt_content']['columns'] = array_replace_recursive(
    $GLOBALS['TCA']['tt_content']['columns'],
    [
        'vnc_content_textimagetiles_item' => [
            'label' => 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:content.element.vnctextimagetiles.item',
            'config' => [
                'type' => 'inline',
                'foreign_table' => 'vnc_content_textimagetiles_item',
                'foreign_field' => 'tt_content',
                'appearance' => [
                    'useSortable' => true,
                    'showSynchronizationLink' => true,
                    'showAllLocalizationLink' => true,
                    'showPossibleLocalizationRecords' => true,
                    'expandSingle' => true,
                    'enabledControls' => [
                        'localize' => true,
                    ]
                ],
                'behaviour' => [
                    'mode' => 'select',
                    'localizeChildrenAtParentLocalization' => true,
                ],
                'minitems' => 2,
                'maxitems' => 99,
            ]
        ]
    ]
);


$GLOBALS['TCA']['tt_content']['types'][$CTYPE]['columnsOverrides']['image']['config']['overrideChildTca']['columns']['crop']['config'] = [
    'cropVariants' => [
        'default' => [
            'title' => 'Default',
            'disabled' => false,
        ],
        'widescreen' => [
            'disabled' => true,
        ],
        'classicscreen' => [
            'disabled' => true,
        ],
        'ultrawide' => [
            'disabled' => true,
        ],
        'univisium' => [
            'disabled' => true,
        ],
        'wide' => [
            'disabled' => true,
        ],
        'free' => [
            'disabled' => true,
        ],
        'upright' => [
            'disabled' => true,
        ],
        'television' => [
            'disabled' => true,
        ],
    ],
];
