<?php

defined('TYPO3') || die();


$CTYPE = 'vncimageslider';
$EXTKEY = 'vnc_sitepackage';
$LOCALLANG = 'vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf';

/***************
 * Add Content Element
 */
if (!isset($GLOBALS['TCA']['tt_content']['types'][$CTYPE])) {
    $GLOBALS['TCA']['tt_content']['types'][$CTYPE] = [];
}

/***************
 * Add content element to seletor list
 */
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTcaSelectItem(
    'tt_content',
    'CType',
    [
        'LLL:EXT:' . $LOCALLANG . ':content.element.' . $CTYPE,
        $CTYPE,
        $CTYPE . '-icon'
    ],
    'text',
    'after'
);

/***************
 * Assign Icon
 */
$GLOBALS['TCA']['tt_content']['ctrl']['typeicon_classes'][$CTYPE] = $CTYPE . '-icon';

/***************
 * Configure element type
 */
$GLOBALS['TCA']['tt_content']['types'][$CTYPE] = array_replace_recursive(
    $GLOBALS['TCA']['tt_content']['types'][$CTYPE],
    [
        'showitem' => '--div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:general,
                --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:palette.general;general,
                header,
                header_layout,
                vnc_content_headlinesize,
                subheader,
                layout,
                image,
                space_after_class,
                --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:language,
                --palette--;;language,
                --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:access,
                --palette--;;hidden,
                --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:palette.access;access,',
        'columnsOverrides' => [
            'image' => [
                'label' =>
                    'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:content.element.images',
                //'description' => 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:teaser.field.image.description',
                'config' => [
                    'type' => 'file',
                    'appearance' => [
                        'createNewRelationLinkTitle' => 'LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:images.addFileReference'
                    ],
                    // custom configuration for displaying fields in the overlay/reference table
                    // to use the imageoverlayPalette instead of the basicoverlayPalette
                    'overrideChildTca' => [

                        'types' => [
                            '0' => [
                                'showitem' => '
                                --palette--;;imageoverlayPalette,
                                --palette--;;filePalette'
                            ],
                            \TYPO3\CMS\Core\Resource\File::FILETYPE_IMAGE => [
                                'showitem' => 'title,alternative,link,--palette--;;filePalette'
                            ],

                        ],
                    ],
                    'minitems' => 0,
                    'maxitems' => 20,
                    'allowed' => 'jpg,jpeg,png',
                ],
            ],
            'layout' => [
                'config' => [
                    'items' => [
                        ['label' => 'Fix', 'value' => 160],
                        ['label'  => 'Flexibel', 'value' => 100],
                        ['label'  => 'Static', 'value' => 0]
                    ]
                ]
            ]
        ]
    ]
);

$GLOBALS['TCA']['tt_content']['types'][$CTYPE]
['columnsOverrides']['image']['config']['overrideChildTca']['columns']['crop']['config'] = [
    'cropVariants' => [
        'default' => [
            'title' => 'Default',
        ],
        'widescreen' => [
            'disabled' => true,
        ],
        'classicscreen' => [
            'disabled' => true,
        ],
        'ultrawide' => [
            'disabled' => true,
        ],
        'univisium' => [
            'disabled' => true,
        ],
        'wide' => [
            'disabled' => true,
        ],
        'free' => [
            'disabled' => true,
        ],
        'upright' => [
            'disabled' => true,
        ],
        'television' => [
            'disabled' => true,
        ],
    ],
];
