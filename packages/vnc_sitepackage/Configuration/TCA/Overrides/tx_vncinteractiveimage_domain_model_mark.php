<?php

defined('TYPO3') || die();

if (!isset($GLOBALS['TCA']['tx_vncinteractiveimage_domain_model_mark'])) {
    $GLOBALS['TCA']['tx_vncinteractiveimage_domain_model_mark'] = [];
}

$GLOBALS['TCA']['tx_vncinteractiveimage_domain_model_mark'] = array_replace_recursive(
    $GLOBALS['TCA']['tx_vncinteractiveimage_domain_model_mark'],
    [
        'columns' => [
            'image' => [
                'config' => [
                    'overrideChildTca' => [
                        'columns' => [
                            'crop' => [
                                'config' => [
                                    'cropVariants' => [
                                        'default' => [
                                            'disabled' => true,
                                        ],
                                        'widescreen' => [
                                            'disabled' => false,
                                        ],
                                        'classicscreen' => [
                                            'disabled' => true,
                                        ],
                                        'ultrawide' => [
                                            'disabled' => true,
                                        ],
                                        'univisium' => [
                                            'disabled' => true,
                                        ],
                                        'wide' => [
                                            'disabled' => true,
                                        ],
                                        'upright' => [
                                            'disabled' => true,
                                        ],
                                        'television' => [
                                            'disabled' => true,
                                        ],
                                        'square' => [
                                            'disabled' => true,
                                        ],
                                    ],
                                ]
                            ]
                        ]
                    ],
                ],
            ],
        ],
    ]
);
