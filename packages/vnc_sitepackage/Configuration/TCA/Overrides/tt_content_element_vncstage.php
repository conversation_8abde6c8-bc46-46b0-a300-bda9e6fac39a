<?php

defined('TYPO3') || die();

$CTYPE = 'vncstage';
$EXTKEY = 'vnc_sitepackage';
$LOCALLANG = 'vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf';

/***************
 * Add Content Element
 */
if (!isset($GLOBALS['TCA']['tt_content']['types'][$CTYPE])) {
    $GLOBALS['TCA']['tt_content']['types'][$CTYPE] = [];
}

/***************
 * Add content element to seletor list
 */
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTcaSelectItem(
    'tt_content',
    'CType',
    [
        'LLL:EXT:' . $LOCALLANG . ':content.element.' . $CTYPE,
        $CTYPE,
        $CTYPE . '-icon'
    ],
    'text',
    'after'
);

/***************
 * Assign Icon
 */
$GLOBALS['TCA']['tt_content']['ctrl']['typeicon_classes'][$CTYPE] = $CTYPE . '-icon';

/***************
 * Configure element type
 */
$GLOBALS['TCA']['tt_content']['types'][$CTYPE] = array_replace_recursive(
    $GLOBALS['TCA']['tt_content']['types'][$CTYPE],
    [
        'showitem' => '--div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:general,
                --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:palette.general;general,
                image,
                space_after_class,
                --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:language,
                --palette--;;language,
                --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:access,
                --palette--;;hidden,
                --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:palette.access;access,',
        'columnsOverrides' => [
            'image' => [
                'label' => 'LLL:EXT:' . $LOCALLANG . ':content.element.vncimage',
                'description' => 'LLL:EXT:' . $LOCALLANG . ':content.element.image.description',
                'config' =>  [
                    'type' => 'file',
                    'maxitems' => 1,
                    'minitems' => 1,
                    'allowed' => 'jpg,jpeg,png,mp4,youtube',
                    'appearance' => [
                        'fileUploadAllowed' => false,
                        'fileByUrlAllowed' => false,
                        'elementBrowserAllowed' => 'jpg,jpeg,png,mp4,youtube'
                    ],
                    'overrideChildTca' => [
                        'columns' => [
                            'title' => [
                                'label' => 'Title',
                                'eval' => '',
                            ]
                        ],
                        'types' => [
                            \TYPO3\CMS\Core\Resource\File::FILETYPE_IMAGE => [
                                # mit Komma anhängen wenn standard Palette (link usw.)
                                # wieder angezeigt werden soll: --palette--;;filePalette
                                'showitem' => 'title,alternative,crop, --palette--;;filePalette'
                            ],
                        ],
                    ],
                ],
            ],
            'bodytext' => [
                'description' => 'LLL:EXT:' . $LOCALLANG . ':content.element.vncstage.bodytext.description',
                'config' => [
                    'type' => 'text',
                    'cols' => '40',
                    'rows' => '5',
                    'enableRichtext' => false,
                ]
            ],
        ]
    ]
);

// configure here all element specific access restrictions to the std crop variants defined in
// packages/vnc_sitepackage/Configuration/TsConfig/Page/TCEFORM/CropVariants.tsconfig
$GLOBALS['TCA']['tt_content']['types'][$CTYPE]['columnsOverrides']
['image']['config']['overrideChildTca']['columns']['crop']['config'] = [
    'cropVariants' => [
        'default' => [
            'disabled' => true,
        ],
        'ultrawide' => [
            'disabled' => false,
        ],
        'univisium' => [
            'disabled' => true,
        ],
        'wide' => [
            'disabled' => true,
        ],
        'widescreen' => [
            'disabled' => false,
        ],
        'upright' => [
            'disabled' => true,
        ],
        'classicscreen' => [
            'disabled' => true,
        ],
        'television' => [
            'disabled' => true,
        ],
        'square' => [
            'disabled' => true,
        ],
    ],
];

$GLOBALS['TCA']['tt_content']['types'][$CTYPE]['columnsOverrides']['vnc_content_video']
['config']['overrideChildTca']['columns']['crop']['config'] = [
    'cropVariants' => [
        'default' => [
            'disabled' => true,
        ],
        'ultrawide' => [
            'disabled' => false,
        ],
        'univisium' => [
            'disabled' => true,
        ],
        'wide' => [
            'disabled' => true,
        ],
        'widescreen' => [
            'disabled' => false,
        ],
        'upright' => [
            'disabled' => true,
        ],
        'television' => [
            'disabled' => true,
        ],
        'square' => [
            'disabled' => true,
        ],
    ],
];
