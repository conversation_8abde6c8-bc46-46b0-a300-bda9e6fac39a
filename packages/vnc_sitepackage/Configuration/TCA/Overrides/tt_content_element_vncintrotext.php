<?php

defined('TYPO3') || die();

$CTYPE = 'vncintrotext';
$EXTKEY = 'vnc_sitepackage';
$LOCALLANG = 'vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf';

/***************
 * Add Content Element
 */
if (!isset($GLOBALS['TCA']['tt_content']['types'][$CTYPE])) {
    $GLOBALS['TCA']['tt_content']['types'][$CTYPE] = [];
}

/***************
 * Add content element to seletor list
 */
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTcaSelectItem(
    'tt_content',
    'CType',
    [
        'LLL:EXT:' . $LOCALLANG . ':content.element.' . $CTYPE,
        $CTYPE,
        $CTYPE . '-icon'
    ],
    'text',
    'after'
);

/***************
 * Assign Icon
 */
$GLOBALS['TCA']['tt_content']['ctrl']['typeicon_classes'][$CTYPE] = $CTYPE . '-icon';

/***************
 * Configure element type
 */
$GLOBALS['TCA']['tt_content']['types'][$CTYPE] = array_replace_recursive(
    $GLOBALS['TCA']['tt_content']['types'][$CTYPE],
    [
        'showitem' => '--div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:general,
                --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:palette.general;general,
                header,
                header_layout,
                bodytext,
                vnc_content_link,
                vnc_content_linktext,
                space_after_class,
                --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:language,
                --palette--;;language,
                --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:access,
                --palette--;;hidden,
                --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:palette.access;access,',
        'columnsOverrides' => [
            'vnc_content_layout' => [
                'onChange' => 'reload'
            ],
            'bodytext' => [
                'description' => 'LLL:EXT:' . $LOCALLANG . ':content.element.bodytext.description',
                'config' => [
                    'type' => 'text',
                    'cols' => '80',
                    'rows' => '5',
                    'enableRichtext' => true,
                    'richtextConfiguration' => 'VncContentBasic'
                ]
            ],
        ]
    ]
);
