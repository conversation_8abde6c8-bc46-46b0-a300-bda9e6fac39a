<?php

defined('TYPO3') || die();

$LOCALLANG = 'vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf';

$GLOBALS['TCA']['tx_vncwarnings_domain_model_warning']['types'][1] = array_replace_recursive(
    $GLOBALS['TCA']['tx_vncwarnings_domain_model_warning']['types'][1],
    [
        'showitem' => ' sys_language_uid,
                        l10n_parent,
                        l10n_diffsource,
                        hidden,
                        header,
                        text,
                        link_text,
                        link,
                        icon,
                        pages,
                        --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:tabs.access,
                        starttime,
                        endtime'
    ]
);

$temporaryColumns = [
    'text' => [
        'exclude' => true,
        'label' =>
            'LLL:EXT:vnc_warnings/Resources/Private/Language/locallang_db.xlf:tx_vncwarnings_domain_model_warning.text',
        'config' => [
            'type' => 'text',
            'enableRichtext' => true,
            'richtextConfiguration' => 'VncContentBasic',
            'fieldControl' => [
                'fullScreenRichtext' => [
                    'disabled' => false,
                ],
            ],
            'cols' => 40,
            'rows' => 15,
            'eval' => 'trim',
        ],
    ],
    'icon' => [
        'label' => 'Icon',
        'config' => [
            'eval' => 'trim',
            'type' => 'input',
            'renderType' => 'selectIcon',
            'iconset-type' => 'nucleo',
            'iconset-path' => 'EXT:vnc_sitepackage/Resources/Public/Libraries/Nucleo-Solid_24px',
        ],
    ],
];

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTCAcolumns(
    'tx_vncwarnings_domain_model_warning',
    $temporaryColumns
);
