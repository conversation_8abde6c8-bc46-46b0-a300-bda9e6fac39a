<?php

defined('TYPO3') || die();

if (!isset($GLOBALS['TCA']['tt_content']['columns']['tx_vncinteractiveimage_image'])) {
    $GLOBALS['TCA']['tt_content']['columns']['tx_vncinteractiveimage_image'] = [];
}

$GLOBALS['TCA']['tt_content']['columns'] = array_replace_recursive(
    $GLOBALS['TCA']['tt_content']['columns'],
    [
        'tx_vncinteractiveimage_image' => [
            'config' => [
                'overrideChildTca' => [
                    'columns' => [
                        'crop' => [
                            'config' => [
                                'cropVariants' => [
                                    'widescreen' => [
                                        'disabled' => true,
                                    ],
                                    'classicscreen' => [
                                        'disabled' => true,
                                    ],
                                    'ultrawide' => [
                                        'disabled' => true,
                                    ],
                                    'univisium' => [
                                        'disabled' => true,
                                    ],
                                    'wide' => [
                                        'disabled' => true,
                                    ],
                                    'upright' => [
                                        'disabled' => true,
                                    ],
                                    'television' => [
                                        'disabled' => true,
                                    ],
                                    'square' => [
                                        'disabled' => true,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
        'tx_vncinteractiveimage_show_title_next_to_marker' => [
            'label' => '',
            'config' => [
                'type' => 'passthrough',
            ],
        ],
    ]
);

unset($GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist']['vncinteractiveimage_vncinteractive']);
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist']['vncinteractiveimage_vncinteractive'] = '
    tx_vncinteractiveimage_name, tx_vncinteractiveimage_layout, tx_vncinteractiveimage_show_fullscreen,
    tx_vncinteractiveimage_show_zoom, tx_vncinteractiveimage_image,
    tx_vncinteractiveimage_icon_mode, tx_vncinteractiveimage_icon_selection, tx_vncinteractiveimage_icon,
    tx_vncinteractiveimage_icon_formelement, tx_vncinteractiveimage_setmarker, tx_vncinteractiveimage_marks
';
