<?php

defined('TYPO3') or die();

(function () {
    $EXTKEY = 'vnc_sitepackage';

    // Should be removed in typo3 13 (addUserTSConfig will be deprecated, user.tsconfig is added by default)
    // See https://docs.typo3.org/m/typo3/reference-tsconfig/main/en-us/UsingSetting/UserTSconfig.html
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addUserTSConfig(
        '@import "EXT:vnc_sitepackage/Configuration/user.tsconfig"'
    );

    // Add default RTE configuration for CElements (incl. Tables)
    $GLOBALS['TYPO3_CONF_VARS']['RTE']['Presets']['VncDefault'] = 'EXT:' . $EXTKEY . '/Configuration/RTE/Default.yaml';
    $GLOBALS['TYPO3_CONF_VARS']['RTE']['Presets']['VncContentBasic'] =
        'EXT:' . $EXTKEY . '/Configuration/RTE/ContentBasic.yaml';


    /***************
     * Register Icons SVG
     */
    $svgIcons = [
        'vnctext-icon' => 'vnctext-icon.svg',
        'vncquote-icon' => 'vncquote-icon.svg',
        'vnctextmedia-icon' => 'vnctextmedia-icon.svg',
        'vnctextimage-icon' => 'vnctextimage-icon.svg',
        'vncimage-icon' => 'vncimage-icon.svg',
        'vncgallery-icon' => 'vncgallery-icon.svg',
        'vnctexttiles-icon' => 'vnctexttiles-icon.svg',
        'vnctextimagetiles-icon' => 'vnctextimagetiles-icon.svg',
        'vncstage-icon' => 'vncstage-icon.svg',
        'vncstageslider-icon' => 'vncstageslider-icon.svg',
        'vncstagesplit-icon' => 'vncstagesplit-icon.svg',
        'vncfullsizeteaser-icon' => 'vncfullsizeteaser-icon.svg',
        'vncfullsizeteasersplit-icon' => 'vncfullsizeteasersplit-icon.svg',
        'vncimageslider-icon' => 'vncimageslider-icon.svg',
        'vncintrotext-icon' => 'vncintrotext-icon.svg'
    ];

    $iconRegistry = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(\TYPO3\CMS\Core\Imaging\IconRegistry::class);
    foreach ($svgIcons as $identifier => $fileName) {
        $iconRegistry->registerIcon(
            $identifier,
            \TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider::class,
            ['source' => 'EXT:' . $EXTKEY . '/Resources/Public/Icons/' . $fileName]
        );
    }
})();
