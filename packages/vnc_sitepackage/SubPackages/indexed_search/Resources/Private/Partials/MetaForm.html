<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">
<li class="nav__item nav__item--icon">
    <span class="input input--text input--search w-100">
        <f:form action="search" method="post" id="tx_indexedsearch--meta" pageUid="{settings.targetPid}">
            <div class="tx-indexedsearch-hidden-fields">
                <f:form.hidden name="search[_sections]" value="0" />
                <f:form.hidden name="search[_freeIndexUid]" id="tx_indexedsearch_freeIndexUid" value="_" />
                <f:form.hidden name="search[ext]" value="{searchParams.ext}" />
                <f:form.hidden name="search[searchType]" value="{searchParams.searchType}" />
                <f:form.hidden name="search[defaultOperand]" value="{searchParams.defaultOperand}" />
                <f:form.hidden name="search[mediaType]" value="{searchParams.mediaType}" />
                <f:form.hidden name="search[sortOrder]" value="{searchParams.sortOrder}" />
                <f:form.hidden name="search[group]" value="{searchParams.group}" />
                <f:form.hidden name="search[languageUid]" value="{searchParams.languageUid}" />
                <f:form.hidden name="search[desc]" value="{searchParams.desc}" />
                <f:form.hidden name="search[numberOfResults]" value="{searchParams.numberOfResults}" />
                <f:form.hidden name="search[extendedSearch]" value="{searchParams.extendedSearch}" />
            </div>
            <div class="tx-indexedsearch-form">
                <f:form.textfield
                    name="search[sword]"
                    value="{sword}"
                    placeholder="Wonach suchen Sie?"
                    id="tx-indexedsearch-searchbox-sword"
                    class="tx-indexedsearch-searchbox-sword"
                    additionalAttributes="{aria-label: 'Suche'}"
                />
                <!-- <label for="tx-indexedsearch-searchbox-sword">Suche</label> -->
            </div>

        <button class="input__button bg-light rounded-circle" role="button" type="submit">
          <span class="svg-icon">
                <svg class="icon" preserveAspectRatio="xMaxYMin">
                    <use xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-magnifier"></use>
                </svg>
            </span>
        </button>
              </f:form>
    </span>
</li>

</html>
