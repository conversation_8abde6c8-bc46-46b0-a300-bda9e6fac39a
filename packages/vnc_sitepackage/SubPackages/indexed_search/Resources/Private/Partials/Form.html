<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:vnc="http://typo3.org/ns/Vancado/VncSitepackage/ViewHelpers"
      data-namespace-typo3-fluid="true">
<div class="tx-indexedsearch-searchbox">
    <f:form action="search" method="post" id="tx_indexedsearch" pageUid="{settings.targetPid}">
        <div class="tx-indexedsearch-hidden-fields">
            <f:form.hidden name="search[_sections]" value="0" />
            <f:form.hidden name="search[_freeIndexUid]" id="tx_indexedsearch_freeIndexUid" value="_" />
            <f:form.hidden name="search[pointer]" id="tx_indexedsearch_pointer" value="0" />
            <f:form.hidden name="search[ext]" value="{searchParams.ext}" />
            <f:form.hidden name="search[searchType]" value="{searchParams.searchType}" />
            <f:form.hidden name="search[defaultOperand]" value="{searchParams.defaultOperand}" />
            <f:form.hidden name="search[mediaType]" value="{searchParams.mediaType}" />
            <f:form.hidden name="search[sortOrder]" value="{searchParams.sortOrder}" />
            <f:form.hidden name="search[group]" value="{searchParams.group}" />
            <f:form.hidden name="search[languageUid]" value="{searchParams.languageUid}" />
            <f:form.hidden name="search[desc]" value="{searchParams.desc}" />
            <f:form.hidden name="search[numberOfResults]" value="{searchParams.numberOfResults}" />
            <f:form.hidden name="search[extendedSearch]" value="{searchParams.extendedSearch}" />
        </div>
            <div class="mb-2 text-white">
                <h<vnc:GetCurrentSearchObjectField field="header_layout"/> class="headline text-white h2 mb-3" for="tx-indexedsearch-searchbox-sword"><vnc:GetCurrentSearchObjectField field="header"/></h<vnc:GetCurrentSearchObjectField field="header_layout"/>>
                <div class="input input--search d-block">
                    <f:form.textfield autofocus="1" name="search[sword]" value="{sword}" id="tx-indexedsearch-searchbox-sword" class="tx-indexedsearch-searchbox-sword" aria-label="Suchbegriff" />
                    <button
                        id="tx-indexedsearch-searchbox-button-submit"
                        name="search[submitButton]"
                        class="input__button bg-light rounded-circle"
                        type="submit"
                        value="{f:translate(key: 'form.submit')}"
                        aria-label="Suche"
                        >
                        <span class="svg-icon svg-icon--large">
                              <svg class="icon" preserveAspectRatio="xMaxYMin">
                                  <use xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-magnifier"></use>
                              </svg>
                          </span>
                    </button>
                </div>
            </div>
            <f:if condition="{searchParams.extendedSearch}">
                <f:if condition="{showTypeSearch}">
                    <div class="tx-indexedsearch-search-for">
                        <label for="tx-indexedsearch-selectbox-searchtype"><f:translate key="form.match" />:</label>
                        <f:if condition="{allSearchTypes}">
                            <f:form.select name="search[searchType]" options="{allSearchTypes}" value="{searchParams.searchType}" id="tx-indexedsearch-selectbox-searchtype" class="tx-indexedsearch-selectbox-searchtype" />
                        </f:if>
                        <f:if condition="{allDefaultOperands}">
                            <f:form.select name="search[defaultOperand]" options="{allDefaultOperands}" value="{searchParams.defaultOperand}" id="tx-indexedsearch-selectbox-defaultoperand" class="tx-indexedsearch-selectbox-defaultoperand" />
                        </f:if>
                    </div>
                </f:if>
                <f:if condition="{showMediaAndLanguageSearch}">
                    <div class="tx-indexedsearch-search-in">
                        <label for="tx-indexedsearch-selectbox-media"><f:translate key="form.searchIn" />:</label>
                        <f:if condition="{allMediaTypes}">
                            <f:form.select name="search[mediaType]" options="{allMediaTypes}" value="{searchParams.mediaType}" id="tx-indexedsearch-selectbox-media" class="tx-indexedsearch-selectbox-media media" />
                        </f:if>
                        <f:if condition="{allLanguageUids}">
                            <f:form.select name="search[languageUid]" options="{allLanguageUids}" value="{searchParams.languageUid}" id="tx-indexedsearch-selectbox-lang" class="tx-indexedsearch-selectbox-lang lang" />
                        </f:if>
                    </div>
                </f:if>
                <f:if condition="{allSections}">
                    <div class="tx-indexedsearch-search-select-section">
                        <label for="tx-indexedsearch-selectbox-sections"><f:translate key="form.fromSection" />:</label>
                        <f:form.select name="search[sections]" options="{allSections}" value="{searchParams.sections}" id="tx-indexedsearch-selectbox-sections" class="tx-indexedsearch-selectbox-sections" />
                    </div>
                </f:if>
                <f:if condition="{allIndexConfigurations}">
                    <div class="tx-indexedsearch-search-freeindexuid">
                        <label for="tx-indexedsearch-selectbox-freeIndexUid"><f:translate key="form.freeIndexUid" />:</label>
                        <f:form.select name="search[freeIndexUid]" options="{allIndexConfigurations}" value="{searchParams.freeIndexUid}" id="tx-indexedsearch-selectbox-freeIndexUid" class="tx-indexedsearch-selectbox-freeIndexUid" />
                    </div>
                </f:if>
                <f:if condition="{showSortOrders}">
                    <div class="tx-indexedsearch-search-select-order">
                        <label for="tx-indexedsearch-selectbox-order"><f:translate key="form.orderBy" />:</label>
                        <f:form.select name="search[sortOrder]" options="{allSortOrders}" value="{searchParams.sortOrder}" id="tx-indexedsearch-selectbox-order" class="tx-indexedsearch-selectbox-order" />&nbsp;
                        <f:form.select name="search[sortDesc]"  options="{allSortDescendings}" value="{searchParams.sortDesc}" id="tx-indexedsearch-selectbox-desc" class="tx-indexedsearch-selectbox-desc" />
                    </div>
                </f:if>
                <f:if condition="{allNumberOfResults}">
                    <div class="tx-indexedsearch-search-select-results">
                        <label for="tx-indexedsearch-selectbox-results"><f:translate key="form.atATime" /></label>
                        <f:form.select name="search[numberOfResults]" options="{allNumberOfResults}" value="{searchParams.numberOfResults}" id="tx-indexedsearch-selectbox-results" class="tx-indexedsearch-selectbox-results" />
                    </div>
                </f:if>
                <f:if condition="{allGroups}">
                    <div class="tx-indexedsearch-search-select-group">
                        <label for="tx-indexedsearch-selectbox-group"><f:translate key="form.style" />:</label>
                        <f:form.select name="search[group]" options="{allGroups}" value="{searchParams.group}" id="tx-indexedsearch-selectbox-group" class="tx-indexedsearch-selectbox-group" />
                        <f:if condition="{settings.blind.extResume} == 0">
                            <f:form.checkbox name="search[extResume]" id="tx_indexedsearch_extResume" checked="{searchParams.extResume}" value="1" />
                        </f:if>
                    </div>
                </f:if>
            </f:if>
            <f:variable name="quickSearch"><vnc:GetCurrentSearchObjectField field="bodytext"/></f:variable>
            <f:if condition="{quickSearch}">
                <div class="quicksearch text-center">
                        <f:for each="{quickSearch}" as="item" >
                            <a href="#" onclick="document.getElementById('tx-indexedsearch-searchbox-sword').value = '{item}';document.getElementById('tx_indexedsearch').submit();" aria-label="Link für die Suche">{item}</a>
                        </f:for>
                </div>
            </f:if>
    </f:form>
</div>
<f:render partial="Rules" />
</html>
