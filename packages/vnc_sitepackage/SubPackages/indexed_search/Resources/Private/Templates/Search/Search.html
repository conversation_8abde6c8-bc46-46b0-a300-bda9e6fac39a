<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:is="http://typo3.org/ns/TYPO3/CMS/IndexedSearch/ViewHelpers"
      xmlns:vnc="http://typo3.org/ns/Vancado/VncSitepackage/ViewHelpers"
      data-namespace-typo3-fluid="true"
>
<section class="page-section">
    <div class="bg-gradient-secondary py-4 mb-5 d-none d-lg-block">
        <div class="container">
            <div class="row">
                <div class="col-12 col-md-10 col-lg-8 col-xl-6 mx-auto">
                    <f:alias map="{sword: searchParams.sword}">
                        <f:render partial="Form" arguments="{_all}" />
                    </f:alias>
                </div>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="search-results">
            <f:for each="{resultsets}" as="result">
                <div class="search-results__header d-lg-flex justify-content-between align-items-center mb-5">
                    <f:if condition="{result.categoryTitle}">
                        <h1 class="headline h2">{result.categoryTitle}</h1>
                    </f:if>
                    <f:if condition="{result.count} > 0">
                        <div class="search-result__search-terms">
                            <f:for each="{searchWords}" as="searchWord" key="key">
                                <f:if condition="{key} > 0">
                                    <f:then>
                                        <f:translate key="searchFor.{searchWord.operatorLabel}" />&nbsp;<span class="search-result__search-term">{searchWord.sword}</span>
                                    </f:then>
                                    <f:else>
                                        <f:translate key="searchFor" />:&nbsp;<strong class="search-result__search-term">{searchWord.sword}</strong>
                                    </f:else>
                                </f:if>
                            </f:for>
                        </div>
                    </f:if>
                    {result.searchedInSectionInfo}
                    <f:if condition="{result.count} > 0">
                        <f:then>
                            <p class="search-results__count">
                                <is:pageBrowsingResults numberOfResults="{result.count}" currentPage="{searchParams.pointer}" resultsPerPage="{searchParams.numberOfResults}" />
                                {result.sectionText}
                            </p>
                            <f:comment><!-- render the anchor-links to the sections inside the displayed result rows --></f:comment>
                            <f:if condition="{result.affectedSections}">
                                <div class="tx-indexedsearch-sectionlinks">
                                    <table cellpadding="0" cellspacing="0" border="0" summary="Result links">
                                        <f:for each="{result.affectedSections}" as="sectionData" key="sectionId">
                                            <tr>
                                                <td width="100%">--&gt;&nbsp;
                                                    <f:link.page section="anchor_{sectionId}" addQueryString="1"
                                                                            argumentsToBeExcludedFromQueryString="{0: 'id'}">
                                                        <f:if condition="{sectionData.0}">
                                                            <f:then>{sectionData.0}</f:then>
                                                            <f:else><f:translate key="unnamedSection" /></f:else>
                                                        </f:if>
                                                        &nbsp;({sectionData.1}&nbsp;<f:translate key="{f:if(condition: '{sectionData.1} > 1', then: 'result.pages', else: 'result.page')}" />)
                                                    </f:link.page>
                                                </td>
                                            </tr>
                                        </f:for>
                                    </table>
                                </div>
                            </f:if>
                           <!-- <is:pageBrowsing maximumNumberOfResultPages="{settings.page_links}" numberOfResults="{result.count}" currentPage="{searchParams.pointer}" resultsPerPage="{searchParams.numberOfResults}" />-->
                        </f:then>
                    </f:if>
                </div>
                <f:if condition="{result.count} > 0">
                    <f:then>
                        <ul class="search-result__items">
                            <f:for each="{result.rows}" as="row">
                                <li class="search-result__item pb-5 mb-4">
                                    <f:if condition="{row.isSectionHeader}">
                                        <f:then>
                                        <div id="anchor_{row.sectionId}" class="tx-indexedsearch-sectionhead">
                                            <h2 class="tx-indexedsearch-title"><f:format.html>{row.sectionTitle}</f:format.html> <span class="tx-indexedsearch-result-count">{row.numResultRows} <f:translate key="{f:if(condition: '{row.numResultRows} > 1', then: 'result.pages', else: 'result.page')}" /></span></h2>
                                        </div>
                                        </f:then>
                                        <f:else>
                                            <f:render partial="Searchresult" arguments="{row: row}" />
                                        </f:else>
                                    </f:if>
                                </li>
                            </f:for>
                        </ul>
                        <div class="row">
                            <vnc:indexedSearchPaginator numberOfResults="{result.count}" maximumNumberOfResultPages="{settings.page_links}" currentPage="{searchParams.pointer}" resultsPerPage="{searchParams.numberOfResults}" />
                        </div>
                    </f:then>
                    <f:else>
                        <div class="search-results__empty-results">
                            <f:translate key="result.noResult" />
                        </div>
                    </f:else>
                </f:if>
            </f:for>
        </div>
    </div>
</section>

</html>
