@import 'EXT:indexed_search/Configuration/TypoScript/constants.typoscript'

plugin.tx_indexedsearch {
  view {
    # cat=plugin.tx_indexedsearch/file; type=string; label=Path to template root (FE)
    templateRootPath = EXT:vnc_sitepackage/SubPackages/indexed_search/Resources/Private/Templates/
    # cat=plugin.tx_indexedsearch/file; type=string; label=Path to template partials (FE)
    partialRootPath = EXT:vnc_sitepackage/SubPackages/indexed_search/Resources/Private/Partials/
  }
  settings {
    # cat=plugin.tx_indexedsearch/settings; type=integer; label=Set the target page where search results are shown
    targetPid = 57
    # cat=plugin.tx_indexedsearch/settings; type=string; label=A list of integer which should be root-pages to search from
    rootPidList = 1
  }
}
