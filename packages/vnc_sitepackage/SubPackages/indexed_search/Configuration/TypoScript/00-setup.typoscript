@import 'EXT:indexed_search/Configuration/TypoScript/setup.typoscript'

page.config.index_enable = 1
plugin.tx_indexedsearch {
  settings {
    displayRules = 0
    svgSpriteFile = {$plugin.tx_vncsitepackage.settings.svgSpriteFile}
  }
  _LOCAL_LANG.de {
    form.submit = Suchen
    searchFor = Suche nach
    result.pages = Seiten
    result.page = Seite
    result.noResult = Keine Ergebnisse
    displayResults = Zeige Ergebnisse <strong>%1$s bis %2$s</strong> von <strong>%3$s</strong>
    displayResults.next = weiter
    displayResults.previous = vorher
    displayResults.page =
    pagination.previous = Zurück
    pagination.next = Vor
  }
}


lib.searchBox = USER
lib.searchBox {
  userFunc = TYPO3\CMS\Extbase\Core\Bootstrap->run
  vendorName = TYPO3\CMS
  extensionName = IndexedSearch
  pluginName = Pi2
  switchableControllerActions {
    Search {
      1 = form
      2 = search
    }
  }
  view =< plugin.tx_indexedsearch.view
  view.templateRootPaths.20 = EXT:vnc_sitepackage/SubPackages/indexed_search/Resources/Private/MetaTemplates/
  settings =< plugin.tx_indexedsearch.settings
}

