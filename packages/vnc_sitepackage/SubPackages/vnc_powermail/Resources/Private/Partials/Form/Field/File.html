{namespace vh=In2code\Powermail\ViewHelpers}

<div class="powermail_fieldwrap powermail_fieldwrap_type_file powermail_fieldwrap_{field.marker} {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses}">
	<f:render partial="Form/FieldLabel" arguments="{_all}" />
    <div>
        <div class="{settings.styles.framework.fieldWrappingClasses} input input--file">
            <!--label class="input__label" for="powermail_field_{field.marker}">TBT: File input</label-->
            <f:variable name="additionalAttributes" value="{data-file-input: ''}" />
            <vh:form.multiUpload
                    id="powermail_field_{field.marker}"
                    name="powermail_field_{field.marker}"
                    property="{field.marker}"
                    additionalAttributes="{vh:validation.uploadAttributes(field:field, additionalAttributes: '{additionalAttributes}')}"
                    class="powermail_file {settings.styles.framework.fieldClasses} {vh:validation.errorClass(field:field, class: 'powermail_field_error')}" />
            <f:if condition="{vh:misc.prefillField(field:field, mail:mail)}">
                <ul class="powermail_fieldwrap_file_list">
                    <f:for each="{vh:misc.prefillField(field:field, mail:mail)}" as="file">
                        <li>
                            <span class="fileName">{file}</span>
                            <f:form.hidden property="{field.marker}." value="{file}" />
                        </li>
                    </f:for>
                    <li>
                        <span class="deleteAllFiles">{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:form.delete_all_files')}</span>
                    </li>
                </ul>
            </f:if>
            <button
                type="button"
                class="button button--light"
                onclick="this.parentElement.querySelector('input[type=file]').click()"
                >
                <span class="caption">{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:form.upload_select')}</span>
            </button>
            <f:variable name="validation" value="{vh:validation.validationDataAttribute(field:field)}" />
            <f:if condition="{validation.data-powermail-required-message}">
                <span class="input__error">{validation.data-powermail-required-message}</span>
            </f:if>
        </div>
    </div>
</div>
