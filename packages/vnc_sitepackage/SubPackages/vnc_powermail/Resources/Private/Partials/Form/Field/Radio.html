<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<div class="input--radio-group powermail_fieldwrap powermail_fieldwrap_type_radio powermail_fieldwrap_{field.marker} {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses} mb-3">
    <p class="{settings.styles.framework.labelClasses}">
        <vh:string.escapeLabels>{field.title}</vh:string.escapeLabels><f:if condition="{field.mandatory}">*</f:if>
        <f:if condition="{field.description}"><span class="powermail-description"><f:format.html parseFuncTSPath="lib.parseFunc">{field.description}</f:format.html></span></f:if>
    </p>
    <div class="{settings.styles.framework.fieldWrappingClasses} radio-group">
        <f:for each="{field.modifiedSettings}" as="setting" iteration="index">
			<span class="radio-group__input input input--radio {settings.styles.framework.radioClasses} {vh:Validation.ErrorClass(field:field, class:'powermail_field_error')}">
					<f:form.radio
                        property="{field.marker}"
                        value="{setting.value}"
                        checked="{vh:Misc.PrefillMultiField(field:field, mail:mail, cycle:index.cycle)}"
                        id="powermail_field_{ttContentData.uid}_{field.marker}_{index.cycle}"
                        additionalAttributes="{vh:Validation.ValidationDataAttribute(field:field, iteration:index)}"
                        class="powermail_radio custom-control-input" />
				<label for="powermail_field_{ttContentData.uid}_{field.marker}_{index.cycle}" class="{settings.styles.framework.labelClasses}">
					<vh:string.escapeLabels>{setting.label}</vh:string.escapeLabels>
				</label>
			</span>
            <f:if condition='!{validation}'>
                <f:variable name="validation" value="{vh:Validation.ValidationDataAttribute(field:field, iteration:index)}" />
            </f:if>
        </f:for>

        <f:if condition="{settings.validation.client}">
            <span class="radio-group__error powermail_field_error_container_{field.marker}"></span>
        </f:if>
        <f:if condition="{validation.data-powermail-required-message}">
            <span class="input__error">{validation.data-powermail-required-message}</span>
        </f:if>
    </div>
</div>
</html>
