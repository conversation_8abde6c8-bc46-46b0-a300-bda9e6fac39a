{namespace vh=In2code\Powermail\ViewHelpers}

<div class="powermail_fieldwrap powermail_fieldwrap_type_date powermail_fieldwrap_{field.marker} {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses}">
	<f:render partial="Form/FieldLabel" arguments="{_all}" />

	<div class="{settings.styles.framework.fieldWrappingClasses}">
		<f:form.textfield
				type="{field.datepickerSettingsOptimized}"
				id="powermail_field_{field.marker}"
				property="{field.marker}"
				value="{vh:misc.prefillField(field:field, mail:mail)}"
				additionalAttributes="{vh:validation.datepickerDataAttribute(field:field, value:'{vh:misc.prefillField(field:field)}')}"
				class="powermail_date {settings.styles.framework.fieldClasses} {vh:validation.errorClass(field:field, class:'powermail_field_error')}" />
        <f:variable name="validation" value="{vh:validation.validationDataAttribute(field:field)}" />
        <f:if condition="{validation.data-powermail-required-message}">
          <span class="input__error">{validation.data-powermail-required-message}</span>
        </f:if>
	</div>
</div>
