{namespace vh=In2code\Powermail\ViewHelpers}

<div class="powermail_fieldwrap powermail_fieldwrap_type_captcha powermail_fieldwrap_{field.marker} {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses} mb-3">
	<f:render partial="Form/FieldLabel" arguments="{_all}" />

	<div class="captcha {settings.styles.framework.fieldWrappingClasses} {vh:validation.errorClass(field:field, class: 'powermail_field_error')}">
        <div class="captcha__input col">
            <f:form.textfield
                    id="powermail_field_{field.marker}"
                    property="{field.marker}"
                    value=""
                    class="powermail_captcha {settings.styles.framework.fieldClasses} {vh:validation.errorClass(field:field, class:'powermail_field_error')}"
                    additionalAttributes="{vh:validation.captchaDataAttribute(field:field)}" />
        </div>

        <div class="captcha__img col-auto">
            <vh:validation.captcha field="{field}" alt="Geben sie die im Bild dargestellte Zeichenfolge ein" class="powermail_captchaimage" id="powermail_captchaimage" />
            <span class="input__error">Bitte lösen Sie die Rechenaufgabe</span>
        </div>


        <f:comment>
		<f:if condition="{settings.validation.client}">
			<div class="powermail_field_error_container powermail_field_error_container_{field.marker}"></div>
		</f:if>
        </f:comment>
	</div>
</div>
