{namespace vh=In2code\Powermail\ViewHelpers}

<div class="powermail_fieldwrap powermail_fieldwrap_type_input powermail_fieldwrap_{field.marker} {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses}">
	<f:render partial="Form/FieldLabel" arguments="{_all}" />

	<div class="{settings.styles.framework.fieldWrappingClasses} input input--text">
		<f:form.textfield
				type="{vh:validation.fieldTypeFromValidation(field:field)}"
				property="{field.marker}"
				placeholder="{field.placeholder}"
				value="{vh:misc.prefillField(field:field, mail:mail)}"
				class="powermail_input {settings.styles.framework.fieldClasses} {vh:validation.errorClass(field:field, class:'input__error')} "
				additionalAttributes="{vh:validation.validationDataAttribute(field:field, additionalAttributes:'{autocomplete: field.autocomplete}')}"
				id="powermail_field_{field.marker}" />
        <f:variable name="validation" value="{vh:validation.validationDataAttribute(field:field)}" />
        <f:if condition="{validation.data-powermail-required-message}">
         <span class="input__error">{validation.data-powermail-required-message}</span>
        </f:if>
	</div>
</div>
