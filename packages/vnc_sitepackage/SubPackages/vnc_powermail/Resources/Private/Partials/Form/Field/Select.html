{namespace vh=In2code\Powermail\ViewHelpers}

<div class="powermail_fieldwrap powermail_fieldwrap_type_select powermail_fieldwrap_{field.marker} {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses}">
    <f:render partial="Form/FieldLabel" arguments="{_all}" />
	<div class="{settings.styles.framework.fieldWrappingClasses} input--dropdown input--select mb-3">
        {f:variable(name: 'svgPath', value: '{f:uri.resource(path:\'{settings.svgSpriteFile}\', extensionName: \'vnc_sitepackage\')}')}
        <f:alias map="{ocb: '{', ccb: '}'}">
            <vh:Form.SelectField
				options="{field.modifiedSettings}"
				property="{field.marker}"
				class="powermail_select visually-hidden"
				id="powermail_field_{field.marker}"
                name="powermail_field_{field.marker}"
                additionalAttributes="{vh:validation.validationDataAttribute(field:field, additionalAttributes:'{autocomplete: field.autocomplete, data-input-dropdown: \'{ocb}
                             \"toggleIcon\": {ocb} \"src\": \"{svgPath}\", \"symbol\": \"chevron-down\" {ccb}
                              {ccb}\'}')}"
                multiple="{field.multiselectForField}"
				value="{vh:Misc.PrefillField(field:field, mail:mail)}" />
        </f:alias>
        <f:comment>
        <!--label class="input__label visually-hidden" for="powermail_field_{field.marker}">
            Bitte auswählen
        </label-->
        </f:comment>
        <f:variable name="validation" value="{vh:validation.validationDataAttribute(field:field)}" />
        <f:if condition="{validation.data-powermail-required-message}">
            <span class="input__error">{validation.data-powermail-required-message}</span>
        </f:if>
        <f:comment><!--
        <span class="input__error">Element is invalid</span>
		<f:if condition="{field.multiselect}">
			<f:if condition="{settings.validation.client}">
				<div class="powermail_field_error_container powermail_field_error_container_{field.marker}"></div>
			</f:if>
		</f:if>-->
        </f:comment>
	</div>
</div>
