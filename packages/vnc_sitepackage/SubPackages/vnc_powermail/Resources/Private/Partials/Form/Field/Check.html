{namespace vh=In2code\Powermail\ViewHelpers}

<div class="input--check-group powermail_fieldwrap powermail_fieldwrap_type_check powermail_fieldwrap_{field.marker} {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses} mb-3">
    <p class="{settings.styles.framework.labelClasses}">
        <vh:string.escapeLabels>{field.title}</vh:string.escapeLabels><f:if condition="{field.mandatory}">*</f:if>
        <f:if condition="{field.description}"><span class="powermail-description"><f:format.html parseFuncTSPath="lib.parseFunc">{field.description}</f:format.html></span></f:if>
    </p>
	<div class="{settings.styles.framework.fieldWrappingClasses}">
		<f:for each="{field.modifiedSettings}" as="setting" iteration="index">
			<div class="input input--checkbox {vh:validation.errorClass(field:field, class:'powermail_field_error')}">
                <f:if condition="{field.mandatory}">
                    <f:then>
                        <f:form.checkbox
                            property="{field.marker}"
                            multiple="true"
                            required="true"
                            value="{setting.value}"
                            checked="{vh:misc.prefillMultiField(field:field, mail:mail, cycle:index.cycle)}"
                            id="powermail_field_{ttContentData.uid}_{field.marker}_{index.cycle}"
                            additionalAttributes="{vh:validation.validationDataAttribute(field:field, iteration:index)}"
                            class="powermail_checkbox powermail_checkbox_{field.uid} custom-control-input" />
                    </f:then>
                    <f:else>
                        <f:form.checkbox
                            property="{field.marker}"
                            multiple="true"
                            value="{setting.value}"
                            checked="{vh:misc.prefillMultiField(field:field, mail:mail, cycle:index.cycle)}"
                            id="powermail_field_{ttContentData.uid}_{field.marker}_{index.cycle}"
                            additionalAttributes="{vh:validation.validationDataAttribute(field:field, iteration:index)}"
                            class="powermail_checkbox powermail_checkbox_{field.uid} custom-control-input" />
                    </f:else>
                </f:if>
				<label class="input__label" for="powermail_field_{ttContentData.uid}_{field.marker}_{index.cycle}">
					<vh:string.escapeLabels>{setting.label}</vh:string.escapeLabels>
				</label>
			</div>
            <f:if condition='!{validation}'>
                <f:variable name="validation" value="{vh:validation.validationDataAttribute(field:field, iteration:index)}" />
            </f:if>
		</f:for>
        <f:if condition="{validation.data-powermail-required-message}">
            <span class="input__error">{validation.data-powermail-required-message}</span>
        </f:if>

		<f:if condition="{settings.validation.client}">
			<div class="powermail_field_error_container powermail_field_error_container_{field.marker} checkbox__error"></div>
		</f:if>

	</div>
</div>
