<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<div class="powermail_fieldwrap powermail_fieldwrap_type_textarea powermail_fieldwrap_{field.marker} {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses}">
	<f:render partial="Form/FieldLabel" arguments="{_all}" />
	<div class="input input--textarea {settings.styles.framework.fieldWrappingClasses}">
		<f:form.textarea
            cols="20"
            rows="5"
            id="powermail_field_{field.marker}"
            property="{field.marker}"
            placeholder="{field.placeholder}"
            value="{vh:misc.prefillField(field:field, mail:mail)}"
            additionalAttributes="{vh:validation.validationDataAttribute(field:field, additionalAttributes:'{autocomplete: field.autocomplete}')}"
            class="powermail_textarea {settings.styles.framework.fieldClasses} {vh:validation.errorClass(field:field, class:'powermail_field_error input__error ')}" />
        <f:variable name="validation" value="{vh:validation.validationDataAttribute(field:field)}" />
        <f:if condition="{validation.data-powermail-required-message}">
            <span class="input__error">{validation.data-powermail-required-message}</span>
        </f:if>
		</div>
</div>
