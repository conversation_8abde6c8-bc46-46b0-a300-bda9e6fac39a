@import 'EXT:powermail/Configuration/TypoScript/Main/setup.typoscript'
@import 'EXT:powermail_cond/Configuration/TypoScript/setup.typoscript'
@import 'EXT:vnc_powermail/Configuration/TypoScript/setup.typoscript'

plugin.tx_powermail {
  view {
    templateRootPaths {
      10 = EXT:vnc_sitepackage/SubPackages/vnc_powermail/Resources/Private/Templates/
    }
    partialRootPaths {
      10 = EXT:vnc_sitepackage/SubPackages/vnc_powermail/Resources/Private/Partials/
    }
    layoutRootPaths {
      10 = EXT:vnc_sitepackage/SubPackages/vnc_powermail/Resources/Private/Layouts/
    }
  }
  settings.setup {
    encryptCertificatePath =
    encryptEmailDomain =
    svgSpriteFile = {$plugin.tx_vncsitepackage.settings.svgSpriteFile}
    misc.ajaxSubmit = 0
    vnc_powermail {
      logoPath = {$plugin.tx_powermail.settings.vnc_powermail.logoPath}
      disclaimerID = {$plugin.tx_powermail.settings.vnc_powermail.disclaimerID}
      privacyID = {$plugin.tx_powermail.settings.vnc_powermail.privacyID}
      headerFontColor = {$plugin.tx_powermail.settings.vnc_powermail.headerFontColor}
      headerLinkColor = {$plugin.tx_powermail.settings.vnc_powermail.headerFontColor}
      headerBackgroundColor = {$plugin.tx_powermail.settings.vnc_powermail.headerBackgroundColor}
      bodyFontColor = {$plugin.tx_powermail.settings.vnc_powermail.bodyFontColor}
      bodyLinkColor = {$plugin.tx_powermail.settings.vnc_powermail.bodyFontColor}
      bodyBackgroundColor = {$plugin.tx_powermail.settings.vnc_powermail.bodyBackgroundColor}
      footerFontColor = {$plugin.tx_powermail.settings.vnc_powermail.footerFontColor}
      footerLinkColor = {$plugin.tx_powermail.settings.vnc_powermail.footerFontColor}
      footerBackgroundColor = {$plugin.tx_powermail.settings.vnc_powermail.footerBackgroundColor}
      copyrightNotice = {$plugin.tx_powermail.settings.vnc_powermail.copyrightNotice}
    }
  }
    _LOCAL_LANG {
    de {
      teaser_buttonText_default = Formular öffnen
    }
  }
}

