<html
    xmlns="http://www.w3.org/1999/xhtml"
    lang="en"
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:variable name="svgSpriteFile">Static/symbol-defs.svg</f:variable>
    <f:if condition="{addressIterator.total} > 1">
        <f:then>
            <f:variable name="columClass">col-12 col-md-6 mb-4</f:variable>
        </f:then>
        <f:else>
            <f:variable name="columClass">col-12</f:variable>
            <f:variable name="className">teaser--container</f:variable>
        </f:else>
    </f:if>

    <div class="{columClass}">
        <f:variable name="record" value="{ files: { 0: address.firstImage } }"></f:variable>
        <f:variable name="teaserType" value="card"></f:variable>
        <f:variable name="teaserContent">
            <h3 class="headline h4">{address.fullName}</h3>
            <f:if condition="{address.position}">
                <p class="bodytext--m mt-4 mb-3">{address.position -> f:format.raw()}</p>
            </f:if>
            <f:if condition="{address.phone}">
                <p class="mb-2">
                    <a class="btn -icon -isPhone" href="tel:{address.cleanedPhone}">
                        {address.phone}
                    </a>
                </p>
            </f:if>
            <f:if condition="{address.email}">
                <p class="mb-2">
                    <a class="btn -icon -isEmail" href="mailto:{address.email}" data-teaser-cta>
                        {address.email}
                    </a>
                </p>
            </f:if>
            <f:if condition="{address.description}">
            <p>
                <f:format.nl2br>{address.description}</f:format.nl2br>
            </p>
            </f:if>
        </f:variable>

        <f:variable name="clipPath" value="clip--stone-01"/>
        <f:render partial="Teaser" arguments="{_all}"></f:render>
    </div>
</html>
