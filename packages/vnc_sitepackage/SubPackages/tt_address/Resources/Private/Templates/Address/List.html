<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:layout name="Default" />

    <f:section name="main">
        <f:render
            section="displayMode_{settings.displayMode}"
            arguments="{_all}"
        />
    </f:section>

    <f:section name="displayMode_single">
        <section class="page-section py-7">
            <div class="container">
                <div class="row">
                    <div class="col col-md-10 col-lg-9 col-xl-8 mx-auto mb-4">
                        <f:if condition="{contentObjectData.header}">
                            <f:then>
                                <h2 class="headline mb-3">
                                    {contentObjectData.header -> f:format.raw()}
                                </h2>
                            </f:then>
                        </f:if>
                        <f:if condition="{contentObjectData.bodytext}">
                            <p>
                                {contentObjectData.bodytext -> f:format.html()}
                            </p>
                        </f:if>
                    </div>

                    <div
                        class="col-12 col-md-10 col-lg-9 col-xl-8 mx-auto"
                    >
                        <div class="row">
                            <f:for
                                each="{addresses}"
                                as="address"
                                iteration="addressIterator"
                            >
                                <f:render partial="Full" arguments="{_all}" />
                            </f:for>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </f:section>

    <f:comment
        ><!--
    Other displayMode options see orig tmpl in ext dir
    displayMode "list" & "map" in FF settings disabled in /tt_content.pi_flexform.tt_address.tsconfig
--></f:comment
    >
</html>
