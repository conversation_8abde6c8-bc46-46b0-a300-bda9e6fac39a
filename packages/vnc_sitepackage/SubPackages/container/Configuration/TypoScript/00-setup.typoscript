
// default/general configuration (will add 'children_<colPos>' variable to processedData for each colPos in container
tt_content.2cols-container < lib.contentElement
tt_content.2cols-container {
  templateName = 2ColsWithHeader
  templateRootPaths {
    10 = EXT:vnc_sitepackage/SubPackages/container/Resources/Private/Templates
  }
  dataProcessing {
    100 = B13\Container\DataProcessing\ContainerProcessor
  }
}

// if needed you can use ContainerProcessor with explicitly set colPos/variable values
tt_content.2cols-container < lib.contentElement
tt_content.2cols-container {
  templateName = 2Cols
  templateRootPaths {
    10 = EXT:vnc_sitepackage/SubPackages/container/Resources/Private/Templates
  }
  dataProcessing {
    200 = B13\Container\DataProcessing\ContainerProcessor
    200 {
      colPos = 201
      as = children_201
    }
    201 = B13\Container\DataProcessing\ContainerProcessor
    201 {
      colPos = 202
      as = children_202
    }
  }
}
