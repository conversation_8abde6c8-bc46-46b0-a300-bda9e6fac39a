<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      data-namespace-typo3-fluid="true"
>
<div class="container t3container">
    <div class="row gap-3 gap-md-0">
        <div class="col-12 col-lg-6 d-flex flex-column justify-content-center order-0 order-md-0">
            <f:for each="{children_201}" as="record">
                <f:if condition="{record.CType} == 'chart_doughnut' && {record.header}">
                    <h{record.header_layout} class="headline h4">{record.header}</h{record.header_layout}>
                </f:if>
                <f:format.raw>
                    {record.renderedContent}
                </f:format.raw>
            </f:for>
        </div>
        <div class="col-12 col-lg-6 d-flex flex-column justify-content-center mb-lg-0 order-1 order-md-0">
            <f:for each="{children_202}" as="record">
                <f:if condition="{record.CType} == 'chart_doughnut' && {record.header}">
                    <h{record.header_layout} class="headline h4">{record.header}</h{record.header_layout}>
                </f:if>
                <f:format.raw>
                    {record.renderedContent}
                </f:format.raw>
            </f:for>
        </div>
    </div>
</div>
</html>



