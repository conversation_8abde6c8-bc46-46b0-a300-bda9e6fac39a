<f:if condition="{interactiveImage.txVncinteractiveimageIconMode} === 'same'">
    <f:then>
        <f:if condition="{interactiveImage.txVncinteractiveimageIconSelection} === 'upload'">
            <f:then>
                <f:if condition="{interactiveImage.txVncinteractiveimageIcon}">
                    <f:image class="mark-icon {f:if(condition: context, then: 'mark-icon--no-hover')}" data-uid="info-{interactiveImage.uid}-{mark.uid}" image="{interactiveImage.txVncinteractiveimageIcon}" alt="{mark.title -> f:format.stripTags()}" aria-label="{mark.title -> f:format.stripTags()}" />
                </f:if>
            </f:then>
        </f:if>
        <f:if condition="{interactiveImage.txVncinteractiveimageIconSelection} === 'formelement'">
            <f:then>
                <f:if condition="{interactiveImage.txVncinteractiveimageIconFormelement}">
                    <i class="mark-icon mark-icon--formelement {f:if(condition: context, then: 'mark-icon--no-hover')} {interactiveImage.txVncinteractiveimageIconFormelement}" data-uid="info-{interactiveImage.uid}-{mark.uid}"></i>
                    <svg width="47" height="62" viewBox="0 0 47 62" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M23.2506 60.2264C22.7834 59.759 17.2997 54.227 11.9315 46.9546C9.23895 43.3069 6.59132 39.2425 4.62036 35.1764C2.64304 31.0972 1.38477 27.0929 1.38477 23.5452C1.38477 9.80791 12.477 1.58496 23.2506 1.58496C34.0241 1.58496 45.1164 9.80791 45.1164 23.5452C45.1164 27.0929 43.8581 31.0972 41.8808 35.1764C39.9098 39.2425 37.2622 43.3069 34.5696 46.9546C29.2014 54.227 23.7177 59.759 23.2506 60.2264ZM23.2566 60.2325C23.2566 60.2325 23.2567 60.2325 23.2567 60.2325L23.2566 60.2325Z" fill="url(#paint0_linear_5244_3682_{mark.uid})" stroke="white" stroke-width="2"/>
                        <defs>
                            <linearGradient id="paint0_linear_5244_3682_{mark.uid}" x1="23.2506" y1="0.584961" x2="23.2506" y2="61.229" gradientUnits="userSpaceOnUse">
                                <stop stop-color="var(--brand-color-secondary-light)"/>
                                <stop offset="1" stop-color="var(--brand-color-secondary-dark)"/>
                            </linearGradient>
                        </defs>
                    </svg>
                </f:if>
            </f:then>
        </f:if>
    </f:then>
</f:if>
<f:if condition="{interactiveImage.txVncinteractiveimageIconMode} === 'different'">
    <f:then>
        <f:if condition="{mark.iconSelection} === 'upload'">
            <f:then>
                <f:if condition="{mark.icon}">
                    <f:image class="mark-icon {f:if(condition: context, then: 'mark-icon--no-hover')}" data-uid="info-{interactiveImage.uid}-{mark.uid}" image="{mark.icon}" alt="{mark.title -> f:format.stripTags()}" aria-label="{mark.title -> f:format.stripTags()}" />
                </f:if>
            </f:then>
        </f:if>
        <f:if condition="{mark.iconSelection} === 'formelement'">
            <f:then>
                <f:if condition="{mark.iconFormelement}">
                    <i class="mark-icon mark-icon--formelement {f:if(condition: context, then: 'mark-icon--no-hover')} {mark.iconFormelement}" data-uid="info-{interactiveImage.uid}-{mark.uid}"></i>
                    <svg width="47" height="62" viewBox="0 0 47 62" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M23.2506 60.2264C22.7834 59.759 17.2997 54.227 11.9315 46.9546C9.23895 43.3069 6.59132 39.2425 4.62036 35.1764C2.64304 31.0972 1.38477 27.0929 1.38477 23.5452C1.38477 9.80791 12.477 1.58496 23.2506 1.58496C34.0241 1.58496 45.1164 9.80791 45.1164 23.5452C45.1164 27.0929 43.8581 31.0972 41.8808 35.1764C39.9098 39.2425 37.2622 43.3069 34.5696 46.9546C29.2014 54.227 23.7177 59.759 23.2506 60.2264ZM23.2566 60.2325C23.2566 60.2325 23.2567 60.2325 23.2567 60.2325L23.2566 60.2325Z" fill="url(#paint0_linear_5244_3682_{mark.uid})" stroke="white" stroke-width="2"/>
                        <defs>
                            <linearGradient id="paint0_linear_5244_3682_{mark.uid}" x1="23.2506" y1="0.584961" x2="23.2506" y2="61.229" gradientUnits="userSpaceOnUse">
                                <stop stop-color="var(--brand-color-secondary-light)"/>
                                <stop offset="1" stop-color="var(--brand-color-secondary-dark)"/>
                            </linearGradient>
                        </defs>
                    </svg>
                </f:if>
            </f:then>
        </f:if>
    </f:then>
</f:if>
<f:if condition="{interactiveImage.txVncinteractiveimageIconMode} === 'numbers'">
    <f:then>
        <p class="mark-icon mark-icon--number {f:if(condition: context, then: 'mark-icon--no-hover')}" data-uid="info-{interactiveImage.uid}-{mark.uid}" aria-label="{mark.title -> f:format.stripTags()}">
            <svg width="47" height="62" viewBox="0 0 47 62" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M23.2506 60.2264C22.7834 59.759 17.2997 54.227 11.9315 46.9546C9.23895 43.3069 6.59132 39.2425 4.62036 35.1764C2.64304 31.0972 1.38477 27.0929 1.38477 23.5452C1.38477 9.80791 12.477 1.58496 23.2506 1.58496C34.0241 1.58496 45.1164 9.80791 45.1164 23.5452C45.1164 27.0929 43.8581 31.0972 41.8808 35.1764C39.9098 39.2425 37.2622 43.3069 34.5696 46.9546C29.2014 54.227 23.7177 59.759 23.2506 60.2264ZM23.2566 60.2325C23.2566 60.2325 23.2567 60.2325 23.2567 60.2325L23.2566 60.2325Z" fill="url(#paint0_linear_5244_3682_{mark.uid})" stroke="white" stroke-width="2"/>
                <defs>
                    <linearGradient id="paint0_linear_5244_3682_{mark.uid}" x1="23.2506" y1="0.584961" x2="23.2506" y2="61.229" gradientUnits="userSpaceOnUse">
                        <stop stop-color="var(--brand-color-secondary-light)"/>
                        <stop offset="1" stop-color="var(--brand-color-secondary-dark)"/>
                    </linearGradient>
                </defs>
            </svg>
            {iterator.cycle}
        </p>
    </f:then>
</f:if>
