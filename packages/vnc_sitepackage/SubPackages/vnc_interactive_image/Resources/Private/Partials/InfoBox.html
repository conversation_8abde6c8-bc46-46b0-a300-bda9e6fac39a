<div class="content-box content-box__info d-flex flex-column">
    <f:for each="{interactiveImage.txVncinteractiveimageMarks}" as="mark" iteration="iterator">
        <div
            class="info-item d-none"
            id="info-{interactiveImage.uid}-{mark.uid}"
        >
            <div class="d-flex align-items-center">
                <f:render
                    partial="MarkIcon"
                    arguments="{interactiveImage: interactiveImage, mark: mark, iterator: iterator, context: 'infoBox'}"/>
                <h3 class="content-box__text col-auto mx-4 fw-bold">
                    <f:format.stripTags>{mark.title}</f:format.stripTags>
                </h3>
            </div>
            <f:if condition="{mark.image}">
                <f:then>
                    <div class="mt-3">
                        <f:image
                            class="content-box__image"
                            cropVariant="widescreen"
                            image="{mark.image}"/>
                    </div>
                </f:then>
            </f:if>
            <div class="mt-3 flex-grow-1">
                <f:format.raw>{mark.bodytext}</f:format.raw>
            </div>
            <f:if condition="{mark.link}">
                <div class="mt-3">
                    <f:link.typolink parameter="{mark.link}" class="content-box__link">
                        Mehr erfahren
                    </f:link.typolink>
                </div>
            </f:if>
        </div>
    </f:for>
</div>

<div class="mt-3 mt-md-4 mt-lg-4">
    <f:render
        partial="InfoBoxNav"
        arguments="{interactiveImage: interactiveImage}"/>
</div>
