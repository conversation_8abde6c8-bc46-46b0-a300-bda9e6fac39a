<html
        xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
        data-namespace-typo3-fluid="true"
>

    <f:if condition="{interactiveImage}">
        <f:then>
            <!-- Check layout -->
            <f:variable name="layout">{interactiveImage.txVncinteractiveimageLayout}</f:variable>
            <f:variable name="showZoom">{interactiveImage.txVncinteractiveimageShowZoom}</f:variable>

            <div class="vncInteractiveImage"
                 data-show-zoom="{showZoom}"
                 data-layout="{layout}"
                 data-cut-out="true"
                 data-scroll-into-view="true"
            >
                <f:comment>
                    <f:if condition="{interactiveImage.txVncinteractiveimageName}">
                        <div class="row heading-block border-bottom-0 mb-2">
                            <h2>{interactiveImage.txVncinteractiveimageName}</h2>
                        </div>
                    </f:if>
                </f:comment>

                <div class="container" data-layout="{layout}">
                    <div class="row">
                        <f:if condition="{layout} === 'infoBox'">
                            <f:then>
                                <div class="col-lg-8 col-12">
                                    <div class="image-container">
                                        <f:image
                                                class="img-fluid"
                                                image="{interactiveImage.txVncinteractiveimageImage}"
                                                alt="{interactiveImage.txVncinteractiveimageName}"
                                        />
                                        <!-- Render Marks Container -->
                                        <f:render
                                                partial="MarksContainer"
                                                arguments="{interactiveImage: interactiveImage, layout: layout}"
                                        />
                                        <f:render section="zoomControls" arguments="{interactiveImage: interactiveImage, layout: layout}" />
                                    </div>
                                </div>
                                <div class="col-lg-4 col-12">
                                    <!-- Render Info Box -->
                                    <f:render
                                            partial="InfoBox"
                                            arguments="{interactiveImage: interactiveImage}"
                                    />
                                </div>
                            </f:then>
                        </f:if>
                    </div>
                </div>
                <f:if condition="{layout} === 'popover'">
                    <f:then>
                        <div class="col-12 relative">
                            <div class="image-container image-container--full-width">
                                <f:image
                                        class="img-fluid"
                                        image="{interactiveImage.txVncinteractiveimageImage}"
                                        alt="{interactiveImage.txVncinteractiveimageName}"
                                />

                                <f:render
                                        partial="MarksContainer"
                                        arguments="{interactiveImage: interactiveImage, layout: layout}"
                                />
                                <f:render section="zoomControls" arguments="{interactiveImage: interactiveImage, layout: layout}" />
                            </div>
                            <div class="col-12 mt-3">
                                <!-- Render Info Box -->
                                <f:render
                                    partial="InfoBox"
                                    arguments="{interactiveImage: interactiveImage}"
                                />
                            </div>
                        </div>
                    </f:then>
                </f:if>
            </div>

            <f:section name="zoomControls">
                <div class="zoom-controls">
                    <f:if condition="{interactiveImage.txVncinteractiveimageShowZoom}">
                        <button class="btn btn-primary zoom-in" id="zoom-in-{interactiveImage.uid}" aria-label="Vergr&ouml;&szlig;ern">
                            <svg width="42" height="42" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect x="1" y="1" width="40" height="40" rx="20" fill="url(#paint0_linear_5244_3807_{interactiveImage.uid}_0)" stroke="white" stroke-width="2"/>
                                <path d="M20.125 15.75H18.375V18.375H15.75V20.125H18.375V22.75H20.125V20.125H22.75V18.375H20.125V15.75Z" fill="white"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M27.125 19.25C27.125 21.105 26.4775 22.8112 25.3925 24.1587L30.9837 29.75L29.75 30.9837L24.1587 25.4013C22.8112 26.4775 21.105 27.125 19.25 27.125C14.91 27.125 11.375 23.59 11.375 19.25C11.375 14.91 14.91 11.375 19.25 11.375C23.59 11.375 27.125 14.91 27.125 19.25ZM13.125 19.25C13.125 22.6275 15.8725 25.375 19.25 25.375C22.6275 25.375 25.375 22.6275 25.375 19.25C25.375 15.8725 22.6275 13.125 19.25 13.125C15.8725 13.125 13.125 15.8725 13.125 19.25Z" fill="white"/>
                                <defs>
                                    <linearGradient id="paint0_linear_5244_3807_{interactiveImage.uid}_0" x1="21" y1="0" x2="21" y2="42" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="var(--brand-color-secondary-light)"/>
                                        <stop offset="1" stop-color="var(--brand-color-secondary-dark)"/>
                                    </linearGradient>
                                </defs>
                            </svg>
                        </button>
                        <button class="btn btn-primary zoom-out" id="zoom-out-{interactiveImage.uid}" aria-label="Verkleinern">
                            <svg width="42" height="42" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect x="1" y="1" width="40" height="40" rx="20" fill="url(#paint0_linear_5244_3815_{interactiveImage.uid}_1)" stroke="white" stroke-width="2"/>
                                <rect x="15.75" y="18.375" width="7" height="1.75" fill="white"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M27.125 19.25C27.125 21.105 26.4775 22.8112 25.3925 24.1587L30.9837 29.75L29.75 30.9837L24.1587 25.4013C22.8112 26.4775 21.105 27.125 19.25 27.125C14.91 27.125 11.375 23.59 11.375 19.25C11.375 14.91 14.91 11.375 19.25 11.375C23.59 11.375 27.125 14.91 27.125 19.25ZM13.125 19.25C13.125 22.6275 15.8725 25.375 19.25 25.375C22.6275 25.375 25.375 22.6275 25.375 19.25C25.375 15.8725 22.6275 13.125 19.25 13.125C15.8725 13.125 13.125 15.8725 13.125 19.25Z" fill="white"/>
                                <defs>
                                    <linearGradient id="paint0_linear_5244_3815_{interactiveImage.uid}_1" x1="21" y1="0" x2="21" y2="42" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="var(--brand-color-secondary-light)"/>
                                        <stop offset="1" stop-color="var(--brand-color-secondary-dark)"/>
                                    </linearGradient>
                                </defs>
                            </svg>
                        </button>
                    </f:if>
                    <f:if condition="{interactiveImage.txVncinteractiveimageShowFullscreen} && {layout} === 'popover'">
                        <button class="btn btn-primary" id="fullscreen" aria-label="Vollbildschirm-Modus">
                            <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect x="1" y="1" width="46" height="46" rx="23" fill="url(#paint0_linear_2686_11520_{interactiveImage.uid}_2)" stroke="white" stroke-width="2"/>
                                <path d="M33 14H15C13.35 14 12 15.35 12 17V31C12 32.65 13.35 34 15 34H33C34.65 34 36 32.65 36 31V17C36 15.35 34.65 14 33 14ZM34 31C34 31.55 33.55 32 33 32H15C14.45 32 14 31.55 14 31V17C14 16.45 14.45 16 15 16H33C33.55 16 34 16.45 34 17V31Z" fill="white"/>
                                <path d="M18 24H16V30H22V28H18V24Z" fill="white"/>
                                <path d="M26 20H30V24H32V18H26V20Z" fill="white"/>
                                <defs>
                                    <linearGradient id="paint0_linear_2686_11520_{interactiveImage.uid}_2" x1="24" y1="0" x2="24" y2="48" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="var(--brand-color-secondary-light)"/>
                                        <stop offset="1" stop-color="var(--brand-color-secondary-dark)"/>
                                    </linearGradient>
                                </defs>
                            </svg>
                        </button>
                    </f:if>
                </div>
            </f:section>
        </f:then>
    </f:if>

</html>
