<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <div class="teaser teaser--card teaser--news{class}" data-teaser>
        <div class="teaser__canvas">
            <div class="teaser__body">
                <div
                    class="teaser__text"
                    >
                    <f:variable name="newsItemId" value="newsItem-{contentObjectData.uid}-{newsItem.uid}"></f:variable>

                    <div>
                        <time
                            class="bodytext--xs d-flex text-teriary mb-2 btn -icon -isDate"
                            datetime="{f:format.date(date:newsItem.datetime, format:'Y-m-d')}"
                            ><f:format.date format="d.m.Y"
                                >{newsItem.datetime}</f:format.date
                            ></time
                        >
                        <article>
                            <h3 id="{newsItemId}-title" class="headline h3">{newsItem.title}</h3>
                            <div class="bodytext--s">
                                <f:if condition="{newsItem.teaser}">
                                <f:then>
                                    {newsItem.teaser -> f:format.crop(maxCharacters:
                                    '{settings.cropMaxCharacters}',
                                    respectWordBoundaries:'1') -> f:format.html()}
                                </f:then>
                                <f:else>
                                    {newsItem.bodytext -> f:format.crop(maxCharacters:
                                    '{settings.cropMaxCharacters}',
                                    respectWordBoundaries:'1') -> f:format.html()}
                                </f:else>
                            </f:if>
                        </div>
                        </article>
                    </div>
                    <n:link
                        newsItem="{newsItem}"
                        class="button button--secondary"
                        additionalAttributes="{'aria-labelledby': '{newsItemId}-title', 'data-teaser-cta': 'true', 'aria-label': '{f:translate(key: \'more-link\')}'}"
                        settings="{settings}"
                        title="{newsItem.title}"
                        id="{newsItemId}-readmore"
                        tabindex="0"
                    >
                        <f:translate key="more-link" />
                    </n:link>
                </div>
            </div>
            <div class="teaser__visual">

                    <f:if condition="{newsItem.mediaPreviews}">
                        <f:then>
                            <f:alias map="{mediaElement: newsItem.mediaPreviews.0}">
                                <f:if
                                    condition="{mediaElement.originalResource.type} == 2"
                                >
                                    <f:image
                                        image="{mediaElement}"
                                        class="img-fluid"
                                        title="{mediaElement.originalResource.title}"
                                        alt="{mediaElement.originalResource.alternative}"
                                        loading="{settings.list.media.image.lazyLoading}"
                                        maxWidth="{f:if(condition: settings.media.maxWidth, then: settings.media.maxWidth, else: settings.list.media.image.maxWidth)}"
                                        maxHeight="{f:if(condition: settings.media.maxHeight, then: settings.media.maxHeight, else: settings.list.media.image.maxHeight)}"
                                    />
                                </f:if>
                                <f:if
                                    condition="{mediaElement.originalResource.type} == 4"
                                >
                                    <f:render
                                        partial="Detail/MediaVideo"
                                        arguments="{mediaElement: mediaElement}"
                                    />
                                </f:if>
                                <f:if
                                    condition="{mediaElement.originalResource.type} == 5"
                                >
                                    <f:image
                                        image="{mediaElement}"
                                        class="img-fluid"
                                        title="{mediaElement.originalResource.title}"
                                        alt="{mediaElement.originalResource.alternative}"
                                        loading="{settings.list.media.image.lazyLoading}"
                                        maxWidth="{f:if(condition: settings.media.maxWidth, then: settings.media.maxWidth, else: settings.list.media.image.maxWidth)}"
                                        maxHeight="{f:if(condition: settings.media.maxHeight, then: settings.media.maxHeight, else: settings.list.media.image.maxHeight)}"
                                    />
                                </f:if>
                            </f:alias>
                        </f:then>
                        <f:else>
                            <f:if condition="{settings.displayDummyIfNoMedia}">
                                <f:image
                                    class="img-fluid"
                                    src="{settings.list.media.dummyImage}"
                                    title=""
                                    alt=""
                                    loading="{settings.list.media.image.lazyLoading}"
                                    maxWidth="{f:if(condition: settings.media.maxWidth, then: settings.media.maxWidth, else: settings.list.media.image.maxWidth)}"
                                    maxHeight="{f:if(condition: settings.media.maxHeight, then: settings.media.maxHeight, else: settings.list.media.image.maxHeight)}"
                                />
                            </f:if>
                        </f:else>
                    </f:if>

            </div>
        </div>
    </div>
</html>
