<html
    xmlns="http://www.w3.org/1999/xhtml"
    lang="en"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>

    <nav class="pagination" aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:page_pagination')}">
        <ul class="d-flex gap-4 justify-content-center align-items-center">
            <f:if
                condition="({pagination.previousPageNumber} && {pagination.previousPageNumber} >= {pagination.firstPageNumber})"
            >
                <f:then>
                <li class="nav__item">
                    <a
                        class="pagination__link pagination__arrow"
                        role="button"
                        href="{f:uri.action(action:actionName, arguments:{currentPage: pagination.previousPageNumber},addQueryString:'untrusted')}"
                        title="{f:translate(key:'widget.pagination.previous', extensionName: 'news')}"
                        aria-label="{f:translate(key:'widget.pagination.previous', extensionName: 'news')}"
                        >
                        <i class="svg-icon">
                            <svg class="icon" preserveAspectRatio="xMaxYMin">
                                <use xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-triangle-right"></use>
                            </svg>
                        </i>
                    </a>
                </li>
                </f:then>
                <f:else>
                    <li class="nav__item">
                        <a role="button" aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:empty_prev')}" aria-disabled="true" class="pagination__link pagination__arrow button--mirrored is-disabled disabled">
                            <i class="svg-icon">
                                <svg class="icon" preserveAspectRatio="xMaxYMin">
                                    <use xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-triangle-right"></use>
                                </svg>
                            </i>
                        </a>
                    </li>
                </f:else>
            </f:if>
            <f:comment><!--
            <f:if condition="{pagination.hasLessPages}">
                <a class="pagination__link">…</a>
            </f:if>
            --></f:comment>
            <f:for each="{pagination.allPageNumbers}" as="page">
                <li class="nav__item">
                    <f:variable name="currentAttribute" value="{f:if(condition: '{page} == {paginator.currentPageNumber}', then:'aria-current=page')}"></f:variable>
                    <a
                        role="button"
                        href="{f:if(condition: '{page} == {paginator.currentPageNumber}', then:'#', else: '{f:uri.action(action:actionName, arguments:{currentPage: page},addQueryString:\'untrusted\')}')}"
                        class="pagination__link nav__link rounded-circle  {f:if(condition: '{page} == {paginator.currentPageNumber}', then:'is-active')}"
                        aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:page')} {page}"
                        {currentAttribute}
                        >{page}</a
                    >
                </li>
            </f:for>
            <f:comment><!--
            <f:if condition="{pagination.hasMorePages}">
                <a class="pagination__link">…</a>
            </f:if>
            --></f:comment>
            <f:if
                condition="({pagination.nextPageNumber} && {pagination.nextPageNumber} <= {pagination.lastPageNumber})"
            >
                <f:then>
                    <li class="nav__item">
                        <a
                            class="pagination__link pagination__arrow"
                            role="button"
                            href="{f:uri.action(action:actionName, arguments:{currentPage: pagination.nextPageNumber},addQueryString:'untrusted')}"
                            aria-label="{f:translate(key:'widget.pagination.next', extensionName: 'news')}"
                            title="{f:translate(key:'widget.pagination.next', extensionName: 'news')}"
                            {next_disabled}
                        >
                            <i class="svg-icon">
                                <svg class="icon" preserveAspectRatio="xMaxYMin">
                                    <use xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-triangle-right"></use>
                                </svg>
                            </i>
                        </a>
                    </li>
                </f:then>
                <f:else>
                    <li class="nav__item">
                        <a role="button" aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:empty_next')}" class="pagination__link nav__link disabled">...</a>
                    </li>
                </f:else>

            </f:if>
        </ul>
    </nav>
</html>
