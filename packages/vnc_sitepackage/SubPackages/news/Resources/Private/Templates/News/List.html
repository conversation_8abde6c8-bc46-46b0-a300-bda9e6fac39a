<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
	  xmlns:n="http://typo3.org/ns/GeorgRinger/News/ViewHelpers"
	  data-namespace-typo3-fluid="true">
<f:layout name="General" />
<f:section name="content">
    <!--TYPO3SEARCH_end-->
	<f:if condition="{news}">
		<f:then>
            <div class="news-container py-5">
            <f:if condition="{settings.limit}==1">
                <f:then>
                    <f:render section="single" arguments="{_all}"></f:render>
                </f:then>
                <f:else>
                    <f:render section="multiple" arguments="{_all}"></f:render>
                </f:else>
            </f:if>
            </div>
		</f:then>
		<f:else>
            <div class="container" data-ajax-container="">
                <div class="row">
                    <f:translate key="list_nonewsfound" />
                </div>
			</div>
		</f:else>
	</f:if>
	<!--TYPO3SEARCH_begin-->
</f:section>


<f:section name="multiple">
    <f:if condition="{contentObjectData.header}">
        <div class="col-12 col-md-10 col-lg-9 col-xl-8 mx-auto text-center mb-3">
            <f:render partial="Header" arguments="{data:contentObjectData,className: 'mb-4'}"/>
        </div>
    </f:if>
    <div class="container" data-ajax-container="">
        <div class="row">
            <f:for each="{pagination.paginator.paginatedItems}" as="newsItem" iteration="iterator">
                <f:render partial="List/Item" arguments="{newsItem: newsItem,settings:settings,iterator:iterator,contentObjectData:contentObjectData}" />
            </f:for>
            <f:if condition="{pageData.uid} != {settings.newsArchivPid}">
                <f:render partial="List/NewsArchivTeaser" arguments="{settings:settings}" />
            </f:if>
        </div>
        <f:if condition="{settings.hidePagination}==0">
            <div class="row">
                <f:render partial="Pagination" arguments="{pagination: pagination.pagination, paginator: pagination.paginator}" />
            </div>
        </f:if>
    </div>
</f:section>

<f:section name="single">
    <div class="container">
    <div class="d-flex gap-5 gap-lg-3 flex-column flex-lg-row">
        <f:if condition="{contentObjectData.header}">
            <div class="col-12 col-lg-5">
                <f:render partial="Text" arguments="{data:contentObjectData,className: 'mb-4'}"/>
                <p>
                    <f:link.page pageUid="{settings.listPid}" class="button button--secondary" tabindex="0"> <f:translate key="list-link" /></f:link.page>
                </p>
            </div>
        </f:if>
        <div class="col-12 col-lg-6 offset-lg-1 news-column" data-ajax-body="">
            <f:for each="{pagination.paginator.paginatedItems}" as="newsItem" iteration="iterator">
                <f:render partial="List/ItemSingle" arguments="{newsItem: newsItem,settings:settings,iterator:iterator}" />
            </f:for>
        </div>
    </div>
    </div>
</f:section>
</html>
