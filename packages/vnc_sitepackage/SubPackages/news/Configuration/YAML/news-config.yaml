routeEnhancers:
  News:
    type: Extbase
    extension: News
    plugin: Pi1
    routes:
      - routePath: '/{news_title}'
        _controller: 'News::detail'
        _arguments: { 'news_title': 'news' }
      - routePath: '/category/{category_title}'
        _controller: 'News::list'
        _arguments: { 'category_title': 'category' }
    defaultController: 'News::list'
    defaults:
      page: '0'
    aspects:
      news_title:
        type: PersistedAliasMapper
        tableName: 'tx_news_domain_model_news'
        routeFieldName: 'path_segment'
      page:
        type: StaticRangeMapper
        start: '1'
        end: '100'
      category-name:
        type: NewsCategory
      tag-name:
        type: NewsTag
