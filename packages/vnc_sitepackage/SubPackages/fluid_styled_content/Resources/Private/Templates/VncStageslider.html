<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<f:layout name="Default"/>

<f:section name="Main">
    <div class="container-xxxl">

        <f:variable name="carouselType" value="stage"></f:variable>
        <f:if condition="{records -> f:count()}>1">
            <f:then>
        <f:variable name="carouselOptions" value="{
            &quot;arrows&quot;: true
        }"></f:variable>
            </f:then>
            <f:else>
                <f:variable name="carouselOptions" value="{
            &quot;arrows&quot;: false, &quot;pagination&quot;: false
        }"></f:variable>
            </f:else>
        </f:if>
        <f:variable name="sliderContent">
            <f:for each="{records}" as="slide" key="label">
                <div class="carousel__item splide__slide">
                    <f:variable name="record" value="{ data: slide.data, files: slide.images }"></f:variable>
                    <f:variable name="teaserType" value="nested"></f:variable>
                    <f:if condition="slide.data.header_layout">
                        <f:then>
                            <f:variable name="headerLayout" value="{slide.data.header_layout}"></f:variable>
                        </f:then>
                        <f:else>
                            <f:variable name="headerLayout" value="h2"></f:variable>
                        </f:else>
                    </f:if>
                    <f:variable name="headerClass" value="h2"></f:variable>
                    <f:variable name="linkType" value="button"></f:variable>
                    <f:variable name="sizes" value="{0:'xxl', 1:'xl', 2:'lg', 3:'sm'}"/>
                    <f:variable name="cropVariants" value="{
                        default:'ultrawide',
                        tablet:'default',
                        mobile:'default'
                    }"/>
                    <f:render partial="Teaser" arguments="{_all}"></f:render>
                </div>
            </f:for>

        </f:variable>
        <f:render partial="Carousel" arguments="{_all}"></f:render>
    </div>
</f:section>
</html>
