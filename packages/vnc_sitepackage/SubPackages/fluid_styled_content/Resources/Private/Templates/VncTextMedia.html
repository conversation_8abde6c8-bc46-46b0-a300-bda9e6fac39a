<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<f:layout name="Default"/>

<f:section name="Main">
    <div class="container">
        <f:for each="{files}" as="file">
            <f:alias map="{offset: 'true'}">
                <f:variable name="download" value="false"/>
                <f:variable name="cropVariant" value="widescreen"/>
                <f:render partial="TextMedia" arguments="{_all}"/>
            </f:alias>
        </f:for>
    </div>
</f:section>

</html>




