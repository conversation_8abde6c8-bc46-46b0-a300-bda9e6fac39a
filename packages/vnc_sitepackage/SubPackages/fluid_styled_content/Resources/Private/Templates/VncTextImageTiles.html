<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>

<f:layout name="Default"/>

<f:section name="Main">

    <f:variable name="carouselOptions" value="{&quot;perPage&quot;:&quot;{data.vnc_content_columns}&quot;,&quot;breakpoints&quot;:{&quot;768&quot;:{&quot;perPage&quot;:2},&quot;480&quot;:{&quot;perPage&quot;:1}},&quot;gap&quot;: &quot;1.5rem&quot;}"></f:variable>

    <div class="container">
        <div class="row">
            <div class="col col-md-10 col-lg-9 col-xl-8 mx-auto mb-3">
                <f:if condition="{data.header}">
                    <f:alias map="{teaserType: '', linkType: 'button', className: ''}">
                        <f:render partial="Text" arguments="{_all}"/>
                    </f:alias>
                </f:if>
            </div>
            <div class="col-12">
            </div>
                <f:variable name="sliderContent">
                    <f:for each="{records}" as="record" iteration="recordIterator">
                        <div class="carousel__item splide__slide">
                            <f:alias map="{teaserType: 'card', linkType: 'button', className: ''}">
                                <f:render partial="Teaser" arguments="{_all}"/>
                            </f:alias>
                        </div>
                    </f:for>
                </f:variable>

                <f:render partial="Carousel" arguments="{_all}"></f:render>
            </div>
        </div>
    </div>

</f:section>
