<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<f:layout name="Default"/>

<f:section name="Main">
    <div class="page-section">
        <div class="split-stage split-stage--reverse container-xxxl">
            <div class="split-stage__item{f:if(condition:'{data.vnc_content_overlay}', then: ' bg-{data.vnc_content_overlay}')}">
                <div class="aspect-ratio">
                    <div class="aspect-ratio__canvas">
                        <div class="split-stage__container">
                            <div class="container teaser p-md-0">
                                <div class="teaser__text">
                                    <f:variable name="linkType" value="button"></f:variable>
                                    <f:variable name="headerClass">h2</f:variable>
                                    <f:render partial="Text" arguments="{_all}"></f:render>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="teaser__stones teaser__stones-introtext">
                    <svg
                        width="104"
                        height="79"
                        viewBox="0 0 104 79"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <g>
                            <path
                                fill-rule="evenodd"
                                clip-rule="evenodd"
                                d="M17.022 17.9694C28.6318 7.58285 35.1065 0 49.8279 0C64.5493 0 76.9372 5.59786 85.172 14.6927C93.4067 23.7875 95.9693 32.0028 86.5343 47.6258C77.0992 63.2488 42.002 72.6658 24.4187 68.7635C6.83535 64.8611 0 58.7291 0 47.6258C0 36.5225 5.41229 28.356 17.022 17.9694Z"
                                fill="url(#def-brand-gradient-primary)"
                            />
                            <path
                                fill-rule="evenodd"
                                clip-rule="evenodd"
                                d="M59.3887 42.9848C61.6061 40.562 75.5205 25.4992 93.6305 29.9078C111.741 34.3165 101.269 62.1471 98.1846 67.5749C95.0998 73.0027 85.8109 82.1487 67.3173 77.9128C59.4687 76.115 53.8007 67.4168 54.0907 58.3697C54.2563 53.2051 57.1713 45.4077 59.3887 42.9848Z"
                                fill="url(#def-brand-gradient-primary-variant)"
                            />
                        </g>
                    </svg>
                </div>
            </div>
            <div class="split-stage__item">
                <div class="split-stage__image">
                    <f:if condition="{files}">
                        <f:for each="{files}" as="file">
                            <f:variable name="cropVariants" value="{
                        default:'widescreen',
                        tablet:'default',
                        mobile:'default'
                    }"/>
                            <f:render partial="Media/Media" arguments="{_all}"></f:render>
                        </f:for>
                    </f:if>
                </div>
            </div>
        </div>
    </div>
    <br>
</f:section>
