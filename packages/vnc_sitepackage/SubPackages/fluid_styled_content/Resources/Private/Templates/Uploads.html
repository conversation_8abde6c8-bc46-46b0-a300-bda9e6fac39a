<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">
<f:layout name="Default" />
<f:section name="Main">


        <div class="container">
            <f:render partial="Header" arguments="{_all}" />
            <div class="row">
                <f:if condition="{files}">
                    <ul class="ce-uploads list-unstyled mb-4" role="list">
                        <f:for each="{files}" as="file" iteration="fileIterator">
                            <li class="d-flex align-items-start gap-3 py-3 border-bottom" role="listitem">
                                <f:if condition="{data.uploads_type} == 1">
                                    <img  width="24" height="24" class="mt-1 flex-shrink-0"  src="{f:uri.resource(path: 'EXT:frontend/Resources/Public/Icons/FileIcons/{file.extension}.gif')}" alt="">
                                </f:if>
                                <div class="flex-grow-1">
                                <f:if condition="{data.uploads_type} == 2">
                                    <f:if condition="{file.type} > 1">
                                        <f:if condition="{f:uri.image(src: 'file:{f:if(condition: file.originalFile, then: \'file:{file.originalFile.uid}\', else: \'file:{file.uid}\')}')} != '/'">
                                            <f:then>
                                                <a class="fw-semibold text-decoration-none link-primary d-inline-block mb-1" href="{file.publicUrl}"  target="_blank" {f:if(condition: file.title, then: ' title="{file.title}"')}>
                                                <f:media file="{file}" width="150" alt="{file.properties.alternative}" class="mt-1 flex-shrink-0" />
                                                </a>
                                            </f:then>
                                            <f:else>
                                                <img width="24" height="24" class="mt-1 flex-shrink-0" src="{f:uri.resource(path: 'EXT:frontend/Resources/Public/Icons/FileIcons/{file.extension}.gif')}" alt="">
                                            </f:else>
                                        </f:if>
                                    </f:if>
                                </f:if>
                                    <a class="fw-semibold text-decoration-none link-primary d-inline-block mb-1" href="{file.publicUrl}" target="_blank" {f:if(condition: file.title, then: ' title="{file.title}"')}>
                                    <span class="ce-uploads-fileName">{f:if(condition: file.properties.title, then: file.properties.title, else: file.name) -> f:format.htmlspecialchars()}</span>
                                    </a>
                                    <f:if condition="{file.properties.description}">
                                        <f:if condition="{data.uploads_description}">
                                            <div class="bodytext--s small mb-1"><span class="ce-uploads-description">{file.properties.description}</span></div>
                                        </f:if>
                                    </f:if>
                                    <f:if condition="{data.filelink_size}">
                                        <div class="bodytext--s text-muted small"><span class="ce-uploads-filesize"><f:format.bytes value="{file.properties.size}" /></span></div>
                                    </f:if>
                                </div>
                            </li>
                        </f:for>
                    </ul>
                </f:if>
            </div>
        </div>
 </f:section>
</html>

