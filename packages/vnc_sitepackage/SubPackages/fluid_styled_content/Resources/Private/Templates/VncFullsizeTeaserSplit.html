<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<f:layout name="Default"/>

<f:section name="Main">
    <div class="page-section">
        <div class="split-stage {f:if(condition: '{data.vnc_content_layout} == \'left\'', then: 'split-stage--reverse ')} has-gutter container-xxxl">
            <div class="split-stage__item{f:if(condition:'{data.vnc_content_overlay}', then: ' bg-{data.vnc_content_overlay}')}">
                <div class="aspect-ratio aspect-ratio--1-1">
                    <div class="aspect-ratio__canvas h-100">
                        <div class="split-stage__container">
                            <div class="container teaser p-md-0">
                                <div class="teaser__text">
                                    <f:variable name="linkType" value="button"></f:variable>
                                    <f:render partial="Text" arguments="{_all}"></f:render>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="split-stage__item">
                <div class="split-stage__image h-100">
                    <f:if condition="{files}">
                        <f:for each="{files}" as="file">
                            <f:render partial="Media/Media" arguments="{_all}"></f:render>
                        </f:for>
                    </f:if>
                </div>
            </div>
        </div>
    </div>
    <br>
</f:section>
