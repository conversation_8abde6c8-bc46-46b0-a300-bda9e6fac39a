<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<f:layout name="Default"/>
<f:section name="Main">
    <f:if condition="{data.vnc_content_columns} > {records -> f:count()}">
        <f:then>
            <f:variable name="columns">{records -> f:count()}</f:variable>
        </f:then>
        <f:else>
            <f:variable name="columns">{data.vnc_content_columns}</f:variable>
        </f:else>
    </f:if>
    <f:variable name="carouselOptions" value="{&quot;perPage&quot;:&quot;{columns}&quot;,&quot;breakpoints&quot;:{&quot;1024&quot;:{&quot;perPage&quot;:3},&quot;768&quot;:{&quot;perPage&quot;:2},&quot;600&quot;:{&quot;perPage&quot;:2},&quot;480&quot;:{&quot;perPage&quot;:1}},&quot;gap&quot;: &quot;1.5rem&quot;}"></f:variable>

    <f:variable name="sliderContent">
        <f:for each="{records}" as="record" iteration="recordIterator">
            <div class="carousel__item splide__slide">
                <f:render partial="Teaser" arguments="{record: record, teaserType: 'card', className: 'text-center', linkType: '', headerClass: 'm'}"/>
            </div>
        </f:for>
    </f:variable>

    <div class="container">
        <div class="vnc-texttiles">
            <f:if condition="{data.header}">
                <f:variable name="headerClass">text-center mb-2 h2</f:variable>
                <f:variable name="introClass">col-md-10 col-lg-9 col-xl-8 mx-auto text-center mb-3</f:variable>
                <f:render partial="Header" arguments="{_all}"/>
            </f:if>

            <f:render partial="Carousel" arguments="{_all}"></f:render>
        </div>
    </div>
</f:section>
</html>
