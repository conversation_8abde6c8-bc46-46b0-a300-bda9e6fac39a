<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<f:layout name="Default"/>

<f:section name="Main">
    <section class="page-section">
        <div class="container">
            <div class="row">
                <f:if condition="{data.colPos} == 201 ||  {data.colPos} == 202">
                    <f:then>
                        <f:comment><!-- Container --></f:comment>
                        <f:variable name="offsetClass" value="col-lg-12"/>
                    </f:then>
                    <f:else if="{data.vnc_content_layout} == 1">
                        <f:variable name="offsetClass" value="col-md-10 col-lg-9 col-xl-8 mx-auto"/>
                    </f:else>
                </f:if>
                <div class="col-12 {offsetClass}">
                    <f:alias map="{headerClass: 'h2 mt-5 mb-3 mb-lg-5'}">
                        <f:variable name="introClass">fw-bold</f:variable>
                        <f:variable name="linkType">button</f:variable>
                        <f:render partial="Text" arguments="{_all}"></f:render>
                    </f:alias>
                </div>
            </div>
        </div>
    </section>
</f:section>

</html>




