<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<f:layout name="Default" />

<f:comment><!--
    Gallery
    Caption tbd
--></f:comment>

<f:section name="Main">
    <section class="page-section">

        <div class="container">
            <f:comment><!-- <div class="row">
                <div class="col-12 offset-md-1 col-md-10 offset-lg-2 col-lg-8 text-center mb-4">
                  <f:if condition="{data.header}">
                    <f:variable name="className">headline h4 mt-5 mb-3 mb-lg-5 text-center</f:variable>
                      <f:render partial="Header" arguments="{_all}"/>
                    </f:if>
                    <f:if condition="{data.bodytext}">
                      <div class="bodytext--m text-center mb-5">
                          {data.bodytext-> f:format.raw()}
                      </div>
                  </f:if>
                </div>
            </div>--></f:comment>
            <div class="gallery">
                    <f:if condition="{images}">
                        <f:variable name="imageCount">{images -> f:count()}</f:variable>

                        <div class="row {f:if(condition:'{imageCount}==1', then:'offset-md-1 col-md-10 offset-lg-2 col-lg-8 ')} {f:if(condition:'{imageCount}==2', then:'')}">
                            <f:variable name="sliderContent">
                                <f:for each="{images}" as="file" iteration="imgIterator">
                                    <div class="carousel__item splide__slide">
                                        <f:variable name="record" value="{ data: file.data, files: file.images }"></f:variable>
                                        <f:variable name="teaserType" value="stage"></f:variable>
                                        <f:if condition="{data.vnc_content_enlarge_icon}">
                                            <f:variable name="lightbox" value="true"/>
                                        </f:if>

                                        <f:comment>
                                        <f:if condition="{data.vnc_content_download_icon}">
                                            <f:variable name="download" value="true"/>
                                        </f:if>
                                        </f:comment>

                                        <f:variable name="download" value="false"/>
                                        <f:variable name="headerClass" value="h1"></f:variable>
                                        <f:variable name="linkType" value="button"></f:variable>
                                        <f:render partial="Media/Media" arguments="{_all}"></f:render>
                                    </div>
                                </f:for>
                            </f:variable>

                            <f:if condition="{imageCount} > 1">
                                <f:then>
                                    <f:variable name="carouselType" value="stage"></f:variable>
                                    <f:variable name="carouselOptions" value="{
                                        &quot;perPage&quot;:&quot;2&quot;,
                                        &quot;breakpoints&quot;:{
                                            &quot;480&quot;:{&quot;perPage&quot;:1}
                                        },
                                        &quot;gap&quot;: &quot;1.5rem&quot;,
                                        &quot;arrows&quot;: true
                                        }">
                                    </f:variable>
                                    <f:render partial="Carousel" arguments="{_all}"></f:render>
                                </f:then>
                                <f:else>
                                    {sliderContent}
                                </f:else>
                            </f:if>

                        </div>
                    </f:if>
            </div>
        </div>
    </section>
</f:section>
<f:comment>
<f:section name="image">
    <div class="col-12 col-md">
        <div class="lightbox-container">
            <f:if condition="{imageCount}==1">
                <picture>
                    <f:comment><!-- min 62em / 1984px | default --></f:comment>
                    <source
                        media="(min-width: 62em)"
                        srcset="{f:uri.image(image: image, width: '1984', cropVariant: 'default', treatIdAsReference: '1')}, {f:uri.image(image: image, width: '3968', cropVariant: 'default', treatIdAsReference: '1')}  2x"
                    />

                    <f:comment><!-- min 36em / 1152px | default --></f:comment>
                    <source
                        media="(min-width: 36em)"
                        srcset="{f:uri.image(image: image, width: '1152', cropVariant: 'default', treatIdAsReference: '1')}, {f:uri.image(image: image, width: '2304', cropVariant: 'default', treatIdAsReference: '1')} 2x"
                    />
                    <f:comment><!-- else | default --></f:comment>
                    <source
                        srcset="{f:uri.image(image: image, width: '1152', cropVariant: 'default', treatIdAsReference: '1')}, {f:uri.image(image: image, width: '2304', cropVariant: 'default', treatIdAsReference: '1')} 2x"
                    />
                    <img
                        src="{f:uri.image(image: image, width: '675', cropVariant: 'default', treatIdAsReference: '1')}"
                        class="js-lazyload media--cover"
                        alt="{image.alternative}"
                        title="{image.title}"
                        loading="lazy"
                    />

                </picture>
            </f:if>

            <f:if condition="{imageCount}>1">
                <picture>
                    <f:comment><!-- min 62em / 992px | default --></f:comment>
                    <source
                        media="(min-width: 62em)"
                        srcset="{f:uri.image(image: image, width: '992', cropVariant: 'default', treatIdAsReference: '1')}, {f:uri.image(image: image, width: '1984', cropVariant: 'default', treatIdAsReference: '1')}  2x"
                    />
                    <f:comment><!-- min 36em / 1152px | default --></f:comment>
                    <source
                        media="(min-width: 36em)"
                        srcset="{f:uri.image(image: image, width: '576', cropVariant: 'default', treatIdAsReference: '1')}, {f:uri.image(image: image, width: '1152', cropVariant: 'default', treatIdAsReference: '1')} 2x"
                    />
                    <f:comment><!-- else | default --></f:comment>
                    <source
                        srcset="{f:uri.image(image: image, width: '576', cropVariant: 'default', treatIdAsReference: '1')}, {f:uri.image(image: image, width: '1152', cropVariant: 'default', treatIdAsReference: '1')} 2x"
                    />
                    <img
                        src="{f:uri.image(image: image, width: '675', cropVariant: 'default', treatIdAsReference: '1')}"
                        class="js-lazyload media--cover"
                        alt="{image.alternative}"
                        title="{image.title}"
                        loading="lazy"
                    />
                </picture>

            </f:if>

            <button
                class="button button--zoom"
                data-bs-toggle="modal"
                data-bs-target="#gallery-item-{data.uid}-{index}"
            >
                <i class="base-icon" title="">
                    <svg class="icon" preserveAspectRatio="xMaxYMin">
                        <use
                            xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-magnifier"
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                            x="0"
                            y="0"
                        ></use>
                    </svg>
                </i>
            </button>
            <div class="modal-container">
                <div
                    class="modal fade"
                    id="gallery-item-{data.uid}-{index}"
                    tabindex="-1"
                    aria-labelledby="modal-label"
                    aria-hidden="true"
                    data-fetch-body
                >
                    <div
                        class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-xl"
                    >
                        <div class="modal-content lightbox">
                            <div class="modal-header">
                                <p class="modal-title" id="moda-label"></p>
                                <button
                                    type="button"
                                    class="btn-close"
                                    data-bs-dismiss="modal"
                                    aria-label="Close"
                                ></button>
                            </div>
                            <div class="modal-body">
                                <picture>
                                    <img
                                        src="{f:uri.image(image: image, width: '2400', treatIdAsReference: '1')}"
                                        class="js-lazyload img-fluid"
                                        alt="{image.alternative}"
                                        title = "{image.title}"
                                        loading="lazy">

                                </picture>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</f:section>
</f:comment>

