<html
    xmlns="http://www.w3.org/1999/xhtml"
    lang="en"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
<f:layout name="Default"/>

<f:section name="Main">
    <f:if condition="{data.vnc_content_layout_stone}">
        <f:variable name="clipPath">clip--stone-01</f:variable>
    </f:if>
    <div
        class="container{f:if(condition: '{data.vnc_content_fullwidth}', then: '-fluid')}"
        >
        <f:for
            each="{files}"
            as="file"
            iteration="imgIterator"
            >
            <f:variable name="headerClass" value="h2"/>
            <f:variable name="cropVariant" value="default"/>
            <f:render partial="TextMedia" arguments="{_all}"/>
        </f:for>
    </div>
</f:section>

</html>
