<html
    xmlns="http://www.w3.org/1999/xhtml"
    lang="en"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
>
<f:layout name="Default"/>

<f:section name="Main">

<div class="container">
    <f:variable name="carouselType" value="{f:if(condition: '{data.layout} == 160', then: 'tiles', else: 'thumbnail')}"></f:variable>
    <f:variable name="carouselOptions" value="{&quot;type&quot;:&quot;loop&quot;,&quot;rewind&quot;:false,&quot;drag&quot;:&quot;free&quot;,&quot;focus&quot;:&quot;center&quot;,&quot;perPage&quot;:null,&quot;autoWidth&quot;:true,&quot;height&quot;:&quot;10rem&quot;,&quot;gap&quot;:&quot;10px&quot;,&quot;padding&quot;:0,&quot;autoScroll&quot;:{&quot;speed&quot;:1},&quot;pagination&quot;:false,&quot;arrows&quot;:false}"></f:variable>

    <f:switch expression="{data.layout}">
        <f:case value="160">
            <f:variable name="sliderHeight" value="160"></f:variable>
            <f:variable name="carouselType" value="thumbnail"></f:variable>
        </f:case>
        <f:case value="100">
            <f:variable name="sliderHeight" value="128"></f:variable>
            <f:variable name="carouselType" value="logo"></f:variable>
        </f:case>
        <f:defaultCase>
            <f:variable name="sliderHeight" value="128"></f:variable>
            <f:variable name="carouselType"></f:variable>
        </f:defaultCase>
    </f:switch>
    <f:if condition="{carouselType}">
        <f:then>
            <f:variable name="sliderContent">
                <f:for each="{images}" as="image" iteration="imgIterator">
                    <div class="carousel__item splide__slide">
                        <f:link.typolink
                            parameter="{image.link}"
                            additionalAttributes="{aria-label:'{f:translate(key:\'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:slideshowlink\')}'}"
                            >
                            <f:variable name="sizes" value="{0:'sm'}" />
                            <f:variable name="height" value="sliderHeight" />
                            <f:alias map="{sizes: sizes, height: sliderHeight}">
                                <f:render partial="Media/Type/Image" arguments="{_all}"></f:render>
                            </f:alias>
                        </f:link.typolink>
                    </div>
                </f:for>
            </f:variable>

            <f:render partial="Carousel" arguments="{_all}"></f:render>
        </f:then>

        <f:else>
            <f:if condition="{data.layout} == 0">
                <div class="container">
                    <div class="d-flex flex-wrap justify-content-center gap-md-5">
                        <f:for each="{images}" as="image" iteration="imgIterator">
                            <div class="col-6 col-md-auto text-center mb-3">
                                <f:link.typolink
                                    parameter="{image.link}"
                                    additionalAttributes="{aria-label:'{f:translate(key:\'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:slideshowlink\')}'}"
                                    >
                                    <img
                                        title="{image.title}"
                                        alt="{image.alternative}"
                                        src="{f:uri.image(image: image, height: 128, treatIdAsReference: '1')}"
                                        class="image-datalayout-{data.layout}"
                                    />
                                </f:link.typolink>
                            </div>
                        </f:for>
                    </div>
                </div>
            </f:if>
        </f:else>
    </f:if>
</div>

</f:section>

</html>
