<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<f:layout name="Default"/>

<f:section name="Main">
    <f:if condition="{data.vnc_content_teaser_type} == 'video'">
        <f:then>
            <f:variable name="record" value="{ data: data, files: { 0: videos.0 } }"></f:variable>
        </f:then>
        <f:else>
            <f:variable name="record" value="{ data: data, files: { 0: images.0 } }"></f:variable>
        </f:else>
    </f:if>

    <div class="container-xxxl">
        <f:variable name="teaserType" value="nested"></f:variable>
        <f:variable name="headerClass" value="h1"></f:variable>
        <f:variable name="linkType" value="button"></f:variable>
        <f:render partial="Teaser" arguments="{_all}"></f:render>

        <f:comment>
            <div
                class="stage stage--layered aspect-ratio--1-1 aspect-ratio-lg--{f:if(condition: '{data.vnc_content_teaser_type} == \'video\'', then: '16-9', else: '2-1')}">
                <div class="stage__media stage__media--background">
                    <f:if condition="{data.vnc_content_teaser_type} == 'video'">
                        <f:then>
                            <f:variable name="video" value="{videos.0}"/>
                            <div class="embed ratio ratio-16x9">
                                <f:render partial="TextVideo/Type" arguments="{file: video, data: data, settings: settings}"/>
                            </div>
                        </f:then>
                        <f:else>
                            <f:comment>image</f:comment>
                            <f:variable name="image" value="{images.0}"/>
                            <picture>
                                <f:comment><!-- min 62em / 1984px | default --></f:comment>
                                <source
                                    media="(min-width: 62em)"
                                    srcset="{f:uri.image(image: image, maxWidth: '1984', cropVariant: 'univisium', treatIdAsReference: '1')}, {f:uri.image(image: image, maxWidth: '3968', cropVariant: 'univisium', treatIdAsReference: '1')}  2x"
                                />

                                <f:comment><!-- min 36em / 1152px | default --></f:comment>
                                <source
                                    media="(min-width: 36em)"
                                    srcset="{f:uri.image(image: image, maxWidth: '1152', cropVariant: 'univisium', treatIdAsReference: '1')}, {f:uri.image(image: image, maxWidth: '2304', cropVariant: 'univisium', treatIdAsReference: '1')} 2x"
                                />
                                <f:comment><!-- else | default --></f:comment>
                                <source
                                    srcset="{f:uri.image(image: image, maxWidth: '576', cropVariant: 'univisium', treatIdAsReference: '1')}, {f:uri.image(image: image, maxWidth: '1152', cropVariant: 'univisium', treatIdAsReference: '1')} 2x"
                                />

                                <img
                                    src="{f:uri.image(image: image, maxWidth: '675', cropVariant: 'univisium', treatIdAsReference: '1')}"
                                    class="media--cover"
                                    alt="{image.alternative}"
                                    title="{image.title}"
                                />
                            </picture>
                        </f:else>
                    </f:if>
                    <div class="teaser__stones teaser__stones-introtext">
                        <svg
                            width="104"
                            height="79"
                            viewBox="0 0 104 79"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <g>
                                <path
                                    fill-rule="evenodd"
                                    clip-rule="evenodd"
                                    d="M17.022 17.9694C28.6318 7.58285 35.1065 0 49.8279 0C64.5493 0 76.9372 5.59786 85.172 14.6927C93.4067 23.7875 95.9693 32.0028 86.5343 47.6258C77.0992 63.2488 42.002 72.6658 24.4187 68.7635C6.83535 64.8611 0 58.7291 0 47.6258C0 36.5225 5.41229 28.356 17.022 17.9694Z"
                                    fill="url(#def-brand-gradient-primary)"
                                />
                                <path
                                    fill-rule="evenodd"
                                    clip-rule="evenodd"
                                    d="M59.3887 42.9848C61.6061 40.562 75.5205 25.4992 93.6305 29.9078C111.741 34.3165 101.269 62.1471 98.1846 67.5749C95.0998 73.0027 85.8109 82.1487 67.3173 77.9128C59.4687 76.115 53.8007 67.4168 54.0907 58.3697C54.2563 53.2051 57.1713 45.4077 59.3887 42.9848Z"
                                    fill="url(#def-brand-gradient-primary-variant)"
                                />
                            </g>
                        </svg>
                    </div>
                </div>
                <div class="stage__media stage__media--main">
                    <div class="row">
                        <div class="col-12">
                            <div class="stage__text ms-2 ms-lg-5">
                                <div class="text--skewed">
                                    <h1 class="headline display-5 display-sm-3 display-md-2 display-xl-1">
                                        <f:format.nl2br>{data.header}</f:format.nl2br>
                                    </h1>
                                    <br/>
                                    <p class="headline display-6 display-sm-5 display-md-3 display-xl-2">
                                        <f:format.nl2br>{data.bodytext}</f:format.nl2br>
                                    </p>
                                </div>
                                <f:if condition="{data.vnc_content_link}">
                                    <p class="mt-3 mt-lg-5">
                                        <f:if condition="{data.vnc_content_linktext}">
                                            <f:variable
                                                name="className"
                                                value="button--secondary"
                                            />
                                            <f:render
                                                partial="Button"
                                                arguments="{className: className, link: data.vnc_content_link, label: data.vnc_content_linktext, uid: data.uid}"
                                            />
                                        </f:if>
                                    </p>
                                </f:if>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </f:comment>
    </div>
</f:section>
