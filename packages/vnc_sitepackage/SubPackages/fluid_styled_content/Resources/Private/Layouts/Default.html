<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      data-namespace-typo3-fluid="true"
>
<f:if condition="{data.frame_class} != none">
    <f:then>
        <f:switch expression="{data.space_after_class}">
            <f:case value="small"><f:variable name="space_after_class" value="mb-4 mb-sm-5 mb-lg-6" /></f:case>
            <f:case value="is-no-mb"><f:variable name="space_after_class" value="mb-0" /></f:case>
            <f:defaultCase><f:variable name="space_after_class" value="mb-5 mb-lg-8" /></f:defaultCase>
        </f:switch>
        <section class="{data.CType} page-section {space_after_class}" id="c{data.uid}">
            <f:render section="Main" />
        </section>
    </f:then>
    <f:else>
        <f:render section="Main" />
    </f:else>
</f:if>
