<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      data-namespace-typo3-fluid="true">
<f:if condition="{data.vnc_content_overline}">
    <f:variable name="overline">{data.vnc_content_overline}</f:variable>
</f:if>
<f:variable name="header">{data.header}</f:variable>
<f:variable name="headerLayout">{data.header_layout}</f:variable>
<f:comment><f:variable name="subHeader">{data.subheader}</f:variable></f:comment>
<f:variable name="introText">{data.vnc_content_introtext}</f:variable>
<f:if condition="!{iconShowed}">
<f:variable name="icon">{data.icon}</f:variable>
</f:if>
<f:if condition="!{headerClass}">
        <f:variable name="headerClass">{data.vnc_content_headlinesize}</f:variable>
</f:if>

<f:switch expression="{headerClass}">
    <f:case value="xxl">
        <f:variable name="headerClass">h1</f:variable>
    </f:case>
    <f:case value="xl">
        <f:variable name="headerClass">h2</f:variable>
    </f:case>
    <f:case value="l">
        <f:variable name="headerClass">h3</f:variable>
    </f:case>
    <f:case value="m">
        <f:variable name="headerClass">h4</f:variable>
    </f:case>
    <f:case value="s">
        <f:variable name="headerClass">h5</f:variable>
    </f:case>
    <f:case value="xs">
        <f:variable name="headerClass">h6</f:variable>
    </f:case>
    <f:defaultCase>
        <f:variable name="headerClass">{headerClass}</f:variable>
    </f:defaultCase>
</f:switch>

<f:if condition="{header}">
    <f:switch expression="{headerLayout}">
        <f:case value="1">
            <h1 class="headline {headerClass} {className}">
                <f:if condition="{icon}">
                    <f:then> <i class="svg-icon {icon}"></i></f:then>
                </f:if>
                {header -> f:format.raw()}
            </h1>
        </f:case>
        <f:case value="2">
            <h2 class="headline {headerClass} {className}">
                <f:if condition="{icon}">
                    <f:then> <i class="svg-icon {icon}"></i></f:then>
                </f:if>
                {header -> f:format.raw()}
            </h2>
        </f:case>
        <f:case value="3">
            <h3 class="headline {headerClass} {className}">
                <f:if condition="{icon}">
                    <f:then> <i class="svg-icon {icon}"></i></f:then>
                </f:if>
                {header -> f:format.raw()}
            </h3>
        </f:case>
        <f:case value="4">
            <h4 class="headline {headerClass} {className}">
                <f:if condition="{icon}">
                    <f:then> <i class="svg-icon {icon}"></i></f:then>
                </f:if>
                {header -> f:format.raw()}
            </h4>
        </f:case>
        <f:case value="5">
            <h5 class="headline {headerClass} {className}">
                <f:if condition="{icon}">
                    <f:then> <i class="svg-icon {icon}"></i></f:then>
                </f:if>
                {header -> f:format.raw()}
            </h5>
        </f:case>
        <f:case value="6">
            <h6 class="headline {headerClass} {className}">
                <f:if condition="{icon}">
                    <f:then> <i class="svg-icon {icon}"></i></f:then>
                </f:if>
                {header -> f:format.raw()}
            </h6>
        </f:case>
        <f:case value="100"></f:case>
        <f:defaultCase>
            <f:comment><!-- H2 as default Header --></f:comment>
            <h2 class="headline {headerClass}">
                <f:if condition="{icon}">
                    <f:then> <i class="svg-icon {icon}"></i></f:then>
                </f:if>
                {header -> f:format.raw()}
            </h2>
        </f:defaultCase>
    </f:switch>
</f:if>

<f:comment><!-- Add a subheader if any --></f:comment>
<f:if condition="{subHeader}">
    <div class="text-body">
        <p class="headline">{subHeader -> f:format.raw()}</p>
    </div>
</f:if>

<f:comment><!-- Add introtext if any --></f:comment>
<f:if condition="{introText}">
    <div class="text-body {introClass}">
        <p>{introText -> f:format.raw()}</p>
    </div>
</f:if>
