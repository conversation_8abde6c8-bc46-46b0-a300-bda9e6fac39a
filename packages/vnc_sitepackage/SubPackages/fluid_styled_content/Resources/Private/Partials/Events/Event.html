<div class="teaser teaser--card " data-teaser="">
    <div class="teaser__canvas">
        <div class="teaser__body">
            <div class="teaser__text">
                <article>
                    <div class="headline h3">
                        {event.title}
                    </div>
                    <p class="overline">
                        <f:if condition="{event.categoryIcon}">
                            <i class="svg-icon {event.categoryIcon}"></i>
                        </f:if>
                        {event.category}
                    </p>
                    <div>
                        <i class="svg-icon icon nc-calendar-date"></i>
                        <f:format.date format="d.m.Y">{event.eventStartdate}</f:format.date>
                        <f:if condition="{event.eventEnddate}">
                            - <f:format.date format="d.m.Y">{event.eventEnddate}</f:format.date>
                        </f:if>
                    </div>
                    <f:if condition="{event.eventStarttime} || {event.eventEndtime}">
                        <div>
                            <i class="svg-icon icon nc-clock"></i>
                            <f:format.date format="H:i">{event.eventStarttime}</f:format.date>
                            <f:if condition="{event.eventEndtime}">
                                - <f:format.date format="H:i">{event.eventEndtime}</f:format.date>
                            </f:if>
                        </div>
                    </f:if>
                    <f:if condition="{event.location}">
                        <div>
                            <i class="svg-icon icon nc-pin-3"></i>
                            {event.location}
                        </div>
                    </f:if>
                    <f:if condition="{event.price}">
                        <div>
                            <i class="svg-icon icon nc-round-euro"></i>
                            {event.price}
                        </div>
                    </f:if>
                    <br>
                    <p>
                        <f:if condition="{event.link}">
                            <f:then>
                                <f:link.typolink parameter="{event.link}"
                                                 class="button button--secondary"
                                                 additionalAttributes="{'data-teaser-cta': 'true'}"
                                >
                                    <f:if condition="{event.linkText}">
                                        <f:then>
                                            {event.linkText}
                                        </f:then>
                                        <f:else>
                                            <f:translate key="LLL:EXT:vnc_events/ContentBlocks/ContentElements/events/Source/Language/Frontend.xlf:show_details" />
                                        </f:else>
                                    </f:if>
                                </f:link.typolink>
                            </f:then>
                            <f:else>
                                <f:if condition="{data.singlePid.0}">
                                    <f:link.typolink parameter="{data.singlePid.0.uid}"
                                                     class="button button--secondary"
                                                     additionalAttributes="{'data-teaser-cta': 'true'}"
                                                     additionalParams="event={event.uid}"
                                    >
                                        <f:translate key="LLL:EXT:vnc_events/ContentBlocks/ContentElements/events/Source/Language/Frontend.xlf:show_details" />
                                    </f:link.typolink>
                                </f:if>
                            </f:else>
                        </f:if>
                    </p>
                </article>
            </div>
        </div>
    </div>
</div>
