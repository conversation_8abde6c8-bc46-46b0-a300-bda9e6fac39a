<html
    lang="en"
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true">


<f:if condition="{data.link}">
    <f:then><f:variable name="href"> href="{data.link}"</f:variable></f:then>
</f:if>

<div class="cite">
    <f:variable name="file" value="{image.0}"/>
    <f:variable name="files" value="{data.images.0}"/>
    <f:if condition="{file}">
        <f:variable name="cropVariant">square</f:variable>
        <f:variable name="sizes" value="{0:'sm'}" />
        <f:variable name="className">cite__visual rounded-circle mx-auto bodytext--l</f:variable>
            <f:render partial="Media/Media" arguments="{_all}"></f:render>
    </f:if>
    <blockquote cite="{data.header}">{data.bodytext -> f:format.nl2br()}</blockquote>
    <address class="author">
        <a class="{f:if(condition:'{href}', then: '{link}')} bodytext--xs" rel="author" {href}>{data.header}</a>
    </address>
</div>
