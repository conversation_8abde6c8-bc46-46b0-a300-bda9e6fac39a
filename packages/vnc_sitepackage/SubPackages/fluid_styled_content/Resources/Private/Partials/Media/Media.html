<html
    xmlns="http://www.w3.org/1999/xhtml"
    lang="en"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:variable
        name="mediaType"
        value="{f:if(condition: '{file.originalFile.properties.type}', then: file.originalFile.properties.type, else: file.type)}"
    ></f:variable>
    <f:variable
        name="actions"
        value="{f:if(condition:'{lightbox} || {download}', then: 'true')}"
    ></f:variable>
    <div class="media-container">
        <f:switch expression="{mediaType}">
            <f:case value="3">
                <f:render
                    partial="Media/Type/Audio"
                    arguments="{file: file, dimensions: dimensions, data: data, settings: settings}"
                />
            </f:case>
            <f:case value="4">
                <f:comment><!-- Youtube --></f:comment>
                <div class="video">
                    <div class="embed embed-responsive embed-responsive-16by9">
                        <f:render
                            partial="Media/Type/Video"
                            arguments="{file: file, data: data, settings: settings}"
                        />
                    </div>
                    <f:comment>
                        <f:if condition="{file.properties.description}">
                            <figcaption>
                                <f:format.nl2br
                                    >{file.properties.description}</f:format.nl2br
                                >
                            </figcaption>
                        </f:if>
                    </f:comment>
                </div>
            </f:case>
            <f:defaultCase>
                <f:comment><!--global --></f:comment>
                <f:render
                    partial="Media/Type/Image"
                    arguments="{image: file, data: data, settings: settings, sizes:sizes, orientation:orientation, cropVariant: cropVariant, cropVariants: cropVariants, className: className, clipPath: clipPath}"
                />
                <f:comment>
                    <f:if condition="{file.properties.description}">
                        <figcaption>
                            <f:format.nl2br
                                >{file.properties.description}</f:format.nl2br
                            >
                        </figcaption>
                    </f:if>
                </f:comment>
            </f:defaultCase>
        </f:switch>

        <f:if condition="{actions}">
            <f:then>
                <f:if condition="{lightbox}">
                    <f:then>
                        <div
                            class="media-container__actions d-flex flex-column gap-2"
                        >
                            <button
                                class="button bg-gradient-secondary p-3"
                                data-modal-toggle="modal-dialog-{file.uid}"
                                aria-controls="modal-dialog-{file.uid}"
                                aria-label="{f:translate(
                                key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:enlarge_with_filename',
                                arguments: {filename: file.name}
                            )}"
                            >
                                <i class="svg-icon" title="">
                                    <svg
                                        class="icon"
                                        preserveAspectRatio="xMaxYMin"
                                    >
                                        <use
                                            xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-plus"
                                            xmlns:xlink="http://www.w3.org/1999/xlink"
                                            x="0"
                                            y="0"
                                        ></use>
                                    </svg>
                                </i>
                            </button>

                            <f:variable name="modalContent">
                                <f:switch expression="{mediaType}">
                                    <f:case value="3">
                                        <f:render
                                            partial="Media/Type/Audio"
                                            arguments="{file: file, dimensions: dimensions, data: data, settings: settings}"
                                        />
                                    </f:case>
                                    <f:case value="4">
                                        <f:comment><!-- Youtube --></f:comment>
                                        <div class="video">
                                            <div
                                                class="embed embed-responsive embed-responsive-16by9"
                                            >
                                                <f:render
                                                    partial="Media/Type/Video"
                                                    arguments="{file: file, data: data, settings: settings}"
                                                />
                                            </div>
                                        </div>
                                    </f:case>
                                    <f:defaultCase>
                                        <f:comment><!--global --></f:comment>
                                        <f:render
                                            partial="Media/Type/Image"
                                            arguments="{image: file, data: data, settings: settings, orientation:orientation}"
                                        />
                                    </f:defaultCase>
                                </f:switch>
                            </f:variable>

                            <f:render
                                partial="ModalDialog"
                                arguments="{
                            uid: '{file.uid}',
                            size: 'lg',
                            content: modalContent
                        }"
                            />

                            <f:if condition="{download}">
                                <f:then>
                                    <a
                                        class="button bg-gradient-secondary p-3"
                                        href="{f:uri.image(image: file, treatIdAsReference: '1')}"
                                        download="{f:if(condition: '{file.title}', then: '{file.title}', else: 'download')}"
                                        aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:download')}"
                                    >
                                        <i class="svg-icon" title="">
                                            <svg
                                                class="icon"
                                                preserveAspectRatio="xMaxYMin"
                                            >
                                                <use
                                                    xlink:href="{f:uri.resource(path:'{settings.svgSpriteFile}', extensionName: 'vnc_sitepackage')}#icon-download"
                                                    xmlns:xlink="http://www.w3.org/1999/xlink"
                                                    x="0"
                                                    y="0"
                                                ></use>
                                            </svg>
                                        </i>
                                    </a>
                                </f:then>
                            </f:if>
                        </div>
                    </f:then>
                </f:if>
            </f:then>
        </f:if>
    </div>
</html>
