<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">

<f:variable name="decorative">{decorative}</f:variable>
<f:variable name="playOnHover" value="false"></f:variable>

<f:if condition="{file.extension} == 'youtube'">
    <f:then>
        <div class="ratio ratio-16x9" data-social-media-widget="youtube" data-overlay-text=""
             data-overlay-button-caption="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:accept_youtube')}">
            <script type="text/plain" class="optanon-category-C0003">
                    SocialMediaWidgets.allow();
            </script>
            <script async defer crossorigin="anonymous" type="text/plain" data-social-media-script="youtube">
                    var id = 'youtube-{file.uid}';
                    SocialMediaWidgets.allowIframe(id);
            </script>
            <f:media class="embed-responsive-item optanon-category-C0003" file="{file}"
                     alt="{file.properties.alternative}"
                     title="EMERGY Video: {file.properties.title}" id="youtube-{file.uid}"/>
        </div>
    </f:then>
    <f:else>
        <f:media
            controls="false"
            file="{file}"
            alt="{file.properties.alternative}"
            title="{file.properties.title}"
            additionalAttributes="{data-video-player: true, autoplay: 'autoplay', muted: 'muted'}"
        />
    </f:else>
</f:if>

