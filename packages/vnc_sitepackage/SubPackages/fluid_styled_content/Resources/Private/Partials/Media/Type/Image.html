<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">
<f:if condition="{image}">


    <f:variable name="decorative">{decorative}</f:variable>
    <f:if condition="{decorative}">
        <f:then>
            <f:variable name="roleAttr">
                role='presentation'
            </f:variable>
            <f:variable name="image.alternative" value=""></f:variable>
        </f:then>
    </f:if>

    <f:if condition="!{cropVariant}">
        <f:then>
            <f:if condition="{data.vnc_content_img_layout}">
                <f:then>
                    <f:variable name="cropVariant">{data.vnc_content_img_layout}</f:variable>
                </f:then>
                <f:else>
                    <f:variable name="cropVariant" value="default"></f:variable>
                </f:else>
            </f:if>
        </f:then>
    </f:if>

    <f:if condition="!{cropVariants}">

        <f:variable name="cropVariants" value="{
        default:cropVariant,
        tablet:cropVariant,
        mobile:cropVariant
        }"/>
    </f:if>

    <f:if condition="{sizes}">
        <f:then>
            <f:variable name="sizes" value="{sizes}"/>
        </f:then>
        <f:else>
            <f:variable name="sizes" value="{0:'lg', 1:'md', 2:'sm'}"/>
        </f:else>
    </f:if>

    <f:if condition="!{breakpoints}">
        <f:then>
            <f:variable name="breakpoints" value="{
            xxl:{media:'min-width', size:1400, maxWidth:2000, cropVariant: cropVariants.default },
            xl:{media:'min-width', size:992, maxWidth:1400, cropVariant: cropVariants.default },
            lg:{media:'min-width', size:768, maxWidth:992, cropVariant: cropVariants.tablet },
            md:{media:'min-width', size:576, maxWidth:768, cropVariant: cropVariants.tablet },
            sm:{media:'min-width', size:375, maxWidth:576, cropVariant: cropVariants.mobile },
            xs:{media:'min-width', size:10, maxWidth:375, cropVariant: cropVariants.mobile }
        }"/>
        </f:then>
    </f:if>

    <f:for each="{breakpoints}" as="breakpoint" key="breakpointKey" iteration="breakpointIteration">
        <f:if condition="{sizes.0} == {breakpointKey}">
            <f:variable name="defaultSize" value="{breakpoint}"/>
        </f:if>
    </f:for>


    <picture>
        <f:for each="{breakpoints}" as="breakpoint" key="breakpointKey" iteration="breakpointIteration">
            <f:for each="{sizes}" as="size" iteration="sizeIteration">
                <f:if condition="{breakpointKey} == {size}">
                    <f:then>
                        <source
                            media="({breakpoint.media}: {breakpoint.size}px)"
                            srcset="{f:uri.image(image: image, maxWidth: '{breakpoint.maxWidth}', height: height, cropVariant: '{breakpoint.cropVariant}', treatIdAsReference: '1')}, {f:uri.image(image: image, maxWidth: '{breakpoint.maxWidth * 2}', cropVariant: '{breakpoint.cropVariant}', treatIdAsReference: '1')} 2x"
                        />
                    </f:then>
                </f:if>
            </f:for>
        </f:for>
        <f:if condition="{image.title}">
            <f:then>
                <f:variable name="image_title" value="{image.title}"/>
            </f:then>
            <f:else if="{image.originalResource.title}">
                <f:variable name="image_title" value="{image.originalResource.title}"/>
            </f:else>
        </f:if>
        <f:if condition="{image.alternative}">
            <f:then>
                <f:variable name="image_alternative" value="{image.alternative}"/>
            </f:then>
            <f:else if="{image.originalResource.alternative}">
                <f:variable name="image_alternative" value="{image.originalResource.alternative}"/>
            </f:else>
        </f:if>

        <f:if condition="{clipPath}">
            <f:then>
                <img
                    src="{f:uri.image(image: image, maxWidth: '{defaultSize.maxWidth}', height: height, cropVariant: '{defaultSize.cropVariant}', treatIdAsReference: '1')}"
                    class="{className}"
                    title="{image_title}"
                    alt="{f:if(condition: '{decorative}', then: '', else: image_alternative)}"
                    {roleAttr}
                    style="clip-path: url(#{clipPath})"
                />
                <svg width="200" height="150" viewBox="0 0 200 150" class="media-container__overlay" version="1.1">
                    <g>
                        <path d="M145.265,99.426 C122.939,119.41 110.488,134 82.177,134 C53.867,134 30.044,123.229 14.208,105.731 C-1.628,88.232 -6.556,72.425 11.588,42.366 C29.732,12.306 97.227,-5.813 131.041,1.696 C164.855,9.204 178,21.002 178,42.366 C178,63.729 167.592,79.442 145.265,99.426 z" fill="url(#def-brand-gradient-primary)"/>
                        <path d="M189.897,123.572 C185.678,128.185 159.198,156.865 124.734,148.47 C90.269,140.076 110.197,87.087 116.067,76.752 C121.938,66.417 139.615,49.003 174.809,57.069 C189.745,60.491 200.532,77.053 199.98,94.279 C199.665,104.112 194.117,118.958 189.897,123.572 z" fill="url(#def-brand-gradient-primary-variant)"/>
                    </g>
                </svg>
            </f:then>
            <f:else>
                <img
                    src="{f:uri.image(image: image, maxWidth: '{defaultSize.maxWidth}', height: height, cropVariant: '{defaultSize.cropVariant}', treatIdAsReference: '1')}"
                    class="{className}"
                    title="{image_title}"
                    alt="{f:if(condition: '{decorative}', then: '', else: image_alternative)}"
                    {roleAttr}
                />
            </f:else>
        </f:if>
        <f:if condition="{data.vnc_content_layout_stone}">
            <svg width="200" height="150" viewBox="0 0 200 150" class="media-container__overlay" version="1.1">
                <g>
                    <path d="M145.265,99.426 C122.939,119.41 110.488,134 82.177,134 C53.867,134 30.044,123.229 14.208,105.731 C-1.628,88.232 -6.556,72.425 11.588,42.366 C29.732,12.306 97.227,-5.813 131.041,1.696 C164.855,9.204 178,21.002 178,42.366 C178,63.729 167.592,79.442 145.265,99.426 z" fill="url(#def-brand-gradient-primary)"></path>
                    <path d="M189.897,123.572 C185.678,128.185 159.198,156.865 124.734,148.47 C90.269,140.076 110.197,87.087 116.067,76.752 C121.938,66.417 139.615,49.003 174.809,57.069 C189.745,60.491 200.532,77.053 199.98,94.279 C199.665,104.112 194.117,118.958 189.897,123.572 z" fill="url(#def-brand-gradient-primary-variant)"></path>
                </g>
            </svg>
        </f:if>
    </picture>

    <f:comment>
        <!--
        <f:if condition="{data.vnc_content_download_icon} || {data.vnc_content_enlarge_icon}">
            <div class="teaser__actions">
                <f:if condition="{data.vnc_content_enlarge_icon}">
                    <a data-action-zoom href="#">
                        <i class="icon nc-zoom"></i>
                    </a>
                </f:if>
                <f:if condition="{data.vnc_content_download_icon}">
                    <a href="{f:uri.image(image: image, treatIdAsReference: '1')}" download="{f:if(condition: '{image.title}', then: '{image.title}', else: 'download-{imageCount}')}">
                        <i class="icon nc-download"></i>
                    </a>
                </f:if>
            </div>
        </f:if>
        -->
    </f:comment>
</f:if>
</html>
