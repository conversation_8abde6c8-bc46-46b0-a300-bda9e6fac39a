<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">
<f:variable name="teaserType">{teaserType}</f:variable>
<f:variable name="linkType">{linkType}</f:variable>

<f:if condition="{teaserType} == stage || {teaserType} == nested">
    <f:then>
        <f:variable name="_teaserType">stage</f:variable>
    </f:then>
    <f:else>
        <f:variable name="_teaserType">{teaserType}</f:variable>
    </f:else>
</f:if>

<f:switch expression="{_teaserType}">
    <f:case value="stage">
        <f:variable name="headerClass">h2</f:variable>
        <f:if condition="!{breakpoints}">
            <f:then>
                <f:variable name="breakpoints" value="{
                    lg:{media:'min-width', size:992, maxWidth:1984, cropVariant: cropVariants.default },
                    md:{media:'min-width', size:576, maxWidth:1024, cropVariant: cropVariants.tablet },
                    sm:{media:'min-width', size:375, maxWidth:576, cropVariant: cropVariants.mobile }
                }" />
            </f:then>
        </f:if>
    </f:case>
    <f:case value="card">
        <f:variable name="headerClass">h4</f:variable>
    </f:case>
    <f:defaultCase>
        <f:variable name="headerClass">h3</f:variable>
    </f:defaultCase>
</f:switch>
<div class="teaser teaser--{teaserType} {className}{f:if(condition:'{data.vnc_content_overlay}', then: ' has-overlay has-overlay--{data.vnc_content_overlay}')}"
    data-teaser>
    <div class="teaser__canvas">
        <div class="teaser__body">
            <div class="teaser__text">
                <f:if condition="{teaserContent}">
                    <f:then>
                        <f:format.raw>{teaserContent}</f:format.raw>
                    </f:then>
                    <f:else>
                        <f:render partial="Text"
                            arguments="{data: record.data, linkType: linkType, headerClass: headerClass,headerLayout: headerLyout, teaserLink: 'true' }">
                        </f:render>
                    </f:else>
                </f:if>

                <f:if condition="{teaserType} == 'nested'">
                    <f:then>
                        <div class="teaser__stones teaser__stones-introtext">
                            <svg width="104" height="79" viewBox="0 0 104 79" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <g>
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M17.022 17.9694C28.6318 7.58285 35.1065 0 49.8279 0C64.5493 0 76.9372 5.59786 85.172 14.6927C93.4067 23.7875 95.9693 32.0028 86.5343 47.6258C77.0992 63.2488 42.002 72.6658 24.4187 68.7635C6.83535 64.8611 0 58.7291 0 47.6258C0 36.5225 5.41229 28.356 17.022 17.9694Z"
                                        fill="url(#def-brand-gradient-primary)" />
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M59.3887 42.9848C61.6061 40.562 75.5205 25.4992 93.6305 29.9078C111.741 34.3165 101.269 62.1471 98.1846 67.5749C95.0998 73.0027 85.8109 82.1487 67.3173 77.9128C59.4687 76.115 53.8007 67.4168 54.0907 58.3697C54.2563 53.2051 57.1713 45.4077 59.3887 42.9848Z"
                                        fill="url(#def-brand-gradient-primary-variant)" />
                                </g>
                            </svg>
                        </div>
                    </f:then>
                </f:if>
            </div>
        </div>
        <div class="teaser__visual">
            <f:for each="{record.files}" as="file" iteration="imageIteration">
                <f:variable name="className"></f:variable>
                <f:render partial="Media/Media" arguments="{_all}"></f:render>
                <f:comment>
                    <f:if condition="{data.vnc_content_columns}==1">
                        <picture>
                            <f:comment><!-- min 62em / 1984px | default --></f:comment>
                            <source media="(min-width: 62em)"
                                srcset="{f:uri.image(image: image, maxWidth: '1984', cropVariant: 'default', treatIdAsReference: '1')}, {f:uri.image(image: image, maxWidth: '3968', cropVariant: 'default', treatIdAsReference: '1')}  2x" />
                            <f:comment><!-- min 36em / 1152px | default --></f:comment>
                            <source media="(min-width: 36em)"
                                srcset="{f:uri.image(image: image, maxWidth: '1152', cropVariant: 'default', treatIdAsReference: '1')}, {f:uri.image(image: image, maxWidth: '2304', cropVariant: 'default', treatIdAsReference: '1')} 2x" />
                            <f:comment><!-- else | default --></f:comment>
                            <source
                                srcset="{f:uri.image(image: image, maxWidth: '1152', cropVariant: 'default', treatIdAsReference: '1')}, {f:uri.image(image: image, maxWidth: '2304', cropVariant: 'default', treatIdAsReference: '1')} 2x" />
                            <img src="{f:uri.image(image: image, maxWidth: '675', cropVariant: 'default', treatIdAsReference: '1')}"
                                class="img-fluid js-lazyload" alt="{image.alternative}" title="{image.title}" />
                        </picture>
                    </f:if>
                    <f:if condition="{data.vnc_content_columns} >= 2">
                        <picture>
                            <f:comment><!-- min 62em / 992px | default --></f:comment>
                            <source media="(min-width: 62em)"
                                srcset="{f:uri.image(image: image, maxWidth: '992', cropVariant: 'default', treatIdAsReference: '1')}, {f:uri.image(image: image, maxWidth: '1984', cropVariant: 'default', treatIdAsReference: '1')}  2x" />

                            <f:comment><!-- min 36em / 1152px | default --></f:comment>
                            <source media="(min-width: 36em)"
                                srcset="{f:uri.image(image: image, maxWidth: '576', cropVariant: 'default', treatIdAsReference: '1')}, {f:uri.image(image: image, maxWidth: '1152', cropVariant: 'default', treatIdAsReference: '1')} 2x" />
                            <f:comment><!-- else | default --></f:comment>
                            <source
                                srcset="{f:uri.image(image: image, maxWidth: '576', cropVariant: 'default', treatIdAsReference: '1')}, {f:uri.image(image: image, maxWidth: '1152', cropVariant: 'default', treatIdAsReference: '1')} 2x" />
                            <img src="{f:uri.image(image: image, maxWidth: '675', cropVariant: 'default', treatIdAsReference: '1')}"
                                class="img-fluid js-lazyload" alt="{image.alternative}" title="{image.title}" />
                        </picture>
                    </f:if>
                </f:comment>
            </f:for>
        </div>
    </div>
</div>

</html>
