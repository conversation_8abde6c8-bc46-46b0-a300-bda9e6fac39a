<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<f:variable name="offset">{offset}</f:variable>
<div class="text-media">
    <div class="row gap-5 gap-lg-0 {f:if(condition: '{data.vnc_content_layout} == \'right\'', then: 'flex-lg-row-reverse')}">
        <div class="col-12 col-lg-{f:if(condition: '{offset}', then: '7 offset-lg-1', else: '6')} d-flex flex-column justify-content-center order-0 order-md-0">
            <f:if condition="{data.vnc_content_enlarge_icon}">
                <f:variable name="lightbox" value="true"/>
            </f:if>
            <f:comment>
                <!-- <f:if condition="{data.vnc_content_download_icon}">
                <f:variable name="download" value="{data.vnc_content_download_icon}"/>
            </f:if> -->
            </f:comment>
            <f:render partial="Media/Media" arguments="{_all}"/>
        </div>
        <div class="col-12 col-lg-{f:if(condition: '{offset}', then: '4', else: '6')} d-flex flex-column justify-content-center mb-lg-0 order-1 order-md-0">
            <f:variable name="headerClass" value="h2"/>
            <f:variable name="linkType" value="button"/>
            <f:render partial="Text" arguments="{_all}"/>
        </div>
    </div>
</div>
