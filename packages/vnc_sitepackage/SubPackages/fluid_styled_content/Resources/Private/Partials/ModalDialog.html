<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      data-namespace-typo3-fluid="true">

<f:variable name="uid">{uid}</f:variable>
<f:variable name="size">{size}</f:variable>
<f:variable name="openAttr">
    <f:if condition="{open}">
        <f:then>
            data-modal-open
        </f:then>
    </f:if>
</f:variable>
<f:variable name="dismissAttr">
    <f:if condition="{dismiss}">
        <f:then>
            data-modal-dismiss
        </f:then>
    </f:if>
</f:variable>
<f:variable name="header">{header}</f:variable>
<f:variable name="footer">{footer}</f:variable>

<f:variable name="footerCloseButton">{footerCloseButton}</f:variable>

<dialog
    id="modal-dialog-{uid}"
    data-modal-dialog="modal-dialog-{uid}"
    class="modal-dialog {f:if(condition: '{! size}', then: 'modal-dialog--{size}')} {className}"
    {openAttr}
    {dismissAttr}

    {f:if(condition: header, then: ' aria-labelledby=modal-dialog-{uid}__title', else: '')}
    >
    <div class="modal-dialog__content">
        <div class="modal-dialog__header d-flex gap-3 align-items-center">
            <div class="modal-dialog__title" id="modal-dialog-{uid}__title">{header -> f:format.raw()}</div>
            <button
                type="button"
                class="btn-close ms-auto js-alert-close"
                aria-label="{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/aria-label.xlf:close')}"
                data-modal-close
            ></button>
        </div>
        <div class="modal-dialog__body">
            {content -> f:format.raw()}
        </div>

        <f:if condition="{footerCloseButton} != 'false'">
            <f:then>
                <div class="modal-dialog__footer d-flex gap-2">
                    <button
                        type="button"
                        class="button button--secondary ms-auto"
                        data-modal-close
                        autofocus
                    >{f:translate(key: 'LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xlf:close')}</button>
                </div>
            </f:then>
        </f:if>
    </div>
</dialog>
