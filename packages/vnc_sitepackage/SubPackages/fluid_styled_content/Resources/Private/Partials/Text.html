<html
    xmlns="http://www.w3.org/1999/xhtml"
    lang="en"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:variable
        name="link"
        value="{f:if(condition: '{data.vnc_content_link}', then: data.vnc_content_link, else: data.link)}"
    ></f:variable>
    <f:variable
        name="linktext"
        value="{f:if(condition: '{data.vnc_content_linktext}', then: data.vnc_content_linktext, else: data.linktext)}"
    ></f:variable>
    <f:variable
        name="linkClassName"
        value="{f:if(condition: '{linkType} == \'button\'', then: 'button button--secondary', else: 'link link--styled')}"
    ></f:variable>

    <f:variable
        name="overline"
        value="{f:if(condition: '{data.vnc_content_overline}', then: data.vnc_content_overline, else: data.overline)}"
    ></f:variable>
    <f:variable
        name="overlineIcon"
        value="{f:if(condition: '{data.vnc_content_overlineIcon}', then: data.vnc_content_overlineIcon, else: data.overlineicon)}"
    ></f:variable>
    <article>
        {record.data.header}
        <f:if condition="{overline}">
            <f:variable name="overlineClass">overline</f:variable>
            <p class="{overlineClass}">
                <f:if condition="{overlineIcon}">
                    <f:then> <i class="svg-icon {overlineIcon}"></i></f:then>
                </f:if>
                {overline -> f:format.raw()}
            </p>
        </f:if>
        <f:if condition="{data.header}">
            <f:if condition="{data.icon}">
                <f:then>
                    <i class="svg-icon {data.icon}"></i>
                    <f:variable name="iconShowed">true</f:variable>
                </f:then>
            </f:if>

            <f:variable name="className">mb-3 mb-lg-4</f:variable>
            <f:if condition="{data.vnc_content_layout_stone}">
                <f:then>
                    <f:variable name="className"
                        >{className} mt-4 mt-md-6</f:variable
                    >
                </f:then>
                <f:else>
                    <f:variable name="className"
                        >{className} mt-2 mt-md-3</f:variable
                    >
                </f:else>
            </f:if>
            <f:variable name="className"
                >{className} mb-3 mb-lg-4 mt-lg-0</f:variable
            >
        <f:if condition="!{headerClass}">
            <f:variable name="headerClass">xl</f:variable>
        </f:if>
            <f:render partial="Header" arguments="{_all}" />
        </f:if>
        <f:if condition="{data.bodytext} || {link}">
            <f:if
                condition="{data.vnc_content_layout}  == 2 && {data.vnc_content_bodytextsecondcol}"
            >
                <f:then>
                    <div class="row">
                        <div class="col-lg-6">
                            <f:format.html>{data.bodytext}</f:format.html>
                        </div>
                        <div class="col-lg-6">
                            <f:format.html
                                >{data.vnc_content_bodytextsecondcol}</f:format.html
                            >
                        </div>
                    </div>
                </f:then>
                <f:else>
                    <div>
                        <f:format.html>{data.bodytext}</f:format.html>
                    </div>
                </f:else>
            </f:if>
            <f:if condition="{link}">
                <p>
                    <f:link.typolink
                        class="{linkClassName}"
                        parameter="{link}"
                        additionalAttributes="{data-teaser-cta: '{teaserLink}', tabIndex: -1}"
                    >
                        <f:if condition="{linktext}"
                            >{linktext -> f:format.raw()}</f:if
                        >
                    </f:link.typolink>
                </p>
            </f:if>
        </f:if>
    </article>
</html>
