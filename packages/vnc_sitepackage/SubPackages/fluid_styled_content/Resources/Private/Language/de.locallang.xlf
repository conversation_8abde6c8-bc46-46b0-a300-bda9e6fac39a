<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
	<file source-language="en" target-language="de" original="" datatype="plaintext" date="2025-02-14T13:48:15+01:00">
		<body>
			<trans-unit id="content.element.accordion.layout.1">
				<source>Without image</source>
				<target>Ohne Bild</target>
			</trans-unit>
			<trans-unit id="content.element.accordion.layout.2">
				<source>With image square left</source>
				<target>Mit Bild quadratisch links</target>
			</trans-unit>
			<trans-unit id="content.element.accordion.layout.3">
				<source>With image portrait left</source>
				<target>Mit Bild hochformat links</target>
			</trans-unit>
			<trans-unit id="content.element.bodytext">
				<source>Text</source>
				<target>Text</target>
			</trans-unit>
			<trans-unit id="content.element.bodytextsecondcol">
				<source>Text right column</source>
				<target>Text rechte Spalte</target>
			</trans-unit>
			<trans-unit id="content.element.bodytextsecondcol.description">
				<source>Only visible in two-columns view</source>
				<target>Wird nur in der Ansicht &#34;2-spaltiger Text mit Überschrift&#34; angezeigt</target>
			</trans-unit>
			<trans-unit id="content.element.columns">
				<source>Columns</source>
				<target>Spalten</target>
			</trans-unit>
			<trans-unit id="content.element.image">
				<source>Image</source>
				<target>Bild</target>
			</trans-unit>
			<trans-unit id="content.element.imagevideo">
				<source>Image/Video</source>
				<target>Bild/Video</target>
			</trans-unit>
			<trans-unit id="content.element.images">
				<source>Images</source>
				<target>Bilder</target>
			</trans-unit>
			<trans-unit id="content.element.layoutstone" approved="yes">
				<source>Layout stone</source>
				<target>Stein-Layout</target>
			</trans-unit>
			<trans-unit id="content.element.layoutstone.label" approved="yes">
				<source>active</source>
				<target>aktiv</target>
			</trans-unit>
			<trans-unit id="content.element.overline">
				<source>Overline</source>
				<target>Dachzeile</target>
			</trans-unit>
			<trans-unit id="content.element.overline.description">
				<source>Text above the headline</source>
				<target>Text oberhalb der Überschrift</target>
			</trans-unit>
			<trans-unit id="content.element.introtext">
				<source>Stage Text</source>
				<target>Introtext</target>
			</trans-unit>
			<trans-unit id="content.element.introtext.description">
				<source>Text shown above the element</source>
				<target>Text, der über dem Element angezeigt wird</target>
			</trans-unit>
			<trans-unit id="content.element.layout">
				<source>View</source>
				<target>Ansicht</target>
			</trans-unit>
			<trans-unit id="content.element.layout.imageLeft">
				<source>Image left</source>
				<target>Bild links</target>
			</trans-unit>
			<trans-unit id="content.element.layout.imageRight">
				<source>Image right</source>
				<target>Bild rechts</target>
			</trans-unit>
			<trans-unit id="content.element.link">
				<source>Link</source>
				<target>Link</target>
			</trans-unit>
			<trans-unit id="content.element.linktext">
				<source>Link text</source>
				<target>Link-Bezeichnung</target>
			</trans-unit>
			<trans-unit id="content.element.linktext.description">
				<source>Optional - when empty the target name or URI will be displayed</source>
				<target>Optional - Wenn leer, wird der Seitenname bzw. die URI angezeigt</target>
			</trans-unit>
			<trans-unit id="content.element.type">
				<source>Content type</source>
				<target>Inhaltsart</target>
			</trans-unit>
			<trans-unit id="content.element.type.big">
				<source>big</source>
				<target>groß</target>
			</trans-unit>
			<trans-unit id="content.element.type.image">
				<source>Image</source>
				<target>Bild</target>
			</trans-unit>
			<trans-unit id="content.element.type.small">
				<source>small</source>
				<target>klein</target>
			</trans-unit>
			<trans-unit id="content.element.type.video">
				<source>Video</source>
				<target>Video</target>
			</trans-unit>
			<trans-unit id="content.element.video">
				<source>Video</source>
				<target>Video</target>
			</trans-unit>
			<trans-unit id="content.element.video.description">
				<source>Select a video</source>
				<target>Bitte wählen Sie ein Video</target>
			</trans-unit>
			<trans-unit id="content.element.vncfullsizeteaser">
				<source>Fullsize Teaser</source>
				<target>Fullsize-Teaser</target>
			</trans-unit>
			<trans-unit id="content.element.vncfullsizeteasersplit">
				<source>Fullsize Split Teaser</source>
				<target>Fullsize-Split-Teaser</target>
			</trans-unit>
			<trans-unit id="content.element.vncfullsizeteasersplit.description">
				<source>A teaser in two halfs: Text and image spanning the full content width</source>
				<target>Ein Teaser in zwei Hälften: Text und Bild über die gesamte Contentbreite</target>
			</trans-unit>
			<trans-unit id="content.element.vncimage">
				<source>Image</source>
				<target>Bild</target>
			</trans-unit>
			<trans-unit id="content.element.vncimage.description">
				<source>Shows a simple image</source>
				<target>Zeigt ein einfaches Bild an</target>
			</trans-unit>
			<trans-unit id="content.element.vncgallery">
				<source>Gallery</source>
				<target>Galerie</target>
			</trans-unit>
			<trans-unit id="content.element.vncgallery.description">
				<source>Shows multiple images in a gallery with optional enlarge and download Icons.</source>
				<target>Zeigt mehrere Bilder in einer Galerie mit optionalen Vergrößern- und Download-Icons</target>
			</trans-unit>
			<trans-unit id="content.element.image.description">
				<source>Please select image</source>
				<target>Bitte wählen Sie ein Bild aus</target>
			</trans-unit>
			<trans-unit id="content.element.vncquote">
				<source>Quote</source>
				<target>Zitat</target>
			</trans-unit>
			<trans-unit id="content.element.vncquote.author">
				<source>Author</source>
				<target>Autor</target>
			</trans-unit>
			<trans-unit id="content.element.vncquote.author.description">
				<source>Optional author</source>
				<target>Optionale Autorenangabe</target>
			</trans-unit>
			<trans-unit id="content.element.vncquote.description">
				<source>A simple quote with author</source>
				<target>Ein einfaches Zitat mit Autorenangabe</target>
			</trans-unit>
			<trans-unit id="content.element.vncquote.quote">
				<source>Quote</source>
				<target>Zitat</target>
			</trans-unit>
			<trans-unit id="content.element.vncstage">
				<source>Stage Image</source>
				<target>Stage Image</target>
			</trans-unit>
			<trans-unit id="content.element.vncstage.bodytext.description">
				<source>Simple Text, line breaks keep preserved</source>
				<target>Einfacher Text, Zeilenumbrüche bleiben erhalten.</target>
			</trans-unit>
			<trans-unit id="content.element.vncstage.description">
				<source>Fullsize image or video </source>
				<target>Fullsize Bild oder Video </target>
			</trans-unit>
			<trans-unit id="content.element.vncstageslider">
				<source>Stage Slider</source>
				<target>Stage Image/Textbox</target>
			</trans-unit>
			<trans-unit id="content.element.vncstageslider.description">
				<source>Multiple stage elements as a slider</source>
				<target>Mehrere Stage Elemente als Slider</target>
			</trans-unit>
			<trans-unit id="content.element.vncstageslider.header">
				<source>Label</source>
				<target>Bezeichnung</target>
			</trans-unit>
			<trans-unit id="content.element.vncstageslider.item">
				<source>Stage Image/Textbox</source>
				<target>Stage-Slider</target>
			</trans-unit>
			<trans-unit id="content.element.vncstagesplit">
				<source>Stage Image/Text</source>
				<target>Stage Image/Text</target>
			</trans-unit>
			<trans-unit id="content.element.vncstagesplit.description">
				<source>A stage element with two halfs: Text and image</source>
				<target>Ein Stage Element in zwei Hälften: Text und Bild</target>
			</trans-unit>
			<trans-unit id="content.element.vnctext">
				<source>Text</source>
				<target>Text</target>
			</trans-unit>
			<trans-unit id="content.element.vnctext.description">
				<source>Standard text element, one or two columns</source>
				<target>Standard Textelement, ein- oder zweispaltig</target>
			</trans-unit>
			<trans-unit id="content.element.vnctext.layout.1">
				<source>1 column text</source>
				<target>1-spaltiger Text</target>
			</trans-unit>
			<trans-unit id="content.element.vnctext.layout.2">
				<source>2 column text</source>
				<target>2-spaltiger Text</target>
			</trans-unit>
			<trans-unit id="content.element.vnctextimage">
				<source>Text/Image</source>
				<target>Text/Image</target>
			</trans-unit>
			<trans-unit id="content.element.vnctextimage.description">
				<source>Text and image element</source>
				<target>Ein Element mit Text und Bild</target>
			</trans-unit>
			<trans-unit id="content.element.vnctextimagetiles">
				<source>Text/Image Tiles</source>
				<target>Text/Bild-Kacheln</target>
			</trans-unit>
			<trans-unit id="content.element.vnctextimagetiles.description">
				<source>Text tiles with image, header, text and link</source>
				<target>Textkacheln mit Bild, Überschrift, Text und Link</target>
			</trans-unit>
			<trans-unit id="content.element.vnctextimagetiles.item">
				<source>Text/Image Tiles</source>
				<target>Text/Bild-Kacheln</target>
			</trans-unit>
			<trans-unit id="content.element.vnctextmedia">
				<source>Text/Media</source>
				<target>Text/Media</target>
			</trans-unit>
			<trans-unit id="content.element.vnctextmedia.description">
				<source>Element with text and image or video. A video will be displayed in classical 16:9 aspect ratio</source>
				<target>Ein Element mit Text und Bild oder Video. Ein Video ist klassisch im Format 16:9 angelegt</target>
			</trans-unit>
			<trans-unit id="content.element.vnctexttiles">
				<source>Text Tiles</source>
				<target>Text-Kacheln</target>
			</trans-unit>
			<trans-unit id="content.element.vnctexttiles.description">
				<source>Simple Text tiles with header, icon, short text and link</source>
				<target>Einfache Textkacheln mit Überschrift, Icon, kurzem Text und Link</target>
			</trans-unit>
			<trans-unit id="content.element.vnctexttiles.item">
				<source>Text Tiles</source>
				<target>Text-Kacheln</target>
			</trans-unit>
			<trans-unit id="content.item.field.bodytext">
				<source>Text</source>
				<target>Text</target>
			</trans-unit>
			<trans-unit id="content.item.field.header">
				<source>Header</source>
				<target>Überschrift</target>
			</trans-unit>
			<trans-unit id="content.item.field.header_layout">
				<source>Header Layout</source>
				<target>Überschrift Layout</target>
			</trans-unit>
			<trans-unit id="content.item.field.image">
				<source>Image</source>
				<target>Bild</target>
			</trans-unit>
			<trans-unit id="content.item.field.link">
				<source>Link</source>
				<target>Link</target>
			</trans-unit>
			<trans-unit id="content.item.field.linktext">
				<source>Link text</source>
				<target>Linktext</target>
			</trans-unit>
			<trans-unit id="content.element.vncimageslider">
				<source>Image Slider</source>
				<target>Bild-Slider</target>
			</trans-unit>
			<trans-unit id="content.element.vncimageslider.description">
				<source>Images as a slider, linkable</source>
				<target>Bilder als Slider, verlinkbar</target>
			</trans-unit>
			<trans-unit id="content.element.vncintrotext">
				<source>Stage Text</source>
				<target>Stage Text</target>
			</trans-unit>
			<trans-unit id="content.element.vncintrotext.description">
				<source>A Note at the stage area of the site</source>
				<target>Ein Hinweistext im Stage Bereich der Seite</target>
			</trans-unit>
			<trans-unit id="content.element.field.downloadicon">
				<source>Download icon</source>
				<target>Download-Icon</target>
			</trans-unit>
			<trans-unit id="content.element.field.downloadicon.description">
				<source>Shows an icon to download the image</source>
				<target>Zeigt ein Icon um das Bild herunterzuladen</target>
			</trans-unit>
			<trans-unit id="content.element.field.enlargeicon">
				<source>Enlarge icon</source>
				<target>Vergrößern-Icon</target>
			</trans-unit>
			<trans-unit id="content.element.field.enlargeicon.description">
				<source>Shows an icon to enlarge the image</source>
				<target>Zeigt ein Icon um das Bild zu vergrößern</target>
			</trans-unit>
			<trans-unit id="backendlayout.subpage.stage">
				<source>Subpage Stage Area</source>
				<target>Unterseite Stage-Bereich</target>
			</trans-unit>
			<trans-unit id="backendlayout.subpage">
				<source>Subpage</source>
				<target>Unterseite</target>
			</trans-unit>
			<trans-unit id="backendlayout.subpage.content">
				<source>Content Area</source>
				<target>Inhalts-Bereich</target>
			</trans-unit>
			<trans-unit id="backendlayout.startpage">
				<source>Startpage</source>
				<target>Startseite</target>
			</trans-unit>
			<trans-unit id="backendlayout.startpage.stage">
				<source>Startpage Stage Area</source>
				<target>Startseite Stage-Bereich</target>
			</trans-unit>
			<trans-unit id="backendlayout.startpage.content">
				<source>Content Area</source>
				<target>Inhalts-Bereich</target>
			</trans-unit>
			<trans-unit id="backendlayout.footer">
				<source>Footer</source>
				<target>Footer</target>
			</trans-unit>
			<trans-unit id="backendlayout.footer.content">
				<source>Footer Column</source>
				<target>Footer Spalte</target>
			</trans-unit>
			<trans-unit id="newcontentelement.wizard.header">
				<source>Vancado Content Elements</source>
				<target>Vancado Content-Elemente</target>
			</trans-unit>
			<trans-unit id="pagination.prev">
				<source>Previous</source>
				<target>Zurück</target>
			</trans-unit>
			<trans-unit id="pagination.next">
				<source>Next</source>
				<target>Vor</target>
			</trans-unit>
			<trans-unit id="content.element.overlay">
				<source>Background color</source>
				<target>Hintergrund-Farbe</target>
			</trans-unit>
			<trans-unit id="content.element.overlay.without">
				<source>without</source>
				<target>ohne</target>
			</trans-unit>
			<trans-unit id="content.element.overlay.light">
				<source>hell</source>
				<target>hell</target>
			</trans-unit>
			<trans-unit id="content.element.overlay.dark">
				<source>dunkel</source>
				<target>dunkel</target>
			</trans-unit>
			<trans-unit id="accept_youtube">
				<source>Accept Youtube Services</source>
				<target>Zugriff auf Youtube gestatten</target>
			</trans-unit>
			<trans-unit id="form.upload_select">
				<source>Select File</source>
				<target>Datei auswählen</target>
			</trans-unit>
			<trans-unit id="form.delete_all_files">
				<source>Delete all files</source>
				<target>Alle Dateien löschen</target>
			</trans-unit>
			<trans-unit id="close">
				<source>close</source>
				<target>schließen</target>
			</trans-unit>
		</body>
	</file>
</xliff>
