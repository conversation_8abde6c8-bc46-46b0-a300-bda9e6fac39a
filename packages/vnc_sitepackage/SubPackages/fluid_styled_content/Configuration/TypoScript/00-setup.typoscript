@import 'EXT:fluid_styled_content/Configuration/TypoScript/setup.typoscript'


lib.contentElement {
  templateRootPaths.504455 = EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Templates/
  partialRootPaths.504455 = EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Partials/
  layoutRootPaths.504455 = EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Layouts/

  settings {
    svgSpriteFile = {$plugin.tx_vncsitepackage.settings.svgSpriteFile}
  }
}

lib.parseFunc_RTE.externalBlocks.table.stdWrap.HTMLparser.tags.table.fixAttrib.class {
  list >
  default = table
}

lib.parseFunc_RTE {
  externalBlocks {
    table {
      stdWrap {
        wrap = <div class="table--scroll table--custom-borders" data-table-scroller><div class="table--scroll__container col-12" data-table-bounds><div class="table--scroll__viewport" data-table-viewport>|</div></div></div>
        HTMLparser {
          tags {
            table {
              fixAttrib {
                class {
                  default = table
                  always = 1
                  list = table
                }
              }
            }
          }
        }
      }
    }
  }
}
