mod.wizards.newContentElement.wizardItems {
  vnccontent {
    header = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:newcontentelement.wizard.header
    before = plugins
    elements {
      vnctext {
        iconIdentifier = vnctext-icon
        title = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vnctext
        description = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vnctext.description
        tt_content_defValues {
          CType = vnctext
        }
      }
      vncquote {
        iconIdentifier = vncquote-icon
        title = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncquote
        description = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncquote.description
        tt_content_defValues {
          CType = vncquote
        }
      }
      vncimage {
        iconIdentifier = vncimage-icon
        title = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncimage
        description = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncimage.description
        tt_content_defValues {
          CType = vncimage
        }
      }
      vncgallery {
        iconIdentifier = vncgallery-icon
        title = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncgallery
        description = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncgallery.description
        tt_content_defValues {
          CType = vncgallery
        }
      }
      vnctextimage {
        iconIdentifier = vnctextimage-icon
        title = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vnctextimage
        description = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vnctextimage.description
        tt_content_defValues {
          CType = vnctextimage
        }
      }
      vnctextmedia {
        iconIdentifier = vnctextmedia-icon
        title = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vnctextmedia
        description = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vnctextmedia.description
        tt_content_defValues {
          CType = vnctextmedia
        }
      }
      vnctextimagetiles {
        iconIdentifier = vnctextimagetiles-icon
        title = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vnctextimagetiles
        description = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vnctextimagetiles.description
        tt_content_defValues {
          CType = vnctextimagetiles
        }
      }
      vnctexttiles {
        iconIdentifier = vnctexttiles-icon
        title = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vnctexttiles
        description = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vnctexttiles.description
        tt_content_defValues {
          CType = vnctexttiles
        }
      }
      vncstage {
        iconIdentifier = vncstage-icon
        title = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncstage
        description = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncstage.description
        tt_content_defValues {
          CType = vncstage
        }
      }
      vncstageslider {
        iconIdentifier = vncstageslider-icon
        title = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncstageslider
        description = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncstageslider.description
        tt_content_defValues {
          CType = vncstageslider
        }
      }
      vncstagesplit {
        iconIdentifier = vncstagesplit-icon
        title = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncstagesplit
        description = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncstagesplit.description
        tt_content_defValues {
          CType = vncstagesplit
        }
      }
      vncfullsizeteaser {
        iconIdentifier = vncfullsizeteaser-icon
        title = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncfullsizeteaser
        description = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncfullsizeteaser.description
        tt_content_defValues {
          CType = vncfullsizeteaser
        }
      }
      vncfullsizeteasersplit {
        iconIdentifier = vncfullsizeteasersplit-icon
        title = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncfullsizeteasersplit
        description = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncfullsizeteasersplit.description
        tt_content_defValues {
          CType = vncfullsizeteasersplit
        }
      }
      vncimageslider {
        iconIdentifier = vncimageslider-icon
        title = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncimageslider
        description = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncimageslider.description
        tt_content_defValues {
          CType = vncimageslider
        }
      }
      vncintrotext {
        iconIdentifier = vncintrotext-icon
        title = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncintrotext
        description = LLL:EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Language/locallang.xml:content.element.vncintrotext.description
        tt_content_defValues {
          CType = vncintrotext
        }
      }

    }
    show := addToList(vnctext, vncquote, vnctextmedia, vnctextimage, vncimage, vncgallery, vnctextimagetiles, vnctexttiles, vncstage, vncstageslider, vncstagesplit, vncfullsizeteasersplit, vncfullsizeteaser, vncimageslider, vncintrotext)
  }
}
