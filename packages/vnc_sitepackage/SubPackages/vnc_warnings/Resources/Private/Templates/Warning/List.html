{namespace vnc=Vancado\VncWarnings\ViewHelpers}
<html
    xmlns="http://www.w3.org/1999/xhtml"
    lang="en"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
    xmlns:vnc="http://typo3.org/ns/Vancado/VncSitepackage/ViewHelpers"
    data-namespace-typo3-fluid="true"
>
    <f:layout name="Default" />
    <f:section name="content">
    <f:comment><!-- Warning Toggler (for testing purposes only)
        <div class="container">
            <div class="row">
                <div class="col d-flex justify-content-center">
                    <f:if condition="{warnings}">
                        <f:for each="{warnings}" as="warning" iteration="warningIterator">
                            <f:if condition="{warning.uid}">
                                <f:then>
                                    <f:render partial="ModalDialogToggle" arguments="{
                                        uid: 'warning-modal-{warning.uid}',
                                        text: 'Toggle Warning {warning.uid}'
                                    }"/>
                                </f:then>
                            </f:if>
                        </f:for>
                    </f:if>
                </div>
            </div>
        </div> -->
    </f:comment>

        <f:if condition="{warnings}">
            <f:for each="{warnings}" as="warning" iteration="warningIterator">
                <f:if condition="{warning.uid}">
                    <f:then>
                        <f:variable name="modalHeader">
                            <h3 class="headline h4 d-inline-flex gap-3 mb-2">
                                <i class="{warning.icon}"></i>
                                {warning.header -> f:format.raw()}
                            </h3>
                        </f:variable>
                        <f:variable name="modalContent">
                            <div class="vnc-infobox">
                                <article class="py-2">
                                    <f:format.html>{warning.text}</f:format.html>
                                    <f:link.typolink
                                        class="button button--secondary"
                                        parameter="{warning.link}"
                                    >{warning.linkText}</f:link.typolink>
                                </article>
                            </div>
                        </f:variable>
                        <f:variable name="footerCloseButton" value="false"></f:variable>
                        <f:render partial="ModalDialog" arguments="{
                            uid: 'warning-modal-{warning.uid}',
                            size: 'lg',
                            open: 'false',
                            header: modalHeader,
                            content: modalContent,
                            footerCloseButton: footerCloseButton
                            className: 'alert'
                        }"/>
                    </f:then>
                    <f:else>
                        current page is not in pages listed by item
                        {warning.uid} - {warningIterator.cycle}
                    </f:else>
                </f:if>
            </f:for>
        </f:if>
    </f:section>
</html>
