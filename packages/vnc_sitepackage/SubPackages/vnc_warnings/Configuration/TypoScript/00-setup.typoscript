@import 'EXT:vnc_warnings/Configuration/TypoScript/setup.typoscript'

plugin.tx_vncwarnings_pi1 {
    view {
        templateRootPaths.2 = EXT:vnc_sitepackage/SubPackages/vnc_warnings/Resources/Private/Templates
        partialRootPaths.3 = EXT:vnc_sitepackage/SubPackages/fluid_styled_content/Resources/Private/Partials/
    }
    persistence {
        storagePid = {$plugin.tx_vncsitepackage.settings.vncWarningsPid}
    }
    settings {
        svgSpriteFile = Static/symbol-defs.svg
    }
}

page.20 < lib.vncWarnings
