plugin.tx_vncsitepackage {
  settings {
    startPagePid = 156
    siteLogo =EXT:vnc_mandant_swcoesfeld/Resources/Public/Images/logo.svg
    imagePath = EXT:vnc_sitepackage/Resources/Public/Images
    svgSpriteFile = Static/symbol-defs.svg
    faviconPath = EXT:vnc_sitepackage/Resources/Public/Favicons/Default/favicon_coesfeld.ico
    metaNavigationPid = 195
    footerNavigationPid = 182
    linkInstagram =
    linkFacebook =
    linkLinkedin =
    linkXing =
    linkYoutube =
    linkX=
  }
}

plugin.tx_indexedsearch {
  settings {
    # cat=plugin.tx_indexedsearch/settings; type=integer; label=Set the target page where search results are shown
    targetPid = 162
    # cat=plugin.tx_indexedsearch/settings; type=string; label=A list of integer which should be root-pages to search from
    rootPidList = 156
  }
}
