{"name": "vnc-frontend", "version": "0.0.1", "description": "Typo3 boilerplate frontend", "scripts": {"core:init": "bash ./scripts/core-init.sh", "core:update": "bash ./scripts/core-update.sh", "core:install": "cd ./core && npm ci && cd ..", "core:build": "cd ./core && npm run build && cd ..", "core:deploy": "bash ./scripts/core-deploy.sh", "core:watch": "nodemon --watch core/src -e js,scss --exec 'npm run core:build && npm run core:deploy'", "docs:deploy": "npm run core:install && cd ./core && npm run fractal:static && cd .. && bash ./scripts/docs-deploy.sh", "app:init": "bash ./scripts/app-init.sh", "app:update": "bash ./scripts/app-update.sh", "app:deploy": "bash ./scripts/app-deploy.sh", "app:watch": "nodemon --watch app/**/src -e js,vue,scss --exec 'npm run app:deploy'", "build": "npm run core:build", "predeploy": "npm run core:install && npm run core:build", "deploy": "npm run core:deploy && npm run app:deploy", "watch": "nodemon --watch core/src --watch app/**/src -e ts,js,vue,scss,svelte --exec 'npm run core:build && npm run core:deploy && npm run app:deploy'"}, "author": "", "license": "ISC", "devDependencies": {"nodemon": "^2.0.20"}, "nodemonConfig": {"watch": ["core/build"], "ext": "css,js", "events": {"restart": "npm run core:deploy"}}}