#!/bin/bash

BASE_BUILD_PATH="./core/build"
BASE_DEPLOY_PATH="../public";

DIR=("$BASE_DEPLOY_PATH/docs")
if ! [ -d "$DIR" ]
then
    echo "Create $DIR";
    mkdir $DIR;
fi

# \sed -i '.bak' "s/\.\.\/\.\./\./" build/components/preview/lead-form*.html
# egrep -rl '.scss' build/components | xargs -I@ sed -i '' 's/.scss/.css/g' @

\sed -i '.bak' "s/src\/scss/css/" core/build/components/**/*.html
\sed -i '.bak' "s/.scss/.css/" core/build/components/**/*.html
\sed -i '.bak' "s/src\/js/js/" core/build/components/**/*.html

\cp -rv $BASE_BUILD_PATH/* $BASE_DEPLOY_PATH/docs/

