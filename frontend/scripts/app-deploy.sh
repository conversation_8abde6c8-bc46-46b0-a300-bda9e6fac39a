#!/bin/bash

BASE_DEPLOY_PATH="../../packages/vnc_sitepackage/ContentBlocks/ContentElements/energy-rate-widget/Assets";

if ! [ -d "app" ]
then
    echo "No apps configured.";
    exit 0;
fi

# DIRS=("$BASE_DEPLOY_PATH/Css" "$BASE_DEPLOY_PATH/Js" "$BASE_DEPLOY_PATH/Static")
# for DIR in "${DIRS[@]}";
# do
#     if ! [ -d "$DIR" ]
#     then
#         echo "Create $DIR";
#         mkdir $DIR;
#     fi
# done

cd ./app;


for d in *; do
  if [ -d "$d" ]; then
    cd $d;
    npm ci;
    npm run build;
    cd ..;
    echo "$d/dist/index.js > $BASE_DEPLOY_PATH/$d.js";
    cat $d/dist/index.js > $BASE_DEPLOY_PATH/$d.js;
    # cat $d/dist/main.css > $BASE_DEPLOY_PATH/Css/$d.css;

    # cp -rv $d/dist/* ../$BASE_DEPLOY_PATH/Static/
  fi
done
