#!/bin/bash

DIR="core"
REPO="**************:vancado/vnc-frontend-core.git";

. ./scripts/.branchesrc
BRANCH=$BRANCH_CORE;

git remote add -f vnc-frontend-core **************:vancado/vnc-frontend-core.git;

if [ -d "$DIR" ]
then
	if [ "$(ls -A $DIR)" ];
    then
        echo "Take action $DIR is not Empty";
	else
        ADD=true;
        echo "$DIR is Empty";
	fi
else
    ADD=true;
fi

if [ "$ADD" == true ]
then
    cd ..;
    git subtree add --prefix frontend/$DIR $REPO $BRANCH --squash;
fi
