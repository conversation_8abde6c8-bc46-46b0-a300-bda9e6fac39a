#!/bin/bash
export GIT_MERGE_AUTOEDIT=no

# shopt -s nullglob;

. ./scripts/.branchesrc

DIR=$1;
REPO=$2;
BRANCH="BRANCH_$(echo $DIR | tr '[:lower:]' '[:upper:]' | tr '-' '_')";
BRANCH=${!BRANCH};
# [ -z "$BRANCH" ] && BRANCH='main';

git remote add -f $DIR $REPO;

if [ -z "$REPO" ]
    then
        echo 'Repo missing';
        exit 0;
fi

if [ -d "$DIR" ]
then
	if [ "$(ls -A $DIR)" ];
    then
        echo "Take action $DIR is not Empty";
	else
        ADD=true;
        echo "$DIR is Empty";
	fi
else
    ADD=true;
fi

if [ $ADD == true ]
then
    cd ..;
    git subtree add --prefix frontend/app/$DIR $REPO $BRANCH --squash;
fi
