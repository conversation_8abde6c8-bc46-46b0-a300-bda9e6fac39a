#!/bin/bash
export GIT_MERGE_AUTOEDIT=no

# shopt -s nullglob;

. ./scripts/.branchesrc
# BRANCH=$BRANCH_CORE;

cd ./app;

directories=(*);

echo $directories;

cd ../..;

for d in $directories; do
    echo ./frontend/app/$d
    # VERSION=$(awk -F'"' '/"version": ".+"/{ print $4; exit; }' ./frontend/app/$d/package.json);
    BRANCH="BRANCH_$(echo $d | tr '[:lower:]' '[:upper:]' | tr '-' '_')";
    BRANCH=${!BRANCH};
    git fetch $d $BRANCH;
    echo "GET $BRANCH";
    git subtree pull --prefix frontend/app/$d $d $BRANCH --squash;
done;
