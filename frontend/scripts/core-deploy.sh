#!/bin/bash

BASE_BUILD_PATH="./core/build"
BASE_DEPLOY_PATH="../packages/vnc_sitepackage/Resources/Public";

DIRS=("$BASE_DEPLOY_PATH/Css" "$BASE_DEPLOY_PATH/Js" "$BASE_DEPLOY_PATH/Static")
for DIR in "${DIRS[@]}";
do
    if ! [ -d "$DIR" ]
    then
        echo "Create $DIR";
        mkdir $DIR;
    fi
done

\cp -rv $BASE_BUILD_PATH/css/*.{css,map} $BASE_DEPLOY_PATH/Css/
\cp -rv $BASE_BUILD_PATH/js/*.{js,map} $BASE_DEPLOY_PATH/Js/

\cp -rv $BASE_BUILD_PATH/static/* $BASE_DEPLOY_PATH/Static/
