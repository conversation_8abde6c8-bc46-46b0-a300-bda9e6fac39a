const fs = require('fs');
const { parse } = require('svg-parser');

// Read from sprite file
fs.readFile('./symbol-defs.svg', 'utf8', function (err, contents) {
  console.log(err);
  const parsed = parse(contents);
  const symbols = parsed.children[0].children[0].children;

  symbols.forEach(symbol => {
    let paths = '';
    const name = symbol.properties.id.replace('icon-','');
    symbol.children.forEach(path => {
      paths = paths + `<path d="${path.properties.d}" />`
    });

    const viewBox = symbol.properties.viewBox;
    const width = viewBox && viewBox.slice(4, 6);
    const height = viewBox && viewBox.slice(7, 9);

    // Build SVG content
    const newIcon = `<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 ${width} ${height}" width="${width}" height="${height}">
      <title>${name}</title>
      <g>
        ${paths}
      </g>
    </svg>`

    // Write to file
    fs.writeFile(`${name}.svg`, newIcon, () => {
      console.log(name);
    });
  });

});