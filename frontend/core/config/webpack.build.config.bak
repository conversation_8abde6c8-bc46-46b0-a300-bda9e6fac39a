const path = require('path');
const paths = require('./paths.config');
const glob = require( 'glob' );
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const SpriteLoaderPlugin = require('svg-sprite-loader/plugin');

const globImporter = require('node-sass-glob-importer');

module.exports = {
  entry: {
    'scripts': [
      path.join(__dirname, '../', paths.src.js, 'scripts.js'),
      ...glob.sync('./src/components/**/!(^_*|^index|*.config|*.stories).js'),
      path.join(__dirname, '../', paths.src.scss, 'style.scss'),
      path.join(__dirname, '../', paths.src.scss, 'preview.scss'),
    ],
  },
  output: {
    path: path.resolve(__dirname, '../', paths.build.root),
    filename: 'js/[name].js',
    chunkFilename: "js/[name].js",
  },
  mode: process.env.NODE_ENV,
  devtool: 'source-map',
  optimization: {
    minimize: process.env.NODE_ENV === 'production'
  },
  plugins: [
    new CopyWebpackPlugin({
      patterns: [
        {
          from: path.resolve(__dirname, '../', 'static'),
          to: '../build/static'
        }
      ]
    }),
    new MiniCssExtractPlugin({
      filename: 'css/style.css'
    },
    new SpriteLoaderPlugin(),
  )],
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        include: [
          path.resolve(__dirname, '../'),
          path.resolve(__dirname, '../src'),
          path.resolve(__dirname, '../node_modules')
        ],
        use: [{
          loader: 'babel-loader',
        }]
      },
      {
        test: /\.svg$/,
        include: path.resolve(__dirname, '../src/assets/svg/**/*.svg'),
        use: [
          {
            loader: 'svg-sprite-loader',
            options: {
              extract: true,
              spriteFilename: 'assets/sprite.svg',
              esModule: false,
            }
          },
          'svgo-loader'
        ],
      },
      {
        test: /\.(scss|css)$/,
        // exclude: /(node_modules)/,
        use: [
          MiniCssExtractPlugin.loader,
          "css-loader",
          {
            loader: "sass-loader",
            options: {
              sassOptions: {
                importer: globImporter(),
                sourceMap: true,
                minimize: process.env.NODE_ENV === 'production',
                outputStyle: 'expanded'
              },
              implementation: require.resolve("sass"),
            },
          },
        ],
      },
      {
        test: /\.(jpe?g|png|ttf|eot|woff(2)?)(\?[a-z0-9=&.]+)?$/,
        use: 'base64-inline-loader'
      },
    ],
  },
  devServer: {
    historyApiFallback: true,
    compress: true,
    proxy: {
      '**': 'http://localhost:3000'
    },
    port: 4000,
  },
  watchOptions: {
    ignored: ['**/build/**', '**/dist/**', '**/node_modules'],
  },
};
