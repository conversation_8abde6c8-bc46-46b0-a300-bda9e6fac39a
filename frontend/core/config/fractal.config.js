(function () {

  'use strict';

  module.exports = {
    'renderer': 'handlebars',
    'project': {
      'title': 'Vancado Component Library'
    },
    'theme': {
      'skin': {
        'name': 'white',
        // 'accent': '',
        // 'complement': '#00aae6',
        // 'links': '#1b1b1b',
      },
      'lang': 'en',
      // 'favicon': '/assets/favicon.ico',
      // 'nav': [ 'docs', 'components' ],
      'styles': [
        'default',
        '/src/scss/preview.scss',
    ]
    },
    'default': {
      'context': {},
      'collator': function( markup, item ) {
        const variant = typeof item.preview === 'string' && item.preview.includes('dark') ? 'dark' : '';
        return `<!-- Start: @${item.handle} -->\n<div class="preview-collator mb-6 ${variant ? `bg-${variant}`: ''}" data-bs-theme="${variant}"><p class="border-bottom py-2 mb-3 fw-bold text-uppercase"><small>${item.label}:</small></p>\n<div class="d-block float-left">\n${markup}\n</div>\n</div>\n<!-- End: @${item.handle} -->\n`
      }
    }
  };
})();