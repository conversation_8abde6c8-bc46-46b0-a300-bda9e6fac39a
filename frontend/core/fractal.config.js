(function () {

  'use strict';

  const path = require( 'path' );
  const frctl = require('@frctl/fractal');
  const mandelbrot = require( '@frctl/mandelbrot' );

  const paths = require( './config/paths.config.js' ),
  config = {
    fractal: require( './config/fractal.config.js' ),
    browsersync: require( './config/browsersync.config.js' ),
  };

  /* Create a new Fractal instance and export it for use elsewhere if required */
  const fractal = module.exports = frctl.create();

  /* Set the title of the project */
  fractal.set( 'project.title', config.fractal.project.title );


  // create a new instance with custom config options
  const theme = mandelbrot( config.fractal.theme );
  fractal.web.theme( theme );


    // const handlebars = require('@frctl/handlebars');
    //   // layouts = require( 'handlebars-layouts' ),
    //   // extend = require('handlebars-extend-block');

    // const hbs = handlebars({
    //   helpers: {
    //     uppercase: function(str) {
    //       return str.toUpperCase();
    //     }
    //   },
    //   partials: {},
    //   pristine: false
    // });
  
    // fractal.components.engine( hbs );
    // fractal.docs.engine( hbs );
    // fractal.components.set( 'ext', '.hbs');

    // register partials
    // const instance = fractal.components.engine();
    // function registerComponents( pattern ) {
    //   glob( pattern, function( er, files ) {
    //     files.forEach( function( file ) {
    //       let partial = path.basename( file, '.hbs' );
    //       instance.handlebars.registerPartial( partial, require( file ) );
    //     });

    //   });
    // }
    // registerComponents( path.join( __dirname, paths.src.components, '**/*.hbs' ) );


  //     instance.handlebars.registerHelper('toJSON', function(object) {
  //       return new instance.handlebars.SafeString(
  //         JSON.stringify( object, true, 2 )
  //       );
  //     });

  //     instance.handlebars.registerHelper('getPartial', function( identifier ) {
  //       return '@' + identifier;
  //     });

  //     instance.handlebars.registerHelper( 'dataset', function( object ) {
  //       let output = '';
  //       object.forEach( element => {
  //         output += ` data-${element.attribute}`;
  //         if( typeof element.value !== 'undefined' ) {
  //           let json = JSON.stringify( element.value );
  //           json = json.replace( /"/g, '&quot;' )
  //           output += `="${json}" `;
  //         }
  //       });
  //       return output;
  //     });

  //     instance.handlebars.registerHelper( 'hex2rgb', function( hex ) {
  //       var c;
  //       if(/^#([A-Fa-f0-9]{4}){1,2}$/.test(hex)){
  //           c= hex.substring(1).split('');
  //           if(c.length== 3){
  //               c= [c[0], c[0], c[1], c[1], c[2], c[2]];
  //           }
  //           c= '0x'+c.join('');
  //           return new instance.handlebars.SafeString(
  //             'rgba('+[(c>>16)&255, (c>>8)&255, c&255].join(',')+',1)'
  //           );
  //       }
  //       throw new Error('Bad Hex');
  //     });

  //     instance.handlebars.registerHelper( 'rgb2hex', function( rgb ) {
  //       rgb = rgb.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i);
  //       return ( rgb && rgb.length === 4) ? '#' +
  //         ( '0' + parseInt(rgb[1],10).toString(16)).slice(-2) +
  //         ( '0' + parseInt(rgb[2],10).toString(16)).slice(-2) +
  //         ( '0' + parseInt(rgb[3],10).toString(16)).slice(-2) : '';
  //     });

  //     instance.handlebars.registerHelper( 'paths', function( paths ) {

  //       const _          = require('lodash');
  //       const utils      = require('@frctl/fractal').utils;

  //       if( paths && paths.constructor === Array ) {
  //         const options = Array.from(arguments).pop();
  //         const root = options.data.root;

  //         paths = paths.map( function( path ) {
  //           if (path.includes('{{')) {
  //               let context = _.defaults({}, this, _.pickBy(root, (item, key) => key.startsWith('_')));
  //               const tpl = instance.handlebars.compile(path, {
  //                   data: false
  //               });
  //               path = tpl(context);
  //           }
    
  //           if (! root || ! root._env || root._env.server) {
  //               return path;
  //           }
    
  //           return utils.relUrlPath(path, _.get(root._env.request || root._request, 'path', '/'), fractal.web.get('builder.urls'));
  //         });

  //       }

  //       return paths;
  //     });

  //     /**/
  //     /* Use this to turn on logging: (in your local extensions file)
  //     /**/
  //     instance.handlebars.registerHelper( 'debug', function( optionalValue ) {
  //       console.log( 'Current Context' );
  //       console.log( '====================' );
  //       console.log( this );

  //       if( optionalValue ) {
  //         console.log( 'Value' );
  //         console.log( '====================' );
  //         console.log( optionalValue);
  //       }
  //     });


  //     // instance.handlebars.registerHelper( 'updatePartial', function( partial, options ) {

  //     //   /* store updated partial template */
  //     //   var update = instance.handlebars.partials[ partial ];

  //     //   /* unregister existing partial */
  //     //   instance.handlebars.unregisterPartial( partial );

  //     //   /* compile update partial */
  //     //   if( typeof update !== 'function' ) {
  //     //     update = instance.handlebars.compile( update );
  //     //   }

  //     //   /* register updated partial */
  //     //   instance.handlebars.registerPartial( partial, update );

  //     //   /* output updated partial */
  //     //   return new instance.handlebars.SafeString( update( options ) );

  //     // });

  //     // instance.handlebars.logger.level = 0; 
  //     // instance.handlebars.logger.log = function(level) {
  //     //   if(level >= instance.handlebars.logger.level) {
  //     //     console.log.apply(console, [].concat(["Handlebars: "], _.toArray(arguments)));
  //     //   }
  //     // };
  //     // // DEBUG: 0, INFO: 1, WARN: 2, ERROR: 3, 
  //     // instance.handlebars.registerHelper( 'log', instance.handlebars.logger.log);
  //     // // Std level is 3, when set to 0, handlebars will log all compilation results
  //     // instance.handlebars.logger.level = 1; 


  /* Tell Fractal where the components will live */
  fractal.components.set( 'path', path.join( __dirname, paths.src.components ) );

  fractal.components.set( 'default.context', config.fractal.default.context );
  fractal.components.set( 'default.collator', config.fractal.default.collator );

  /* Tell Fractal where the documentation pages will live */
  fractal.docs.set( 'path', path.join( __dirname, paths.src.docs ) );
  // fractal.docs.set('markdown', {
  //   gfm: true,
  //   tables: true,
  //   breaks: false,
  //   pedantic: false,
  //   sanitize: false,
  //   smartLists: true,
  //   smartypants: true
  // });

  fractal.web.set( 'builder.dest', path.join( __dirname, paths.build.root ) );

  fractal.web.set( 'static.path', path.join( __dirname, paths.src.static ) );
  // fractal.web.set( 'static.mount', 'docs' );

  fractal.web.set( 'server.sync', true);
  fractal.web.set( 'server.syncOptions', config.browsersync() );
}());
