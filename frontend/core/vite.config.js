import { defineConfig } from 'vite';
import fractal from './fractal.config.js';
import sassGlobImports from 'vite-plugin-sass-glob-import';
import ViteCSSExportPlugin from 'vite-plugin-css-export'

const path = require('path')

export default defineConfig(async ({ command }) => {
  const config = {
    // plugins: [vue()],
    plugins: [
      sassGlobImports(),
      ViteCSSExportPlugin()
    ],
    build: {
      outDir: 'build',
      emptyOutDir: false,
      sourcemap: true,
      assetsDir: './public',
      assetsInlineLimit: 4096 * 12,
      rollupOptions: {
        input: 'src/js/scripts.js',
        output: {
          entryFileNames: 'js/[name].js',
          assetFileNames: (assetInfo) => (assetInfo.name.includes('scripts.css') ? 'css/style.css' : assetInfo.name),
        }
      },
    },
    resolve: {
      alias: {
        // vue: 'vue/dist/vue.esm-bundler.js',
        '@src': path.resolve(__dirname, 'src'),
      },
    },
    server: {
      port: 4000
    }
  };

  if (command === 'serve') {
    fractal.set('viteServer', true)
    const server = fractal.web.server({ sync: true });

    await server.start();

    config.server = {
      ...config.server,
      hmr: {
        protocol: 'ws',
      },
      proxy: {
        '^(?!/(src|public|static|node_modules|@vite|@id|__vite_ping)).*': {
          target: server.url,
        },
      },
    }
  }

  return config
})