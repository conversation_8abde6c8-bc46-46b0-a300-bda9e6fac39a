import { defineConfig } from 'vite'
import sassGlobImports from 'vite-plugin-sass-glob-import';
import path from 'path';

export default defineConfig(async ({ command }) => {
  const config = {
    // plugins: [vue()],
    plugins: [
      sassGlobImports()
    ],
    build: {
      manifest: true,
      outDir: 'dist',
      assetsDir: 'dist',
      emptyOutDir: false,
      sourcemap: true,
      assetsInlineLimit: 4096 * 12,
      rollupOptions: {
        // input: 'src/js/index.js',
        external: ['bootstrap'],
        // output: {
        //   entryFileNames: 'js/[name].js',
        //   assetFileNames: (assetInfo) => (assetInfo.name.includes('scripts.css') ? 'css/style.css' : assetInfo.name),
        // }
      },
      lib: {
        entry: path.resolve(__dirname, 'src/js/index.js'),
        name: 'VncComponentLib',
        fileName: (format) => `vnc-component-lib.${format}.js`,
      },
    },
    resolve: {
      alias: {
        // vue: 'vue/dist/vue.esm-bundler.js',
        '@src': path.resolve(__dirname, 'src'),
      },
    },
  };

  return config
})