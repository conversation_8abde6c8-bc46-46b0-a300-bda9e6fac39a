---
title: VNC Form Components
context: {
}
---

## Form validation

###  Frontend validation

Initially, forms fields are not visually marked as valid or invalid at all. Once the user submits the form, the validation logic gets activated.

On first submit the form gets flagged with an additional data attribute: _data-validate_. Only then invalid fields are marked visually using afore mentioned css pseudo selectors.

Inside fractal's previews most input componenents are rendered inside preview forms which already have this data attribute applied, so we can show all states right away. Default fields can be excluded here using the _formnovalidate_ attribute, which is a boolean field in the input's context.

### Backend validation

In case of backend errors, two things have to be taken care of:

- The form itself needs to be rendered with the data attribute, so faulty fields can be marked visually right from the start.

- On field level the invalid field needs the css class _.is-invalid_ attached to it.

<form data-validate>
  <div class="input input--text">
      <input type="text" class="is-invalid" />
  </div>
</form>

<xmp>
  <form data-validate>
    <div class="input input--text">
      <input type="text" class="is-invalid" />
    </div>
  </form>
</xmp>

### AJAX Submits

AJAX-Calls are implemented via Fetch-API, where the form elements needs the data attribute _data-fetch-submit_.

With this attribute set, JS will listen for the submit event and will execute an AJAX-Request with the forms given action, method and so on.

Default behaviour for updating the DOM when getting a successful response is just to replace the form inheriting the submit emitter. In case another element should be replaced instead of the form, this can be set inside the _data-fetch-submit_ attribute, using the key _mountpoint_, where you can point to an css-class or id of an element.

#### Exemplary form element pointing to another element being set as mountpoint:
<xmp>
  <form
    action="shopping-cart--redeemed.html"
    data-fetch-submit="{
      &quot;mountpoint&quot;:&quot;.mount-shopping-cart&quot;
    }"
    novalidate
  >
  ...
  </form>
</xmp>

In cases where we want to manipulate the response before updating the DOM we use a custom submit event, with an additional flag set in the event's detail object: _parsedata_, which when set true tells the submit handler to not replace the DOM contents right away but to skip this step and instead pass the response data back to the event emitter.

#### Exemplary custom submit event:
<xmp>
  var submitEvent = new CustomEvent( 'submit', {
    detail: {
      parsedata: true
    },
    bubbles: true,
    cancelable: false
  });
  form.dispatchEvent( submitEvent );
</xmp>


The response gets also passed back using a custom _success_ event which itself carries the reponse data in it's detail object (generic implementation here, which will fall back to replacing the form with no valid mountpoint set):

#### Exemplary custom success event:
<xmp>
  var successEvent = new CustomEvent( 'success', {
    detail: {
      mountpoint: dataset.mountpoint || form,
      data: responsedata
    },
    bubbles: true,
    cancelable: false
  });
  form.dispatchEvent( successEvent );
</xmp>

### Css

Frontend validation relies on existing HTML5 Browser implementation and therefore should work out of the box by only using the pseudo classes as listed:

<pre>
  :enabled
  :disabled
  :read-write
  :read-only
  :valid
  :invalid
</pre>

