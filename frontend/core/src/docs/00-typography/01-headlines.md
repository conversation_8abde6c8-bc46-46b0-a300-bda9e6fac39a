---
title: Headlines
context: {
  headlines: [
    {
        text: 'XXL Headline',
        tag: 'p',
        size: 'h1',
    },
    {
        text: 'XL Headline',
        tag: 'p',
        size: 'h2',
    },
    {
        text: 'Large Headline',
        tag: 'p',
        size: 'h3',
    },
    {
        text: 'Mid Sized Headline',
        tag: 'p',
        size: 'h4',
    },
    {
        text: 'Small Headline',
        tag: 'p',
        size: 'h5',
    },
    {
        text: 'Extra Small Headline',
        tag: 'p',
        size: 'h6',
    },
  ],
  displaySizes: [
    { className: 'display-1', text: 'Displaysize 1' },
    { className: 'display-2', text: 'Displaysize 2' },
    { className: 'display-3', text: 'Displaysize 3' },
    { className: 'display-4', text: 'Displaysize 4' },
    { className: 'display-5', text: 'Displaysize 5' },
    { className: 'display-6', text: 'Displaysize 6' },
  ]
}
---

- basic headline sizes set in _variables.scss
- extended with responsive sizes in typography.scss

<article>

## Headlines
***

{{#each headlines}}
{{ render '@headline' (contextData '@headline' this) }}
{{/each}}

### HTML:
```
{{#each headlines}}
{{ render '@headline' (contextData '@headline' this) }}
{{/each}}
```

## Display Sizes
***

{{#each displaySizes}}
{{ render '@headline' (contextData '@headline' this) }}
{{/each}}

### HTML:
```
{{#each displaySizes}}
{{ render '@bodytext' (contextData '@bodytext' this) }}
{{/each}}
```
</article>

## Template:

```
{{view '@headline'}}
```

<!-- * [Project colors](/docs/colors)
* [Buttons components](/docs/buttons)

* [Getting started](/docs/setup) -->
