<!-- {{ _self.content}} -->
{{#each variants}}
  <p class="border-bottom py-2 mb-3 fw-bold text-uppercase"><small>{{ className }}</small></p>
  <div class="row align-items-center mb-5">
    {{#each ../items}}
      <div class="col text-center">
        <div class="border border-dark rounded p-2">
          <i class="svg-icon {{ ../className }}" title="{{ title }}">
            <svg class="icon" preserveAspectRatio="xMaxYMin">
              <use xlink:href="{{ path '/static/symbol-defs.svg#icon-{{ symbol }}' }}" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0"></use>
            </svg>
          </i>
        </div>
        <small>{{ label }}</small>
      </div>
    {{/each}}
  </div>
  {{/each}}




<!-- {{#each items}}
  <p class="border-bottom py-2 mb-3"><small>{{ label }}</small></p>
  <div class="row align-items-center mb-5">
    <div class="col">
      <div class="border border-dark rounded p-2">
        <i class="svg-icon" title="{{ title }}">
          <svg class="icon" preserveAspectRatio="xMaxYMin">
            <use xlink:href="{{ path '/static/symbol-defs.svg#icon-{{ symbol }}' }}" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0"></use>
          </svg>
        </i>
      </div>
    </div>
    <div class="col">
      <div class="border border-dark rounded p-2">
        <i class="svg-icon text-primary" title="{{ title }}">
          <svg class="icon" preserveAspectRatio="xMaxYMin">
            <use xlink:href="{{ path '/static/symbol-defs.svg#icon-{{ symbol }}' }}" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0"></use>
          </svg>
        </i>
      </div>
    </div>
    <div class="col">
      <div class="border border-dark rounded p-2">
        <i class="svg-icon text-success" title="{{ title }}">
          <svg class="icon" preserveAspectRatio="xMaxYMin">
            <use xlink:href="{{ path '/static/symbol-defs.svg#icon-{{ symbol }}' }}" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0"></use>
          </svg>
        </i>
      </div>
    </div>
    <div class="col">
      <div class="border border-dark rounded p-2">
        <i class="svg-icon text-danger" title="{{ title }}">
          <svg class="icon" preserveAspectRatio="xMaxYMin">
            <use xlink:href="{{ path '/static/symbol-defs.svg#icon-{{ symbol }}' }}" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0"></use>
          </svg>
        </i>
      </div>
    </div>
    <div class="col">
      <div class="border border-dark rounded p-2">
        <i class="svg-icon text-info" title="{{ title }}">
          <svg class="icon" preserveAspectRatio="xMaxYMin">
            <use xlink:href="{{ path '/static/symbol-defs.svg#icon-{{ symbol }}' }}" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0"></use>
          </svg>
        </i>
      </div>
    </div>
    <div class="col"></div>
    <div class="col">
      <div class="border border-dark rounded p-3">
        <i class="svg-icon svg-icon--large" title="{{ title }}">
          <svg class="icon" preserveAspectRatio="xMaxYMin">
            <use xlink:href="{{ path '/static/symbol-defs.svg#icon-{{ symbol }}' }}" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0"></use>
          </svg>
        </i>
      </div>
    </div>
    <div class="col">
      <div class="border border-dark rounded p-4">
        <i class="svg-icon svg-icon--xl" title="{{ title }}">
          <svg class="icon" preserveAspectRatio="xMaxYMin">
            <use xlink:href="{{ path '/static/symbol-defs.svg#icon-{{ symbol }}' }}" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0"></use>
          </svg>
        </i>
      </div>
    </div>
    <div class="col">
      <div class="border border-dark rounded p-5">
        <i class="svg-icon svg-icon--xxl" title="{{ title }}">
          <svg class="icon" preserveAspectRatio="xMaxYMin">
            <use xlink:href="{{ path '/static/symbol-defs.svg#icon-{{ symbol }}' }}" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0"></use>
          </svg>
        </i>
      </div>
    </div>
  </div>
{{/each}} -->
