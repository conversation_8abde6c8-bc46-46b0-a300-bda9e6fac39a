const iconData = require('../../assets/icons.json');
module.exports = {
	title: "Icons",
  context: {
    symbol: 'plus',
    title: `Plus icon`,
    items: iconData.icons.map((icon, index) => ({
        name: index === 0 ? 'default' : icon.properties.name,
        label: `${icon.properties.name.charAt(0).toUpperCase()}${icon.properties.name.slice(1)}`,
        symbol: icon.properties.name,
        title: `${icon.properties.name} icon`,
      })
    ),
    variants: [
      { className: 'text-primary' },
      { className: 'text-success' },
      { className: 'text-error' },
      { className: 'svg-icon--large' },
      { className: 'svg-icon--xl' },
    ]
  }
}
