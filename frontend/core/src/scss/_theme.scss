:root,
*::before,
*::after {
  --brand-color-primary-dark: #{$primary-dark}; // #5e7a00
  --brand-color-primary-light: #{$primary-light}; // #789414
  --brand-color-secondary-dark: #{$secondary-dark}; // #49083a
  --brand-color-secondary-light: #{$secondary-light}; // #7b1463
  --brand-color-tertiary-dark: #{$tertiary-dark}; // #000000
  --brand-color-tertiary-light: #{$tertiary-light}; // #585856

  --brand-color-primary-variant-dark: #{$primary-variant-dark}; // #B0B108
  --brand-color-primary-variant-light: #{$primary-variant-light}; // #C8C920

  --brand-color-primary: var(--brand-color-primary-dark);
  --brand-color-secondary: var(--brand-color-secondary-dark);
  --brand-color-tertiary: var(--brand-color-tertiary-dark);

  --brand-color-signal-success: #{$success}; // #5e7a00
  --brand-color-signal-info: #{$info}; // #0071ad
  --brand-color-signal-warning: #{$warning}; // #e06500
  --brand-color-signal-danger: #{$danger}; // #c82314

  --brand-color-dark: #4e4e4e;
  --brand-color-light: #fafafa;
  --brand-color-black: #000;
  --brand-color-white: #fff;

  --body-color: #4e4e4e;
  --body-background-color: var(--brand-color-white);

  --link-color: var(--brand-color-secondary);
  --link-disabled-color: #{$link-disabled-color};

  --headline-font-family: 'Ropa-Sans';

  --border-color: var(--bs-border-color);
  --border-width: var(--bs-border-width);
  --border-radius: var(--bs-border-radius);

//   --vnc-accent-gradient-dark-start: #789314;
//   --vnc-accent-gradient-dark-stop: #5A7800;
//   --vnc-accent-gradient-light-start: #C8C920;
//   --vnc-accent-gradient-light-stop: #B0B108;

//   --brand-accent-gradient-01: linear-gradient(0deg, var(--vnc-accent-gradient-dark-start) 0%, var(--vnc-accent-gradient-dark-stop) 100%)
//   --brand-accent-gradient-02: linear-gradient(0deg, var(--vnc-accent-gradient-light-start) 0%, var(--vnc-accent-gradient-light-stop) 100%);

  --brand-gradient-primary-start: var(--brand-color-primary-light);
  --brand-gradient-primary-stop: var(--brand-color-primary-dark);
  --brand-gradient-primary: linear-gradient(180deg, var(--brand-gradient-primary-start) 0%, var(--brand-gradient-primary-stop) 100%);

  --brand-gradient-primary-variant-start: var(--brand-color-primary-variant-light);
  --brand-gradient-primary-variant-stop: var(--brand-color-primary-variant-dark);
  --brand-gradient-primary-variant: linear-gradient(180deg, var(--brand-gradient-primary-variant-start) 0%, var(--brand-gradient-primary-variant-stop) 100%);

  --brand-gradient-secondary-start: var(--brand-color-secondary-light);
  --brand-gradient-secondary-stop: var(--brand-color-secondary-dark);
  --brand-gradient-secondary: linear-gradient(180deg, var(--brand-gradient-secondary-start) 0%, var(--brand-gradient-secondary-stop) 100%);
}

.theme--light {
  --body-color: var(--brand-color-primary);
  --body-background-color: var(--brand-color-white);
}

.theme--dark {
  --body-color: var(--brand-color-light);
  --body-background-color: var(--brand-color-primary);
}

