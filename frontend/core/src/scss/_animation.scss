.bounce-enter-active {
    animation: bounce-in 0.32s;
}
.bounce-leave-active {
    animation: bounce-in 0.2s reverse;
}
@keyframes bounce-in {
    0% {
        transform: scale(0);
    }
    50% {
        transform: scale(1.25);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes morph {
    0% {
        border-radius: 40% 60% 60% 40% / 70% 30% 70% 30%;
    }
    100% {
        border-radius: 40% 60%;
    }
}

@keyframes spin {
    to {
        transform: rotate(1turn);
    }
}

.stone {
    position: fixed;
    width: 100%;
    height: 100%;
    background-size: contain;
    background-repeat: no-repeat;
    background-attachment: fixed;
    z-index: -1;
    will-change: transform;
    transform-origin: 50% 50%;

    &__first {
        background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyBpZD0iRWJlbmVfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9IjAgMCA2MDAgNjAwIj4KICA8IS0tIEdlbmVyYXRvcjogQWRvYmUgSWxsdXN0cmF0b3IgMjkuMy4wLCBTVkcgRXhwb3J0IFBsdWctSW4gLiBTVkcgVmVyc2lvbjogMi4xLjAgQnVpbGQgMTQ2KSAgLS0+CiAgPHBhdGggZD0iTTQxNi41LDYwMEMxNTMuOCw2MDAsMCw0ODYuNiwwLDI4Ni40UzE5OSwwLDMxNCwwQzU5Mi43LDAsNjAwLDIzMC40LDYwMCwzMjEuNnMtMy44LDE1Ny4zLTQxLjgsMjExLjctODkuNCw2Ni43LTE0MS43LDY2LjdaIiBmaWxsPSIjYjliYWFiIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGlzb2xhdGlvbj0iaXNvbGF0ZSIgb3BhY2l0eT0iLjEiLz4KICA8bWV0YWRhdGE+CiAgICA8c2Z3IHhtbG5zPSJodHRwOi8vbnMuYWRvYmUuY29tL1NhdmVGb3JXZWIvMS4wLyI+CiAgICAgIDxzbGljZXMvPgogICAgICA8c2xpY2VTb3VyY2VCb3VuZHMgeD0iMCIgeT0iMCIgd2lkdGg9IjYwMCIgaGVpZ2h0PSI2MDAiIGJvdHRvbUxlZnRPcmlnaW49InRydWUiLz4KICAgIDwvc2Z3PgogIDwvbWV0YWRhdGE+Cjwvc3ZnPg==");
        left: 5rem;
        bottom: 2rem;
        height: 700px;
        width: 600px;
        fill: #b9baab;
    }

    &__second {
        background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyBpZD0iRWJlbmVfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9IjAgMCA2MDAgNjAwIj4KICA8IS0tIEdlbmVyYXRvcjogQWRvYmUgSWxsdXN0cmF0b3IgMjkuMy4wLCBTVkcgRXhwb3J0IFBsdWctSW4gLiBTVkcgVmVyc2lvbjogMi4xLjAgQnVpbGQgMTQ2KSAgLS0+CiAgPHBhdGggZD0iTTAsNDE2LjVDMCwxNTMuOCwxMTMuNCwwLDMxMy42LDBzMjg2LjQsMjE5LDI4Ni40LDMxNGMwLDI3OC43LTIzMC40LDI4Ni0zMjEuNiwyODZzLTE1Ny4zLTMuOC0yMTEuNy00MS44UzAsNDY4LjgsMCw0MTYuNVoiIGZpbGw9IiNiOWJhYWIiIGZpbGwtcnVsZT0iZXZlbm9kZCIgaXNvbGF0aW9uPSJpc29sYXRlIiBvcGFjaXR5PSIuMiIvPgogIDxtZXRhZGF0YT4KICAgIDxzZncgeG1sbnM9Imh0dHA6Ly9ucy5hZG9iZS5jb20vU2F2ZUZvcldlYi8xLjAvIj4KICAgICAgPHNsaWNlcy8+CiAgICAgIDxzbGljZVNvdXJjZUJvdW5kcyB4PSIwIiB5PSIwIiB3aWR0aD0iNjAwIiBoZWlnaHQ9IjYwMCIgYm90dG9tTGVmdE9yaWdpbj0idHJ1ZSIvPgogICAgPC9zZnc+CiAgPC9tZXRhZGF0YT4KPC9zdmc+");
        right: -5rem;
        bottom: -4rem;
        height: 1000px;
        width: 1300px;
        fill: #b9baab;
    }

    @media (max-width: 1199px) {
        display: none;
    }

    @media (min-width: 1200px) and (max-width: 1399px) {
        &__first {
            height: 600px;
            width: 500px;
        }

        &__second {
            height: 600px;
            width: 900px;
            bottom: -6rem;
        }
    }
    @media (min-width: 1400px) and (max-width: 1600px) {
        &__first {
            height: 650px;
            width: 550px;
        }

        &__second {
            right: -7rem;
            height: 800px;
            width: 1200px;
        }
    }
}

.animation-controls {
    position: fixed;
    bottom: 1.5rem;
    left: 1rem;
    z-index: 9999;
    display: flex;
    align-items: flex-start;

    @media (max-width: 1200px){
        display: none;
    }
    .toggle-animations {
      height: 3rem;
      width: 3rem;
      padding: 0.5rem;
      border: 2px solid var(--brand-color-white);
      color: var(--brand-color-white);
      cursor: pointer;
      display: flex;
      align-items: center;
      overflow: hidden;
      transition: width 0.3s ease-in-out, color 0.3s ease-in-out;

      .label-text {
        opacity: 0;
        margin-left: 0.2rem;
        font-size: 1.5rem;
        padding: 0.5rem;
        white-space: nowrap;
        transition: opacity 0.3s ease-in-out;
      }

      .animation-icon {
        padding: 0.2rem;
      }

      &.--expanded {
        width: 16rem;
        justify-content: flex-start;
        padding-left: 1rem;

        .label-text {
          opacity: 1;
        }
      }

      &:hover,
      &:focus-visible {
        color: var(--brand-color-light);
      }

&:focus-visible {
        outline: 1px solid var(--brand-color-light);
      }
    }
  }
