// Override Bootstrap's Sass default variables
//
// Nearly all variables in Bootstrap are written with the `!default` flag.
// This allows you to override the default values of those variables before
// you import Bootstrap's source Sass files.
//
// Overriding the default variable values is the best way to customize your
// CSS without writing _new_ styles. For example, change you can either change
// `$body-color` or write more CSS that override's Bootstrap's CSS like so:
// `body { color: red; }`.

// Toggle global options
@import 'colors.module';

$enable-gradients: false;
$enable-shadows: true;
$enable-rfs: false;

$border-radius: 0;
$border-width: .125rem;

$font-size-root: 1rem;
$font-size-base: 1.25rem;
$font-size-sm: $font-size-base * .875;
$font-size-lg: $font-size-base * 1.25;

$font-sizes: (
  xl: $font-size-base * 2.5,
  l: $font-size-base * 1.75,
  m: $font-size-base * 1.25,
  s: $font-size-base * 1.125,
  xs: $font-size-base * 1,
  xxs: $font-size-base * .875,
);

$h1-font-size: $font-size-root * 3;
$h2-font-size: $font-size-root * 2.5;
$h3-font-size: $font-size-root * 1.75;
$h4-font-size: $font-size-root * 1.5;
$h5-font-size: $font-size-root * 1.125;
$h6-font-size: $font-size-root * 1;

$headings-line-height: .96;
$headings-font-weight: 500;

$display-font-sizes: (
  1: $h1-font-size,
  2: $h2-font-size,
  3: $h3-font-size,
  4: $h4-font-size,
  5: $h5-font-size,
  6: $h6-font-size
);

$display-line-height: $headings-line-height;
$display-font-weight: $headings-font-weight;

$font-family-base: 'Ropa-Sans', Arial, sans-serif;
$font-family-headline: 'Ropa-Sans', Impact, sans-serif;

$btn-font-weight: bold;
$btn-border-width: $border-width;

$btn-border-radius: $border-radius;
$btn-border-radius-sm: $border-radius;
$btn-border-radius-lg: $border-radius;

// $border-color: #C4C4C4;

$modal-content-border-width: 0;
$modal-content-border-radius: $border-radius;
$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width);
$modal-content-box-shadow-xs: none;
$modal-content-box-shadow-sm-up: none;

$modal-backdrop-bg: #454545;
$modal-backdrop-opacity: .6;

$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
  xxl: 1320px,
);

.container-xxxl {
  width: 100%;
  max-width: 1920px;
  padding-inline: 0;
  margin-inline: auto;
  aspect-ratio: 3 / 1;
}

$spacer: 1rem;
$spacers: (
  0: 0,
  1: $spacer * .25,
  2: $spacer * .5,
  3: $spacer,
  4: $spacer * 1.5,
  5: $spacer * 3,
  6: $spacer * 5,
  7: $spacer * 7.5,
  8: $spacer * 10
);
// $enable-negative-margins: true;

$nav-breakpoint: xl;

$page-header-meta-height: 3rem;
$page-header-main-sm-height: 4rem;
$page-header-main-lg-height: 7rem;
$page-header-height: #{$page-header-main-sm-height} + #{$page-header-meta-height};
