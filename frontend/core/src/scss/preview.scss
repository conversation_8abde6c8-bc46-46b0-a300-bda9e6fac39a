@charset "UTF-8";
:root {
  --background: #fbfbfb;
  --text: #554d56;
  --deemphasized: #979197;
  --selected-bg: rgba(151, 145, 151, 0.2);
  --drop-shadow: rgba(193, 190, 193, 0.4);
  --preview-bg: #ffffff;
  --skin-links: rgba(211, 172, 63, 1);
}

html {
  color: var(--text);
  font: 15px/1.5 Helvetica, Arial, sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 500!important;
  color: inherit;
}

b, strong {
  font-weight: 500;
}

.Frame-panel--sidebar {
  background-color: var(--background);
}
[dir=ltr] .Frame-panel--sidebar {
  border-inline-end: 0;
}

.Frame,
.Pen,
.Preview {
  background-color: var(--preview-bg);
}
.Frame-handle,
.Pen-handle,
.Preview-handle {
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}
.Frame-handle:hover::before,
.Pen-handle:hover::before,
.Preview-handle:hover::before {
  content: "⋮";
}

.Header {
  background-color: var(--background);
  color: inherit;
  box-shadow: var(--drop-shadow) 0 1px 0 0;
  padding: 2px 0;
}
.Header-button {
  color: var(--deemphasized);
  margin-inline-end: -3rem;
}
.Header-title {
  font-size: 16px;
  margin-inline: auto;
}
.Tree-entityLink {
  color: inherit;
}

// .Tree-title {
//   font-size: inherit;
//   text-transform: capitalize;
//   color: inherit;
//   font-weight: 500;
//   letter-spacing: unset;
// }
// .Tree-item {
//   font-size: inherit;
// }
// .Tree-item.is-current {
//   background-color: var(--selected-bg);
// }
// .Tree-item.is-current .Tree-entityLink {
//   background-color: unset;
// }
// .Tree-entityLink {
//   color: inherit;
//   padding-block-start: 0.2rem;
//   padding-block-end: 0.2rem;
// }
// .Tree-collectionLabel {
//   font-size: inherit;
// }
// .Tree-collectionItems {
//   margin-block-end: 0.3rem;
// }
// [dir=ltr] .Tree .Tree-entityLink, [dir=ltr] .Tree .Tree-collectionLabel {
//   padding-inline-start: 3rem;
// }
// [dir=ltr] .Tree .Tree-entityLink::before, [dir=ltr] .Tree .Tree-collectionLabel::before {
//   left: 2rem;
// }
// [dir=ltr] .Tree .Tree-collectionItems .Tree-entityLink, [dir=ltr] .Tree .Tree-collectionItems .Tree-collectionLabel {
//   padding-inline-start: 4rem;
// }
// [dir=ltr] .Tree .Tree-collectionItems .Tree-entityLink::before, [dir=ltr] .Tree .Tree-collectionItems .Tree-collectionLabel::before {
//   left: 3rem;
// }
// [dir=ltr] .Tree .Tree-collectionItems .Tree-collectionItems .Tree-entityLink, [dir=ltr] .Tree .Tree-collectionItems .Tree-collectionItems .Tree-collectionLabel {
//   padding-inline-start: 5rem;
// }
// [dir=ltr] .Tree .Tree-collectionItems .Tree-collectionItems .Tree-entityLink::before, [dir=ltr] .Tree .Tree-collectionItems .Tree-collectionItems .Tree-collectionLabel::before {
//   left: 4rem;
// }
// [dir=ltr] .Tree .Tree-collectionItems .Tree-collectionItems .Tree-collectionItems .Tree-entityLink, [dir=ltr] .Tree .Tree-collectionItems .Tree-collectionItems .Tree-collectionItems .Tree-collectionLabel {
//   padding-inline-start: 6rem;
// }
// [dir=ltr] .Tree .Tree-collectionItems .Tree-collectionItems .Tree-collectionItems .Tree-entityLink::before, [dir=ltr] .Tree .Tree-collectionItems .Tree-collectionItems .Tree-collectionItems .Tree-collectionLabel::before {
//   left: 5rem;
// }
.Tree-items {
  padding-inline-start: 1rem;
  padding-inline-end: 1rem;
  margin-block-end: 0.5rem;
}

.Document-title {
  font-weight: 500;
}

.Preview {
  border: 0;
}
[dir=ltr] .Preview-handle {
  background-color: var(--background);
  border-color: var(--drop-shadow);
  border-inline-start: 0;
}

.Prose {
  max-width: 800px;
}

.Prose h1, .Prose h2, .Prose h3, .Prose h4, .Prose h5, .Prose h6 {
  font-weight: 500;
}

.Pen-preview {
  margin: 0;
}
.Pen-header {
  max-height: unset;
  padding: 1.5rem 2rem;
}
.Pen-title {
  font-size: 1.75rem;
  font-weight: 500;
}
.Pen-previewLink:active, .Pen-previewLink:hover, .Pen-previewLink:link, .Pen-previewLink:visited {
  color: inherit;
}
.Pen-previewLink svg {
  vertical-align: middle;
}
.Pen-handle:hover::before {
  content: "⋯";
}
.Pen-info {
  border: 0;
  margin: 0;
}

.Status-label {
  border-radius: 0.2rem;
}

.Browser-panel {
  background: var(--background);
}

#tree-docs .Tree-title,
#tree-components .Tree-title {
  background-size: contain;
  background-repeat: no-repeat;
  padding-inline-start: 2rem;
  margin-inline-start: -1rem;
  background-position: 0.2rem 50%;
}

#tree-docs .Tree-title,
#tree-components .Tree-title {
  font-weight: bold;
}

.bd-example-row .row > [class^="col-"] {
  padding-block-start: .75rem;
  padding-block-end: .75rem;
  background-color: rgba(39,41,43,0.03);
  border: 1px solid rgba(39,41,43,0.1);
}

@import "main";

//----------------------------------*\
// COMPONENTS
//----------------------------------*/
// @import '../components/**/*.scss';
@import '../components/atoms/00-icon/icon.scss';
@import '../components/atoms/02-link/links.scss';
@import '../components/atoms/03-button/button.scss';
@import '../components/atoms/06-progress-meter/progress-meter.scss';
@import '../components/molecules/00-alert/alert.scss';
@import '../components/templates/accordion/accordion.scss';
@import '../components/molecules/auto-suggest/auto-suggest.scss';
@import '../components/molecules/breadcrumb/breadcrumb.scss';
@import '../components/molecules/carousel/carousel.scss';
@import '../components/molecules/gallery/gallery.scss';
@import '../components/molecules/lightbox/lightbox.scss';
@import '../components/molecules/nav/nav.scss';
@import '../components/molecules/pagination/pagination.scss';
@import '../components/molecules/tooltip/tooltip.scss';
@import '../components/molecules/table/tables.scss';
@import '../components/molecules/teaser/teaser.scss';
@import '../components/molecules/form-elements/input/input.scss';
@import '../components/molecules/form-elements/input-range/input-range.scss';
@import '../components/organisms/basic-form/basic-form.scss';
@import '../components/organisms/page-footer/page-footer.scss';
@import '../components/organisms/page-header/page-header.scss';
@import '../components/organisms/rate-calculator/rate-calculator.scss';
@import '../components/organisms/split-stage/split-stage.scss';
@import '../components/organisms/stage/stage.scss';
@import '../components/templates/tabs/tabs.scss';
@import '../components/templates/modal/modal.scss';
@import '../components/templates/popover/popover.scss';
