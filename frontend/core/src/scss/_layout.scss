:root {
    --unit-100vh: 100vh;
}
@supports (height: 100dvh) {
    :root {
        --unit-100vh: 100dvh;
    }
}

body {
  overflow-x: hidden;
  height: var(--unit-100vh);

  display: flex;
  flex-direction: column;
}

.page-content {
  flex: 1 0 auto;
  display: flex;
  flex-direction: column;
}
body:has(.nav--main.offcanvas.show) {
    .page-content {
        filter: blur(.25rem);
    }
}

.page-section {
  margin: 0 0 3rem;
  &__body {
      margin: 0 auto;
  }
}

@mixin make-container($padding-x: $container-padding-x) {
  width: 100%;
  max-width: var(--content-max-width);
  padding-inline: $padding-x;
  margin-inline: auto;
}

.container--modal {
  @each $name, $value in $grid-breakpoints {
    @if $value!= 0 {
      @media all and (min-width: $value) {
        --content-max-width: #{map-get($container-max-widths, #{$name})};
      }
    }
  }

  @include make-container();
}
