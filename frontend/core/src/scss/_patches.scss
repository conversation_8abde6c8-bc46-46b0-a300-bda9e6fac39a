body.preview {
    padding: 1rem 2rem;
    [data-bs-theme="dark"] {
        color: $body-color-dark;
        background-color: $body-bg-dark;
    }
    &--collated {
        padding: 0;
        .preview-collator {
            padding: 1rem 2rem;
        }
    }
}

.preview-target-icon {
    .preview-collator > .d-block {
        display: inline-block !important;
    }
}

.brand-logo {
    display: block;
    flex: none;
    display: grid;
    place-content: center;
    svg {
        display: inline-block;
        stroke-width: 0;
        stroke: currentColor;
        fill: currentColor;

        aspect-ratio: 3.41666667 / 1;
        max-width: 100%;
        max-height: 100%;
        height: 3rem;
        @include media-breakpoint-up(xl) {
            height: 4rem;
        }
    }
}

.vnc-texttiles {
    .teaser__text > article > .svg-icon {
        display: grid;
        place-content: center;
        width: 5rem;
        height: 5rem;
        background: var(--brand-gradient-secondary);
        color: #fff;
        border-radius: 50%;
        margin-inline: auto;
        margin-block-end: 1rem;
        font-size: 2rem;
    }
    .teaser:hover,
    .teaser:focus,
    .teaser:focus-within {
        .teaser__text > article > .svg-icon {
            background: var(--brand-color-secondary);
        }
    }
}

.search-result__item {
    .link {
        // color: inherit !important;
    }
}

.vnc-texttiles {
    .headline {
        .svg-icon {
            display: none;
        }
    }
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
    // position: static!important;
    clip: auto;
    height: auto;
    width: 100%;
    overflow: auto;
    outline: none;
}
.sr-only-focusable {
    position: absolute !important;
    z-index: 101;
    height: 1px;
    width: 1px;
    overflow: hidden;
    clip: rect(1px, 1px, 1px, 1px);
    background: var(--brand-gradient-primary) !important;
    color: var(--brand-color-white) !important;
    display: block;
    padding: 0.5rem;
    text-align: center;
}

.carousel__controls:has(.carousel__prev[disabled] ~ .carousel__next[disabled]) {
    display: none;
}

.vnc_accordion__intro {
    margin-bottom: 2rem;
}

.tx-ttaddress {
    .teaser--card.teaser--container {
        .teaser__canvas {
            @include media-breakpoint-up(lg) {
                gap: 4rem;
            }
        }
    }
}

.powermail_fieldwrap_type_captcha {
    .powermail_field {
        display: flex;
        gap: 1.5rem;
        flex-direction: column;
        @include media-breakpoint-up(md) {
            flex-direction: row;
        }
    }
    .powermail_captcha,
    .powermail_captchaimage {
        height: var(--input-height);
        border: var(--border-width) solid var(--vnc-input-border-color);
        border-radius: var(--border-radius);
    }
    .powermail_captcha {
        width: 100%;
        color: var(--input-color);
        padding: 0 1rem;
    }
    form[data-validate] &:has(:invalid) {
        .input__error {
            display: block;
        }
    }
}

.input--file__file-list {
    color: #fff;
    margin-bottom: 1rem;
}

#SgCookieOptin
    .sg-cookie-optin-box
    .sg-cookie-optin-box-cookie-detail-sublist.sg-cookie-optin-visible {
    height: auto !important;
}

#SgCookieOptin .sg-cookie-optin-box .sg-cookie-optin-box-button button {
    &:focus,
    &:focus-visible {
        outline: 0.125rem solid var(--sg-cookie-optin-color-button-all);
        outline-offset: 0.125rem;
    }
}

.sg-cookie-optin-iframe-consent-accept {
    &:focus,
    &:focus-visible {
        outline: 0.125rem solid var(--sg-cookie-optin-color-button-all);
        outline-offset: 0.125rem;
    }
}

.sg-cookie-optin-iframe-consent-link {
    &:focus,
    &:focus-visible {
        outline: none;
        text-decoration: underline;
        text-underline-offset: 4px;
    }
}

.news-container .container .row nav ul {
    flex-wrap: wrap;
}
