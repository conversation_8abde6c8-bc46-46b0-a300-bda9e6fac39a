.aspect-ratio {
  position: relative;

  &--1-1 {
    aspect-ratio: 1 / 1;
  }

  &--2-1 {
    aspect-ratio: 2 / 1;
  }

  &--2-3 {
    aspect-ratio: 2 / 3;
  }

  &--3-1 {
    aspect-ratio: 3 / 1;
  }

  &--3-2 {
    aspect-ratio: 3 / 2;
  }

  &--3-4 {
    aspect-ratio: 3 / 4;
  }

  &--4-3 {
    aspect-ratio: 4 / 3;
  }

  &--5-2 {
    aspect-ratio: 5 / 2;
  }

  &--5-4 {
    aspect-ratio: 5 / 4;
  }

  &--16-9 {
    aspect-ratio: 16 / 9;
  }

  &--9-16 {
    aspect-ratio: 9 / 16;
  }
}


@each $name, $value in $grid-breakpoints {
  @media all and (min-width: $value) {
    .aspect-ratio-#{$name}--1-1 {
      aspect-ratio: 1 / 1;
    }

    .aspect-ratio-#{$name}--2-1 {
      aspect-ratio: 2 / 1;
    }

    .aspect-ratio-#{$name}--2-3 {
      aspect-ratio: 3 / 2;
    }

    .aspect-ratio-#{$name}--3-1 {
      aspect-ratio: 3 / 1;
    }

    .aspect-ratio-#{$name}--3-2 {
      aspect-ratio: 3 / 2;
    }

    .aspect-ratio-#{$name}--3-4 {
      aspect-ratio: 3 / 4;
    }

    .aspect-ratio-#{$name}--4-3 {
      aspect-ratio: 4 / 3;
    }

    .aspect-ratio-#{$name}--5-2 {
      aspect-ratio: 5 / 2;
    }

    .aspect-ratio-#{$name}--5-4 {
      aspect-ratio: 5 / 4;
    }

    .aspect-ratio-#{$name}--16-9 {
      aspect-ratio: 16 / 9;
    }

    .aspect-ratio-#{$name}--9-16 {
      aspect-ratio: 9 / 16;
    }
  }
}

.bg--primary {
  background-color: var(--brand-color-primary);
  color: var(--bs-white);
}

.bg--secondary {
  background-color: var(--brand-color-secondary);
  color: var(--bs-white);
}

.bg--tertiary {
  background-color: var(--brand-color-tertiary);
  color: var(--bs-white);
}

.has-overlay {
  &::after {
    content: '';
    position: absolute;
    inset-inline-start: 0;
    inset-block-start: 0;
    width: 100%;
    height: 100%;
    opacity: .5;
    background-color: $light;
  }
  &--dark {
    color: var(--brand-color-light);
    &::after {
      background-color: $dark;
    }
  }
  &--light {
    color: var(--brand-color-dark);
    &::after {
      background-color: $light;
    }
    &--dark {
      color: var(--brand-color-light);
      &::after {
        background-color: var(--brand-color-dark);
      }
    }
    &--light {
      color: var(--brand-color-dark);
      &::after {
        background-color: var(--brand-color-light);
      }
    }
  }
}

.backdrop {
  &--light {
    border-image: fill 0 linear-gradient(#0003,#000);
  }
  &--dark {
    border-image: fill 0 linear-gradient(#0003,#000);
  }
}

.bg-gradient-primary {
    background: var(--brand-gradient-primary) !important;
}

.bg-gradient-secondary {
    background: var(--brand-gradient-secondary) !important;
}

.justify-content-xxl-center {
    @include media-breakpoint-up(xxl) {
        justify-content: center;
    }
}

.h-lg-100 {
    @include media-breakpoint-up(lg) {
        height: 100%;
    }
}

.h-xl-100 {
    @include media-breakpoint-up(xl) {
        height: 100%;
    }
}
