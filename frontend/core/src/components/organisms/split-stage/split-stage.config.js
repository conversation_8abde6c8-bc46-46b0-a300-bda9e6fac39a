module.exports = {
    preview: '@preview-full',
    context: {
        reverse: false,
        gutter: true,
        ratio: '1-1',
        color: 'secondary',
        left: {
            title: 'Lorem ipsum dolor sit amet',
            text: 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet'
        },
        right: {
            img: {
                src: '/static/placeholder/vancado-placeholder--1-1.jpg'
            },
            text: {
                align: 'left',
                color: 'secondary',
                html: '<h3 class="open-borders__text headline h3">Three<br />Items<br />Headline</h3>',
            }
        }
    },
    variants: [
        {
            name: 'primary',
            context: {
                color: 'primary'
            }
        },
        {
            name: 'secondary',
            context: {
                color: 'secondary'
            }
        },
        {
            name: 'reverse',
            context: {
                reverse: true
            }
        },
        {
            name: 'no-gutter',
            context: {
                gutter: false
            }
        },
        {
            name: 'landscape',
            context: {
                ratio: '16-9',
                right: {
                    img: {
                        src: '/static/placeholder/vancado-placeholder--16-9.jpg'
                    }
                }
            }
        },
    ]
}
