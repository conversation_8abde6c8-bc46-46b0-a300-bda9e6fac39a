.split-stage {
    --content-column-max-width: none;
    // --content-column-gap-width: calc(#{$grid-gutter-width} / 2);
    --content-column-gap-width: 0px;

    @each $name, $value in $grid-breakpoints {
        @if $value != 0 {
            @media all and (min-width: $value) {
                --content-column-max-width: calc(
                    #{map-get($container-max-widths, #{$name})} / 2 - var(
                            --content-column-gap-width
                        ) / 2
                );
            }
        }
    }
}

.split-stage {
    position: relative;
    display: flex;
    width: 100%;
    flex-direction: column;

    &--reverse {
        flex-direction: column-reverse;
    }

    &.has-gutter {
        gap: var(--content-column-gap-width);
    }

    &__container {
        padding: 3rem 1rem;
    }

    &__item {
        position: relative;
        flex: 1 1 50%;

        &:first-child {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2;
            @include media-breakpoint-down(xl) {
                margin-top: -3rem;
                background-image: url("data:image/svg+xml,%3Csvg%20viewBox%3D%220%200%20360%20323%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20preserveAspectRatio%3D%22none%22%3E%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M0.000341286%2020.8385C24.9845%20-0.140416%2061.8298%20-4.19902%20168.895%203.85598C222.862%207.91612%20359.778%2020.8385%20359.778%2020.8385C359.778%2020.8385%20360.278%20219.138%20359.778%20285.071C248.346%20347.508%2067.9578%20321.035%200.00105991%20289.567C0.000876252%20229.128%20-0.00067208%2076.2824%200.000341286%2020.8385Z%22%20fill%3D%22%23F1F1EE%22%2F%3E%3C%2Fsvg%3E");
                background-repeat: no-repeat;
                background-size: 100%;
                background-color: transparent;
                background-position: bottom;
            }
            @include media-breakpoint-down(md) {
                margin-top: 0;
                background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22360%22%20height%3D%22265%22%20viewBox%3D%220%200%20360%20265%22%20fill%3D%22none%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M0%200.26471C0%200.26471%20270.443%20-0.331144%20359.778%200.265104C359.778%200.265104%20360.278%20177.434%20359.778%20233.022C248.346%20285.663%2067.9578%20263.343%200.00105819%20236.812C0.00087453%20185.856%200%200.26471%200%200.26471Z%22%20fill%3D%22%23F1F1EE%22%2F%3E%3C%2Fsvg%3E");
                background-size: cover;
            }
            @include media-breakpoint-up(xl) {
                background-color: #F1F1EE;
                &::after {
                    content: '';
                    display: block;
                    height: 100%;
                    width: 6%;
                    position: absolute;
                    right: 100%;
                    top: 0;
                    background-image: url("data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjAiIHk9IjAiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1NDAiIHZpZXdCb3g9IjAsIDAsIDUwLCA1NDAiPgogIDxnIGlkPSJFYmVuZV8xIj4KICAgIDxwYXRoIGQ9Ik01MCwwIEw1MCw1NDAgTDAsNTQwIEMwLDU0MCAxMDcuNTY1LDI3NyAwLDAgTDUwLDAgeiIgZmlsbD0iI0YxRjFFRSIvPgogIDwvZz4KPC9zdmc+Cg==");
                    background-repeat: no-repeat;
                    background-size: cover;

                    @-moz-document url-prefix() {
                        height: 101%;
                        top: -1%;
                    }
                }
            }
        }

        &:last-child {
            z-index: 1;
        }

        .split-stage__image {
            height: 100%;
            @include media-breakpoint-up(lg) {
                .media-container {
                    height: 100%;
                }
            }
        }
    }

    // @include media-breakpoint-down(xl) {
    //     &__item {
    //         &:first-child {
    //             background-image: url("data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20width='359'%20height='324'%20viewBox='0%200%20359%20324'%20fill='none'%3E%3Cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M0.000341286%2020.9791C24.9845%200.00020881%2061.8298%20-4.0584%20168.895%203.99661C222.862%208.05674%20359.778%2020.9791%20359.778%2020.9791C359.778%2020.9791%20360.278%20219.279%20359.778%20285.212C248.346%20347.649%2067.9578%20321.175%200.00105991%20289.707C0.000876252%20229.269%20-0.00067208%2076.423%200.000341286%2020.9791Z'%20fill='%23F1F1EE'/%3E%3C/svg%3E");
    //             position: relative;
    //             // top: 90%;
    //             left: 0;
    //             right: 0;
    //         }
    //     }
    // }

    @include media-breakpoint-up(xl) {
        justify-content: start;
        flex-direction: row;
        &__item {
            flex: 0 0 calc(50% - var(--content-column-gap-width) / 2);
            // &:first-child{
            //   position: absolute;
            //   right: 0;
            //   bottom: 0;
            //   top: 0;
            //   left: 47%;
            // }
        }

        &__canvas {
            position: relative;
        }

        &__container {
            // float: right;
            // padding: 3rem 3rem 3rem 3rem;
            width: 100%;
            max-width: var(--content-column-max-width);
            height: 100%;
            display: flex;
            align-items: center;
        }

        &--reverse {
            flex-direction: row-reverse;
            .split-stage__container {
                // float: left;
                // padding: 3rem 0 3rem 3rem;
            }
        }
    }
}
