export const EventList = (() => {
    window.addEventListener('DOMContentLoaded', () => {
        const accordions = document.querySelectorAll('[ data-vnc-events-accordion]');
        Array.from(accordions).forEach((accordion) => {
            let items;
            let itemsPerPage = 5;
            let page = 1;
            let pages = 0;
            let pagination;
            let buttonMore;
            let filter = [];
            let filterInputs;

            const initAccordion = (accordion) => {
                items = accordion.querySelectorAll('[data-vnc-events-accordion-item]');
                pagination = accordion.querySelector('[data-vnc-events-pagination]');
                buttonMore = accordion.querySelector('[data-vnc-events-list-button-more] button');

                const buttonFilter = accordion.querySelector('[data-vnc-events-list-filter] button');
                const filterContent = accordion.querySelector('[data-filter-content]');
                filterInputs = filterContent.querySelectorAll('input');


                setItemsPerPage();
                resetPages();
                executeFilter();
                render();

                buttonFilter.addEventListener('click', (e) => {
                    filterContent.toggleAttribute('data-show');
                });

                buttonMore.addEventListener('click', (e) => {
                    if (page >= pages) {
                        return;
                    }
                    page++;
                    render();
                });

                filterInputs.forEach(input => {
                    input.addEventListener('change', (e) => {
                        executeFilter();
                        resetPages();
                        render();
                    });
                });
            };

            const calcPages = () => {
                let count = 0;
                items.forEach((item) => {
                    const ariaHidden = item.getAttribute('aria-hidden');
                    if (ariaHidden === 'false') {
                        count++;
                    }
                });
                return Math.ceil(count / itemsPerPage);
            };

            const setPagination = () => {
                pagination.innerText = `${page} von ${pages}`;
            };

            const showItems = () => {
                const maxCount = page * itemsPerPage;

                items.forEach(item => item.removeAttribute('data-show'));
                Array.from(items)
                    .filter(item => item.getAttribute('aria-hidden') === 'false')
                    .forEach((item, i) => {
                        if (i + 1 <= maxCount) {
                            item.setAttribute('data-show', '');
                        }
                    })
                ;
            };

            const showButton = () => {
                if (page >= pages || pages === 1) {
                    buttonMore.setAttribute('data-hide', '');
                    buttonMore.disabled = true;
                } else {
                    buttonMore.removeAttribute('data-hide');
                    buttonMore.disabled = false;
                }
            };

            const setFilter = () => {
                filter.length = 0;
                let filtered = Array
                    .from(filterInputs)
                    .filter(input => input.checked)
                    .map(input => parseInt(input.value))
                ;
                if (filtered.length === 0) {
                    filtered = Array
                        .from(filterInputs)
                        .map(input => parseInt(input.value))
                    ;
                }
                filtered.forEach(value => filter.push(value))
            };

            const filterItems = () => {
                Array.from(items).forEach((item) => {
                    const category = parseInt(item.getAttribute('data-vnc-events-category'));
                    if (filter.indexOf(category) > -1) {
                        item.setAttribute('aria-hidden', 'false');
                    } else {
                        item.setAttribute('aria-hidden', 'true');
                    }
                });
            }

            const setItemsPerPage = () => {
                const prevItemsPerPage = itemsPerPage;
                if (window.innerWidth <= 991) {
                    itemsPerPage = 5;
                } else {
                    itemsPerPage = 10;
                }

                return prevItemsPerPage !== itemsPerPage;
            }

            const render = () => {
                showItems();
                setPagination();
                showButton();
            }

            const resetPages = () => {
                page = 1;
                pages = calcPages();
            }

            const executeFilter = () => {
                setFilter();
                filterItems();
            }

            initAccordion(accordion);
            window.addEventListener('resize', () => {
                if (setItemsPerPage()) {
                    resetPages();
                    render();
                }
            });
        });
    });

})();

export default EventList;
