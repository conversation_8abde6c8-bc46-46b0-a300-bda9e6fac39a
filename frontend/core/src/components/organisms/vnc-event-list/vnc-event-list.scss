[data-vnc-events-accordion] {
    [data-vnc-events-accordion-item] {
        display: none;

        &[data-show] {
            display: block;
        }

        button {
            &:focus-visible {
                outline: 0.125rem solid var(--brand-color-primary-dark);
            }

            .icon__info {
                min-width: 130px;
            }
        }

        .svg-icon {
            &.--grey {
                color: #575756;
            }
        }
        .fs-6 {
            font-size: 1rem;
        }
    }

    [data-vnc-events-list-filter] {
        position: relative;
        margin-bottom: 0.5rem;
        display: flex;
        flex-direction: row-reverse;

        [data-filter-content] {
            display: none;
            padding: 0.5rem;
            position: absolute;
            right: 0;
            top: calc(100% + 0.5rem);
            z-index: 1;
            background: var(--vnc-input-background-color);
            border: thin solid transparent;

            &[data-show] {
                display: block;
            }

            .event-input {
                display: grid;
                place-content: center;
                width: 1.77777em;
                height: 1.77777em;
                margin: 0;
                padding: 0;
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
                background-color: #fff;
                font: inherit;
                color: currentColor;
            }
        }
    }

    [data-vnc-events-list-button-more] {
        button {
            &[data-hide] {
                display: none !important;
            }
        }
    }
}
