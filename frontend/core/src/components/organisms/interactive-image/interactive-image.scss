.vncInteractiveImageContainer {
    .button {
        background: var(--brand-gradient-secondary) !important;
        transition: none;

        &:active,
        &:disabled {
            background-color: var(--brand-color-secondary-dark)!important;
            background-image: none!important;
            opacity: 1.0;
        }
    }

    .input--dropdown {
        .dropdown {
            .svg-icon {
                width: 2rem;
                height: 2rem;
                font-size: 2rem;
                background-image: none;
                color: var(--brand-color-secondary-light);
            }
        }
    }
}

.vncInteractiveImage {
    > .container,
    .image-container {
        .content-box {
            &__info {
                padding: 0;
                background: transparent;

                > div > div {
                    position: relative;

                    > svg {
                        position: absolute;
                        width: 30px;
                        height: 40px;
                        left: 6px;
                        top: 1px;
                    }
                }
            }

            &__nav {
                gap: 11px;

                .nav {
                    &-prev,
                    &-next {
                        &:hover {
                            path {
                                fill: rgba(73, 8, 58, 1) !important;
                            }
                        }

                        path {
                            fill: rgba(185, 186, 171, 1);
                        }
                    }

                    &-points {
                        .nav-point {
                            width: 16px;
                            height: 16px;
                            background-color: #575756;

                            &.active {
                                scale: 1.5;
                                background: var(--brand-gradient-secondary) !important;
                            }
                        }
                    }
                }
            }

            &__popover {
                .row {
                    p:not(.mark-icon) {
                        font-size: 28px;
                    }
                }
            }

            &__text {
                margin-left: 12px !important;
                background: linear-gradient(0deg, #5A7800 0%, #789314 100%);
                -webkit-background-image: -webkit-linear-gradient(0deg, #5A7800 0%, #789314 100%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                font-size: 28px;

                @media (max-width: 768px) {
                    font-size: 24px;
                }
            }

            p:not(.content-box__text) {
                @media (max-width: 1200px) {
                    font-size: 18px;
                }
            }

            .mark-icon {
                position: relative;
                display: block !important;
                width: 30px;
                height: 40px;
                // padding: 10px 0;
                margin: 0 0 0 6px;
                background-color: transparent;
                line-height: 15px;
                text-align: center;

                &:before {
                    display: block;
                    padding-top: 10px;
                }

                svg {
                    position: absolute;
                    width: 30px;
                    height: 40px;
                    top: 0;
                    left: 0;
                    z-index: -1;
                }
            }
        }

        .zoom-controls {
            bottom: 16px;
            right: 16px;
            z-index: 1;

            button {
                color: transparent;

                &:hover {
                    svg {
                        stop {
                            stop-color: var(--brand-color-secondary-dark);
                        }
                    }
                }

                &.zoom-in {
                    background-color: transparent;
                    margin-bottom: 8px;
                }

                &.zoom-out {
                    background-color: transparent;
                    background-size: cover;
                }

                &#fullscreen {
                    background-color: transparent;
                    margin-top: 8px;
                }

                @media (max-width: 767.98px) {
                    width: 40px;
                    height: 40px;
                }

                @media (max-width: 575.98px) {
                    width: 40px;
                    height: 40px;
                }
            }
        }
    }

    .image-container {
        img {
            height: auto;
        }

        .marks-container {

            .content-box {
                &.content-box__popover {
                    .mark-icon {
                        position: relative;
                        font-size: inherit;
                    }
                }
            }

            .mark {
                width: 45px;
                height: 60px;
                background-size: cover;

                &[data-selection="formelement"] {
                    .content-box {
                        &__popover {
                            > div > div {
                                position: relative;

                                > svg {
                                    top: 4px;
                                    left: 21px;
                                }
                            }
                        }
                    }
                }

                &:hover {
                    .mark-icon {
                        transform: none;
                        scale: 1.15;

                        &--number {
                            transform: none;
                            scale: none;
                            padding-top: 5px;

                            > svg {
                                transform: scale(1.1);
                                transform-origin: center bottom;
                            }
                        }

                        + svg {
                            scale: 1.15;
                        }
                    }

                    svg {
                        stop {
                            stop-color: var(--brand-color-secondary-dark);
                        }
                    }
                }

                svg {
                    position: absolute;
                    width: 45px;
                    height: 60px;
                    top: 0;
                    left: 0;
                    z-index: -1;
                }

                &-icon {
                    background-color: transparent;
                    font-size: 28px;

                    @media (max-width: 991.98px) {
                        width: 42px;
                        height: 42px;
                    }
                }

                .content-box__popover {
                    svg {
                        width: 30px;
                        height: 40px;
                    }

                    /*
                    .mark-icon--formelement + svg {
                        position: absolute;
                        left: 30px;
                        width: 30px;
                        height: 40px;
                    }

                     */
                }
            }
        }
    }

    .image-container .marks-container .mark-icon {
        background-color: transparent;

        &--formelement {
            width: 36px;
            height: 36px;
            margin-top: 2px;

            @media (max-width: 991.98px) {
                width: 36px;
                height: 36px;
            }
        }
    }

    &[data-layout="popover"] {
        .content-box {
            &__info {
                h3 {
                    background: linear-gradient(0deg, #5A7800 0%, #789314 100%);
                    -webkit-background-image: -webkit-linear-gradient(0deg, #5A7800 0%, #789314 100%);
                    background-clip: text;
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                }

                .info-item {
                    > div:nth-child(1) {
                        position: relative;

                        > svg {
                            width: 42px;
                            z-index: 0;
                        }
                    }
                }

                .mark-icon {
                    position: relative;
                    background-color: transparent;
                    text-align: center;

                    svg,
                    + svg {
                        position: absolute;
                        top: -3px;
                        left: 0;
                        width: 100%;
                        height: auto;
                        z-index: -1;
                    }
                }
            }
        }
    }
}

@media (max-width: 1024px) {
    .vncInteractiveImage .image-container {
        position: relative;
        width: 100%;
    }
    .vncInteractiveImage .content-box__popover {
        padding: 1.5rem;
        width: 100%;
        max-width: 100%;
        height: auto;
        max-height: 100%;
        bottom: 0;

        > div > div {
            position: relative;

            > svg {
                position: absolute;
                top: 1px;
                left: 18px;
                width: 30px;
                height: 40px;
            }
        }
    }

    .vncInteractiveImage .content-box__close {
        padding: 1.5rem;
        margin-top: -20px;
    }
}
