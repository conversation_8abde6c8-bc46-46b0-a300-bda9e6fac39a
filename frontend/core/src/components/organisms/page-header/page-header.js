import { Offcanvas } from "bootstrap";
import Headroom from "@pluginjs/headroom";
import "@pluginjs/headroom/dist/headroom.css";

export const StickyHeader = (() => {
    window.addEventListener("load", function () {
        const windowWidth = window.innerWidth;
        const header = document.querySelector("[data-sticky-header]");

        if (!header) return;

        document.documentElement.dataset.withStickyHeader = true;

        const headroom = new Headroom(header, {
            offset: 100,
            tolerance: {
                down: 10,
                up: 30,
            },
            classes: {
                initial: "page-header",
                pinned: "page-header--pinned",
                unpinned: "page-header--unpinned",
            },
        });

        const toggleHeadroom = () => {
          if(windowWidth <= 1025) {
            headroom.destroy();
          }
        };

        toggleHeadroom();
        // window.addEventListener("resize", toggleHeadroom);
    });
})(window, document);

export default StickyHeader;
