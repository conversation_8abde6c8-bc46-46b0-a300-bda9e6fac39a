:root,
*::before,
*::after {
  --page-header-meta-height: #{$page-header-meta-height};
  --page-header-main-sm-height: #{$page-header-main-sm-height};
  --page-header-main-lg-height: #{$page-header-main-lg-height};
  --page-header-height: calc(var(--page-header-main-sm-height) + var(--page-header-meta-height));
  @include media-breakpoint-up(xl) {
    --page-header-height: calc(var(--page-header-main-lg-height) + var(--page-header-meta-height));
  }

  --page-header-background-color: #F1F1EE;

  --page-header-gradient: linear-gradient(0deg, var(--brand-color-primary-dark) 0%, var(--brand-color-primary-light) 100%);
}

.page-header {
  display: flex;
  flex-direction: column;
  background-color: var(--page-header-background-color);
  z-index: 10;
  transition: transform 0.3s ease-in-out;
  &.page-header--unpinned {
    transform: translateY(-100%);
  }

  &.page-header--pinned {
    transform: translateY(0);
  }
  &__main {
    height: var(--page-header-main-sm-height);
    .brand-link {
        position: absolute;
        left: .75rem;
        top: 0;
        z-index: 1;
        height: 100%;
        @include media-breakpoint-up($nav-breakpoint) {
            left: 0;
            top: calc(var(--page-header-height) - var(--page-header-main-lg-height) );
            height: var(--page-header-main-lg-height);
        }
    }
    @include media-breakpoint-up($nav-breakpoint) {
      height: auto;
      position: relative;
    }
  }

  &__meta {
    @include media-breakpoint-up($nav-breakpoint) {
        height: var(--page-header-meta-height);
        background: var(--page-header-gradient);
    }
  }

  &__nav {
    @include media-breakpoint-down($nav-breakpoint) {
        height: 0;
    }
    @include media-breakpoint-up($nav-breakpoint) {
        height: var(--page-header-main-lg-height);
        display: flex;
        place-content: center;
        background-color: var(--page-header-background-color);
        // filter: drop-shadow(0px 6px 2px rgba(185,186,171,0.6));
        box-shadow: 0 .375rem .375rem #57575620;

        .container-xl {
            height: 100%;
        }

        .nav__list {
            justify-content: flex-end;
            height: 100%;
            .nav__list {
                justify-content: unset;
            }
        }
    }
  }

  .navbar-toggler {
    &[aria-expanded=true] {
      .nc-menu-8 { display: none; }
      .nc-e-remove { display: block; }
    }
    &[aria-expanded=false] {
      .nc-menu-8 { display: block; }
      .nc-e-remove { display: none; }
    }
  }

  .tx-indexedsearch-form {
    label {
        display: none;
    }
  }
}

.offcanvas-end {
  @include media-breakpoint-down(xl) {
    transform: translateX(100%);
    transition: transform 180ms ease-in-out;
    &.show {
      transform: translateX(0);
    }
  }

  .triangle {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: -31px;
    z-index: 1;
    filter: drop-shadow(0 6px 2px #57575620);
    display: none;
    svg {
        width: 32px;
        height: 32px;
    }
  }
  &:has(.is-selected) {
    @include media-breakpoint-up(xl) {
        .triangle {
            display: block;
        }
    }
  }
}


html[data-with-sticky-header] {
  scroll-padding-top: var(--page-header-height);
  body {
    scroll-padding-top: var(--page-header-height);
    padding-block-start: var(--page-header-height);
  }

  .page--main {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - var(--page-header-height));
  }
}
