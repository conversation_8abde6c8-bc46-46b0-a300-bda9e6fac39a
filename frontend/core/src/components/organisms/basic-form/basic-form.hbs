<div class="form {{ className }}">
  {{#> @form }}
    <div class="row">
      <div class="col-12 col-md-6">
        {{> @input-dropdown dropdown className="mb-3" }}
      </div>
    </div>

    <div class="row">
      <div class="col-12 col-md-6 py-4">
        {{> @radio-group radioGroup row=true required=true }}
      </div>
      <div class="col-12 col-md-6 py-4">
        <div class="d-flex gap-3">
          {{> @input-checkbox id="Checkbox1" label="Mandatory" required=true }}
          {{> @input-checkbox id="Checkbox2" label="Some checkbox" }}
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-12 col-md-6 py-3">
        {{> @input label="First name" color="primary" type="text" name="firstName" id="firstName" required=true }}
      </div>
      <div class="col-12 col-md-6 py-3">
        {{> @input label="Last name" color="primary" type="text" name="lastName" id="lastName" required=true }}
      </div>
      <div class="col-12 col-md-6 py-3">
       {{> @input label="Address" color="primary" type="text" name="address" id="address" required=true }}
     </div>
     <div class="col-12 col-md-6 py-3">
       {{> @input label="House Number" color="primary" type="text" name="houseNumber" id="houseNumber" required=true }}
     </div>
     <div class="col-12 col-md-6 py-3">
       {{> @input label="Post code" color="primary" type="text" name="postCode" id="postCode" required=true }}
     </div>
     <div class="col-12 col-md-6 py-3">
       {{> @input label="Country" color="primary" type="text" name="country" id="country" required=true }}
     </div>
     <div class="col-12 col-md-6 py-3">
      {{> @input label="Phone number" color="primary" type="text" name="phoneNumber" id="phoneNumber" required=true }}
    </div>
    <div class="col-12 col-md-6 py-3">
      {{> @input label="E-Mail address" color="primary" type="text" name="emailAddress" id="emailAddress" required=true }}
    </div>

      <div class="col-12 col-md-12 py-4">
        {{> @textarea label="Message" color="primary" type="text" name="message" id="message" required=true }}
      </div>
    </div>

    <div class="row">
      <div class="col-12 col-md-6">
        {{> @input-file
          label="File upload"
          color="light"
          animated=true
          name="fileUpload"
          id="fileUpload"
          required=true
          action=null
        }}
      </div>
    </div>

    <div class="text-center my-4">
      {{> @button type="submit" text="Form input" color="light" animated="true" }}
    </div>
  {{/@form }}
</div>
