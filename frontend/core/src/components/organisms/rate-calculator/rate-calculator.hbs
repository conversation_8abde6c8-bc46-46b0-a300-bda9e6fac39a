<div class="rate-calculator">
  {{#> @tabs tabs }}
    {{#each items}}
      <div class="tab-group__item">
        <form action="{{ ../../action }}">
          <div class="py-3">
            <input type="hidden" name="energyType" value="{{ tabItem.energyType }}">
            <div class="row align-items-center">
              <div class="col-6 col-lg-3">
                {{> @input (contextData '@input' tabItem.zip) }}
              </div>
              <div class="col-6 col-lg-3">
                {{> @input (contextData '@input' tabItem.consumption) }}
              </div>
              <div class="col-6 col-lg-3">
                {{#if tabItem.persons}}
                  {{> @input-range (contextData '@input-range--step' tabItem.persons) }}
                {{/if}}
                {{#if tabItem.size}}
                  {{> @input-range (contextData '@input-range--step' tabItem.size) }}
                {{/if}}
              </div>
              <div class="col-6 col-lg-3">
                {{> @button (contextData '@button' tabItem.submit) }}
              </div>
            </div>
            <div class="row">
              <div class="col-auto ms-auto">
                {{> @input-checkbox (contextData '@input-checkbox' tabItem.b2b) }}
              </div>
              {{#if tabItem.ageVerification}}
                <div class="col-auto">
                  {{> @link (contextData '@link' tabItem.ageVerification) }}
                </div>
              {{/if}}
            </div>
          </div>
        </form>
      </div>
    {{/each}}
  {{/@tabs }}
  </div>
</div>
