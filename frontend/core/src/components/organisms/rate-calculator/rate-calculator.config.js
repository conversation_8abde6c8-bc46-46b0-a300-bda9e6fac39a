module.exports = {
  context: {
    action: 'https://bestellung.stadtwerke-gronau.de',
    tabs: {
      items: [
        {
          navItem: 'Power',
          active: true,
          tabItem: {
            energyType: 'electricity',
            zip: {
              label: '<PERSON><PERSON><PERSON>itzahl',
              placeholder: 'z.B. 40489',
              name: 'zipCode',
              required: true
            },
            persons: {
              label: 'Personen in Ihrem <PERSON>',
              min: 1,
              max: 6,
              step: 1,
              steps: [1, 2, 3, 4, 5, 6]
            },
            consumption: {
              label: 'Jahresverbrauch',
              placeholder: '2500 kWh',
              name: 'consumption',
              required: true
            },
            submit: {
              text: 'Jetzt berechnen',
              icon: { symbol: 'plus' },
              type: 'submit'
            },
            b2b: {
              label: 'Gewerbekunde',
            },
            ageVerification: {
              text: 'Ich in 18 - 25 Jahre alt.',
            }
          }
        },
        {
          navItem: 'Gas',
          active: true,
          tabItem: {
            energyType: 'natural_gas',
            zip: {
              label: '<PERSON><PERSON><PERSON>',
              placeholder: 'z.B. 40489',
              name: 'zipCode',
              required: true,
            },
            size: {
              label: 'Wohnfläche',
              min: 25,
              max: 150,
              step: 25,
              steps: [25, 50, 75, 100, 125, 150]
            },
            consumption: {
              label: 'Jahresverbrauch',
              placeholder: '2500 kWh',
              name: 'consumption',
              required: true
            },
            submit: {
              text: 'Jetzt berechnen',
              icon: { symbol: 'plus' },
              type: 'submit'
            },
            b2b: {
              label: 'Gewerbekunde',
              name: 'filter_targetGroup',
              value: 'BUSINESS_CUSTOMER'
            },
          }
        },
      ]
    }
  }
}
