.page-footer {
    position: relative;
    padding-top: 8.554572vw;
    background: linear-gradient(180deg, #f8f8f7 0%, #f1f1ee 100%);
    background-position: 0 8.554572vw;
    background-repeat: no-repeat;
    &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        z-index: -1;
        display: block;
        width: 100%;
        height: 8.554572vw;
        background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPCFET0NUWVBFIHN2ZyBQVUJMSUMgIi0vL1czQy8vRFREIFNWRyAxLjEvL0VOIiAiaHR0cDovL3d3dy53My5vcmcvR3JhcGhpY3MvU1ZHLzEuMS9EVEQvc3ZnMTEuZHRkIj4KPHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjAiIHk9IjAiIHdpZHRoPSIzNjAiIGhlaWdodD0iMzEiIHZpZXdCb3g9IjAsIDAsIDM2MCwgMzEiPgogIDxnIGlkPSJFYmVuZV8xIj4KICAgIDxwYXRoIGQ9Ik0zNTUuNzg4LDAgQzM1OS4wMjEsMC4wMzggMzU3LjYxOCwtMC4wMjYgMzYwLDAuMTI2IEwzNjAsMzEgTDAsMzEgTDAsMjIuMzYxIEMxMi40NjMsMjAuNTExIDI5LjQyNiwyNy4wOTUgNDIuMzk3LDI5LjA4NCBDNTguNzQyLDMxLjU5MSA4Ni40MTUsMzMuMTU5IDEyOC4xNjcsMTUuOTU0IEMxMzcuMjA5LDEyLjIyOCAxNjQuNTEzLDMuNzQ1IDE5MS4yNTQsNy4zODUgQzIxNi40NTksMTAuODE4IDI0MC42LDE4LjM0NCAyNjIuNTA5LDIwLjUxMSBDMjgyLjUyMSwyMi40OTEgMzA2LjA4MiwyMS4yMTcgMzIzLjc1NCwxMy43NDMgQzMzMi4zMiwxMC43ODUgMzM5LjEyOSw0LjAzOCAzNDcuNzc5LDEuMzIgQzM1MS4zNTgsMC4xOTUgMzUyLjI0NywwLjI5OSAzNTUuNzg4LDAgeiIgZmlsbD0iI0Y4RjhGNyIvPgogIDwvZz4KPC9zdmc+Cg==");
        background-repeat: no-repeat;
        background-size: 100%;
        filter: drop-shadow(0 0 0.125rem #57575620);
    }

    &__copyright p {
        font-size: 0.875rem;
        font-weight: lighter;
    }

    article {
        a {
            color: var(--link-color) !important;
        }
    }
    .nav--social {
        .svg-icon {
            width: 2.6rem;
            height: auto;
            aspect-ratio: 1 / 1;
            @include media-breakpoint-up(md) {
                width: 3rem;
            }
            .icon {
                width: 1.375rem;
                height: auto;
                aspect-ratio: 1 / 1;
            }
        }
    }

    .nav__list {
        justify-content: center;
    }

    h3 {
        font-size: 1.5rem;
        font-weight: 500 !important;
        @include media-breakpoint-up(md) {
            font-size: 1.75rem;
        }

        color: var(--brand-color-primary);
        margin-block-end: 1rem !important;
    }

    &__bottom {
        background: var(--page-header-gradient);
        color: #fff;
    }

    .brand-logo {
        margin-inline: auto;
    }
}
