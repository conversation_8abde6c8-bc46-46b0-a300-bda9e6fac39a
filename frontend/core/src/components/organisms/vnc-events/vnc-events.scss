[data-vnc-events-carousel] {
    .splide {
        &__arrows {
            display: flex;
            justify-content: center;

            > * {
                margin: 0 4px;

                svg > path {
                    fill: var(--bs-dark);
                    &:hover {
                        fill: var(--brand-color-secondary);
                    }
                }

                &:disabled svg > path {
                    fill: var(--link-disabled-color, #9bbaab);
                }
            }
        }

        &__slide {
            // Floating text arrangement for 2er cards
            @media (min-width: 1024px) {
                &:nth-child(even) .teaser__text {
                    align-self: flex-end;
                }
                &:nth-child(odd) .teaser__text {
                    align-self: flex-start;
                }
            }
        }
    }

    .overline {
        display: flex;
        align-items: center;
        font-size: 1rem;
        gap: 0.5rem;
    }

    .event__data {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        width: 100%;

        > div {
            flex: 1 1 auto;
            min-width: 0;
            display: flex;
            align-items: center;
            gap: 0.25rem;

            i {
                flex-shrink: 0;
            }
        }
    }
}

[data-vnc-events-accordion-item] {
    .accordion {
        &__toggle {
            min-height: 5rem;
        }
    }

    .button {
        &__infoline,
        &__infoline_sm {
            i {
                width: 1.25rem;
                height: 1.25rem;
                font-size: 1.25rem;
                transform: rotate(0deg) !important;
            }
        }
    }

    .icon {
        &__info {
            i {
                transform: rotate(0deg) !important;
            }
        }
    }
}
