[data-vnc-events-carousel] {
    .splide {
        &__arrows {
            display: flex;
            justify-content: center;

            > * {
                margin: 0 4px;
            }
        }
    }

    .overline {
        display: flex;
        align-items: center;
        font-size: 1rem;
    }
}

[data-vnc-events-accordion-item] {
    .accordion {
        &__toggle {
            min-height: 5rem;
        }
    }

    .button {
        &__infoline,
        &__infoline_sm {
            i {
                width: 1.25rem;
                height: 1.25rem;
                font-size: 1.25rem;
                transform: rotate(0deg) !important;
            }
        }
    }

    .icon {
        &__info {
            i {
                transform: rotate(0deg) !important;
            }
        }
    }
}
