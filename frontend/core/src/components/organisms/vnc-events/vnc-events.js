import Splide from '@splidejs/splide';

export const VncEvents = (() => {
    window.addEventListener('load', function () {
        setTimeout(() => {
            const elements = document.querySelectorAll('[data-vnc-events-carousel]');

            Array.from(elements).forEach((el) => {
                let arrows, breakpoints, pagination;
                let itemsPerSlide = parseInt(el.dataset.itemsPerSlide);

                if (itemsPerSlide > 1) {
                    breakpoints = {
                        breakpoints: {
                            768: {
                                perPage: 1,
                            },
                            1024: {
                                perPage: 2,
                            },
                        }
                    }
                }
                const splide = new Splide(el, {
                    perPage: itemsPerSlide,
                    rewind: true,
                    ...breakpoints,
                });
                const arrowFirst = splide.root.querySelector('.splide__arrow--first');
                const arrowLast = splide.root.querySelector('.splide__arrow--last');

                function updatePagination() {
                    const pages = Math.ceil(splide.length / itemsPerSlide);
                    let recentPage = Math.floor(splide.index / itemsPerSlide) + 1;

                    if (Math.floor(splide.index / itemsPerSlide) !== splide.index / itemsPerSlide) {
                        recentPage = Math.ceil((splide.index + itemsPerSlide) / itemsPerSlide);
                    }
                    if (pagination) {
                        pagination.textContent = `Seite ${recentPage} von ${pages}`;
                    }
                }

                // create custom pagination
                splide.on('pagination:mounted', function(data) {
                    pagination = splide.root.querySelector('.splide__pagination');
                    arrows = splide.root.querySelector('.splide__arrows');

                    if (splide.length <= itemsPerSlide) {
                        arrows.remove();
                        return;
                    }

                    pagination.removeAttribute('role');
                    pagination.setAttribute('aria-label', 'Aktuelle Seite');
                    updatePagination();
                });

                splide.on('move', function(nexIndex, prevIndex, destIndex) {
                    updatePagination();
                });

                splide.on('resized', function() {
                    itemsPerSlide = splide.options.perPage;
                    updatePagination();
                });

                arrowFirst.addEventListener('click', (e) => {
                    splide.go(0);
                });

                arrowLast.addEventListener('click', (e) => {
                    let ratio = splide.length / itemsPerSlide;
                    let ratioFloor = Math.floor(splide.length / itemsPerSlide);
                    let itemToGo = 0;

                    itemToGo = (ratioFloor * itemsPerSlide) - itemsPerSlide;
                    if (ratio !== ratioFloor) {
                        itemToGo += splide.length % itemsPerSlide;
                    }
                    splide.go(itemToGo);
                });

                splide.mount();
            });
        }, 0);
    });
})();

export default VncEvents;
