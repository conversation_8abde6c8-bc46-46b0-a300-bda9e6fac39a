function convertFigcaptionToCaption() {
    const figures = document.querySelectorAll("figure.table");
    figures.forEach(figure => {
        const figcaption = figure.querySelector("figcaption");
        const table = figure.querySelector("table");
        if (figcaption && table && !table.querySelector("caption")) {
            const caption = document.createElement("caption");
            caption.innerHTML = figcaption.innerHTML;
            table.insertBefore(caption, table.firstChild);
            figcaption.remove();
        }
    });
}

function addScopeToTableHeaders() {
    const tables = document.querySelectorAll("table");
    tables.forEach(table => {
        const theadCells = table.querySelectorAll("thead th");
        theadCells.forEach(th => {
            if (!th.hasAttribute("scope")) {
                th.setAttribute("scope", "col");
            }
        });

        const tbodyRows = table.querySelectorAll("tbody tr");
        tbodyRows.forEach(row => {
            const firstTh = row.querySelector("th");
            if (firstTh && !firstTh.hasAttribute("scope")) {
                firstTh.setAttribute("scope", "row");
            }
        });
    });
}

function removeDeprecatedSummaryAttributes() {
    const tablesWithSummary = document.querySelectorAll("table[summary]");
    tablesWithSummary.forEach(table => {
        table.removeAttribute("summary");
    });
}

function ensureTableRole() {
    const tables = document.querySelectorAll("table");
    tables.forEach(table => {
        if (!table.hasAttribute("role")) {
            table.setAttribute("role", "table");
        }
    });
}

function handleScrollButtons() {
    window.addEventListener('click', (event) => {
        const button = event.target.closest('[data-table-scroll]');
        const container = event.target.closest('[data-table-scroller]');
        const viewport = container?.querySelector('[data-table-viewport]');
        if (button && viewport) {
            const dir = button.dataset.tableScroll;
            if (dir === 'start') {
                viewport.scrollLeft = 0;
            } else if (dir === 'end') {
                viewport.scrollLeft = viewport.scrollWidth - viewport.clientWidth;
            }
        }
    });
}

function applyResponsiveScrollClass() {
    const tables = Array.from(document.querySelectorAll('[data-table-scroller]'));
    tables.forEach((table) => {
        let options = {};
        try {
            options = JSON.parse(table.dataset.tableScroller || "{}");
        } catch (e) {
            console.warn("Invalid JSON in data-table-scroller", e);
        }

        if (options.maxCols) {
            const cols = Array.from(table.querySelectorAll('thead td'));
            if (cols.length > options.maxCols) {
                const viewport = table.querySelector('[data-table-viewport]');
                const container = viewport?.closest('.col-12');
                if (container) {
                    container.classList.add('col-xl-10', 'mx-xl-auto');
                }
            }
        }
    });
}

function makeFiguresFocusable() {
    document.querySelectorAll("figure.table").forEach(figure => {
        if (!figure.hasAttribute("tabindex")) {
            figure.setAttribute("tabindex", "0");
        }
    });
}

document.addEventListener("DOMContentLoaded", () => {
    convertFigcaptionToCaption();
    addScopeToTableHeaders();
    removeDeprecatedSummaryAttributes();
    ensureTableRole();
    applyResponsiveScrollClass();
    makeFiguresFocusable();
});

handleScrollButtons();

export default {};
