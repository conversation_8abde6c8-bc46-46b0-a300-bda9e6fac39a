<div class="table--scroll table--custom-borders" data-table-scroller='{{#if maxCols}}{ "maxCols": {{ maxCols}} }{{/if}}'>
    <figure class="table--scroll__container col-12" data-table-bounds>
        {{#if caption}}
        <figcaption>
            {{{ caption }}}
        </figcaption>
        {{/if}}
        <div class="table--scroll__viewport" data-table-viewport>
            <table class="table {{~#if responsive}} table--responsive{{/if~}}">
                {{#if cols}}
                <colgroup>
                    {{#each cols}}
                    <col {{#if width }}style="width: {{ width }}" {{/if}}>
                    {{/each}}
                </colgroup>
                {{/if}}
                {{#if thead}}
                <thead>
                    <tr>
                        {{#each thead.cells}}
                        <{{ type }} class="table__head">
                            {{ text }}
                        </{{ type }}>
                        {{/each}}
                    </tr>
                </thead>
                {{/if}}
                {{#if tfoot}}
                <tfoot>
                    <tr>
                        {{#each tfoot.cells}}
                        <{{ type }} class="table__foot" {{#if colspan}} colspan="{{ colspan }}" {{/if}}>
                            {{ text }}
                        </{{ type }}>
                        {{/each}}
                    </tr>
                </tfoot>
                {{/if}}
                {{#if tbody}}
                <tbody>
                    {{#each tbody.rows}}
                    <tr class="is-collapsed">
                        {{#each cells}}
                        <{{ type }} {{#if name}} data-content="{{name}}" {{/if}} {{~#if @index}}
                            data-accordion-body{{else}} data-accordion-toggle{{/if~}}>
                            <span>{{ text }}</span>
                        </{{ type }}>
                        {{/each}}
                    </tr>
                    {{/each}}
                </tbody>
                {{/if}}
            </table>
        </div>
    </figure>
    <div class="table__controls text-center d-flex gap-5 py-3">
        <button class="button rounded-circle" data-table-scroll="start" aria-label="go to start">&#x276F;</button>
        <button class="button rounded-circle" data-table-scroll="end" aria-label="go to end">&#x276F;</button>
    </div>
</div>
