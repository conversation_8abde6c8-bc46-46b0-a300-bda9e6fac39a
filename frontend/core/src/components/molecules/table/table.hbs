<table
  class="table
    {{~#if borders}} table--custom-borders{{/if~}}
    {{~#if bordersTHead}} table--custom-borders--thead{{/if~}}
    {{~#if bordersTh}} table--custom-borders--th{{/if~}}
    {{~#if transparentHeader}} table--transparent-header{{/if~}}
    {{~#if responsive}} table--responsive{{/if~}}
  ">
  {{#if caption}}<caption>{{ caption }}</caption>{{/if}}
  {{#if cols}}
    <colgroup>
        {{#each cols}}
          <col {{#if width }}style="width: {{ width }}" {{/if}}>
        {{/each}}
    </colgroup>
  {{/if}}
  {{#if thead}}
    <thead>
      <tr>
        {{#each thead.cells}}
          <{{ type }} class="table__head">
            {{ text }}
          </{{ type }}>
        {{/each}}
      </tr>
    </thead>
  {{/if}}
  {{#if tfoot}}
    <tfoot>
      <tr>
        {{#each tfoot.cells}}
          <{{ type }} class="table__foot" {{#if colspan}} colspan="{{ colspan }}" {{/if}}>
            {{ text }}
          </{{ type }}>
        {{/each}}
      </tr>
    </tfoot>
  {{/if}}
  {{#if tbody}}
    <tbody>
      {{#each tbody.rows}}
        <tr class="is-collapsed{{#if active}} is-active{{/if}}">
          {{#each cells}}
            <{{ type }}
              {{#if name}} data-content="{{name}}"{{/if}}
              {{~#if @index}} data-accordion-body{{else}} data-accordion-toggle{{/if~}}
              >
              <span>{{ text }}</span>
            </{{ type }}>
          {{/each}}
        </tr>
      {{/each}}
    </tbody>
  {{/if}}
</table>
