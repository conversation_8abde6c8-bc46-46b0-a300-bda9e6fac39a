:root,
*::before,
*::after {
    --table-bg-color: var(--brand-color-light);

    --table-head-color: var(--brand-color-primary);
    --table-head-bg-color: var(--brand-color-white);
    --table-highlight-color: var(--brand-color-white);
    --table-highlight-bg-color: var(--brand-color-dark);
    --table-border-width: .125rem;
    --table-border-color: var(--brand-color-white);
    --table-head-border-color: var(--brand-color-white);
}

.table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    font-variant-numeric: lining-nums tabular-nums;

    caption {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
    }

    th {
        background-color: var(--table-head-color) !important;
        color: var(--brand-color-white) !important;
        border: var(--table-border-width) solid var(--table-border-color);
        padding: 0.8rem 1rem;
    }

    td {
        padding: 0.8rem 1rem;
        border: var(--table-border-width) solid var(--table-border-color);
    }

    tbody tr:nth-child(odd) td {
        background-color: #F8F8F7;
    }

    tbody tr:nth-child(even) td {
        background-color: #F1F1EE;
    }

    .is-active td {
        background-color: var(--table-highlight-bg-color);
        color: var(--table-highlight-color);
    }

    thead {
        th,
        td {
            font-weight: bold;
            vertical-align: top;
        }
    }

    thead td,
    th {
        background-color: var(--table-head-bg-color);
        color: var(--table-head-color);
    }

    &--transparent-header {
        thead {
            th,
            td {
                background-color: transparent;
            }
        }
    }

    &--custom-borders {
        th,
        td {
            border: none;
            box-shadow: -.125rem 0 0 0 inset var(--table-border-color), 0 -.125rem 0 0 inset var(--table-border-color);

            &:last-child {
                box-shadow: 0 -.125rem 0 0 inset var(--table-border-color);
            }
        }

        :has(tr:not(:first-child) th:first-child + td) th:first-child {
            box-shadow: inset -.125rem -.125rem 0 0 var(--brand-color-white);
        }

        tr:last-child:not(tr:only-child) {
            th,
            td {
                box-shadow: -.125rem 0 0 0 inset var(--table-border-color);

                &:last-child {
                    box-shadow: none;
                }
            }
        }

        thead {
            th,
            td {
                position: relative;
                box-shadow: -.125rem 0 0 0 inset var(--table-border-color), 0 -.125rem 0 0 inset var(--table-head-border-color);

                &:last-child {
                    box-shadow: 0 -.125rem 0 0 inset var(--table-head-border-color);
                }

                &:not(:last-child)::after {
                    content: "";
                    position: absolute;
                    bottom: 0px;
                    right: 0px;
                    z-index: 1;
                    display: block;
                    width: 0.125rem;
                    height: 0.125rem;
                    background: var(--table-head-border-color);
                }
            }
        }

        tfoot {
            tr {
                th,
                td {
                    box-shadow: -.125rem 0 0 0 inset var(--table-border-color);

                    &:last-child {
                        box-shadow: none;
                    }
                }
            }

            ~ tbody {
                tr:last-child:not(tr:only-child) {
                    th,
                    td {
                        box-shadow: -.125rem 0 0 0 inset var(--table-border-color), 0 -.125rem 0 0 inset var(--table-border-color);

                        &:last-child {
                            box-shadow: 0 -.125rem 0 0 inset var(--table-border-color);
                        }
                    }
                }
            }
        }

        &--th {
            thead {
                th,
                td {
                    box-shadow: -.125rem 0 0 0 inset var(--table-border-color), 0 -.125rem 0 0 inset var(--table-head-border-color);

                    &:last-child {
                        box-shadow: 0 -.125rem 0 0 inset var(--table-head-border-color);
                    }
                }
            }

            th:first-child,
            td:first-child {
                box-shadow: -.125rem 0 0 0 inset var(--table-head-border-color), 0 -.125rem 0 0 inset var(--table-border-color);
            }

            thead {
                th:first-child,
                td:first-child {
                    box-shadow: -.125rem 0 0 0 inset var(--table-head-border-color), 0 -.125rem 0 0 inset var(--table-head-border-color);
                }
            }

            tfoot {
                tr:last-child {
                    th:first-child,
                    td:first-child {
                        box-shadow: -.125rem 0 0 0 inset var(--table-head-border-color);
                    }
                }
            }

            tbody tr:last-child,
            tfoot ~ tbody tr:last-child:not(tr:only-child) {
                th:first-child,
                td:first-child {
                    box-shadow: -.125rem 0 0 0 inset var(--table-head-border-color), 0 -.125rem 0 0 inset var(--table-border-color);
                }
            }
        }
    }

    &--responsive {
        @include media-breakpoint-down(lg) {
            display: flex;
            flex-direction: column-reverse;

            tbody,
            tr {
                display: block;
                margin-block-end: .5rem;
            }

            thead {
                display: none;
            }

            th,
            td {
                display: flex;
                justify-content: space-between;
                gap: .5rem;
                padding: 0;
                border: none;
                font-weight: normal;
                margin-block-end: .125rem;
                box-shadow: none;

                &:first-of-type {
                    box-shadow: none;
                }

                &::before {
                    content: attr(data-content);
                    display: block;
                    flex: 0 0 50%;
                    border-right: .125rem solid $white;
                    padding: 1rem;
                    font-weight: bold;
                }

                span {
                    display: block;
                    padding: 1rem;
                }
            }

            th::before {
                // border-color: var(--brand-color-primary);
            }

            tbody th:first-of-type::after {
                content: none;
            }

            tfoot td {
                display: block;
                padding: 1rem;

                &::before {
                    content: none;
                }
            }

            .is-collapsed {
                td {
                    display: none;

                    &:first-child {
                        display: block;
                    }
                }
            }
        }
    }

    &__controls {
        display: flex;
        justify-content: center;

        // button:first-of-type {
        //     transform: rotate(180deg);
        // }
    }
}

.table--scroll {
    position: relative;
    width: 100%;

    &__viewport {
        max-width: 100%;
        overflow-x: auto !important;
        // scroll-behavior: smooth;

        .table {
            width: auto;
        }

        th,
        td {
            min-width: 4rem;
        }

        -ms-overflow-style: auto;
        scrollbar-color: var(--brand-color-secondary) var(--brand-color-light);
        scrollbar-width: thin;

        &::-webkit-scrollbar-thumb {
            background-color: var(--brand-color-secondary);
        }

        &::-webkit-scrollbar-track {
            background-color: var(--brand-color-light);
        }

        &::-webkit-scrollbar-corner {
            background-color: var(--brand-color-light);
        }

        &::-webkit-scrollbar {
            width: 0.5rem;
            height: 0.5rem;
        }
    }

    @include media-breakpoint-up(xl) {
        .col-xl-10 {
            position: relative;
            z-index: 1;
        }

        .col-xl-10 + .table__controls {
            position: absolute;
            z-index: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 100%;
            justify-content: space-between;
        }
    }
}
