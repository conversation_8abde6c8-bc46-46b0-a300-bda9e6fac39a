const { variants } = require( '../carousel/carousel.config' );

module.exports = {
    context: {
        name: 'defaults',
        caption: 'Default table caption',
        cols: [
            { width: '12rem' },
            {},
            {},
            {},
            {},
            {},
            { width: '8rem' },
        ],
        thead: {
            cells: [
                {
                    type: 'td',
                    width: 0,
                    text: 'Column 01'
                },
                {
                    type: 'td',
                    width: 0,
                    text: 'Column 02'
                },
                {
                    type: 'td',
                    width: 0,
                    text: 'Column 03'
                },
                {
                    type: 'td',
                    width: 0,
                    text: 'Column 04'
                },
                {
                    type: 'td',
                    width: 0,
                    text: 'Column 05'
                },
                {
                    type: 'td',
                    width: 0,
                    text: 'Column 06'
                },
                {
                    type: 'td',
                    width: 0,
                    text: 'Column 07'
                },
                {
                    type: 'td',
                    width: 0,
                    text: 'Column 08'
                }
            ]
        },
        tfoot: {
            cells: [
                {
                    type: 'td',
                    width: 0,
                    text: 'Footer Column 01'
                },
                {
                    type: 'td',
                    width: 0,
                    colspan: 7,
                    text: 'Footer content span across 7 columns'
                },
            ]
        },
        tbody: {
            rows: [
                {
                    cells: [
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 01 / Thead Cell 01',
                            name: 'Column 01'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 01 / Cell 02',
                            name: 'Column 02'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 01 / Cell 03',
                            name: 'Column 03'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 01 / Cell 04',
                            name: 'Column 04'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 01 / Cell 05',
                            name: 'Column 05'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 01 / Cell 06',
                            name: 'Column 06'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 01 / Cell 07',
                            name: 'Column 07'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 01 / Cell 08',
                            name: 'Column 08'
                        }
                    ]
                },
                {
                    active: true,
                    cells: [
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 02 / Thead Cell 01',
                            name: 'Column 01'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 02 / Cell 02',
                            name: 'Column 02'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 02 / Cell 03',
                            name: 'Column 03'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 02 / Cell 04',
                            name: 'Column 04'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 02 / Cell 05',
                            name: 'Column 05'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 02 / Cell 06',
                            name: 'Column 06'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 02 / Cell 07',
                            name: 'Column 07'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 02 / Cell 08',
                            name: 'Column 08'
                        }
                    ]
                },
                {
                    cells: [
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 02 / Thead Cell 01',
                            name: 'Column 01'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 02 / Cell 02',
                            name: 'Column 02'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 02 / Cell 03',
                            name: 'Column 03'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 02 / Cell 04',
                            name: 'Column 04'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 02 / Cell 05',
                            name: 'Column 05'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 02 / Cell 06',
                            name: 'Column 06'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 02 / Cell 07',
                            name: 'Column 07'
                        },
                        {
                            type: 'td',
                            width: 0,
                            text: 'Row 02 / Cell 08',
                            name: 'Column 08'
                        }
                    ]
                }
            ]
        }
    },
    variants: [
        {
            name: 'borders',
            context: {
                borders: true,
            }
        },
        {
            name: 'transparent-header',
            context: {
                transparentHeader: true,
            }
        },
        {
            name: 'borders-th',
            context: {
                borders: true,
                bordersTh: true,
            }
        },
        {
            name: 'responsive',
            context: {
                borders: true,
                responsive: true,
                cols: [
                    { width: '12rem' },
                    {},
                    {},
                    {},
                    {},
                    {},
                    { width: '8rem' },
                ],
                thead: {
                    cells: [
                        {
                            type: 'th',
                            width: 0,
                            text: 'Column 01'
                        },
                        {
                            type: 'th',
                            width: 0,
                            text: 'Column 02'
                        },
                        {
                            type: 'th',
                            width: 0,
                            text: 'Column 03'
                        },
                        {
                            type: 'th',
                            width: 0,
                            text: 'Column 04'
                        },
                        {
                            type: 'th',
                            width: 0,
                            text: 'Column 05'
                        },
                        {
                            type: 'th',
                            width: 0,
                            text: 'Column 06'
                        },
                        {
                            type: 'th',
                            width: 0,
                            text: 'Column 07'
                        },
                        {
                            type: 'th',
                            width: 0,
                            text: 'Column 08'
                        }
                    ]
                },
                tfoot: {
                    cells: [
                        {
                            type: 'th',
                            width: 0,
                            text: 'Footer Column 01'
                        },
                        {
                            type: 'td',
                            width: 0,
                            colspan: 7,
                            text: 'Footer content span across 7 columns'
                        },
                    ]
                },
                tbody: {
                    rows: [
                        {
                            cells: [
                                {
                                    type: 'th',
                                    width: 0,
                                    text: 'Row 01 / Thead Cell 01',
                                    name: 'Column 01'
                                },
                                {
                                    type: 'td',
                                    width: 0,
                                    text: 'Row 01 / Cell 02',
                                    name: 'Column 02'
                                },
                                {
                                    type: 'td',
                                    width: 0,
                                    text: 'Row 01 / Cell 03',
                                    name: 'Column 03'
                                },
                                {
                                    type: 'td',
                                    width: 0,
                                    text: 'Row 01 / Cell 04',
                                    name: 'Column 04'
                                },
                                {
                                    type: 'td',
                                    width: 0,
                                    text: 'Row 01 / Cell 05',
                                    name: 'Column 05'
                                },
                                {
                                    type: 'td',
                                    width: 0,
                                    text: 'Row 01 / Cell 06',
                                    name: 'Column 06'
                                },
                                {
                                    type: 'td',
                                    width: 0,
                                    text: 'Row 01 / Cell 07',
                                    name: 'Column 07'
                                },
                                {
                                    type: 'td',
                                    width: 0,
                                    text: 'Row 01 / Cell 08',
                                    name: 'Column 08'
                                }
                            ]
                        },
                        {
                            cells: [
                                {
                                    type: 'th',
                                    width: 0,
                                    text: 'Row 02 / Thead Cell 01',
                                    name: 'Column 01'
                                },
                                {
                                    type: 'td',
                                    width: 0,
                                    text: 'Row 02 / Cell 02',
                                    name: 'Column 02'
                                },
                                {
                                    type: 'td',
                                    width: 0,
                                    text: 'Row 02 / Cell 03',
                                    name: 'Column 03'
                                },
                                {
                                    type: 'td',
                                    width: 0,
                                    text: 'Row 02 / Cell 04',
                                    name: 'Column 04'
                                },
                                {
                                    type: 'td',
                                    width: 0,
                                    text: 'Row 02 / Cell 05',
                                    name: 'Column 05'
                                },
                                {
                                    type: 'td',
                                    width: 0,
                                    text: 'Row 02 / Cell 06',
                                    name: 'Column 06'
                                },
                                {
                                    type: 'td',
                                    width: 0,
                                    text: 'Row 02 / Cell 07',
                                    name: 'Column 07'
                                },
                                {
                                    type: 'td',
                                    width: 0,
                                    text: 'Row 02 / Cell 08',
                                    name: 'Column 08'
                                }
                            ]
                        }
                    ]
                }
            }
        },
        {
            name: 'scroll',
            context: {
                maxCols: 6,
            }
        }
    ]
}
