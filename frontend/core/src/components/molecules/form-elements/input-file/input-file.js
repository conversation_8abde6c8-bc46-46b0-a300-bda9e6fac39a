export const InputFile = ((window, document, undefined) => {
  'use strict';

  window.FileInput = () => {
    async function upload(action, formData, progress) {
      const cancel = progress.parentElement.querySelector('[data-cancel]');

      const xhr = new XMLHttpRequest();
      const success = await new Promise((resolve, reject) => {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            // console.log('upload progress:', event.loaded / event.total);
            progress.querySelector('progress').value = event.loaded / event.total;
          }
        });

        xhr.addEventListener('loadend', (data) => {
          resolve(xhr.readyState === 4 && xhr.status === 200 ? data : null);

          progress.hidden = true;
          cancel.hidden = true;
        });

        cancel.addEventListener('click', () => {
          xhr.abort();

          progress.hidden = true;
          cancel.hidden = true;
        });

        xhr.open('PUT', action, true);
        xhr.setRequestHeader('Content-Type', 'application/octet-stream');
        xhr.send(formData);

        progress.value = 0;
        progress.max = 1;
        progress.hidden = false;
        cancel.hidden = false;
      });

      //console.log('success:', success);
      return success;
    }

    function createIcon(symbol, className = 'icon') {
        const svgNS = 'http://www.w3.org/2000/svg';
        const useNS = 'http://www.w3.org/1999/xlink';

        const svg = document.createElementNS(svgNS, 'svg');
        svg.classList.add(className);

        const use = document.createElementNS(svgNS, 'use');
        use.setAttributeNS(useNS, 'href', `#${symbol}`);

        svg.appendChild(use);
        return svg;
    }

    async function onChange( event ) {
      const input = event.target.closest('input[data-file-input]');
      if (input) {
        const parent = input.parentElement;
        const files = input.files;
        const options = input.dataset.fileInput && JSON.parse(input.dataset.fileInput);

        if (files.length) {
          const div = document.querySelector('.input--file__file-list') || document.createElement('div');
          div.innerHTML = '';
          div.className = 'input--file__file-list';

          const formData = new FormData();
          Array.from(files).forEach(file => {
            formData.append('file', file, file.name);
          });

          let uploadPassed = {};
          if (options.action) {
            const progress = parent.querySelector('[data-progress-meter]');
            // progress.addEventListener('progress', (event) => {
            //   console.info('PROGRESS CHANGED', event, event.detail);
            // });

            uploadPassed = await upload(options.action, formData, progress);
            //console.log('uploaded', uploadPassed);
          }

          if (uploadPassed) {
            Array.from(files).forEach(file => {
              const p = document.createElement('p');
              p.innerHTML = file.name;
              p.className="d-flex gap-2"
              div.appendChild(p);

              const button = document.createElement('button');
              button.type = 'button';
              button.className = 'btn text-white';
              button.innerHTML = 'x';
              button.setAttribute('data-file-remove', true);
              p.appendChild(button);
            });
          }

          parent.insertBefore(div, parent.firstChild);
        }
      }
    }

    function removeFile(event) {

    }

    return {
      init: (() => {
        window.addEventListener('DOMContentLoaded', () => {
          document.addEventListener('change', onChange);
          document.addEventListener('click', (event) => {
            const button = event.target.closest('[data-file-remove]');
            if (button) {
                const file = button.parentElement;
                const index = [...file.parentElement.children].indexOf(file);
                const input = file.parentElement.parentElement.querySelector('input[type=file]');
                //console.log(input, input.files);
                [...input.files].slice(index,1);
                file.parentElement.removeChild(file);
            }
          });
        });
      })()
    };
  };

  FileInput();

})( window, document );

export default InputFile;
