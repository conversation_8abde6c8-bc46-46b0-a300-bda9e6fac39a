<div class="input input--file">
  <label class="input__label" for="{{id}}">{{label}}</label>
  <input
    id="{{id}}"
    name="{{name}}" 
    type="file"
    data-file-input="{
      {{#if action}}&quot;action&quot;: &quot;{{ action }}&quot;{{/if}}
    }"
    {{#if multiple}} multiple{{/if}}
    {{#if accept}} accept="{{accept}}"{{/if}}
    {{#if required}} required{{/if}}
    {{#if disabled}} disabled{{/if}}
  />
  <button
    class="button button--{{ color }} {{#if animated }} button--animated{{/if}}"
    onclick="this.parentElement.querySelector('input[type=file]').click()"
    {{#if disabled}} disabled{{/if}}
  ><span class="caption">{{label}}</span></button>
  {{#if action}}
    {{> @progress-meter id="upload-progress" value="0" max="1" label=null emitter="true" hidden="true" }}
    <button class="button button--size-s" type="button" data-cancel hidden>Cancel</button>
  {{/if}}
  <span class="input__error">Element is invalid</span>
</div>
