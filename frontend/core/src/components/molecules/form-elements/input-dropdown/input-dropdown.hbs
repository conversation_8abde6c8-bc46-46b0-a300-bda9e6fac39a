<div class="input input--dropdown input--select {{ className }}">
  <select
    id="{{id}}"
    name="{{name}}"
    {{#if value}} value="{{value}}"{{/if}}
    {{#if required}} required{{/if}}
    data-input-dropdown='{
      "caption": "{{ label }}"
      {{#if toggleIcon}}, "toggleIcon": { "src": "{{ path toggleIcon.src }}", "symbol": "{{ toggleIcon.symbol }}" }{{/if}}
    }'
  >
    {{#each options}}
      <option
        class="option"
        value="{{ value }}"
        {{#if selected}} selected{{/if}}
      >{{ text }}</option>
    {{/each}}
  </select>
  <label class="input__label" for="{{id}}">{{label}}</label>
  <span class="input__error">Element is invalid</span>
</div>
