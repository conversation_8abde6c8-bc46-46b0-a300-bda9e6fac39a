export const InputSelect = ((window, document, undefined) => {

    'use strict';

    window.InputDropdown = function() {
      function init(select) {
        const parent = select.parentElement;
        const options = select.querySelectorAll('option');
        const config = select.dataset.inputDropdown && JSON.parse(select.dataset.inputDropdown);

        const id = select.id ? `${select.id}--input-dropdown` : `input-dropdown--${Math.random().toString(36).substring(2, 12)}`

        const dropdown = document.createElement('div');
        dropdown.className = 'dropdown';

        const toggle = document.createElement('button');
        toggle.dataset.dropdownToggle = true;
        toggle.ariaExpanded = false;
        toggle.type = 'button';
        toggle.role = 'button';
        toggle.id = id;

        const caption = document.createElement('span');
        caption.className = 'caption';
        toggle.appendChild(caption);

        const captionText = document.createElement('span');
        captionText.className = 'caption__text text-truncate';
        caption.innerHTML = select.options[ select.selectedIndex ].text;
        caption.appendChild(captionText);

        if (config.toggleIcon) {
          const i = document.createElement('i');
          i.className = 'svg-icon icon nc-circle-ctrl-down';
          toggle.appendChild(i);
        }

        dropdown.appendChild(toggle);

        const ul = document.createElement('ul');
        ul.className = 'dropdown-menu visually-hidden';

        ul.setAttribute('aria-labelledby', id);

        const items = Array.from(options).map((option, index) => {
          const li = document.createElement('li');
          const a = document.createElement('a');
          a.className = 'dropdown-item';
          a.dataset.value = option.value;
          a.innerHTML = option.text;
          a.dataset.index = index;
          a.href = '#';
          a.addEventListener('focus', () => {
            const ariaExpanded = a.closest('.dropdown')?.querySelector('button')?.getAttribute('aria-expanded');
            if (ariaExpanded && ariaExpanded === 'false') {
                show(ul);
            }
          });

          a.addEventListener('focusout', (e) => {
              if (e.relatedTarget.tagName !== 'A') {
                  hide(ul);
              }
          });


          li.appendChild(a);
          ul.appendChild(li);
        });

        dropdown.appendChild(ul);

        parent.addEventListener('click', selectItem);

        select.addEventListener('change', updateDisplay);

        parent.appendChild(dropdown);

        const toggles = parent.querySelectorAll('[data-dropdown-toggle]');
        [ ...toggles ].forEach((item) => {
          toggle.addEventListener('click', toggleDropdown);
        });

        document.addEventListener('click', (event) => {
          if (! parent.contains(event.target)) {
            hide(ul);
          }
        });

        const label = select.parentElement.querySelector('.input__label');
        const hiddenElements = [ select, ...(label ? [label] : []) ];
        hiddenElements.forEach(element => {
          if (element) {
            element.classList.add('visually-hidden');
          }
        });
      }

      function selectItem(event) {
        if (event.target.closest('.dropdown-item')) {
          event.preventDefault();
          // event.stopPropagation();
          // event.stopImmediatePropagation();

          const parent = event.target.closest('.input--dropdown');
          const select = parent.querySelector('select');

          // todo: check object feasability
          select.value = event.target.dataset.value;

          const changeEvent = new Event('change');
          select.dispatchEvent(changeEvent);

          hide(event.target.closest('ul'));
        }
      }

      function updateDisplay(event) {
        const parent = event.target.closest('.input--dropdown');
        const select = parent.querySelector('select');
        const caption = parent.querySelector('.dropdown .caption');
        // console.log('Toggle', select.options[ select.selectedIndex ].text);
        caption.innerHTML = select.options[ select.selectedIndex ].text;
      }

      function toggleDropdown(event) {
        const dropdown = event.target.closest('.dropdown');
        const menu = dropdown && dropdown.querySelector('.dropdown-menu');
        if (menu && menu.classList.contains('visually-hidden')) {
          show(menu);
        }
        else {
          hide(menu);
        }
      }

      function show(ul) {
        ul.classList.remove('visually-hidden');
        ul.closest('.dropdown')?.querySelector('.svg-icon')?.classList.add('open');
        ul.closest('.dropdown')?.querySelector('button')?.setAttribute('aria-expanded', 'true');
      }

      function hide(ul) {
        ul.classList.add('visually-hidden');
        ul.closest('.dropdown')?.querySelector('.svg-icon')?.classList.remove('open');
        ul.closest('.dropdown')?.querySelector('button')?.setAttribute('aria-expanded', 'false');
      }

      return {
        init: (() => {
          window.addEventListener('DOMContentLoaded', () => {
            const selects = document.querySelectorAll( 'select[data-input-dropdown]' );
            Array.from(selects).forEach(select => {
              if (! select.dataset.initialized) {
                init(select);
                select.dataset.initialized = true;
              }
            });
          });
        })()
      };
    };

    InputDropdown();

  })( window, document );

  export default InputSelect;
