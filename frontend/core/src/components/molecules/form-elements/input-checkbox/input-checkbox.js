export const InputCheckbox = ((window, document, undefined) => {

  'use strict';

  window.Checkbox = function() {
    function validate(checkbox) {
      const siblings = Array.from(document.getElementsByName(checkbox.name));
      const mandatory = siblings.some(sibling => sibling.required);
      if (mandatory) {
        const invalid = siblings.every(sibling => ! sibling.checked);
        siblings.forEach(sibling => sibling.setCustomValidity(invalid ? 'invalid' : ''));
      }
    }

    function init(checkbox) {
      checkbox.addEventListener('change', event => validate(checkbox));
      validate(checkbox);
    }
    return {
      init: (() => {
        window.addEventListener('DOMContentLoaded', () => {
          const checkboxes = document.querySelectorAll( 'input[type=checkbox]' );
          Array.from(checkboxes).forEach(checkbox => {
            if (! checkbox.dataset.initialized) {
              init(checkbox);
              checkbox.dataset.initialized = true;
            }
          });
        });
      })()
    };
  };

  Checkbox();

})( window, document );

export default InputCheckbox;
