<div class="input input--range {{ className }}" data-form-input>
  <input
    type="range"
    id="{{id}}"
    name="{{name}}"
    {{#if value}} value="{{value}}"{{/if}}
    {{#if min}} min="{{min}}"{{/if}}
    {{#if max}} max="{{max}}"{{/if}}
    {{#if step}} step="{{step}}"{{/if}}
    {{#if required}} required{{/if}}
    data-input-range
  >
  {{#if label}}
    <label for="{{id}}" role="button" class="input__label">{{ label }}</label>
  {{/if}}
  <div class="input--range__steps">
    {{#each steps}}
      <button class="input__label" value="{{this}}" data-input-range-step>{{this}}</button>
    {{/each}}
  </div>
  <span class="input__error">Element is invalid</span>
</div>
