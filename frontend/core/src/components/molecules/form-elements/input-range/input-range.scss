:root,
*::before,
*::after {
  --range-track-height: .3125rem;
  --range-thumb-width: 2rem;
  --range-thumb-height: 2rem;
  --range-thumb-radius: 50%;

  --range-step-width: var(--range-thumb-width);

  --range-track-color: var(--brand-color-dark);
  --range-thumb-color: var(--brand-color-primary);
  --range-highlight-color: var(--brand-color-primary);

}

.input--range {
  input[type=range] {
    // transform: translateY( calc( (var(--range-thumb-height) / 2 - var(--range-track-height) / 2) * -1 )  );
    appearance: none;
    background: transparent;
    border: none;
    padding: 0;
    cursor: pointer;
    &::-webkit-slider-runnable-track {
      background: var(--range-track-color);
      height: var(--range-track-height);
    }
    &::-moz-range-track {
      background: var(--range-track-color);
      height: var(--range-track-height);
    }
    &::-webkit-slider-thumb {
      appearance: none;
      transform: translateY( calc( (var(--range-thumb-height) / 2 - var(--range-track-height) / 2) * -1 )  );
      background-color: var(--range-thumb-color);
      width: var(--range-thumb-width);
      height: var(--range-thumb-height);
      border-radius: var(--range-thumb-radius);
    }
    &::-moz-range-thumb {
      border: none;
      background-color: var(--range-thumb-color);
      width: var(--range-thumb-width);
      height: var(--range-thumb-height);
      border-radius: var(--range-thumb-radius);
    }
  }

  &__track {
    position: relative;
    // padding: 0 calc(var(--range-thumb-width) / 2);
    background: var(--range-track-color);
    height: var(--range-track-height);
    background-clip: content-box;
  }

  &__thumb {
    position: absolute;
    top: 50%;
    transform: translateY( -50% ) translateX( -50% );
    background-color: var(--range-thumb-color);
    width: var(--range-thumb-width);
    height: var(--range-thumb-height);
    border-radius: var(--range-thumb-radius);
    transition: all 120ms ease;
  }

  &__steps {
    order: 2;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .input__label {
      width: var(--range-step-width);
      text-align: center;
      &:hover {
        color: var(--brand-color-primary);
      }
    }
  }
}