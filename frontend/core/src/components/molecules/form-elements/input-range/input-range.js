export const InputRange = (() => {
  return {
    init: (() => {
      window.addEventListener('DOMContentLoaded', () => {
        document.body.addEventListener('click', (event) => {
          const step = event.target.closest('[data-input-range-step]');
          if (step) {
            const index = [ ...step.parentElement.children ].indexOf(step);
            const container = step.closest('[data-form-input]');
            if (container) {
              const input = container.querySelector('[data-input-range]');
              input.value = index;

              input.dispatchEvent(new Event('change', {
                bubbles: true,
                cancelable: true
              }));
          
            }
          }
        });
      });

      document.body.addEventListener('change', (event) => {
          const input = event.target.closest('[data-input-range]');
          if (input) {
            const container = input.closest('[data-form-input]');
            if (container) {
              const thumb = container.querySelector('[data-input-range-thumb]');
              if (thumb) {
                const width = thumb.parentElement.clientWidth;
                thumb.style.left = `${((input.value) / input.max * 100)}%`;
                thumb.innerHTML = input.value;
              }
              // input.value = index + 1;
            }
          }
      });
    })()
  };
})();


export default InputRange;