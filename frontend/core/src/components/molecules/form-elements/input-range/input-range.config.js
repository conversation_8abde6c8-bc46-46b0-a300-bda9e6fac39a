module.exports = {
    context: {
        id: 'input-range',
        name: 'input-range',
        value: '0',
        label: 'Range input',
        className: '',
        required: true,
        handleIcon: {
            src: '/static/symbol-defs.svg',
            symbol: 'plus'
        },
        min: 0,
        max: 100,
        step: 'any',
    },
    variants: [
        {
            name: 'step',
            context: {
                min: 0,
                max: 5,
                step: 1,
                steps: [ 0, 1, 2, 3, 4, 5 ]
            }
        }
    ]
}
