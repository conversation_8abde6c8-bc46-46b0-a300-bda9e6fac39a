module.exports = {
  collated: true,
  context: {
    name: 'radio-group',
    value: '',
    required: true,
    items: [
      { id: 'radio-01', value: 'value01', label: 'Radiobox I' },
      { id: 'radio-02', value: 'value02', label: 'Radiobox II' },
      { id: 'radio-03', value: 'value03', label: 'Radiobox III' },
    ]
  },
  variants: [
    {
      name: 'disabled',
      label: 'Disabled radiogroup',
      context: {
        disabled: true,
        items: [
          { id: 'disabled-radio-01', value: 'value01', label: 'Radiobox I' },
          { id: 'disabled-radio-02', value: 'value02', label: 'Radiobox II' },
          { id: 'disabled-radio-03', value: 'value03', label: 'Radiobox III' },
        ]
          }
    },
    {
      name: 'container',
      label: 'Responsive container radiogroup',
      context: {
        className: 'container--checkbox',
        items: [
          { id: 'container-radio-01', value: 'value01', label: 'Radiobox I' },
          { id: 'container-radio-02', value: 'value02', label: 'Radiobox II' },
          { id: 'container-radio-03', value: 'value03', label: 'Radiobox III' },
        ]
          }
    },
  ]
}
