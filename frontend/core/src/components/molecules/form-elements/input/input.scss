:root,
*::before,
*::after {
    --brand-color-error: var(--bs-danger);
    --input-height: 4rem;
    --input-color: var(--body-color);

    --vnc-label-font-size: 1rem;

    --vnc-input-border-color: #F1F1EE;
    --vnc-input-background-color: var(--brand-color-white);
    --vnc-dropdown-highlight-color: var(--vnc-input-border-color);
}

.input {
    @include media-breakpoint-up(md) {
        --input-height: 4rem;
        --vnc-label-font-size: unset;
    }
}

.input--light {
    --input-color: var(--bs-white);
}

.input {
    position: relative;
    color: var(--input-color);
    display: flex;
    flex-direction: column;
    text-align: start;

    > input,
    > textarea,
    > p,
    > span {
        order: 1;
    }

    > label {
        order: 0;
    }

    &__label {
        font-weight: bold;
        font-size: var(--vnc-label-font-size);
        // margin-bottom: .375rem;
    }

    &__error {
        order: 2;
    }

    input,
    textarea {
        display: block;
        width: 100%;
        height: var(--input-height);
        outline: none;
        border: var(--border-width) solid var(--vnc-input-border-color);
        border-radius: var(--border-radius);
        color: var(--input-color);
        padding: 0 1rem;
    }

    &:has(input,textarea) {

    }

    textarea {
        height: auto;
    }

    input[type=checkbox],
    input[type=radio] {
        display: grid;
        place-content: center;
        width: 1.77777em;
        height: 1.77777em;
        margin: 0;
        padding: 0;
        appearance: none;
        // border: 0.125em solid currentColor;
        background-color: #fff;
        font: inherit;
        color: currentColor;

        &:focus-visible {
            outline: 2px solid white;
        }
    }

    input[type=checkbox]::before,
    input[type=radio]::before {
        content: '';
        width: 1em;
        height: 1em;
        transform: scale(0);
        transition: 120ms transform ease-in-out;
        box-shadow: inset 1em 1em var(--brand-color-secondary);
    }

    input[type=radio] {
        border-radius: 1em;
    }

    input[type=radio]::before {
        border-radius: 1em;
    }

    input:checked::before {
        transform: scale(1);
    }

    input:disabled {
        ~ label {
            opacity: .62;
        }
    }

    input[type="file"] {
        opacity: 0;
        z-index: -1;
        position: absolute;
        top: -1px;
        left: 0;
        width: 0;
        height: 0;
    }

    .input__button {
        position: absolute;
        left: 0;
        top: 0;
        width: 3rem;
        height: 100%;

        // .svg-icon {
        //   color: var(--input-color);
        // }

        button + input {
            padding-inline-start: 3rem;
        }
    }
}

.input--radio,
.input--checkbox {
    display: inline-flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: .5rem;
    line-height: 1.1;
    display: flex;
    align-items: center;
    gap: 0.5em;
    margin-block-end: .5rem;

    input,
    label {
        order: unset;
    }

    label {
        flex: 1 1 auto;
        cursor: pointer;
        text-align: start;
    }

    .input__label {
        font-weight: normal;
        font-size: unset;
        margin: 0;
    }

    .input__error {
        flex-basis: 100%;
    }
}

.check-group,
.radio-group--row {
    display: flex;
    flex-wrap: wrap;
    gap: 0 1rem;

    > div[role=radiogroup] {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
    }
}

.container--checkbox {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;

    container-name: container-checkbox;
    container-type: inline-size;

    .input {
        flex: 1 0 100%;
    }
}

@container container-checkbox (min-width: #{map-get($grid-breakpoints, 'md')}) {
    .container--checkbox .input {
        flex: 0 0 auto;
    }
}


.input--dropdown {
    display: flex;
    flex-direction: column;

    > select {
        display: none;
    }

    .dropdown {
        position: relative;

        > button {
            display: flex;
            align-items: center;
            width: 100%;
            max-width: 100%;
            height: var(--input-height);
            gap: .25rem;
            text-align: left;
            border: var(--border-width) solid var(--vnc-input-border-color);
            background-color: var(--vnc-input-background-color);
        }

        .caption {
            display: flex;
            align-items: center;
            flex: 1 1 calc(100% - var(--input-height) - .25rem);
            // max-width: calc(100% - var(--input-height) - 0.25rem);
            height: 100%;
            padding: 0 1rem;
            color: var(--input-color);
            border-radius: var(--border-radius);
            background-color: var(--body-background-color);
            overflow: hidden;
        }

        .svg-icon {
            width: 2rem;
            height: 2rem;
            font-size: 2rem;
            flex: none;
            background-repeat: no-repeat;
            background-image: none;
            color: var(--brand-color-secondary-light);
            border-radius: 50rem;
            margin: 1.5rem;
            transition: transform 200ms ease-in-out;

            &.open {
                transform: rotate3d(0, 0, 1, 180deg);
            }

            svg {
                width: .875rem;
                height: .875rem;
            }
        }

        .dropdown-menu {
            min-width: 100%;
            display: block;
            position: absolute;
            z-index: 10;
            max-height: 20rem;
            overflow-y: auto;
            padding: 0!important;
            margin: 0;
            text-align: start;
            list-style: none;
            background-color: var(--vnc-input-background-color);
            background-clip: padding-box;
            border: thin solid transparent;
            border-radius: 0;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            // inset: 1px -1px auto -1px !important;
        }

        .dropdown-item {
            width: 100%;
            display: flex;
            align-items: center;
            height: var(--input-height);
            padding: .25rem 1rem;
            border: 0;
            font-weight: 400;
            text-align: inherit;
            text-decoration: none!important;
            white-space: nowrap;
            color: var(--body-color)!important;
            background-color: var(--vnc-input-background-color);
            cursor: pointer;

            .input {
                flex: 1 0 100%;
                margin: 0;
            }

            &:hover,
            &:focus {
                background-color: var(--vnc-dropdown-highlight-color);
            }
        }

        .dropdown-menu.show {
            display: block;
        }
    }

    form[data-validate] & {
        select:invalid {
            ~ .dropdown .caption {
                color: var(--brand-color-error);
                border-color: var(--brand-color-error);
            }
        }

        .input__error {
            order: 1;
        }
    }

    ul {
        list-style: none;
        margin: 0;
        padding: 0;

        li {
            &::before {
                content: none;
            }
        }
    }

    .bg--secondary &__toggle .svg-icon {
        background-color: var(--brand-color-primary);
    }
}

.input--geo {
    &__text {
        > .svg-icon {
            color: var(--brand-color-secondary);
        }
    }
}


.caption {
    &__text {
        display: block;
        flex: 1 0 auto;
        max-width: 100%;
    }

    &__count {
        order: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 1.8rem;
        height: 1.8rem;
        flex: none;
        margin-inline-start: auto;
        border-radius: 100vw;
        background-color: var(--brand-color-secondary);
        color: var(--bs-white);
    }

    &__count ~ .caption__text {
        max-width: calc(100% - 2.5rem);
    }
}

.input--light,
.button--light {
    .caption__count {
        border: .125rem solid var(--brand-color-white);
        background-color: transparent;
    }
}

.input--borderless {
    &--borderless {

        .dropdown > button > .caption,
        input {
            border-color: transparent;
        }
    }
}

.input__error {
    display: none;
    color: #fff!important;
    background-color: var(--brand-color-error);
    padding: 0 1rem;
}

form[data-validate] {
    .is-invalid,
    :invalid {
        border-color: var(--brand-color-error);

        ~ label {
            --button-text-color: var(--brand-color-error);

            &.button {
                --button-text-color: var(--brand-color-white);
                --button-accent-color: var(--brand-color-error);
            }
        }

        ~ .input__error {
            display: block;
        }
    }

    .input-group:has(:invalid),
    .input--check-group:has(:invalid),
    .input--radio-group:has(:invalid) {
        .input__error {
            display: block;
        }
    }

    // &:has(:invalid) {
    //   .button[type=submit] {
    //     --button-text-color: var(--brand-color-white);
    //     --button-background-color: var(--brand-color-error);
    //     --button-border-color: var(--brand-color-error);
    //     --button-hover-background-color: var(--brand-color-error);
    //     pointer-events: none;
    //   }
    // }
}

.form-group {
    > .input__label {
        display: inline-grid;
        place-content: center;
        font-weight: bold;
        font-size: var(--vnc-label-font-size);
        height: 2rem;
    }
}

.powermail_fieldwrap_type_input,
.powermail_fieldwrap_type_select,
.powermail_fieldwrap_type_textarea,
.input--text,
.input--textarea,
.input--dropdown {
    > .input__label {
        display: inline-grid;
        place-content: center;
        font-weight: 700;
        padding: 0 1rem;
        color: var(--brand-color-secondary);
        background-color: var(--vnc-input-border-color);
        font-size: var(--vnc-label-font-size);
        height: 2rem;
    }

    &:focus-within {
        > .input__label {
            background-color: var(--vnc-input-border-color);
        }

        input[type="text"] {
            border-color: var(--vnc-input-border-color);
        }
    }


    form[data-validate] &:has(:invalid,.is-invalid) {
        input {
            border-color: var(--brand-color-signal-danger);
        }

        > .input__label {
            color: #fff;
            background-color: var(--brand-color-signal-danger);
        }
    }

}

.powermail_fieldwrap_type_check,
.powermail_fieldwrap_type_radio {
    form[data-validate] &:has(:invalid,.is-invalid) {
        .input__label {
            color: var(--brand-color-signal-danger);
        }
    }
}


.form.bg-gradient-secondary {
    padding: 2rem;
    color: #fff;

    .dropdown {
        > button {
            background-color: #fff;
        }

        // .svg-icon {
        //     background: none;
        // }
    }

    .form-group:not(.powermail_fieldwrap_type_select,.powermail_fieldwrap_type_input,.powermail_fieldwrap_type_textarea) {
        .input__label {
            color: var(--vnc-input-background-color);
            margin-bottom: 1.125rem;
        }
    }

    a[href] {
        color: #fff;
        text-decoration: underline;
    }
}

.powermail_field.radio-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0 1rem;

    > *:not(.radio-group__input) {
        flex-basis: 100%;
    }
}
