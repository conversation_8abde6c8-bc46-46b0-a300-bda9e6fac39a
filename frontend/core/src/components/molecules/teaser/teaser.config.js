module.exports = {
  collated: true,
  context: {
    type: null,
    title: 'Teaser Card',
    text: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
    img: {
      src: '/static/placeholder/vancado-placeholder--3-2.jpg'
    },
    cta: {
      text: 'More',
      secondary: true
    }
  },
  variants: [
    {
      name: 'default',
      label: 'Basic',
      context: {
        title: 'Basic Teaser',
        link: {
          text: '+49 174 12345678',
          href: 'javascript:void(0);',
          icon: {
            symbol: 'phone',
            size: 'large'
          }
          // class: 'py-3 p-lg-3'
        },
        background: true,
        text: null,
        img: null,
        cta: null
      }
    },
    {
      name: 'teaser-overlay-light',
      label: 'Overlay Teaser Light',
      context: {
        type: 'overlay',
        overlay: 'light',
        title: 'Teaser Card',
        text: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
        media: { image: true, media: '@image' },
        cta: {
          text: 'More',
          secondary: true
        }
      }
    },
    {
      name: 'teaser-overlay-dark',
      label: 'Overlay Teaser Dark',
      context: {
        type: 'overlay',
        overlay: 'dark',
        title: 'Teaser Card',
        text: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
        media: { image: true, media: '@image' },
        cta: {
          text: 'More',
          secondary: true
        }
      }
    },
    {
      name: 'teaser-card',
      context: {
        type: 'card',
        title: 'Teaser Card',
        text: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
        media: { image: true, media: '@image' },
        cta: {
          text: 'More',
          secondary: true
        }
      }
    },
    {
      name: 'teaser-card-deco',
      context: {
        type: 'card',
        title: 'Teaser Card with decorative image',
        text: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
        media: { image: true, media: '@image--decorative' },
        cta: {
          text: 'More',
          secondary: true
        }
      }
    },
    {
      name: 'teaser-card-bg',
      label: 'Teaser Card with background',
      context: {
        type: 'card',
        background: true,
        title: 'Teaser Card',
        // publishDate: '01.01.2024',
        text: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
        media: { image: true, media: '@image' },
        cta: {
          text: 'More',
          secondary: true
        }
      }
    },
    {
      name: 'overline-teaser',
      label: 'Teaser with overline',
      context: {
        background: true,
        title: 'Contact card',
        overline: {
          text: 'Category',
          icon: {
            title: 'Category',
            symbol: 'house'
          }
        },
        img: null,
        link: null
      }
    },
    {
      name: 'overline-card',
      label: 'Teaser Card with overline',
      context: {
        type: 'card',
        background: true,
        title: 'Contact card',
        overline: {
          text: 'Category',
          icon: {
            title: 'Category',
            symbol: 'house'
          }
        },
        media: { image: true, media: '@image' },
        link: null
      }
    },
    {
      name: 'video-card',
      label: 'Teaser Card with video',
      context: {
        type: 'card',
        background: true,
        title: 'Responsive card',
        media: { video: true, media: '@video--hoverplay' },
        link: null
      }
    },
    {
      name: 'responsive-container-card',
      label: 'Responsive container card',
      context: {
        type: 'card',
        container: true,
        background: true,
        title: 'Responsive card',
        overline: {
          text: 'Category',
          icon: {
            title: 'Category',
            symbol: 'house'
          }
        },
        media: { image: true, media: '@image' },
        link: null,
        cta: null
      }
    },
    {
      name: 'default-stage',
      label: 'Basic Stage',
      context: {
        type: 'stage',
        title: 'Basic Stage',
        text: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
        background: true,
        text: null,
        img: null,
        cta: null
      }
    },

    {
      name: 'teaser-stage',
      label: 'Stage teaser',
      context: {
        type: 'stage',
        overlay: 'dark',
        title: 'Teaser Card',
        text: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
        media: { image: true, media: '@image--wd' },
        cta: {
          text: 'More',
          secondary: true
        }
      }
    },

    {
      name: 'teaser-stage-nested',
      label: 'Nested Stage teaser',
      context: {
        type: 'stage',
        title: 'Teaser Card',
        className: 'teaser--nested',
        text: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
        media: { image: true, media: '@image--picture' },
        cta: {
          text: 'More',
          secondary: true
        }
      }
    },

    // {
    //   name: 'teaser-card-row',
    //   context: {
    //     direction: 'row'
    //   }
    // },
  ]
}
