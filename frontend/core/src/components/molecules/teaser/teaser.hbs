<div
  class="teaser
    {{~#if type}} teaser--{{type}}{{/if~}}
    {{~#if direction}} teaser--{{direction}}{{/if~}}
    {{~#if container}} teaser--container{{/if~}}
    {{~#if background}} teaser--background bg-light{{/if~}}
    {{~#if overlay}} has-overlay has-overlay--{{overlay}}{{/if~}}
    {{~#if className}} {{ className }}{{/if~}}
  "
  data-teaser
  >
  <div class="teaser__canvas">
    <div class="teaser__body">
      <div class="teaser__text">
        <div class="container p-0">
            <div class="text-content">
            {{!-- {{#if publishDate}}
              <small class="d-block mb-3"><time>{{ publishDate }}</time></small>
            {{/if}} --}}
            {{#if overline}}
              <small class="d-block mb-3"><a class="link" href="#cat">{{> @icon overline.icon }}{{ overline.text }}</a></small>
            {{/if}}
            <h4 class="teaser__headline headline">
              {{{ title }}}
            </h4>
            <p>
              {{{ text }}}
              {{#if link}}
                {{> @link link }}
              {{/if}}
            </p>
            {{#if cta}}
              <a
                class="teaser__cta button button--secondary"
                href="#"
                data-teaser-cta
              >{{cta.text}}</a>
            {{/if}}
            </div>
          </div>
        </div>
    </div>
    {{#if media}}
      <div class="teaser__visual">
        {{> @media media }}
        {{!-- <img class="img-fluid" src="{{ path img.src }}" /> --}}
      </div>
    {{/if}}
  </div>
</div>
