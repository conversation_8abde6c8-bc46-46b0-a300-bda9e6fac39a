export const Teaser = (() => {
    const teasers = document.querySelectorAll('[data-teaser]');
    if (teasers) {
        Array.from(teasers).forEach(teaser => {
            const cta = teaser.querySelector('[data-teaser-cta]');
            if (cta && cta.href && cta.target !== '_blank') {
                teaser.addEventListener('click', event => {
                    //console.log('Teaser click handler');
                    location.href = cta.href;
                });
            }
        })
    }
})();

export default Teaser;
