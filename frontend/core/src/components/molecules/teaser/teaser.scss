.teaser {
    --teaser-overline-size: 0.75rem;
    --teaser-title-size: 28px;

    --teaser-padding-y: 1rem;
    --teaser-padding-x: 0.75rem;

    --teaser-bg-color: $background-text;

    &__stones {
        position: absolute;
        z-index: 3;
        width: 6.5rem;
        height: 4.9rem;
        transform: scale(1.5);
        transition: transform 0.3s ease;
        right: 7rem;

        &-stage {
            bottom: 1rem;

            @include media-breakpoint-down(lg) {
                bottom: -2rem;
            }
            @include media-breakpoint-down(md) {
                bottom: 0rem;
            }
        }

        &-introtext {
            bottom: -2rem;
        }

        &-slider {
            bottom: 0;
            right: 9rem;
        }

        @include media-breakpoint-down(lg) {
            transform: scale(1.2);
            right: 3rem;
        }
        @include media-breakpoint-down(md) {
            transform: scale(1.1);
            right: 2.5rem;
        }
        @include media-breakpoint-down(sm) {
            transform: scale(1);
            right: 1rem;
        }
    }
    article:has(+ .teaser__stones) {
        position: relative;
        z-index: 10;
    }

    &:not(.teaser--stage):not(.teaser--container):not(.teaser--nested):not(
            .teaser--wide
        ) {
        max-width: 1024px;
    }

    &__canvas {
        display: flex;
        flex-flow: column;

        &-introtext {
            position: relative;
            width: auto;
            padding-bottom: 2rem;
            flex-shrink: 0;
            background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22360%22%20height%3D%22265%22%20viewBox%3D%220%200%20360%20265%22%20fill%3D%22none%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M0%200.26471C0%200.26471%20270.443%20-0.331144%20359.778%200.265104C359.778%200.265104%20360.278%20177.434%20359.778%20233.022C248.346%20285.663%2067.9578%20263.343%200.00105819%20236.812C0.00087453%20185.856%200%200.26471%200%200.26471Z%22%20fill%3D%22%23F1F1EE%22%2F%3E%3C%2Fsvg%3E");
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
            z-index: 1;
            @include media-breakpoint-down(lg) {
                background-position: bottom;
            }

            p {
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;
            }
        }
    }

    &__body {
        padding: 2rem 0;
        position: relative;
        z-index: 3;

        &--introtext {
            display: flex;
            justify-content: center;
            align-items: center;
            padding-top: 4rem;
        }

        @include media-breakpoint-down(xxl) {
            padding: 2rem 0;
        }
        @include media-breakpoint-down(xl) {
            padding: var(--teaser-padding-y) 0;
        }
    }

    &__text {
        &--introtext {
            width: 50%;
            @include media-breakpoint-down(lg) {
                width: 100%;
            }
        }
    }

    &__headline {
        font-family: "Ropa Sans Pro";
        font-style: normal;
        font-weight: 700;
        line-height: 32px;
        margin-block-end: 0.5rem;
    }

    &__cta {
        margin-block-start: 2rem;
    }

    &__visual {
        order: 0;
        img {
            display: block;
            width: 100%;
        }
    }

    &__body {
        order: 1;
    }

    &--card {
        &:not(.teaser--background) {
            .teaser {
                &__body {
                    padding: 1rem 0;
                }
            }
        }
        &.teaser--background {
            .teaser {
                &__body {
                    padding: 1rem;
                }
            }
        }
    }

    &--stage,
    &--nested,
    &--overlay {
        position: relative;
        @include media-breakpoint-down(md) {
            background-color: var(--teaser-bg-color);
            &::after {
                content: none;
            }
        }
        @include media-breakpoint-up(md) {
            &:has(.teaser__visual) {
                .teaser__body {
                    // position: absolute;
                    z-index: 1;
                    // inset-inline-start: 0;
                    inset-inline-end: 0;
                    // inset-block-start: 0;
                    inset-block-end: 0;
                }
            }
        }
    }

    &--stage {
        &.has-overlay {
            @include media-breakpoint-down(md) {
                .teaser__canvas {
                    &::after {
                        content: none;
                    }
                }
                .teaser__visual {
                    display: none;
                }
                .text-content {
                    color: var(--bs-body-color);
                }
            }
            @include media-breakpoint-up(md) {
                .container {
                    position: relative;
                    .text-content {
                        max-width: 50%;
                        margin-inline-start: auto;
                    }
                }
            }
        }
    }

    &--nested {
        > .teaser__canvas > .teaser__body {
            position: relative;
            padding: 2rem 3rem;
            .teaser__text {
                padding: 1rem;
                background-image: none;
                margin: 0;
                max-width: fit-content;
                transform: none;
            }

            @include media-breakpoint-down(xl) {
                margin-top: -3rem;
                background-image: url("data:image/svg+xml,%3Csvg%20viewBox%3D%220%200%20360%20323%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20preserveAspectRatio%3D%22none%22%3E%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M0.000341286%2020.8385C24.9845%20-0.140416%2061.8298%20-4.19902%20168.895%203.85598C222.862%207.91612%20359.778%2020.8385%20359.778%2020.8385C359.778%2020.8385%20360.278%20219.138%20359.778%20285.071C248.346%20347.508%2067.9578%20321.035%200.00105991%20289.567C0.000876252%20229.128%20-0.00067208%2076.2824%200.000341286%2020.8385Z%22%20fill%3D%22%23F1F1EE%22%2F%3E%3C%2Fsvg%3E");
                background-repeat: no-repeat;
                background-size: 100%;
                background-color: transparent;
                background-position: bottom;
                // justify-content: end;
                // position: absolute;
                .teaser__text {
                    // padding-block: 0;
                }
            }
            @include media-breakpoint-down(md) {
                margin: 0;
                background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22360%22%20height%3D%22265%22%20viewBox%3D%220%200%20360%20265%22%20fill%3D%22none%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M0%200.26471C0%200.26471%20270.443%20-0.331144%20359.778%200.265104C359.778%200.265104%20360.278%20177.434%20359.778%20233.022C248.346%20285.663%2067.9578%20263.343%200.00105819%20236.812C0.00087453%20185.856%200%200.26471%200%200.26471Z%22%20fill%3D%22%23F1F1EE%22%2F%3E%3C%2Fsvg%3E");
                background-size: cover;
                // .teaser__text::after {
                //     margin-top: -3rem;
                //     background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22360%22%20height%3D%22265%22%20viewBox%3D%220%200%20360%20265%22%20fill%3D%22none%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M0%200.26471C0%200.26471%20270.443%20-0.331144%20359.778%200.265104C359.778%200.265104%20360.278%20177.434%20359.778%20233.022C248.346%20285.663%2067.9578%20263.343%200.00105819%20236.812C0.00087453%20185.856%200%200.26471%200%200.26471Z%22%20fill%3D%22%23F1F1EE%22%2F%3E%3C%2Fsvg%3E");
                //     background-repeat: no-repeat;
                //     background-size: cover;
                //     background-color: transparent;
                //     background-position: bottom;
                // }
            }
            @include media-breakpoint-up(xl) {
                justify-content: end;
                position: absolute;
                .teaser__text {
                    width: 600px;
                    margin: 0 auto;
                    padding: 6rem 5rem 4rem;
                    background-image: url('data:image/svg+xml;utf8,<svg viewBox="0 0 600 600" fill="none" xmlns="http://www.w3.org/2000/svg"  preserveAspectRatio="none"><path d="M63.9255 50.3659C148.84 -28.5142 326.953 -5.44081 474.08 55.778C618.69 115.95 621.597 290.627 568.115 415.285C514.633 539.942 432.274 589.534 348.824 597.007C265.374 604.48 165.912 599.834 111.001 561.327C-36.2173 458.089 -20.9889 129.246 63.9255 50.3659Z" fill="%23F3F3F0"/></svg>');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                    background-position: center center;
                    transform: translateY(4rem);
                    p {
                        hyphens: none;
                    }
                }
            }

            .splide__track,
            .carousel__controls {
                margin: 0;
                .teaser__text {
                    padding: 7rem 3rem 3rem;
                }
            }
        }

        @include media-breakpoint-down(md) {
            position: relative;
            .teaser__body {
                position: absolute;
                z-index: 1;
                inset-inline-start: 0;
                inset-inline-end: 0;
                // inset-block-start: 0;
                inset-block-end: 0;
                background-color: var(--teaser-bg-color);

                display: flex;
                flex-direction: column;
                justify-content: center;
            }
            // .text-content {
            //   padding: var(--teaser-padding-y) var(--teaser-padding-x);
            //   background: rgba(228, 228, 228);
            //   width: 50%;
            // }
            .teaser__body {
                padding-block: 0;
            }
            .carousel__controls {
                margin: 0;
            }
        }
        @include media-breakpoint-up(md) {
            .text-content {
                padding: var(--teaser-padding-y) var(--teaser-padding-x);
                background: rgba(228, 228, 228);
                width: 50%;
            }
        }
    }

    &--container {
        // container-type: inline-size;
        // container-name: teaser-row-container;
        container: teaser-row-container / inline-size;

        flex: 0 1 50%;
    }

    &:has([data-teaser-cta]) {
        cursor: pointer;
    }
}

@container teaser-row-container (min-width: 576px) {
    .teaser__canvas {
        flex-flow: row;
        gap: 2.5rem;
    }
    .teaser__visual {
        flex: 0 0 50%;
    }
    .media-container {
        // aspect-ratio: 1 / 1;
        .img {
            aspect-ratio: 1 / 1;
            content: url("/static/placeholder/vancado-placeholder--1-1.jpg");
        }
    }
}

.container-xxxl {
    position: relative;
}

.teaser__text:has(.carousel--stage):has(.teaser__stones) {
    @include media-breakpoint-down(md) {
        .teaser__stones {
            bottom: 8vw;
        }
        &::after {
            width: 90% !important;
            height: 90% !important;
            bottom: 16vw !important;
        }
    }
    @include media-breakpoint-down(sm) {
        .teaser__stones {
            transform: scale(1);
            right: 1rem;
            bottom: -2rem;
        }
        &::after {
            bottom: 24vw !important;
        }
    }
}
