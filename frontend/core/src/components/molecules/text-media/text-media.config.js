module.exports = {
  collated: true,
  context: {
    direction: 'row',
    breakpoint: 'md',
    items: [
      {
        title: 'Text media', 
        text: 'Lorem ipsum sapientem ne neque dolor erat,eros solet invidunt duo Quisque aliquid leo. Pretium patrioque sociis eu nihil Cum enim ad, ipsum alii vidisse justo id. Option porttitor diam voluptua. Cu Eam augue dolor dolores quis, Nam aliquando elitr Etiam consetetur. Fringilla lucilius mel adipiscing rebum. Sit nulla Integer ad volumus, dicta scriptorem viderer lobortis est Utinam, enim commune corrumpit Aenean erat tellus. Metus sed amet dolore justo, gubergren sed.',
        // cta: {
        //   text: 'More',
        //   secondary: true
        // }
      },
      {
        media: '@media',
      },
    ]
  },
  variants: [
    {
      name: 'controls',
      context: {
        direction: 'row',
        items: [
          {
            title: 'Text media', 
            text: 'Lorem ipsum sapientem ne neque dolor erat,eros solet invidunt duo Quisque aliquid leo. Pretium patrioque sociis eu nihil Cum enim ad, ipsum alii vidisse justo id. Option porttitor diam voluptua. Cu Eam augue dolor dolores quis, Nam aliquando elitr Etiam consetetur. Fringilla lucilius mel adipiscing rebum. Sit nulla Integer ad volumus, dicta scriptorem viderer lobortis est Utinam, enim commune corrumpit Aenean erat tellus. Metus sed amet dolore justo, gubergren sed.',
            // cta: {
            //   text: 'More',
            //   secondary: true
            // }
          },
          {
            media: '@media--actions',
          },
        ]
      }
    },
    {
      name: 'media-carousel',
      context: {
        direction: 'row',
        items: [
          {
            title: 'Text media',
            text: 'Lorem ipsum sapientem ne neque dolor erat,eros solet invidunt duo Quisque aliquid leo. Pretium patrioque sociis eu nihil Cum enim ad, ipsum alii vidisse justo id. Option porttitor diam voluptua. Cu Eam augue dolor dolores quis, Nam aliquando elitr Etiam consetetur. Fringilla lucilius mel adipiscing rebum. Sit nulla Integer ad volumus, dicta scriptorem viderer lobortis est Utinam, enim commune corrumpit Aenean erat tellus. Metus sed amet dolore justo, gubergren sed.',
            // cta: {
            //   text: 'More',
            //   secondary: true
            // }
          },
          {
            carousel: true,
            media: '@media--carousel',
          },
        ]
      }
    },
    {
      name: 'column',
      context: {
        direction: 'column',
        items: [
          {
            media: '@media--actions',
          },
          {
            title: 'Text media', 
            text: 'Lorem ipsum sapientem ne neque dolor erat,eros solet invidunt duo Quisque aliquid leo. Pretium patrioque sociis eu nihil Cum enim ad, ipsum alii vidisse justo id. Option porttitor diam voluptua. Cu Eam augue dolor dolores quis, Nam aliquando elitr Etiam consetetur. Fringilla lucilius mel adipiscing rebum. Sit nulla Integer ad volumus, dicta scriptorem viderer lobortis est Utinam, enim commune corrumpit Aenean erat tellus. Metus sed amet dolore justo, gubergren sed.',
            // cta: {
            //   text: 'More',
            //   secondary: true
            // }
          },
        ]
      }
    },
    {
      name: 'carousel',
      context: {
        carousel: true,
        options: '@carousel.options',
        items: [
          {
            direction: 'column',
            items: [
              {
                media: '@media',
              },
              {
                title: 'Text media',
                text: 'Lorem ipsum sapientem ne neque dolor erat,eros solet invidunt duo Quisque aliquid leo. Pretium patrioque sociis eu nihil Cum enim ad, ipsum alii vidisse justo id. Option porttitor diam voluptua. Cu Eam augue dolor dolores quis, Nam aliquando elitr Etiam consetetur. Fringilla lucilius mel adipiscing rebum. Sit nulla Integer ad volumus, dicta scriptorem viderer lobortis est Utinam, enim commune corrumpit Aenean erat tellus. Metus sed amet dolore justo, gubergren sed.',
              },
            ]
          },
          {
            direction: 'column',
            items: [
              {
                media: '@media',
              },
              {
                title: 'Text media',
                text: 'Lorem ipsum sapientem ne neque dolor erat,eros solet invidunt duo Quisque aliquid leo. Pretium patrioque sociis eu nihil Cum enim ad, ipsum alii vidisse justo id. Option porttitor diam voluptua. Cu Eam augue dolor dolores quis, Nam aliquando elitr Etiam consetetur. Fringilla lucilius mel adipiscing rebum. Sit nulla Integer ad volumus, dicta scriptorem viderer lobortis est Utinam, enim commune corrumpit Aenean erat tellus. Metus sed amet dolore justo, gubergren sed.',
              },
            ]
          }
        ]
      }
    },
  ]
}
