<div
  class="media-container
    {{~#if actions}}{{#if actions.download}} media-container--download{{/if}}{{/if~}}
    {{~#if actions}}{{#if actions.lightbox}} media-container--lightbox{{/if}}{{/if~}}
   "
  >
  {{#if image}}
    {{> @image media style=style }}
  {{/if}}
  {{#if video}}
    {{> @video media }}
  {{/if}}
  {{#if clip}}
  <svg width="0" height="0" preserveAspectRatio="none">
    <defs>
        <mask id="mask--stone-01">
            <path d="M0.107,0.084 C0.248,-0.048 0.545,-0.009 0.79,0.093 C1.031,0.193 1.036,0.484 0.947,0.692 C0.858,0.9 0.721,0.983 0.581,0.995 C0.442,1.007 0.277,1 0.185,0.936 C-0.06,0.763 -0.035,0.215 0.107,0.084 z" fill="url(#Gradient_1)"/>
            {{!-- <path fill-rule="evenodd" clip-rule="evenodd" d="M63.9255 50.3659C148.84 -28.5142 326.953 -5.44081 474.08 55.778C618.69 115.95 621.597 290.627 568.115 415.285C514.633 539.942 432.274 589.534 348.824 597.007C265.374 604.48 165.912 599.834 111.001 561.327C-36.2173 458.089 -20.9889 129.246 63.9255 50.3659Z"/> --}}
        </mask>

        <clipPath id="clip--stone-01" clipPathUnits="objectBoundingBox">
            <path d="M0.893,0.916 C0.752,1.048 0.455,1.009 0.21,0.907 C-0.031,0.807 -0.036,0.516 0.053,0.308 C0.142,0.1 0.28,0.017 0.419,0.005 C0.558,-0.007 0.723,0 0.815,0.064 C1.06,0.237 1.035,0.785 0.893,0.916 z" fill="#000000"/>
        </clipPath>
        <clipPath id="clip--stone-02" clipPathUnits="objectBoundingBox">
            <path d="M0.107,0.084 C0.248,-0.048 0.545,-0.009 0.79,0.093 C1.031,0.193 1.036,0.484 0.947,0.692 C0.858,0.9 0.72,0.983 0.581,0.995 C0.442,1.007 0.277,1 0.185,0.936 C-0.06,0.763 -0.035,0.215 0.107,0.084 z" fill="#000000"/>
        </clipPath>
    </defs>
  </svg>
  <svg width="200" height="150" viewBox="0 0 200 150" class="media-container__overlay" version="1.1">
    <defs>
        <linearGradient id="accent-gradient-01" >
        <stop offset="0" stop-color="var(--brand-gradient-primary-start)"/>
        <stop offset="1" stop-color="var(--brand-gradient-primary-stop)"/>
        </linearGradient>
        <linearGradient id="accent-gradient-02" >
        <stop offset="0" stop-color="var(--brand-gradient-primary-variant-start)"/>
        <stop offset="1" stop-color="var(--brand-gradient-primary-variant-stop)"/>
        </linearGradient>
    </defs>
    <g id="Ebene_1_1747405475">
        <path d="M145.265,99.426 C122.939,119.41 110.488,134 82.177,134 C53.867,134 30.044,123.229 14.208,105.731 C-1.628,88.232 -6.556,72.425 11.588,42.366 C29.732,12.306 97.227,-5.813 131.041,1.696 C164.855,9.204 178,21.002 178,42.366 C178,63.729 167.592,79.442 145.265,99.426 z" fill="url(#accent-gradient-01)"/>
        <path d="M189.897,123.572 C185.678,128.185 159.198,156.865 124.734,148.47 C90.269,140.076 110.197,87.087 116.067,76.752 C121.938,66.417 139.615,49.003 174.809,57.069 C189.745,60.491 200.532,77.053 199.98,94.279 C199.665,104.112 194.117,118.958 189.897,123.572 z" fill="url(#accent-gradient-02)"/>
    </g>
  </svg>

  {{/if}}
  {{#if actions }}
    <div class="media-container__actions d-flex flex-column gap-2">
      {{#if actions.download}}
        {{#> @link href=media.src className="button button--light p-3 media-container__download" download=true ariaLabel="Download" }}
          {{> @icon symbol="download" }}
        {{/@link}}
      {{/if}}
      {{#if actions.lightbox}}
        <button
          class="button button--light p-3 media-container--zoom"
          data-modal-toggle="{{ actions.lightbox.id }}"
          aria-controls="{{ actions.lightbox.id }}"
          aria-label="Enlarge view"
          >
          {{> @icon symbol="magnifier" }}
        </button>
      {{/if}}
    </div>
  {{/if}}

  {{#if actions}}
    {{#if actions.lightbox}}
      {{#> @modal id=actions.lightbox.id size="xl" show=false trigger=false footer=false }}
        <div class="lightbox">
          <picture>
            {{> @image media className="media--cover" }}
          </picture>
        </div>
      {{/ @modal }}
    {{/if}}
  {{/if}}

</div>
