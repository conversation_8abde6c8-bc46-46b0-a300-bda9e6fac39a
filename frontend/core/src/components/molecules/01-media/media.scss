.media-container {
  position: relative;
  img,
  video,
  object {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &__actions {
    position: absolute;
    right: 0;
    bottom: 0;
    margin: 1rem;
  }

  &--carousel {
    .carousel__item {
      width: 100%;
      &[aria-hidden="true"] {
        visibility: hidden;
      }
    }
  }

  &--masked {
    // svg {
    //     display: block;
    //     width: 100%;
    //     height: 100%;
    // }
    img {
        -webkit-mask-image: url('/static/stone.svg');
        mask-image: url('/static/stone.svg');
        mask-size: 100% 100%;
        // -webkit-clip-path: url(#mask--stone-01);
        // clip-path: url(#mask--stone-01);
    }
  }
  &__overlay {
    position: absolute;
    right: 0;
    bottom: -10%;
    width: 32%;
    height: auto;
  }

  &--zoom {
    @include media-breakpoint-down(sm) {
      display: none;
    }
  }
}

