module.exports = {
  name: 'media',
  collated: true,
  default: 'image',
  variants: [
    {
      name: 'image',
      label: 'Basic picture',
      context: {
        image: true,
        media: '@image',
      }
    },
    {
      name: 'picture',
      label: 'Responsive picture',
      context: {
        image: true,
        media: '@image--picture',
      }
    },
    {
      name: 'download',
      label: 'Media download',
      context: {
        image: true,
        media: '@image--picture',
        actions: {
          download: true
        }
      }
    },
    {
      name: 'lightbox',
      label: 'Media lightbox',
      context: {
        image: true,
        media: '@image--picture',
        actions: {
          lightbox: {
            id: 'media-lightbox'
          },
        }
      }
    },
    {
      name: 'actions',
      label: 'Full controls',
      context: {
        image: true,
        media: '@image',
        actions: {
          download: true,
          lightbox: {
            id: 'media-lightbox'
          },
        }
      }
    },
    {
        name: 'clipped',
        label: 'Clipped',
        context: {
          image: true,
          media: '@image',
          clip: '#clip--stone-01',
          style: 'clip-path: url(#clip--stone-01)'
        }
    },
    {
      name: 'video',
      label: 'Video with poster',
      context: {
        video: true,
        media: '@video--poster'
      }
    },
    {
      name: 'carousel',
      label: 'Media carousel',
      context: {
        carousel: true,
        controls: {
          circle: true
        },
        // options: '@carousel.options',
        options: JSON.stringify({
          perPage: 4,
          gap: '1rem',
          breakpoints: {
              640: {
                perPage: 2,
              //   gap    : '.7rem',
              //   height : '6rem',
              },
              480: {
                perPage: 1,
              //   gap    : '.7rem',
              //   height : '6rem',
              },
            },
        }),
        items: [
          {
            image: true,
            media: '@image--wd',
            actions: {
              download: true,
              lightbox: {
                id: 'media-lightbox'
              },
            }
          },
          {
            image: true,
            media: '@image--wd',
            actions: {
              download: true,
              lightbox: {
                id: 'media-lightbox'
              },
            }
          },
          {
            video: true,
            media: '@video--hoverplay'
          },
          {
            image: true,
            media: '@image--wd',
            actions: {
              download: true,
              lightbox: {
                id: 'media-lightbox'
              },
            }
          },
          {
            image: true,
            media: '@image--wd',
            actions: {
              download: true,
              lightbox: {
                id: 'media-lightbox'
              },
            }
          },
        ]
      }
    },
  ]
}
