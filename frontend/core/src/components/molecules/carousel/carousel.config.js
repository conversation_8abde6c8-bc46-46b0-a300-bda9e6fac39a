const cards = [
    {
        type: 'card',
        title: 'Teaser Card 01',
        text: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
        media: { image: true, media: '@image' },
        cta: {
            text: 'More',
            secondary: true
        }
    },
    {
        type: 'card',
        title: 'Teaser Card 02',
        text: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
        media: { image: true, media: '@image' },
        cta: {
            text: 'More',
            secondary: true
        }
    },
    {
        type: 'card',
        title: 'Teaser Card 03',
        text: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
        media: { image: true, media: '@image' },
        cta: {
            text: 'More',
            secondary: true
        }
    },
    {
        type: 'card',
        title: 'Teaser Card 04',
        text: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
        media: { image: true, media: '@image' },
        cta: {
            text: 'More',
            secondary: true
        }
    },
    {
        type: 'card',
        title: 'Teaser Card 05',
        text: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
        media: { image: true, media: '@image' },
        cta: {
            text: 'More',
            secondary: true
        }
    },
    {
        type: 'card',
        title: 'Teaser Card 06',
        text: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
        media: { image: true, media: '@image' },
        cta: {
            text: 'More',
            secondary: true
        }
    },
    {
        type: 'card',
        title: 'Teaser Card 07',
        text: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
        media: { image: true, media: '@image' },
        cta: {
            text: 'More',
            secondary: true
        }
    },
    {
        type: 'card',
        title: 'Teaser Card 08',
        text: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
        media: { image: true, media: '@image' },
        cta: {
            text: 'More',
            secondary: true
        }
    },
];
const thumbnails = [
    {
        src: '/static/placeholder/vancado-placeholder--3-2.jpg',
    },
    {
        src: '/static/placeholder/vancado-placeholder--3-2.jpg',
    },
    {
        src: '/static/placeholder/vancado-placeholder--3-2.jpg',
    },
    {
        src: '/static/placeholder/vancado-placeholder--3-2.jpg',
    },
    {
        src: '/static/placeholder/vancado-placeholder--3-2.jpg',
    },
    {
        src: '/static/placeholder/vancado-placeholder--3-2.jpg',
    },
    {
        src: '/static/placeholder/vancado-placeholder--3-2.jpg',
    },
    {
        src: '/static/placeholder/vancado-placeholder--3-2.jpg',
    },
];

const images = [
    {
        src: '/static/placeholder/vancado-placeholder--3-2.jpg',
        width: 240,
        height: 160
    },
    {
        src: '/static/placeholder/vancado-placeholder--16-9.jpg',
        width: 284,
        height: 160
    },
    {
        src: '/static/placeholder/vancado-placeholder--3-4.jpg',
        width: 120,
        height: 160
    },
    {
        src: '/static/placeholder/vancado-placeholder--1-1.jpg',
        width: 160,
        height: 160
    },
    {
        src: '/static/placeholder/vancado-placeholder--4-5.jpg',
        width: 128,
        height: 160
    }
];

module.exports = {
    preview: '@preview-full',
    collated: true,
    context: {
        buttonClass: '',
        exposeControls: false,
        options: JSON.stringify({
            perPage: 4,
            breakpoints: {
                640: {
                  perPage: 2,
                //   gap    : '.7rem',
                //   height : '6rem',
                },
                480: {
                  perPage: 1,
                //   gap    : '.7rem',
                //   height : '6rem',
                },
              },
        }),
        items: cards,
        controls: true
    },
    variants: [
        {
            name: 'auto-width',
            context: {
                images: true,
                buttonClass: '',
                options: JSON.stringify({
                    type: 'loop',
                    rewind: false,
                    drag: 'free',
                    focus: 'center',
                    perPage: null,
                    autoWidth: true,
                    height: '10rem',
                    gap: '10px',
                    padding: 0,
                    // autoplay: true,
                    // interval: 1000,
                    // speed: 1000,
                    // autostart: true,
                    autoScroll: {
                        speed: 1,
                    },
                    pagination: false,
                    arrows: false
                }),
                items: images,
                controls: false
            }
        },
        {
            name: 'stage',
            context: {
                options: JSON.stringify({
                    syncElement: '.stage-navigation .splide',
                    type: 'fade',
                    pagination: false,
                    arrows: false,
                    cover: true,
                }),
                items: cards,
            }
        },
        {
            name: 'stage-navigation',
            context: {
                items: thumbnails,
                controls: null,
                className: 'stage-navigation',
                options: JSON.stringify({
                    rewind: true,
                    isNavigation: true,
                    gap: 10,
                    arrows: false,
                    focus: 'center',
                    pagination: false,
                    fixedWidth: 120,
                    // cover           : true,
                    // dragMinThreshold: {
                    //   mouse: 4,
                    //   touch: 10,
                    // },
                    // breakpoints : {
                    //   640: {
                    //     fixedWidth  : 66,
                    //     fixedHeight : 38,
                    //   },
                    // },
                }),
            }
        }
        // {
        //     name: 'grid-col-2',
        //     context: {
        //         grid: true,
        //         thumbnails: false,
        //         cols: 2,
        //         exposeControls: false,
        //         options: JSON.stringify({
        //             cellSelector: '.carousel__item',
        //             pageDots: false,
        //             prevNextButtons: false,
        //             contain: true,
        //             groupCells: true
        //         }),
        //         items: cards,
        //         controls: {
        //             circle: true
        //         }
        //     }
        // }
    ]
};
