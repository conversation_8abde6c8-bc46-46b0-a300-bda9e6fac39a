@mixin expose-controls() {
    // position: relative;
    // .carousel__controls {
    //   button {
    //     position: absolute;
    //     top: 50%;
    //     transform: translateY(-50%);
    //     width: 3rem;
    //     height: 3rem;
    //     font-size: 3rem;
    //     line-height: 1;
    //     transform-origin: center;
    //     margin: 0 calc(#{$grid-gutter-width} / 2);
    //   }
    //   .carousel__prev {
    //     transform: scale(-1, 1) translateY(-50%);
    //     right: 100%;
    //   }
    //   .carousel__next {
    //     left: 100%;
    //   }
    // }
}

.carousel {
    &__controls {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1.5rem;
        margin-top: 2rem;
        // @include media-breakpoint-up(xl) {
        //     margin-top: 3rem;
        // }
    }
    // .splide__arrows:not(.carousel_controls) {
    //     display: none;
    // }

    &--expose-controls {
        @include expose-controls();
    }

    &--xl-expose-controls {
        @include media-breakpoint-up(xl) {
            @include expose-controls();
        }
    }

    .splide {
        &__track {
            + .teaser__stones {
                transform: scale(1.25) translateY(-66%);
                @include media-breakpoint-up(md) {
                    transform: scale(1.5) translateY(-53%);
                }
            }
        }

        &__slide {
            position: relative;
            img {
                display: block;
                height: 100%;
            }
        }

        &__pagination {
            gap: 1rem;
            &__page {
                display: block;
                width: 1rem;
                height: 1rem;
                border-radius: 50%;
                background-color: var(--bs-dark);
                &.is-active {
                    width: 1.5rem;
                    height: 1.5rem;
                    background-color: var(--brand-color-secondary);
                }
            }
        }

        &__arrow {
            font-size: 2.5rem;
            color: var(--brand-color-secondary);
            &--prev {
                transform: scale(-1);
                &:focus-visible {
                    outline: 2px solid var(--bs-body-color);
                    outline-offset: 2px;
                }
            }
            &--next {
                &:focus-visible {
                    outline: 2px solid var(--bs-body-color);
                    outline-offset: 2px;
                }
            }
            &:disabled {
                color: var(--link-disabled-color);
            }
        }
    }
    //   ul {
    //     list-style: none;
    //     padding: 0;
    //   }
    //   &__canvas {
    //     margin: 0 0 2rem;
    //     &:not(.flickity-enabled) {
    //       display: flex;
    //       flex-wrap: nowrap;
    //       > * {
    //         flex: none;
    //       }
    //     }
    //   }

    //   &--thumbnail {
    //     .carousel__item {
    //       height: 8rem;
    //       margin-inline-start: $grid-gutter-width;
    //     }
    //     img {
    //       display: block;
    //       height: 100%;
    //       width: auto;
    //     }
    //   }

    //   &--grid {
    //     .carousel__item {
    //       width: 100%;
    //       margin-inline-start: $grid-gutter-width;
    //     }

    //     &--cols-2 {
    //       .carousel__item {
    //         @include media-breakpoint-up(sm) {
    //           width: calc( 50% - #{$grid-gutter-width} / 2 );
    //         }
    //       }
    //     }
    //     &--cols-3 {
    //       .carousel__item {
    //         @include media-breakpoint-up(sm) {
    //           width: calc( 50% - #{$grid-gutter-width} / 2 );
    //         }
    //         @include media-breakpoint-up(md) {
    //           width: calc( 33.33333% - #{$grid-gutter-width} / 3 * 2 );
    //         }
    //       }
    //     }
    //     &--cols-4 {
    //       .carousel__item {
    //         @include media-breakpoint-up(sm) {
    //           width: calc( 50% - #{$grid-gutter-width} / 2 );
    //         }
    //         @include media-breakpoint-up(md) {
    //           width: calc( 33.33333% - #{$grid-gutter-width} / 3 * 2 );
    //         }
    //         @include media-breakpoint-up(lg) {
    //           width: calc( 25% - #{$grid-gutter-width} / 4 * 3 );
    //         }
    //       }
    //     }
    //   }

    //   &--grid--cols-1 {
    //     @include media-breakpoint-up(md) {
    //       position: relative;
    //       .carousel__controls {
    //         position: absolute;
    //         left: 0;
    //         bottom: 0;
    //         width: 100%;
    //         justify-content: space-between;
    //         .button {
    //           margin: 0;
    //           transform: translateY(-100%);
    //           transform-origin: center;
    //         }
    //         .button:first-of-type {
    //           transform: rotate(180deg) translateY(100%);
    //         }
    //       }
    //     }
    //   }
}

.carousel__stone {
    &-container {
        position: absolute;
        bottom: 20%;
        right: 0;
    }
    &-background {
        position: relative;
        z-index: 2;
        // background-image: url("data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20width='213'%20height='317'%20viewBox='0%200%20213%20317'%20fill='none'%3E%3Cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M33.7739%2026.61C78.6371%20-15.065%20172.74%20-2.87456%20250.472%2029.4694C326.875%2061.2601%20328.411%20153.548%20300.154%20219.409C271.898%20285.27%20228.385%20311.471%20184.295%20315.419C140.206%20319.367%2087.6566%20316.912%2058.6453%20296.568C-19.1348%20242.024%20-11.0892%2068.285%2033.7739%2026.61Z'%20fill='url(%23paint0_linear_3272_11167)'/%3E%3Cdefs%3E%3ClinearGradient%20id='paint0_linear_3272_11167'%20x1='158.5'%20y1='0'%20x2='158.5'%20y2='317'%20gradientUnits='userSpaceOnUse'%3E%3Cstop%20stop-color='%23F8F8F7'/%3E%3Cstop%20offset='1'%20stop-color='%23F1F1EE'/%3E%3C/linearGradient%3E%3C/defs%3E%3C/svg%3E");
        // z-index: 5;
        // background-repeat: no-repeat;
        // background-size: cover;
    }
}

// .carousel__item[aria-hidden=true] a,
// .carousel__item[aria-hidden=true] button {
//   visibility: hidden;
// }

.splide:has(.teaser--nested) {
    .splide__track {
        overflow-y: visible;
        overflow-x: clip;
    }
}
