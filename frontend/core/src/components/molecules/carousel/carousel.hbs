<div class="carousel
    {{#if thumbnails }} carousel--thumbnail{{/if}}
    {{#if grid }} carousel--grid{{/if}}
    {{#if cols }} carousel--grid--cols-{{ cols }}{{/if}}
    {{#if exposeControls }} carousel--expose-controls{{/if}}
    {{#if className }} {{ className }}{{/if}}
  ">

    <div class="carousel__canvas splide" data-vnc-carousel="{{ options }}">
      <div class="splide__track">
        <ul class="splide__list">
          {{#>@partial-block}}
            {{#each items}}
              <div class="carousel__item splide__slide">
                  {{#if src}}{{> @image }}{{/if}}
                  {{#if text}}{{> @teaser }}{{/if}}
              </div>
            {{/each}}
          {{/@partial-block}}
        </ul>
      </div>
      {{#if controls}}
        <div class="carousel__controls splide__arrows">
          {{!-- <button class="carousel__prev splide__arrow splide__arrow--prev {{ buttonClass }}" data-table-scroll="start">&#x276F;</button>
          <button class="carousel__next splide__arrow splide__arrow--next {{ buttonClass }}" data-table-scroll="end">&#x276F;</button> --}}
          <button class="carousel__prev splide__arrow splide__arrow--prev {{ buttonClass }}" data-table-scroll="start">{{> @icon symbol='triangle-right' }}</button>
          <ul class="splide__pagination"></ul>
          <button class="carousel__next splide__arrow splide__arrow--next {{ buttonClass }}" data-table-scroll="end">{{> @icon symbol='triangle-right' }}</button>
        </div>
      {{/if}}

      <div class="splide__progress">
        <div class="splide__progress__bar"></div>
      </div>
    </div>
</div>
