import Splide from '@splidejs/splide';
import { AutoScroll } from '@splidejs/splide-extension-auto-scroll';

export const Carousel = (() => {
  function initCarousel(element) {
    if (element) {
      if (element.classList.contains('is-initialized')) {
        return;
      }

      const dataset = element.dataset.vncCarousel && JSON.parse(element.dataset.vncCarousel);
      const options = {
        ...dataset
      };

      const slides = Array.from(element.querySelectorAll('.splide__slide'));
      if (! options.perPage || slides.length >= options.perPage) {
        const carousel = new Splide( element, options );

        if (options.syncElement) {
          const syncElement = document.querySelector(options.syncElement);
          if (syncElement) {
            const syncSplide = initCarousel(syncElement);
            carousel.sync( syncSplide );
          }
        }

        carousel.mount((options.autoScroll ? { AutoScroll } : {}));
        return carousel;
      }
    }
  }

  window.addEventListener('load', () => {
    setTimeout(() => {
      const elements = document.querySelectorAll('[data-vnc-carousel]');
      Array.from(elements).forEach(element => {
        initCarousel(element);
      });
    }, 0);
  });
})();

export default Carousel;
