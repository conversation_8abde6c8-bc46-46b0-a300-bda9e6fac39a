.auto-suggest {
  position: relative;
  .suggestions {
    position: absolute;
    z-index: 1;
    top: calc(100% + .25rem);
    left: 0;
    width: 100%;
    margin: 0;
    padding: .5rem 0;
    background-color: var(--brand-color-white);
    color: var(--brand-color-dark);
    border: .125rem solid transparent;
    box-shadow: $box-shadow-sm;
    &__suggestions {
      li {
        list-style-type: "";
      }
    }
    .suggestions__item {
      cursor: pointer;
      padding: .5rem 1rem;
      &::before {
        content: none;
      }
      &:hover {
        text-decoration: underline;
        // background-color: var(--brand-color-secondary);
      }
    }
    p {
      margin: 0;
    }
  }
}
