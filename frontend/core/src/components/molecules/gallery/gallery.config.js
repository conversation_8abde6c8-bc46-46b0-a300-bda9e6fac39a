const items = [
    {
        id: 'gallery-item-01',
        image: true,
        media: '@image',
        actions: {
          lightbox: {
            id: 'gallery-item-01'
          },
        }
    },
    {
        id: 'gallery-item-02',
        image: true,
        media: '@image',
        actions: {
          lightbox: {
            id: 'gallery-item-02'
          },
        }
    },
    {
        id: 'gallery-item-03',
        image: true,
        media: '@image',
        actions: {
          lightbox: {
            id: 'gallery-item-03'
          },
        }
    },
];

module.exports = {
    collated: true,
    context: {
        col: 12,
        offset: 0,
        items
    },
    variants: [
        {
            name: 'default',
            label: 'Single',
            context: {
                items: [ ...items ].splice( 0, 1 ),
                col: 8,
                offset: 2
            }
        },
        {
            name: 'double',
            context: {
                items: [ ...items ].splice( 0, 2 ),
                col: 10,
                offset: 1
            }
        },
        {
            name: 'triple',
        },
        {
            name: 'flexible',
            context: {
                cols: { md: 6, lg: 4, xl: 3 },
                items: [ ...items, ...items ]
            },
        },
        {
            name: 'container',
            label: 'Container with 3 items',
            context: {
                items: [
                    '@teaser--responsive-container-card',
                    '@teaser--responsive-container-card',
                    '@teaser--responsive-container-card',
                ]
            }
        },
        {
            name: 'container',
            label: 'Container with 2 items',
            context: {
                items: [
                    '@teaser--responsive-container-card',
                    '@teaser--responsive-container-card',
                ]
            }
        }
    ]
}
