:root,
*::before,
*::after {
    --nav-brand-breakpoint: $nav-breakpoint;
}

.nav {
    --nav-accent-color: var(--brand-color-secondary);
    --nav-background-color: var(--brand-color-secondary);
    --meta-nav-color: var(--bs-light);
    --meta-nav-icon-color: var(--bs-light);
    --meta-nav-pipe-color: var(--brand-color-secondary);

    @include media-breakpoint-up($nav-breakpoint) {
        --nav-background-color: transparent;
        --meta-nav-color: var(--brand-color-primary);
        --meta-nav-icon-color: var(--brand-color-secondary);
        --meta-nav-pipe-color: var(--brand-color-secondary);
    }
}

.nav {
    position: relative;
    user-select: none;

    &__item {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        font-weight: 700;

        &--pipe {
            @include media-breakpoint-up($nav-breakpoint) {
                &::before {
                    content: "|";
                    color: var(--nav-accent-color);
                    font-weight: bold;
                }
                &:first-of-type::before {
                    content: none;
                }
            }
        }

        .button {
            padding: 0;
        }

        .nav {
            display: flex;
            text-transform: none;
            clip-path: inset(0 0 100% 0);
            transition: all 80ms ease-in-out;
            width: 100vw;
            height: 0;

            //   @include media-breakpoint-down($nav-breakpoint) {
            //     .container-#{$nav-breakpoint} {
            //       padding: 0;
            //     }
            //   }
            @include media-breakpoint-up($nav-breakpoint) {
                position: absolute;
                top: 100%;
                left: calc(-1 * (100vw - 100%) / 2);
                z-index: -1;
                height: auto;
            }
        }

        &.is-selected,
        &.is-active,
        &.active,
        &:has(.nav__item.active) {
            > .nav__link {
                color: var(--brand-color-primary);
            }
        }

        &:focus-within,
        &:hover {
            > .nav__link {
                position: relative;
                text-decoration: none;

                // color: var(--brand-color-secondary);
            }
        }

        &.is-selected {
            .nav {
                height: auto;
                clip-path: inset(0 0 0 0);

                @include media-breakpoint-up($nav-breakpoint) {
                    .container {
                        background-color: #f8f8f7;
                        //   border-top: thin solid #57575633;
                    }
                }
            }
        }

        .svg-icon {
            color: var(--brand-color-primary);
        }
    }

    &__link {
        display: block;
        text-decoration: none;
        color: inherit;

        &:hover,
        &:focus,
        &:active {
            color: inherit;
            background: none;
        }

        &:focus-visible {
            outline: 2px solid var(--bs-body-color);
            outline-offset: 2px;

            @include media-breakpoint-down($nav-breakpoint) {
                outline-offset: -1px;
            }
        }

        @include media-breakpoint-down($nav-breakpoint) {
            width: 100%;
            + ul {
                width: 100%;

                .nav__link {
                    border: none;
                }
            }
        }
        // @include media-breakpoint-up($nav-breakpoint) {
        //     &.border-bottom {
        //         border-bottom: thin solid #C8C920;
        //     }
        // }
    }

    &--sub {
        background-color: #f8f8f7;
        @include media-breakpoint-down($nav-breakpoint) {
            margin-left: -0.75rem;
        }
        @include media-breakpoint-up($nav-breakpoint) {
            padding: 2rem 0 3.25rem;
            .nav__item {
                display: block;
                flex-basis: 20%;
            }
            .nav__link {
                border: none;
            }
        }
    }
}

.input--search {
    button > span > .icon {
        stroke: var(--brand-color-secondary-light);
        fill: var(--brand-color-secondary-light);
    }
    &:hover {
        button > span > .icon {
            stroke: var(--brand-color-secondary-dark);
            fill: var(--brand-color-secondary-dark);
        }
    }
    input {
        &:focus-visible {
            outline: 2px solid #fff;
            outline-offset: 2px;
            border-radius: 0 2rem 2rem 0;
        }
    }
}

.input--search,
.nav--search {
    display: inline-flex;
    place-content: center;

    .input--text input {
        height: 2rem;
    }

    input {
        border-radius: 0 50rem 50rem 0;
    }

    .input__button {
        right: 0;
        left: auto;
        width: auto;
        aspect-ratio: 1 / 1;
    }

    .svg-icon {
        aspect-ratio: unset;
    }
}

body:has(.bg-gradient-secondary .tx-indexedsearch-searchbox) {
    .nav--search {
        @include media-breakpoint-up($nav-breakpoint) {
            display: none;
        }
    }
}

.nav--meta {
    .nav__link {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.25rem;
        font-weight: 700;
        color: #fff;
        text-align: center;

        &:focus-visible,
        &:hover {
            outline: none;
            text-decoration: underline;
            text-underline-offset: 4px;
        }

        .svg-icon {
            display: inline-flex;
            place-content: center;
            color: #fff;
        }

        .svg-icon .icon {
            font-size: 1.2em;
        }
    }
}

@include media-breakpoint-down($nav-breakpoint) {
    .nav__link {
        &:focus-visible {
            outline: none;
            text-decoration: underline !important;
            text-underline-offset: 2px;
        }
    }

    .nav--search {
        background: var(--page-header-gradient);
        position: absolute;
        top: 100%;
        left: 0;
        width: 100vw;
        height: var(--page-header-meta-height);
        padding-right: calc(var(--bs-gutter-x) * 0.5);
    }
    .nav--meta {
        background: var(--page-header-gradient);
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100vw;
        height: 4rem;
        display: block;
        clip-path: inset(100% 0 0 0);
        transition: all 100ms ease-in-out;

        .nav__item {
            display: inline-flex;

            .svg-icon {
                display: grid;
                width: 1.875rem;
                height: 1.875rem;
                margin-inline: auto;
            }
        }

        .nav__link {
            display: block;
        }
    }
    .page-header:has(.nav--main.show) {
        .nav--meta {
            clip-path: inset(0 0 0 0);
        }
    }
    .nav--main {
        top: var(--page-header-meta-height);
        background-color: var(--page-header-background-color);
    }
    .nav__lvl-1 {
        &:not(:last-child):not(.is-selected) {
            border-bottom: thin solid var(--brand-color-primary-variant-light);
        }

        > .nav__link {
            font-size: 1rem;
            font-weight: bold;
        }

        &:last-child {
            > .nav__link {
                border: none;
            }
        }

        &.is-selected {
            > .nav__link {
                .svg-icon {
                    transform: rotate(180deg);
                }
            }
        }
    }
    .nav__lvl-2 {
        .nav__item {
            display: none;
        }

        &.is-selected {
            .nav__item {
                display: block;
            }

            > .nav__link {
                border-bottom: thin solid
                    var(--brand-color-primary-variant-light);

                .svg-icon {
                    transform: rotate(90deg);
                }
            }
        }

        .nav__link {
            border: none;
        }

        .svg-icon {
            transform: rotate(270deg);
        }
    }
    .nav__lvl-3 {
        padding-inline-start: 1.5rem;

        .svg-icon {
            display: none;
        }
    }

    .nav__list:has(.nav__lvl-2.is-selected) {
        .nav__lvl-1:not(.nav__lvl-1:has(.nav__lvl-2.is-selected)),
        .nav__lvl-1:has(.nav__lvl-2.is-selected) > .nav__link,
        .nav__lvl-2:not(.is-selected) {
            display: none !important;
        }
    }
}

@include media-breakpoint-up($nav-breakpoint) {
    .nav--main {
        width: 100vw;
    }
    .nav__lvl-1 {
        > .nav__link {
            font-size: 1.25rem;
        }

        &:hover,
        &.is-selected {
            > .nav__link {
                color: var(--brand-color-primary);
            }
        }
    }
    .nav__item.nav__lvl-2 {
        > .nav__link {
            color: var(--brand-color-primary);
            border-bottom: thin solid var(--brand-color-primary-variant-light);
            font-weight: 700;
        }

        &:has(.nav__item.active, .nav__item.is-selected) {
            > .nav__link {
                color: var(--brand-color-primary);
            }
        }

        .nav__item.active,
        .nav__item.is-selected {
            color: var(--brand-color-primary);
        }
    }
    .nav__item.nav__lvl-3 {
        .svg-icon {
            display: none;
            position: absolute;
            width: 2rem;
            height: 2rem;
            left: -1.5rem;
            top: 50%;
            transform: translateY(-50%);
            color: inherit;
        }

        &:hover {
            position: relative;
            color: var(--brand-color-primary);

            .svg-icon {
                display: block;
            }
        }
    }
}

.nav--footer {
    .nav__list {
        gap: 0.5rem;
    }

    .nav__item {
        display: inline-flex;
        gap: 0.5rem;
        flex-wrap: nowrap;

        &:not(:first-child)::before {
            content: "|";
        }
    }

    .nav__link {
        padding: 0;

        &:focus-visible,
        &:hover {
            outline: none;
            text-decoration: underline;
            text-underline-offset: 4px;
        }
    }
}
