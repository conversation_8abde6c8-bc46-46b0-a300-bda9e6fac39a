export const PageNavigation = ((window, document, undefined) => {

    'use strict';

    const config = {
        classes: {
            list: 'nav__list',
            item: 'nav__item',
            link: 'nav__link',
            active: 'is-selected'
        }
    };

    window.MainMenu = function () {
        function init(nav) {
            nav.addEventListener('click', (event) => {
                const link = event.target.closest(`.${config.classes.link}`);
                if (link) {
                    const navItem = link.closest(`.${config.classes.item}`);

                    if (navItem.querySelector(`.${config.classes.list}`)) {
                        event.preventDefault();

                        if (navItem.classList.contains(config.classes.active)) {
                            navItem.classList.remove(config.classes.active);
                        } else {
                            const siblings = navItem.parentNode.children;
                            Array.from(siblings).forEach((siblings) => {
                                siblings.classList.remove(config.classes.active)
                            });

                            navItem.classList.add(config.classes.active);
                        }
                    }
                }
            });

            function preventAll(event) {
                event.preventDefault();
                event.stopPropagation();
                event.stopImmediatePropagation();
            }

            nav.addEventListener('keydown', event => {
                if (event.keyCode !== 9) return;

                const target = event.target;
                const parentNode = target.parentNode;

                if (parentNode.classList.contains('nav__lvl-1') && !parentNode.classList.contains('is-selected')) {
                    if (event.shiftKey && parentNode.previousSibling) {
                        preventAll(event);
                        parentNode.previousSibling.querySelector('a').focus();
                    } else if (!event.shiftKey && parentNode.nextSibling) {
                        preventAll(event);
                        parentNode.nextSibling.querySelector('a').focus();
                    } else {
                        const focusableElements = document.querySelectorAll('#page-content a, #page-content button, #page-content[tabindex="0"]');
                        if (focusableElements.length > 0) {
                            preventAll(event);
                            focusableElements[0].focus();
                        }
                    }
                } else if (parentNode.classList.contains('nav__lvl-1') && parentNode.classList.contains('is-selected')) {
                    if (event.shiftKey && parentNode.previousSibling) {
                        preventAll(event);
                        parentNode.previousSibling.querySelector('a').focus();
                    }
                }
            })

            nav.querySelectorAll('.nav__lvl-2 a').forEach(element => {
                element.addEventListener('focus', event => {
                    const firstLevel = element.closest('.nav__lvl-1');
                    if (!firstLevel.classList.contains('is-selected')) {
                        firstLevel.querySelector('a').focus();
                    }
                });
            });
        }

        return {
            init: (() => {
                window.addEventListener('DOMContentLoaded', () => {
                    const nav = document.querySelector('.nav.nav--main');
                    if (!nav.dataset.initialized) {
                        init(nav);
                        nav.dataset.initialized = true;
                    }
                });
            })()
        };
    };

    MainMenu();

})(window, document);

export default PageNavigation;
