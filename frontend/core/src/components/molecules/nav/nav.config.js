module.exports = {
    preview: '@preview-full',
    context: {
        id: 'nav',
        class: '',
        items: [
            {
                link: {
                    text: 'Nav Item 01',
                    href: 'javascript:void(0);',
                    class: 'py-3 p-lg-3'
                }
            },
            {
                link: {
                    text: 'Nav Item 02',
                    href: 'javascript:void(0);',
                    class: 'py-3 p-lg-3'
                }
            },
            {
                link: {
                    text: 'Nav Item 03',
                    href: 'javascript:void(0);',
                    class: 'py-3 p-lg-3'
                }
            },
            {
                link: {
                    text: 'Nav Item 04',
                    href: 'javascript:void(0);',
                    class: 'py-3 p-lg-3'
                }
            },
            {
                link: {
                    text: 'Nav Item 05',
                    href: 'javascript:void(0);',
                    class: 'py-3 p-lg-3'
                }
            },
        ]
    },
    variants: [
        {
            name: 'meta',
            context: {
                type: 'meta',
                class: 'nav--meta',
                items: [
                    {
                        link: {
                            text: 'MetaNav Item 01',
                            href: 'javascript:void(0);',
                            class: 'py-3 py-lg-2 px-lg-3'
                        },
                        class: 'nav__item--pipe',
                    },
                    {
                        link: {
                            text: 'MetaNav Item 02',
                            href: 'javascript:void(0);',
                            class: 'py-3 py-lg-2 px-lg-3'
                        },
                        class: 'nav__item--pipe',
                    },
                    {
                        link: {
                            text: 'MetaNav Item 03',
                            href: 'javascript:void(0);',
                            class: 'py-3 py-lg-2 px-lg-3'
                        },
                        class: 'nav__item--pipe',
                    },
                    {
                        link: {
                            text: 'MetaNav Item 04',
                            href: 'javascript:void(0);',
                            class: 'py-3 py-lg-2 px-lg-3'
                        },
                        class: 'nav__item--pipe',
                    },
                    {
                        link: {
                            text: 'MetaNav Item 05',
                            href: 'javascript:void(0);',
                            class: 'py-3 py-lg-2 px-lg-3'
                        },
                        class: 'nav__item--pipe',
                    },
                ]
            }
        },
        {
            name: 'sub',
            context: {
                type: 'sub',
                class: 'nav--sub',
                items: [
                    {
                        link: {
                            text: 'SubNav Item 01',
                            href: 'javascript:void(0);',
                            class: 'py-3 p-lg-3'
                        }
                    },
                    {
                        link: {
                            text: 'SubNav Item 02',
                            href: 'javascript:void(0);',
                            class: 'py-3 p-lg-3'
                        }
                    },
                    {
                        link: {
                            text: 'SubNav Item 03',
                            href: 'javascript:void(0);',
                            class: 'py-3 p-lg-3'
                        }
                    },
                    {
                        link: {
                            text: 'SubNav Item 04',
                            href: 'javascript:void(0);',
                            class: 'py-3 p-lg-3'
                        }
                    },
                    {
                        link: {
                            text: 'SubNav Item 05',
                            href: 'javascript:void(0);',
                            class: 'py-3 p-lg-3'
                        }
                    },
                ]
            }
        },
        {
            name: 'default',
            label: 'Main',
            context: {
                type: 'main',
                class: 'nav--main',
                items: [
                    {
                        link: {
                            text: 'Nav Item 01',
                            href: 'javascript:void(0);',
                            class: 'py-3 p-lg-3'
                        },
                        nav: '@nav--sub'
                    },
                    {
                        link: {
                            text: 'Nav Item 02',
                            href: 'javascript:void(0);',
                            class: 'py-3 p-lg-3'
                        }
                    },
                    {
                        link: {
                            text: 'Nav Item 03',
                            href: 'javascript:void(0);',
                            class: 'py-3 p-lg-3'
                        }
                    },
                    {
                        link: {
                            text: 'Nav Item 04',
                            href: 'javascript:void(0);',
                            class: 'py-3 p-lg-3'
                        }
                    },
                    {
                        link: {
                            text: 'Nav Item 05',
                            href: 'javascript:void(0);',
                            class: 'py-3 p-lg-3'
                        }
                    },
                ]
            }
        }
    ]
}
