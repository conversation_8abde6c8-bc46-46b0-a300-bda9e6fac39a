.alert {
    position: relative;
    @include media-breakpoint-up(md) {
        &__close {
            position: absolute;
            left: 100%;
            top: 0;
        }
    }
}

.vnc-infobox article {
    position: relative;
    p {
        font-size: 1.125rem;
        color: var(--brand-color-tertiary-light);
    }
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        font-size: 1.25rem;
        line-height: 1.5rem;
        font-weight: bold;
        color: var(--brand-color-primary);
        @include media-breakpoint-up(md) {
            font-size: 1.5rem;
            line-height: 1.75rem;
        }
    }
}
.vnc-infobox .svg-icon:has(+ .headline) {
    position: absolute;
    right: 100%;
    top: 0;
    transform: translate(-100%, 30%) scale(1.25);
    color: var(--brand-color-primary);
}
.vnc-infobox .container {
    padding-inline: 4rem;
}
@include media-breakpoint-down(lg) {
    .vnc-infobox .svg-icon:has(+ .headline) {
        position: absolute;
        right: 100%;
        top: 0;
        transform: translate(-100%, 0.4rem) scale(1.25);
    }
}
