//import {initAnimations} from "../../../../../frontend/core/src/js/animations";

export const Alert = (() => {
    const cookieName = "vnc_warnings_dismissed",
        cookie = getCookie(cookieName);

    /**
     * Initialize alert box
     *
     * @param element
     */
    function initAlert(element) {
        if (element) {
            // click on class js-alert-close will close alert
            const closeJs = element.querySelector(".js-alert-close");
            if (closeJs) {
                closeJs.addEventListener("click", (e) => {
                    e.preventDefault();
                    addId(id);
                    element.classList.remove("is-open");
                });
            }

            // get id of element
            const id = element.getAttribute("id");

            // check if cookie is set
            if (!existId(id)) {
                // if cookie is not set
                // open element
                const dialog = id && document.querySelector(`[data-modal-dialog=${id}]`);
                document.documentElement.style.setProperty('--scroll-pos', `${Math.round(document.documentElement.scrollTop)}px`);
                dialog?.showModal();
            }
        }
    }

    /**
     * @desc Test if id of element is in cookie vnc_warnings_dismissed
     *
     * @param alertId
     * @returns {boolean}
     */
    function existId(alertId) {
        // check if cookie is set
        if (cookie) {
            // get array of ids from cookie
            const ids = JSON.parse(cookie);
            // check if id of element is in cookie vnc_warnings_dismissed
            return !!ids.includes(alertId);
        }
        return false;
    }

    /**
     * @desc Add id of element to cookie vnc_warnings_dismissed
     *
     * @param alertId
     */
    function addId(alertId) {
        // check if cookie is set
        if (cookie) {
            // get array of ids from cookie
            const ids = JSON.parse(cookie);
            // add id of element to array of ids
            ids.push(alertId);
            // write array of ids to cookie vnc_warnings_dismissed
            document.cookie =
                "vnc_warnings_dismissed=" + JSON.stringify(ids) + ";;path=/";
        } else {
            // if cookie is not set
            // create array of ids
            const ids = [];
            // add id of element to array of ids
            ids.push(alertId);
            // write array of ids to cookie vnc_warnings_dismissed
            document.cookie =
                "vnc_warnings_dismissed=" + JSON.stringify(ids) + ";;path=/";
        }
    }

    /**
     * Get cookie value
     *
     * @param cookieName
     * @returns {string}
     */
    function getCookie(cookieName) {
        const name = cookieName + "=";
        const decodedCookie = decodeURIComponent(document.cookie);
        const ca = decodedCookie.split(";");
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === " ") {
                c = c.substring(1);
            }
            if (c.indexOf(name) === 0) {
                return c.substring(name.length, c.length);
            }
        }
        return "";
    }

    window.addEventListener("load", () => {
        setTimeout(() => {
            const elements = document.querySelectorAll(".alert");
            Array.from(elements).forEach((element) => {
                initAlert(element);
            });
        }, 0);
    });
})();

export default Alert;
