module.exports = {
  name: 'figure',
  collated: true,
  context: {
  },
  variants: [
    {
      name: 'default',
      label: 'Responsive image',
      context: {
        media: [{
          image: true, 
          media: '@image--picture'
        }],
        caption: 'Figure caption: Responsive image'
      }
    },
    {
      name: 'video',
      label: 'Video with controls',
      context: {
        media: [{
          video: true,
          media: '@video--controls'
        }],
        caption: 'Figure caption: Video with controls'
      }
    },
    {
      name: 'mixed',
      label: 'Figure with mixed media',
      context: {
        media: [{
          image: true,
          media: {
            src: '/static/placeholder/vancado-placeholder--3-4.jpg',
            caption: 'Fig caption / label',
            srcset: [
              { media: '(min-width: 992px)', src: '/static/placeholder/vancado-placeholder--4-3.jpg'},
              { media: '(min-width: 576px)', src: '/static/placeholder/vancado-placeholder--1-1.jpg'},
            ],
            alt: 'Responsive placeholder image',
            className: 'img-fluid aspect-ratio--3-4 aspect-ratio-md--1-1 aspect-ratio-lg--4-3',
          },
        }, {
          video: true,
          media: {
            src: '/static/sample.mp4',
            alt: 'Default placeholder video',
            autoplay: true,
            loop: true,
            className: 'img-fluid aspect-ratio--3-4 aspect-ratio-md--1-1 aspect-ratio-lg--4-3',
          }
        }],
        caption: 'Figure caption: Video with controls'
      }
    },
  ]
}
