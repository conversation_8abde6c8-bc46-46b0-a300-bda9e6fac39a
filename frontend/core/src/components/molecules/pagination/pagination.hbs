<div class="pagination">
    {{#if radio}}
      <div class="input--radio-group radio-group--row">
        {{#each items}}
        <div class="input input--radio mb-0">
          <input
            type="radio"
            name="{{value}}"
            id="{{value}}-{{@index}}"
            value="{{value}}"
            {{#if active}} checked{{/if}}
            {{#if disabled}} disabled{{/if}}
          />
          <label class="input__label" for="{{value}}-{{@index}}">{{ text }}</label>
        </div>
        {{/each}}
      </div>
    {{else}}
      <ul class="pagination__list">
        <li class="pagination__item">
          <a role="button" class="pagination__link pagination__arrow" data-table-scroll="start" aria-label="go to start page">
            {{> @icon symbol="triangle-right" }}
          </a>
        </li>
        {{#each items}}
          <li class="pagination__item">
            <a {{#if value}} href="#{{ value }}" {{/if}} class="pagination__link {{#if active}} is-active{{/if}}" role="button">
                {{ text }}
            </a>
          </li>
        {{/each}}
        <li class="pagination__item">
          <a role="button" class="pagination__link pagination__arrow" data-table-scroll="end" aria-label="go to last page">
            {{> @icon symbol="triangle-right" }}
          </a>
        </li>
      </ul>
    {{/if}}
</div>
