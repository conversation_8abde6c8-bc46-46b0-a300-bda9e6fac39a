.pagination {
    --pagination-color: inherit;
    --pagination-bg-color: var(--brand-color-tertiary-light);
    --pagination-highlight-color: #fff;
  }

  .pagination {
    &__list {
      display: flex;
    //   justify-content: center;
      align-items: center;
      gap: 1rem;
    }

    &__link {
      flex: none;
      display: block;
      width: 1.5rem;
      height: 1.5rem;

      display: grid;
      place-content: center;

      text-decoration: none;

      color: var(--pagination-highlight-color);
      background-color: var(--pagination-bg-color);

      &.is-active {
        color: var(--pagination-highlight-color);
        background: var(--brand-gradient-secondary);
        transform: scale(1.3333);
      }
    }

    &__arrow {
      font-size: 2rem;
      background: none;
      li:first-of-type & {
        transform: scale(-1);
      }
      .svg-icon {
        color: var(--brand-color-secondary);
      }
      &.is-disabled .svg-icon {
        color: var(--link-disabled-color);
      }
    }

    .is-disabled {
      opacity: .5;
      pointer-events: none;
      cursor: default;
    }

    .radio-group {
      display: flex;
      gap: 1rem;
    }
    input[type=radio] {
      display: none;
      + label {
        display: block;
        cursor: pointer;
      }
      &:checked + label {
        color: var(--brand-color-secondary);
      }
    }
  }
