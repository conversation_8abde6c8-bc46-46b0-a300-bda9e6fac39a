module.exports = {
    preview: '@preview-centered',
    variants: [
        {
            name: 'default',
            context: {
                title: 'Tooltip',
                caption: 'Tooltip',
                html: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
            },        
        },
        {
            name: 'trigger',
            context: {
                title: 'Tooltip Trigger',
                caption: 'Tooltip Trigger',
                direction: 'top',
                html: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
            },        
        },
        {
            name: 'top',
            context: {
                visible: true,
                title: 'Top Tooltip',
                caption: 'Top Tooltip',
                direction: 'top',
                html: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
            },        
        },
        {
            name: 'right',
            context: {
                visible: true,
                title: 'Right Tooltip',
                caption: 'Right Tooltip',
                direction: 'right',
                html: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
            },        
        },
        {
            name: 'bottom',
            context: {
                visible: true,
                title: 'Bottom Tooltip',
                caption: 'Bottom Tooltip',
                direction: 'bottom',
                html: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
            },        
        },
        {
            name: 'left',
            context: {
                visible: true,
                title: 'Left Tooltip',
                caption: 'Left Tooltip',
                direction: 'left',
                html: 'Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
            },        
        },
    ]
}