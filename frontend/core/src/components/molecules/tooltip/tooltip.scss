.tooltip {
  position: absolute;

  min-width: 8em;
  max-width: 16em;
  background-color: var(--brand-color-dark);
  padding: .5em .875em;

  font-size: .75rem;
  color: var(--brand-color-light);

  &::before {
    content: '';
    height: 0px;
    width: 0px;
    position: absolute;
    z-index: 1;
  }
  &--top {
    bottom: calc(100% + 1em);
    left: 50%;
    transform: translate(-50%, 0);

    &::before {
      left: 50%;
      top: 100%;
      transform: translate(-50%, 0);
  
      border-left: .62em solid transparent;
      border-right: .62em solid transparent;
      border-top: .62em solid var(--brand-color-dark);
    }
  }
  &--right {
    left: calc(100% + 1em);
    top: 50%;
    transform: translate(0, -50%);

    &::before {
      right: 100%;
      top: 50%;
      transform: translate(0, -50%);
  
      border-top: .62em solid transparent;
      border-bottom: .62em solid transparent;
      border-right: .62em solid var(--brand-color-dark);
    }
  }
  &--bottom {
    top: calc(100% + 1em);
    left: 50%;
    transform: translate(-50%, 0);

    &::before {
      left: 50%;
      bottom: 100%;
      transform: translate(-50%, 0);
  
      border-left: .62em solid transparent;
      border-right: .62em solid transparent;
      border-bottom: .62em solid var(--brand-color-dark);
    }
  }
  &--left {
    right: calc(100% + 1em);
    top: 50%;
    transform: translate(0, -50%);

    &::before {
      left: 100%;
      top: 50%;
      transform: translate(0, -50%);
  
      border-top: .62em solid transparent;
      border-bottom: .62em solid transparent;
      border-left: .62em solid var(--brand-color-dark);
    }
  }
}

[data-toggle="tooltip"] {
  position: relative;

  .tooltip {
    visibility: hidden;
    opacity: 0;
    transition: opacity 200ms ease;
    pointer-events: none;
  }
  
  .tooltip.is-visible,
  &:hover .tooltip {
    visibility: visible;
    opacity: 1;
  }
}
