.map {
    height: 500px;
    width: 100%;
    margin: 2rem 0;
    &__close {
        background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTciIHZpZXdCb3g9IjAgMCAxOCAxNyIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9InJlbW92ZSI+CjxwYXRoIGlkPSJQYXRoIiBkPSJNMTcuMyAxLjRMMTUuOSAwTDguOSA3TDEuOSAwTDAuNSAxLjRMNy41IDguNEwwLjUgMTUuNEwxLjkgMTYuOEw4LjkgOS44TDE1LjkgMTYuOEwxNy4zIDE1LjRMMTAuMyA4LjRMMTcuMyAxLjRaIiBmaWxsPSIjN0QxNDY0Ii8+CjwvZz4KPC9zdmc+Cg==");
        background-position: center center;
        background-repeat: no-repeat;
        height: 1rem;
        width: 1rem;
        cursor: pointer;
        position: absolute;
        top: 0rem;
        right: 2rem;
        z-index: 1;
        @include media-breakpoint-up(xl) {
            right: 4rem;
        }
        @include media-breakpoint-up(xxl) {
            top: 3rem;
        }
    }
    &__info {
        flex-direction: column;
        justify-content: center;
        align-items: start;
        position: relative;
        &--show {
            display: flex;
        }
        &--hidden {
            display: none;
        }

        @include media-breakpoint-up(xxl) {
            position: absolute;
            background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDEiIGhlaWdodD0iMzAwIiB2aWV3Qm94PSIwIDAgMzAxIDMwMCIgZmlsbD0ibm9uZSI+CiAgPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0zNC44OTc0IDQ0LjU1NjlDNzYuNjExNCAtMS4zMDAyNiAxNzkuMTA2IC0xMi43NDMyIDI0OS4xOTggMTQuODA4NUMzMTguMDkyIDQxLjg4OSAzMDAuMDM3IDE1My45MjMgMjkzLjE3IDE5Ny4xNzFDMjg2LjMwMiAyNDAuNDE5IDI2MC4zMjkgMjc1LjQyMiAyMjMuMDYxIDI4NS4yMDdDMTg1Ljc5MiAyOTQuOTkyIDUxLjc5MjEgMzI2LjY2IDIwLjIzOTkgMjQ5LjcwOEMtOS4yMDQwOSAxNzcuODk5IC02LjgxNjYgOTAuNDE0IDM0Ljg5NzQgNDQuNTU2OVoiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl81MDA2XzIwNzIpIi8+CiAgPGRlZnM+CiAgICA8bGluZWFyR3JhZGllbnQgaWQ9InBhaW50MF9saW5lYXJfNTAwNl8yMDcyIiB4MT0iMTUwLjUiIHkxPSIwIiB4Mj0iMTUwLjUiIHkyPSIzMDAiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KICAgICAgPHN0b3Agc3RvcC1jb2xvcj0iI0Y4RjhGNyIvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiNGMUYxRUUiLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgo8L3N2Zz4=");
            padding: 4rem 3rem 2rem;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            background-position: center center;
            top: 50px;
            right: 100px;
            height: 400px;
            width: 400px;
        }
    }

    &__text {
        margin: 1rem 0;
    }

    &__link {
        margin: 1rem 0;
    }
}

.marker {
    &__custom {
        position: absolute;
        width: 46px;
        height: 61px;
        transform: translate(-50%, -100%);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        img {
            width: 2.5rem;
            height: 2.5rem;
        }
    }
    &__icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -75%);
        font-size: 24px;
        color: white;

        &.icon {
            color: white;
            font-size: 17px;
        }
    }
    &__info {
        position: relative;
    }
}
