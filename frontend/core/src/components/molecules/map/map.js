export const Map = (() => {
    let map;
    let locations;
    let bounds;

    const markerBackground = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDciIGhlaWdodD0iNjIiIHZpZXdCb3g9IjAgMCA0NyA2MiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIzLjI1MDYgNjAuMjI2NEMyMi43ODM0IDU5Ljc1OSAxNy4yOTk3IDU0LjIyNyAxMS45MzE1IDQ2Ljk1NDZDOS4yMzg5NSA0My4zMDY5IDYuNTkxMzIgMzkuMjQyNSA0LjYyMDM2IDM1LjE3NjRDMi42NDMwNCAzMS4wOTcyIDEuMzg0NzcgMjcuMDkyOSAxLjM4NDc3IDIzLjU0NTJDMS4zODQ3NyA5LjgwNzkxIDEyLjQ3NyAxLjU4NDk2IDIzLjI1MDYgMS41ODQ5NkMzNC4wMjQxIDEuNTg0OTYgNDUuMTE2NCA5LjgwNzkxIDQ1LjExNjQgMjMuNTQ1MkM0NS4xMTY0IDI3LjA5MjkgNDMuODU4MSAzMS4wOTcyIDQxLjg4MDggMzUuMTc2NEMzOS45MDk4IDM5LjI0MjUgMzcuMjYyMiA0My4zMDY5IDM0LjU2OTYgNDYuOTU0NkMyOS4yMDE0IDU0LjIyNyAyMy43MTc3IDU5Ljc1OSAyMy4yNTA2IDYwLjIyNjRaTTIzLjI1NjYgNjAuMjMyNUMyMy4yNTY2IDYwLjIzMjUgMjMuMjU2NyA2MC4yMzI1IDIzLjI1NjcgNjAuMjMyNUwyMy4yNTY2IDYwLjIzMjVaIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfNTQ5N184NDEpIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiLz4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0icGFpbnQwX2xpbmVhcl81NDk3Xzg0MSIgeDE9IjIzLjI1MDYiIHkxPSIwLjU4NDk2MSIgeDI9IjIzLjI1MDYiIHkyPSI2MS4yMjkiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iIzdEMTQ2NCIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiM0OTA4M0EiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K";

    function initMap() {
        const mapElement = document.getElementById("map");
        const mapLinks = document.querySelectorAll('#map + div + .map__links a');
        if (!mapElement) {
            console.warn("Google Map container not found!");
            return;
        }

        locations = JSON.parse(mapElement.dataset['locations']);
        if (mapLinks.length > 0) {
            locations.forEach((location, i) => {
                if (mapLinks[i]) {
                    const href = mapLinks[i].getAttribute('href');
                    const target = mapLinks[i].getAttribute('target');
                    const title = mapLinks[i].getAttribute('title');

                    location.buttonUrlHref = href ? href : '';
                    location.buttonUrlTarget = target ? target : '';
                    location.buttonUrlTitle = title ? title : '';
                    mapLinks[i].remove();
                }
            });
        }

        bounds = new google.maps.LatLngBounds();
        map = new google.maps.Map(mapElement, {
            zoom: 12,
            maxZoom: 23,
            minZoom: 10,
            center: { lat: 51.8345, lng: 7.0893 },
            // disableDefaultUI: true,
            scaleControl: true,
            zoomControl: true,
            mapTypeControl: false,
            streetViewControl: false,
            fullscreenControl: false,
            styles: [
                {
                    featureType: "poi",
                    elementType: "labels.icon",
                    stylers: [{ visibility: "off" }],
                },
            ],
        });

        locations?.forEach((location) => {
            let iconClass = '';
            if (location.type === 'biogasanlage') {
                iconClass = 'nc-farm-2';
            } else if (location.type === 'photovoltaikanlage') {
                iconClass = 'nc-panel';
            } else if (location.type === 'wasserkraftwerk') {
                iconClass = 'nc-drops';
            } else if (location.type === 'windkraftanlage') {
                iconClass = 'nc-windmill';
            }

            const position = new google.maps.LatLng(location.lat, location.lng);
            bounds.extend(position);

            const markerHtml = `
              <div class="marker__custom">
                  <img src="${markerBackground}" class="marker__background" alt="Marker-Icon zu ${location.title}"/>
                  <i class="marker__icon icon ${iconClass}"></i>
              </div>
          `;

            const marker = new google.maps.OverlayView();
            marker.onAdd = function () {
                const div = document.createElement("div");
                div.innerHTML = markerHtml;
                div.style.position = "absolute";

                div.addEventListener("click", () => showInfo(location));

                this.getPanes().overlayMouseTarget.appendChild(div);
                this.div = div;
            };

            marker.draw = function () {
                const projection = this.getProjection();
                if (!projection) return;

                const position = projection.fromLatLngToDivPixel(
                    new google.maps.LatLng(location.lat, location.lng)
                );
                this.div.style.left = position.x + "px";
                this.div.style.top = position.y + "px";
            };

            marker.onRemove = function () {
                this.div.parentNode.removeChild(this.div);
                this.div = null;
            };

            marker.setMap(map);
        });

        map.fitBounds(bounds);
    }

    function closeInfo() {
        const mapInfo = document.getElementById("map__info");
        if (!mapInfo) return;

        mapInfo.classList.remove("map__info--show");
        mapInfo.classList.add("map__info--hidden");
    }

    function showInfo(location) {
        const mapInfo = document.getElementById("map__info");
        if (!mapInfo) return;

        const buttonText = location.buttonText ? location.buttonText : 'Mehr';
        const type = location.type.charAt(0).toUpperCase() + location.type.slice(1);

        mapInfo.innerHTML = `
            <button class="map__close"></button>
            <h2 class="headline h4">${location.title}</h2>
            <p class="map__text"><strong>Typ:</strong> ${type}</p>
            <p class="map__text"><strong>In Betrieb seit:</strong> ${location.inOperationSince}</p>
            <p class="map__text"><strong>Leistung:</strong> ${location.power}</p>
            <p class="map__text"><strong>Erzeugte Energie:</strong> ${location.energyGenerated}</p>
            <p class="map__text"><strong>Versorgung:</strong> ${location.householdsSupplied}</p>
        `;

        if (location.buttonUrlHref) {
            mapInfo.innerHTML += `<a class="map__link button button--secondary" href="${location.buttonUrlHref}" target="${location.buttonUrlTarget}" title="${location.buttonUrlTitle}">${buttonText}</a>`;
        }

        mapInfo.classList.remove("map__info--hidden");
        mapInfo.classList.add("map__info--show");
        mapInfo.scrollIntoView({
            behavior: 'smooth',
        });

        const closeBtn = mapInfo.querySelector('.map__close');
        if (closeBtn) {
            closeBtn.addEventListener('click', closeInfo);
        }

    }

    window.initMap = initMap;

    document.addEventListener("DOMContentLoaded", () => {
        if (document.getElementById("map")) {
            const script = document.createElement("script");
            script.src =
                "https://maps.googleapis.com/maps/api/js?key=AIzaSyDDYRQIpSDk6TJpm9TtgggebQkWQHOvpbQ&callback=initMap";
            script.async = true;
            script.defer = true;
            document.body.appendChild(script);
        }
    });

})();

export default Map;
