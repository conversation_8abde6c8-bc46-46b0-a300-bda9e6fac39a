module.exports = {
    collated: true,
    preview: '@preview-colors',
    context: {
        id: 'white',
        color: [ { value: '#FFFFFF' } ]
    },
    variants: [
        {
            name: 'primary',
            context: {
                label: 'Primary',
                color: [ { value: '#0088d8' }, { value: '#49bbff' }, { value: '#8ed5ff' }, { value: '#b9e5ff' } ]
            }
        },
        {
            name: 'secondary',
            context: {
                label: 'Secondary',
                color: [ { value: '#0F0F0F' }, { value: '#FAFAFA' } ]
            }
        },
        {
            name: 'tertiary',
            context: {
                label: 'Tertiary',
                color: [ { value: '#000000' }, { value: '#FFFFFF' } ]
            }
        },
        {
            name: 'signal',
            context: {
                label: 'Signal colors',
                color: [
                    { text: 'Info', value: '#008CA0' },
                    { text: 'Success', value: '#008C32' },
                    { text: 'Warning', value: '#D3AC3F' },
                    { text: 'Error', value: '#C33200' },
                ]
            }
        },
    ]
}
