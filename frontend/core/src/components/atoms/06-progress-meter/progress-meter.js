export const ProgressMeter = (() => ({
    init: (() => {
      const progressElements = document.body.querySelectorAll('progress[data-emit-progress]');
      const observer = new MutationObserver(async (mutations) => {
        const emitted = mutations.map(mutation => {
          mutation.target.dispatchEvent(new CustomEvent('progress', {
            bubbles: true,
            cancelable: true,
            detail: { value: mutation.target.value, max: mutation.target.max }
          }));
        });
      });

      const observers = Array.from(progressElements).map(progressElement => {
        observer.observe(progressElement, {
          attributes: true,
        });

        return observer;
      });
    })()
}))();

export default ProgressMeter;