module.exports = {
  collated: true,
  preview: '@preview-article',
  default: 'h1',
  context: {
    tag: 'p',
    size: 'h2',
    title: 'Base Headline',
    text: 'Base Headline',
  },
  variants: [
    {
      name: 'h1',
      title: 'Extra extra large headline',
      label: 'Extra extra large headline',
      context: {
        tag: 'p',
        size: 'h1'
      }
    },
    {
      name: 'xl',
      title: 'Extra large headline',
      label: 'Extra large headline',
      context: {
        tag: 'p',
        size: 'h2'
      }
    },
    {
      name: 'l',
      title: 'Large headline',
      label: 'Large headline',
      context: {
        tag: 'p',
        size: 'h3'
      }
    },
    {
      name: 'm',
      title: 'Medium sized headline',
      label: 'Medium sized headline',
      context: {
        tag: 'p',
        size: 'h4'
      }
    },
    {
      name: 's',
      title: 'Small headline',
      label: 'Small headline',
      context: {
        tag: 'p',
        size: 'h5'
      }
    },
    {
      name: 'xs',
      title: 'Extra small headline',
      label: 'Extra small headline',
      context: {
        tag: 'p',
        size: 'h6'
      }
    },

  ]
}