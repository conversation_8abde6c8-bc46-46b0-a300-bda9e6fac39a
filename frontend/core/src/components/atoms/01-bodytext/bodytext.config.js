module.exports = {
    name: 'bodytext',
    collated: true,
    preview: '@preview-article',
    default: 'm',
    context: {
        text: 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.',
    },
    variants: [
        {
            name: 'xl',
            title: 'Extra large text',
            label: 'Extra large text',
            context: {
                tag: 'p',
                size: 'xl'
            }
        },
        {
            name: 'l',
            title: 'Large text',
            label: 'Large text',
            context: {
                tag: 'p',
                size: 'l'
            }
        },
        {
            name: 'm',
            title: 'Medium sized text',
            label: 'Medium sized text',
            context: {
                tag: 'p',
                size: 'm'
            }
        },
        {
            name: 's',
            title: 'Small text',
            label: 'Small text',
            context: {
                tag: 'p',
                size: 's'
            }
        },
        {
            name: 'xs',
            title: 'Extra small text',
            label: 'Extra small text',
            context: {
                tag: 'p',
                size: 'xs'
            }
        },
        {
            name: 'xxs',
            title: 'Extra extra small text',
            label: 'Extra extra small text',
            context: {
                tag: 'p',
                size: 'xxs'
            }
        },
        {
            name: 'split',
            title: 'Text in columns',
            label: 'Text in columns',
            context: {
                tag: 'p',
                size: 'm',
                columns: true
            }
        },
    ]
}
