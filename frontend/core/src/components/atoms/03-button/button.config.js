module.exports = {
  collated: true,
  preview: '@preview-collated',
  default: 'primary',
  context: {
    text: 'Base button',
    inverted: false,
    type: 'button'
  },
  variants: [
    {
      name: 'primary',
      label: 'Primary',
      context: {
        text: 'Primary button',
        color: 'primary',
      }
    },
    {
      name: 'secondary',
      label: 'Secondary',
      context: {
        text: 'Secondary button',
        color: 'secondary',
      }
    },
    {
      name: 'outline',
      label: 'Outline',
      context: {
        text: 'Outlined button',
        outline: true,
        color: 'primary'
      }
    },
    {
      name: 'light',
      label: 'Light',
      preview: '@preview-dark',
      context: {
        text: 'Light button',
        light: true,
      }
    },
    {
      name: 'icon',
      label: 'Icon',
      context: {
        text: 'Button with icon',
        icon: {
          symbol: 'plus'
        },
      }
    },
    // {
    //     name: 'animated',
    //     label: 'animated',
    //     context: {
    //         text: 'Animated button',
    //         animated: true,
    //     }
    // },
    {
      name: 'small',
      label: 'Small',
      context: {
        text: 'Small button',
        small: true,
        icon: {
          symbol: 'plus'
        },
      }
    },
    {
      name: 'disabled',
      label: 'Disabled',
      context: {
        text: 'Disabled button',
        disabled: true,
      }
    },
  ]
}
