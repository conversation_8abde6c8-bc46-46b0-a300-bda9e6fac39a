button,
.button {
    appearance: none;
    background: none;
    border: none;
    margin: 0;
    padding: 0;
    outline: none;
}

.button {
    --button-accent-color: var(--brand-color-primary);
    --button-highlight-color: var(--button-accent-color);

    --button-text-color: var(--bs-light);
    --button-background-color: var(--button-accent-color);
    --button-background-image: none;
    --button-border-color: transparent;

    --button-hover-text-color: var(--button-text-color);
    --button-hover-background-color: var(--button-accent-color);

    --button-padding-y: 0;
    --button-padding-x: 1.5rem;
    --button-border-width: 0.125rem;
}

.button {
    display: inline-flex;
    gap: 0.5rem;
    align-items: center;
    min-height: 3rem;
    font-weight: bold;
    text-decoration: none;
    padding: var(--button-padding-y) var(--button-padding-x);
    color: var(--button-text-color);
    background-color: var(--button-background-color);
    background-image: var(--button-background-image);
    background-repeat: no-repeat;
    border-radius: 50rem;
    // border: var(--button-border-width) solid var(--button-border-color);
    transition: all 240ms ease;
    cursor: pointer;

    &:hover,
    &:focus,
    &:focus-visible {
        --button-text-color: var(--bs-light);
        --button-background-image: none;
        // color: var(--bs-light);
        // background-image: none;
        text-decoration: none;
    }
    &:focus,
    &:focus-visible {
        outline: 0.125rem solid var(--button-accent-color);
        outline-offset: 0.125rem;
        border-radius: 50rem;
    }

    &:active {
        --button-text-color: var(--bs-light);
        --button-background-image: var(--brand-color-secondary-light);
        text-decoration: none;
        // background-color: var(--button-highlight-color);
    }

    &--primary {
        --button-accent-color: var(--brand-color-primary);
        --button-highlight-color: var(--brand-color-primary-light);
        --button-background-image: var(--brand-gradient-primary);
    }

    &--secondary {
        --button-accent-color: var(--brand-color-secondary);
        --button-highlight-color: var(--brand-color-secondary);
        --button-background-image: var(--brand-gradient-secondary);

        &:active {
            --button-accent-color: var(--brand-color-secondary-light);
            --button-highlight-color: var(--brand-color-secondary-light);
            --button-background-image: var(--brand-color-secondary-light);
            text-decoration: none;
        }
    }

    &--outline {
        --button-text-color: var(--button-accent-color);
        --button-background-color: none;
        --button-background-image: none;
        --button-border-color: var(--button-accent-color);
        &:hover,
        &:focus {
            --button-text-color: var(--bs-light);
            --button-background-color: var(--button-accent-color);
        }
        &:active {
            --button-background-color: var(--button-accent-color);
        }
    }

    &--light {
        --button-text-color: var(--brand-color-secondary-light);
        --button-accent-color: var(--brand-color-secondary);
        background-color: var(--bs-light);
        &:hover,
        &:focus {
            --button-text-color: var(--button-accent-color);
            //   --button-background-color: var(--button-accent-color);
        }
        &:focus {
            outline: 0.125rem solid var(--button-accent-color);
            outline-offset: 0;
        }
        &:focus-visible {
            outline: 0.125rem solid var(--bs-light);
            outline-offset: 2px;
        }
    }

    &--dark {
        --button-accent-color: var(--bs-dark);
    }

    &--size {
        &-s {
            font-size: 1rem;
            min-height: 2rem;
            padding: 0 0.75rem;
        }
        &-l {
            font-size: 1.25rem;
        }
    }

    &--mirrored {
        transform: scale(-1, 1);
    }

    &--toggle {
        input[type="checkbox"] {
            display: none;
        }
    }

    &.rounded-circle {
        justify-content: center;
        align-items: center;
        width: 3rem;
        height: 3rem;
        padding: 0;
    }

    &small,
    &.small {
        min-height: 2rem;
        font-size: 1rem;
        padding: 0.25rem 0.5rem;
    }

    & > .rounded-circle {
        .svg-icon {
            font-size: inherit;
        }
    }

    .caption {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0 auto;
    }

    &:disabled {
        opacity: 0.8;
        pointer-events: none;
        background-color: var(--link-disabled-color);
        background-image: none;
    }
}

.sg-cookie-optin-box-button {
  > button {
    &:focus-visible {
      outline: 0.125rem solid var(--button-accent-color);
      outline-offset: 0.125rem;
    }
  }
}

