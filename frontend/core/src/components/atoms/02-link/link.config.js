module.exports = {
  collated: true,
  context: {
    text: 'Base link',
    href: '#'
  },
  variants: [
    {
      name: "primary",
      context: {
        text: 'Primary link',
        color: 'primary',
      }
    },
    {
      name: "secondary",
      context: {
        text: 'Secondary link',
        color: 'secondary',
      }
    },
    {
      name: "icon",
      context: {
        text: 'Link with icon',
        icon: {
          symbol: 'plus'
        },
      }
    },
    {
      name: "appendIcon",
      context: {
        text: 'Link with appended icon',
        appendIcon: {
          symbol: 'arrow-right'
        },
      }
    },
    {
      name: "styled",
      context: {
        text: 'Styled link with icon',
        styled: true,
        icon: {
          symbol: 'plus'
        },
      }
    },
    {
      name: "disabled",
      context: {
        text: 'Disabled link',
        disabled: true,
      }
    },
  ]
}
