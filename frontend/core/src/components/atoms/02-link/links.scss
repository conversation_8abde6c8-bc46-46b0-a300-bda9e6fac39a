a {
  display: inline-flex;
  gap: .5rem;
  color: var(--link-color);
  text-decoration: none;
  border-radius: 0;
  &:hover,
  &:focus {
    --link-color: var(--brand-color-secondary-dark);
    text-decoration: underline;
    outline: none;
  }
  &:focus-visible {
    outline: 1px solid var(--brand-color-light);
    outline-offset: 2px;
  }
  &:active {
    --link-color: var(--brand-color-secondary-light);
    text-decoration: underline;
    outline: none;
  }
//   &:focus {
//     outline: thin solid var(--brand-color-secondary-dark);
//     outline-offset: .125rem;
//   }
}

article a,
.link {
    &:focus {
        color: #fff;
        background-image: var(--brand-gradient-secondary);
        text-decoration: none;
        outline: none;
        border-radius: 0;
    }
}

.link {
  &--primary {
    --link-color: var(--brand-color-primary-light);
    &:hover,
    &:active {
      color: var(--brand-color-primary);
    }
    &:focus {
        --link-color: #fff;
      }
    }
  &--secondary {
    --link-color: var(--brand-color-secondary-light);
    &:hover,
    &:active {
      --link-color: var(--brand-color-secondary);
    }
    &:focus {
      --link-color: #fff;
    }
  }

  &--styled {
    // &:is(:link, :visited) {
    //   color: var(--brand-color-secondary);
    // }
    // &:is(:hover, :focus, :active) {
    //   text-decoration: none;
    // }
    // &:is(:hover, :focus, :active) {
    //   color: var(--brand-color-secondary);
    //   text-decoration: none;
    // }
    // &:is(:link, :active) {
    //   color: var(--brand-color-primary);
    // }
    // &:not(:active) .svg-icon {
    //   color: var(--brand-color-secondary);
    // }
  }

  &--disabled {
    pointer-events: none;
    opacity: .6;
  }
}

.btn.-icon {
    display: inline-block;
    vertical-align: middle;
    align-items: center;
    &::before {
        display: inline-block;
        font: normal normal normal 0.8em / 1 'Nucleo';
        color: inherit;
        flex-shrink: 0;
        text-transform: none;
        text-decoration: none;
        margin-right: .5rem;
        vertical-align: middle;
    }
    &.-text::before {
        content: "\ea76";
    }
    &.-isDownload::before {
        content: "\ed27";
    }
    &.-isExternal::before {
        content: "\f009";
    }
    &.-isPhone::before {
        content: "\f1d6";
    }
    &.-isEmail::before {
        content: "\f323";
    }
    &.-isWarning::before {
        content: "\ebc2";
    }
    &.-isDate::before {
        content: "\ebcd";
    }
}
