module.exports = {
  name: 'video',
  collated: true,
  context: {
    src: '/static/sample.mp4',
    alt: 'Default placeholder video',
    autoplay: true
  },
  variants: [
    {
      name: 'controls',
      context: {
        controls: true,
        autoplay: false,
        loop: true
      },
    },
    {
      name: 'poster',
      context: {
        poster: '/static/placeholder/vancado-placeholder--16-9.jpg',
        controls: true,
        autoplay: false
      },
    },
    {
      name: 'hoverplay',
      label: 'Play on mouse over',
      context: {
        hoverplay: true,
        muted: true,
        autoplay: false
      },
    },
    {
      name: 'responsive',
      label: 'Play on mouse over',
      context: {
        hoverplay: true,
        muted: true,
        autoplay: false,
        srcset: JSON.stringify({ mediaQuery: '(max-width: 600px)', src: '/static/sample.mp4'})
      },
    },
  ]
}
