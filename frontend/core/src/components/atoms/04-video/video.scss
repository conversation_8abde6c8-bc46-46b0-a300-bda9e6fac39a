video {
  max-width: 100%;
  height: auto;
}

[data-play-on-hover] {
  cursor: pointer;
}

.video-wrapper {
  position: relative;
  display: inline-block;
  width: 100%;
  aspect-ratio: 3/1;

  .text-media & {
    aspect-ratio: unset;
  }
}

.play-pause-btn {
  position: absolute;
  bottom: 1%;
  left: 1%;
  color: var(--brand-color-white);
  height: 3rem;
  width: 3rem;
  font-size: 1.5rem;
  padding: 0.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease-in-out;
  pointer-events: none;
  border: 2px solid var(--brand-color-white);
  &.--active {
    opacity: 1;
    pointer-events: auto;
  }
  &.--hide {
    opacity: 0;
    pointer-events: none;
  }
  &:focus-visible {
    opacity: 1;
    pointer-events: auto;
    outline: 1px solid var(--brand-color-light);
  }

}

.play-pause-btn:hover {
  color: var(--brand-color-light);
}
