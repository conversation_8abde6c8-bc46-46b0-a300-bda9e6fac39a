export const Videoplayer = (() => {
    const players = document.querySelectorAll("video[data-video-player]");

    players.forEach((player) => {
        player.removeAttribute("controls");

        if (player.dataset.playOnHover !== undefined) {
            player.addEventListener("mouseover", () => player.play());
            player.addEventListener("mouseout", () => player.pause());
        } else {
            player.addEventListener("click", () => {
                if (player.paused) {
                    player.play();
                } else {
                    player.pause();
                }
            });
        }

        const playPauseBtn = document.createElement("button");
        playPauseBtn.classList.add("play-pause-btn", "button", "button--secondary");
        playPauseBtn.setAttribute("aria-label", "Play/Pause");
        playPauseBtn.setAttribute("tabindex", "0");
        playPauseBtn.innerHTML = "&#9658;";
        playPauseBtn.classList.add("--hide");
        playPauseBtn.classList.remove("--active");
        playPauseBtn.style.pointerEvents = "none";

        playPauseBtn.addEventListener("click", (event) => {
            event.stopPropagation();
            if (player.paused) {
                player.play();
                playPauseBtn.innerHTML = "&#10074;&#10074;";
            } else {
                player.pause();
                playPauseBtn.innerHTML = "&#9658;";
            }
        });

        const wrapper = document.createElement("div");
        wrapper.classList.add("video-wrapper");
        player.parentNode.insertBefore(wrapper, player);
        wrapper.appendChild(player);
        wrapper.appendChild(playPauseBtn);

        wrapper.addEventListener("mouseover", () => {
            playPauseBtn.classList.remove("--hide");
            playPauseBtn.classList.add("--active");
            playPauseBtn.style.pointerEvents = "auto";
        });

        wrapper.addEventListener("mouseleave", () => {
            if (!playPauseBtn.matches(":focus-visible")) {
                playPauseBtn.classList.remove("--active");
                playPauseBtn.classList.add("--hide");
            }
        });

        player.addEventListener("play", () => {
            playPauseBtn.innerHTML = "&#10074;&#10074;";
        });

        player.addEventListener("pause", () => {
            playPauseBtn.innerHTML = "&#9658;";
        });

        playPauseBtn.addEventListener("focus", () => {
            playPauseBtn.classList.remove("--hide");
            playPauseBtn.classList.add("--active");
        });
    });
})();
