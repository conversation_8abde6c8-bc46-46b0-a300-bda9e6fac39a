.svg-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1em;
  aspect-ratio: 1 / 1;
  .icon, svg{
    display: inline-block;
    width: 1em;
    height: 1em;
    stroke-width: 0;
    stroke: currentColor;
    fill: currentColor;
      path, circle {
          fill: currentColor;
      }
  }
  &--xxl {
    .icon {
      width: 8em;
      height: 8em;
    }
  }
  &--xl {
    .icon {
      width: 4em;
      height: 4em;
    }
  }
  &--large {
    .icon {
      width: 2em;
      height: 2em;
    }
  }
}

