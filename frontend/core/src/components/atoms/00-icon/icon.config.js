const iconData = require('../../../assets/icons.json');
module.exports = {
  collated: true,
  preview: '@preview-article',
  context: {
  },
  variants: [
    ...iconData.icons.map((icon, index) => ({
        name: index === 0 ? 'default' : icon.properties.name,
        label: `${icon.properties.name.charAt(0).toUpperCase()}${icon.properties.name.slice(1)}`,
        context: {
          symbol: icon.properties.name,
          title: `${icon.properties.name} icon`
        }
      })
    )
  ]
}
