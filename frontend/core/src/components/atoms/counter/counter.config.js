const context = {
  from: 0,
  to: 2000,
  duration: 2000,
};

module.exports = {
  name: 'counter',
  context: {
    ...context,
    json: JSON.stringify(context)
  },
  variants: [
    {
      name: 'localized',
      context: {
        locale: 'de',
        json: JSON.stringify({ ...context, locale: 'de' })
      }
    },
    {
      name: 'decimals',
      context: {
        decimals: 2,
        json: JSON.stringify({ ...context, decimals: 2, locale: 'de' })
      }
    },
    {
      name: 'unit',
      context: {
        unit: 'ms',
        json: JSON.stringify({ ...context, unit: 'ms', locale: 'de', decimals: 2 })
      }
    },
    {
      name: 'suffix',
      context: {
        suffix: '+',
        json: JSON.stringify({ ...context, suffix: '+', locale: 'de', decimals: 2, unit: 'ms' })
      }
    }
  ]
}
