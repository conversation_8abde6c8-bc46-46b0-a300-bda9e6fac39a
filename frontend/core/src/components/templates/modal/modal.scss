dialog {
  opacity: 0;
  transition: opacity 800ms;
  position: absolute;
  animation: modal-in 320ms forwards ease;
}
dialog[open] {
  width: 100%;
  opacity: 1;
  pointer-events: inherit;
}
dialog::backdrop {
  backdrop-filter: blur(.25rem);
  background-color: rgba(0, 0, 0, .6);
}

@keyframes modal-in {
  from {
    translate: 0 -100%;
    opacity: 0;
  }
}

.modal-dialog {
  background-color: var(--bs-light);
  border: none;
  padding: 3rem 1rem;
  min-width: 300px;
  max-width: 300px!important;

  &__header {
      .btn-close {
        position: absolute;
        top: 4%;
        right: 3%;
        background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTciIGhlaWdodD0iMTciIHZpZXdCb3g9IjAgMCAxNyAxNyIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9InJlbW92ZSI+CjxwYXRoIGlkPSJQYXRoIiBkPSJNMTYuOCAxLjRMMTUuNCAwTDguNCA3TDEuNCAwTDAgMS40TDcgOC40TDAgMTUuNEwxLjQgMTYuOEw4LjQgOS44TDE1LjQgMTYuOEwxNi44IDE1LjRMOS44IDguNEwxNi44IDEuNFoiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl81MjM0XzUyNikiLz4KPC9nPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzUyMzRfNTI2IiB4MT0iOC40IiB5MT0iMCIgeDI9IjguNCIgeTI9IjE2LjgiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iIzdEMTQ2NCIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiM0OTA4M0EiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K");
        background-repeat: no-repeat;
        background-position: center center;
        opacity: 0.75;

        &:hover {
          opacity: 1;
        }
      }
    }

  @include media-breakpoint-up(sm) {
    padding: 4rem 3rem;
    max-width: 500px!important;
  }
  @include media-breakpoint-up(md) {
    max-width: 700px!important;
  }
  @include media-breakpoint-up(lg) {
    padding: 4rem 7rem;
    max-width: 900px!important;
  }
  @include media-breakpoint-up(xl) {
    padding: 4rem 7rem;
    max-width: 1200px!important;
  }

  &--sm {
    max-width: 300px;
  }
  &--md {
    max-width: 500px;
  }
  &--lg {
    max-width: 900px;
  }
  &--xl {
    max-width: 1200px;
  }
}

:root {
  --scroll-pos: 0;
}
body:has(dialog[open]) {
  overflow: hidden;
  position: fixed;
  width: 100%;
  top: calc(-1 * var(--scroll-pos));
}

.modal-dialog {
  container-name: modal-dialog;
  container-type: inline-size;
}

@container modal-dialog (min-width: 600px) {
  .modal-dialog {
    &__header {
    //   .modal-dialog__title,
    //   .btn-close {
    //     font-size: 2rem;
    //   }
    }
  }
}

