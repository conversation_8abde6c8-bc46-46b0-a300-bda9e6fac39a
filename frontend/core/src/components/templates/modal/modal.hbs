<div class="modal-container">
  {{#if trigger}}
    <div class="w-100 h-100 d-flex justify-content-center align-items-center my-5">
      <button
        type="button"
        class="button button--secondary"
        data-modal-toggle="{{ id }}"
        aria-controls="{{ id }}"
      >Launch demo modal</button>
    </div>
  {{/if}}

  <dialog
    id="{{ id }}"
    data-modal-dialog="{{ id }}"
    class="modal-dialog
    {{#if size}} modal-dialog--{{size}}{{else}} modal-dialog--lg{{/if}}
    {{ className }}
    "
    {{#if open}} data-modal-open{{/if}}
    {{#if dismiss}} data-modal-dismiss{{/if}}
    {{#if title}} aria-labelledby="{{ id }}-label"{{/if}}
    >
    <div class="modal-dialog__content">
      <div class="modal-dialog__header d-flex gap-2">
        {{#if title}}<p class="modal-dialog__title" :id="{{ id }}-label">{{ title }}</p>{{/if}}
        <button
          type="button"
          class="btn-close ms-auto"
          aria-label="Close"
          data-modal-close
        ></button>
      </div>
      <div class="modal-dialog__body">

        {{#>@partial-block }}
          empty layout
        {{/@partial-block }}

      </div>
      {{#if footer}}
        <div class="modal-dialog__footer d-flex gap-2">
          <button
            type="button"
            class="button ms-auto"
            data-modal-close
            autofocus
          >Close</button>
        </div>
      {{/if}}
    </div>
  </dialog>
</div>
