:root,
*::before,
*::after {
  --vnc-accordion-intendation: 1rem;
}


.accordion {
  @include media-breakpoint-up(md) {
    --vnc-accordion-intendation: 1.5rem;
  }

  &__item {
    position: relative;
    border-bottom: .125rem solid var(--brand-color-primary-variant-light);

    &:first-of-type {
      border-top: .125rem solid var(--brand-color-primary-variant-light);
    }
  }

  &__toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 5rem;
    color: #575756;
    font-size: 1.25rem;
    font-weight: 700;

    .svg-icon {
        flex: none;
        width: 2rem;
        height: 2rem;
        background-repeat: no-repeat;
        color: var(--brand-color-secondary-light);
        border-radius: 50rem;
      }

    i {
        font-size: 2rem;
        transform: rotate(0);
        transform-box: fill-box;
        transform-origin: center;
        transition: transform 180ms ease-in-out;
    }

    &[aria-expanded=true] i {
      transform: rotate(180deg);
    }
  }

  .accordion__panel {
    display: grid;
    grid-template-rows: 1fr;
    transition: grid-template-rows 320ms cubic-bezier(.5, 0, .5, 1);

    > div {
      overflow-y: hidden;
    }
  }
  .accordion__panel.is-collapsed {
    grid-template-rows: 0fr;
  }

  // .accordion__panel {
  //     position: relative;
  //     // z-index: 2;
  //     border: thin solid transparent;
  //     transition: all 320ms ease-in-out;
  // }
  &__item {
    padding-inline: var(--vnc-accordion-intendation);
  }
}


@keyframes accordion-toggle {
  from {
    translate: 0 -100%;
    opacity: 0;
  }
}
