module.exports = {
  name: 'accordion',
  collated: true,
  context: {
    headingTag: 'h3',
    options: JSON.stringify({
      exclusive: true
    }),
    accordionItems: [
      {
        toggle: {
          text: 'Base accordion item 01'
        },
        body: {
          content: '<div class="py-2"><p>Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.</p></div>',
        }
      },
      {
        expanded: true,
        toggle: {
          text: 'Base accordion item 02'
        },
        body: {
          content: '<div class="py-2"><p>Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.</p></div>',
        }
      },
      {
        toggle: {
          text: 'Base accordion item 03'
        },
        body: {
          content: '<div class="py-2"><p>Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.</p></div>',
        }
      }
    ]
},
  variants: [
    {
      name: 'force',
      label: 'Force expanded',
      context: {
        options: JSON.stringify({
          exclusive: true,
          force: true
        })
      }
    },
    {
      name: 'non-exclusive',
      label: 'Non exclusive',
      context: {
          options: JSON.stringify({
            exclusive: false
          }),
          accordionItems: [
          {
            toggle: {
              text: 'Base accordion item 01'
            },
            body: {
              content: '<div class="py-2"><p>Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.</p></div>',
            }
          },
          {
            toggle: {
              text: 'Base accordion item 02'
            },
            body: {
              content: '<div class="py-2"><p>Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.</p></div>',
            }
          },
          {
            toggle: {
              text: 'Base accordion item 03'
            },
            body: {
              content: '<div class="py-2"><p>Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.</p></div>',
            }
          }
        ]
      }
    },
  ]
}
