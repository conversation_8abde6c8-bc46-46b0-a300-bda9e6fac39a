export const Accordion = (() => {
  function accordionItem(accordion) {
    const panel = accordion && accordion.querySelector('[data-accordion-panel]');
    const container = accordion.closest('[data-accordion]');
    const options = container.dataset.accordion && JSON.parse(container.dataset.accordion);

    function expand(toggle) {
      const parent = toggle && toggle.closest('[data-accordion-item]');
      const panel = parent && parent.querySelector('[data-accordion-panel]');

      panel.hidden = false;
  
      const reflow = panel.offsetHeight;
  
      panel.classList.remove('is-collapsed')
      toggle.ariaExpanded = true;
  
      panel.addEventListener('transitionend', (event) => {
        panel.firstElementChild.focus();
      }, { once: true });
    }
  
    function collapse(toggle) {
      const parent = toggle && toggle.closest('[data-accordion-item]');
      const panel = parent && parent.querySelector('[data-accordion-panel]');

      if (! panel.hidden) {
        panel.classList.add('is-collapsed')
        toggle.ariaExpanded = false;
    
        panel.addEventListener('transitionend', () => {
          panel.hidden = true;
        }, { once: true });
      }
    }
  
    window.addEventListener('DOMContentLoaded', () => {
      accordion.addEventListener('click', (event) => {
        const toggle = event.target.closest('[data-accordion-toggle]');
        if (toggle) {
          if (options.exclusive) {
            const items = container.querySelectorAll('[data-accordion-item]');
            Array.from(items).forEach(item => {
              const _toggle = item.querySelector('[data-accordion-toggle]');
              if (!item.contains(event.target)) {
                collapse(_toggle);
              }
            });
          }
  
          const panel = accordion && accordion.querySelector('[data-accordion-panel]');
          if (panel) {
            if (panel.hidden) {
              expand(toggle);
            }
            else if(!options.force) {
              collapse(toggle);
            }
          }
        }
      });
    });
  }

  const accordions = document.querySelectorAll('[data-accordion-item]');
  if (accordions) {
    Array.from(accordions).forEach(accordion => {
      accordionItem(accordion);
    });
  }
})();

export default Accordion;