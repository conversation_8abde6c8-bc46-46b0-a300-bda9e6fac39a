<div class="accordion" data-accordion="{{options}}">
  {{#each accordionItems}}
    <div
      class="accordion__item"
      data-accordion-item
      >
      <{{../headingTag}} class="accordion__heading h5">
        <button
          type="button"
          class="accordion__toggle d-flex gap-2 w-100 text-start"
          data-accordion-toggle
          id="accordion-toggle-{{../_self.name}}-{{@index}}"
          aria-expanded="{{#if expanded}}true{{else}}false{{/if}}"
          aria-controls="accordion-body-{{../_self.name}}-{{@index}}"
          >
          <span class="caption">{{ this.toggle.text }}</span>
          {{> @icon symbol="chevron-wide" className="ms-auto" }}
        </button>
      </{{../headingTag}}>
      <div
        class="accordion__panel {{#unless expanded }} is-collapsed{{/unless}}"
        data-accordion-panel
        role="region"
        id="accordion-panel-{{../_self.name}}-{{@index}}"
        aria-labelledby="accordion-toggle-{{../_self.name}}-{{@index}}"
        {{#unless expanded }}hidden{{/unless}}
        >
        <div class="accordion__content">
          {{#>@partial-block}}
            {{{ this.body.content }}}
          {{/@partial-block}}
        </div>
      </div>
    </div>
  {{/each}}
</div>

