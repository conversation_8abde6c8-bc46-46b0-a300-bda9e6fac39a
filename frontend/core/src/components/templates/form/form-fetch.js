export { serialize } from './_form-serialize.js';

export const FormFetch = (() => {
  let form,
    request,
    mountpoint;

  async function submit(event) {
    const form = event.target.closest('form');
    const dataset = {
      mountpoint: form.dataset.fetchMountpoint
    };

    const formdata = {
      method: form.method,
      credentials: 'same-origin',
      headers: {
        'X-Requested-With': 'XHR',
        'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8'
      }
    };

    if (form.method === 'post') {
      formdata.body = serialize(form);
    }

    const fetched = await fetch(form.action, formdata)
      .then((response) => {
        if (response.ok) {
          return response.text();
        }
      })
      .catch(function (error) {
        console.error(error);
      });

    const mountpoint = dataset && dataset.mountpoint
      ? form.closest(dataset.mountpoint) || form.querySelector(dataset.mountpoint) || form
      : form;

    const frag = document.createElement('div');
    frag.innerHTML = fetched;

    const update = frag.querySelector(dataset.mountpoint) || frag.querySelector('[data-fetch-body]');

    mountpoint.parentNode.replaceChild(update, mountpoint);

    // trigger doc ready events
    window.dispatchEvent(new Event('DOMContentLoaded', {
      bubbles: true,
      cancelable: true
    }));

    window.dispatchEvent(new Event('load', {
      bubbles: true,
      cancelable: true
    }));

    return fetched;
  }

  return {
    init: (() => {
      window.addEventListener('DOMContentLoaded', function () {

        // refactor this
        if (document.documentElement.classList.contains('submitter')) {
          return;
        }
        document.documentElement.classList.add('submitter');

        document.body.addEventListener('submit', function (event) {
          form = event.target.closest('[data-fetch-submit]');
          if (form) {
            event.preventDefault();

            if (form.checkValidity()) {
              request = submit(event);
              request.then(() => {
                //console.log('...fetched as promised');
              });
            }
          }
        });
      });
    })()
  };
})();

export default FormFetch;
