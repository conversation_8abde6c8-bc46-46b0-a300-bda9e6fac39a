export const FormValidation = (() => {
  function validate(event) {
    const form = event.target.closest('form[data-custom-validate]');
    const config = form && form.dataset.customValidate && JSON.parse(form.dataset.customValidate);
    const buttons = form && form.querySelectorAll('button[type=submit]');

    if (!buttons) {
      return false;
    }

    // if (form.checkValidity()) {
    //   Array.from(buttons).forEach((button) => {
    //     if (config.toggleSubmit) {
    //       button.disabled = false;
    //     }
    //   });
    // }
    // else {
    //   Array.from(buttons).forEach((button) => {
    //     if (config.toggleSubmit) {
    //       button.disabled = true;
    //     }
    //   });
    // }
  }

  function submit(event) {
    const form = event.target.closest('form');

    form.dataset.validate = true;

    if (form.checkValidity && !form.checkValidity()) {
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();
    }
  }

  return {
    init: (() => {
      window.addEventListener('DOMContentLoaded', () => {
        document.addEventListener('input', validate);
        document.addEventListener('change', validate);

        document.addEventListener('submit', submit);

        const forms = document.querySelectorAll('form');

        // suppress html5 error notifications
        Array.from(forms).forEach(function (form) {
          form.noValidate = true;
        });
      });
    })()
  };
})();

export default FormValidation;
