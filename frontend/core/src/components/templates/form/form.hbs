<div class="form {{ className }}">
  <form
    {{#if id }} id="{{ id }}"{{/if}}
    {{#if name }} name="{{ name }}"{{/if}}
    {{#if action }} action="{{ action }}"{{/if}}
    {{#if method }} method="{{ method }}"{{/if}}
    {{#unless autocomplete}} autocomplete="off" role="presentation"{{/unless }}
    {{#if novalidate }} novalidate{{/if}}
    {{#if customValidate }} data-custom-validate='{ "toggleSubmit": {{ toggleSubmit }} }'{{/if}}
    {{#if fetchSubmit }} data-fetch-submit='{ "action": "{{ action }}", "method": "{{ method }}" }'{{/if}}
    {{#if mountpoint }} data-fetch-mountpoint="{{ mountpoint}}"{{/if}}
    >
    {{#>@partial-block}}
      Form content
    {{/@partial-block}}
  </form>
</div>
