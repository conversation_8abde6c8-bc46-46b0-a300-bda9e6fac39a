.tabs {
  .tab-nav {
    display: flex;
    gap: .5rem;
  }

  .tab-group {
    position: relative;
    display: flex;
  }

  // .tab-group__item {
  //   display: none;
  // }

  // > input[type=radio] {
  //   &:nth-child(0):checked ~ .tab-group > .tab-group__item:nth-child(0) {
  //     display: block;
  //   }
  //   &:nth-child(1):checked ~ .tab-group > .tab-group__item:nth-child(1) {
  //     display: block;
  //   }
  //   &:nth-child(2):checked ~ .tab-group > .tab-group__item:nth-child(2) {
  //     display: block;
  //   }
  //   &:nth-child(3):checked ~ .tab-group > .tab-group__item:nth-child(3) {
  //     display: block;
  //   }
  //   &:nth-child(4):checked ~ .tab-group > .tab-group__item:nth-child(4) {
  //     display: block;
  //   }
  // }

  .tab-group__item {
    flex: 1 0 100%;
    transform: translateX(-100%);
    transition: all 300ms ease-in-out;
  }

  > input[type=radio] {
    position: absolute;
    appearance: none;

    @for $value from 0 through 8 {
      &:nth-child(#{$value}):checked ~ .tab-group > * {
        transform: translateX(calc(100% - 100% * #{$value}));
      }
    }
  }
}
