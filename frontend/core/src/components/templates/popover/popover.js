import { isSupported } from "./popover-polyfill";

export const Popover = (() => {
  // if (!("anchorName" in document.documentElement.style)) {
  //   window.UPDATE_ANCHOR_ON_ANIMATION_FRAME = true;
  //   import('./css-anchor-positioning');
  // }

  document.addEventListener('DOMContentLoaded', () => {
    if (isSupported()) {
      //console.log('Load popover polyfill');
    }
  });

  document.addEventListener('DOMContentLoaded', () => {
    const popovers = document.querySelectorAll('[popover]');
    Array.from(popovers).forEach(popover => {
      const toggles = document.querySelectorAll(`[popovertarget=${popover.id}]`);
      Array.from(toggles).forEach(toggle => {
        toggle.addEventListener('mouseenter', () => {
          //console.log('mouseenter', toggles);
          popover.showPopover();
        });
        toggle.addEventListener('mouseleave', () => {
          // popover.hidePopover();
        });
      });
    });
  });
})();

export default Popover;
