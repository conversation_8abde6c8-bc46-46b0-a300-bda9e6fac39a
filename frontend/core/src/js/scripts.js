import { AnimatedCounter } from '../components/atoms/counter/counter';
import { ProgressMeter } from '../components/atoms/06-progress-meter/progress-meter';
import { Accordion } from '../components/templates/accordion/accordion';
import { Carousel } from '../components/molecules/carousel/carousel';
import { InputFile } from '../components/molecules/form-elements/input-file/input-file';
import { InputSelect } from '../components/molecules/form-elements/input-dropdown/input-select';
import { InputRange } from '../components/molecules/form-elements/input-range/input-range';
import { ScrollTable } from '../components/molecules/table/table';
import { InputCheckbox } from '../components/molecules/form-elements/input-checkbox/input-checkbox';
import { Teaser } from '../components/molecules/teaser/teaser';
import { Map } from '../components/molecules/map/map';
import { VncEvents } from '../components/organisms/vnc-events/vnc-events';
import { VncEventList } from '../components/organisms/vnc-event-list/vnc-event-list';

import { FormFetch, serialize } from '../components/templates/form/form-fetch';
import { FormValidation } from '../components/templates/form/form-validation';

import { ModalDialog } from '../components/templates/modal/modal';
import { Popover } from '../components/templates/popover/popover';

import { StickyHeader } from '../components/organisms/page-header/page-header';

import { Videoplayer } from '../components/atoms/04-video/video';

import { PageNavigation } from '../components/molecules/nav/nav';


import { initAnimations } from './animations';
import { Alert } from '../components/molecules/00-alert/alert';
import { initInteractiveImage } from "./interactive-image";

initAnimations();
initInteractiveImage();

(function() {
    const toggles = document.querySelectorAll('[data-bs-toggle]');
    toggles.forEach(toggle => {
        const targetId = toggle.dataset.bsTarget;
        const targetNode = document.querySelector(targetId);

        const config = { attributes: true, childList: true, subtree: true };

        const callback = (mutationList, observer) => {
            for (const mutation of mutationList) {
                if (mutation.type === "attributes") {
                    toggle.ariaExpanded = targetNode.classList.contains('show');
                }
            }
        };

        const observer = new MutationObserver(callback);
        observer.observe(targetNode, config);
    });

    window.addEventListener('DOMContentLoaded', () => {
        function toggleFieldset(fieldsetToggle)
        {
            const next = fieldsetToggle.closest('fieldset').nextElementSibling;
            if (next) {
                next.toggleAttribute('hidden');
            }
        }

        [...document.querySelectorAll('[data-toggle-following-sibling]')].forEach(fieldsetToggle => {
            toggleFieldset(fieldsetToggle);
        })
        document.addEventListener('click', event => {
            const fieldsetToggle = event.target.closest('[data-toggle-following-sibling]');
            if (fieldsetToggle) {
                fieldsetToggle.toggleAttribute('hidden');
                toggleFieldset(fieldsetToggle);
            }
        });
    });

    const config = {attributes: true, childList: true, subtree: true};
    const callback = (mutationList, observer) => {
        for (const mutation of mutationList) {
            if (
                mutation.target.closest('.sg-cookie-optin-box') &&
                mutation.type === "attributes" &&
                mutation.attributeName === 'aria-hidden'
            ) {
                mutation.target.hidden = mutation.target.ariaHidden === 'true';
            }
        }
    };
    const observer = new MutationObserver(callback);
    const targetNode = document.documentElement || document.body;
    observer.observe(targetNode, config);

})()

import '../scss/style.scss';
