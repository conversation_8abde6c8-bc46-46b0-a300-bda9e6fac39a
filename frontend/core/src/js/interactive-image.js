export function initInteractiveImage() {
    window.addEventListener('DOMContentLoaded', () => {
        const vncInteractiveImages = document.querySelectorAll('.vncInteractiveImage');

        vncInteractiveImages?.forEach(vncInteractiveImage => {
            const popovers = vncInteractiveImage.querySelectorAll(".content-box__popover");

            popovers?.forEach(popover => {
                 popover.addEventListener('afterShowDesktopPopover', (e) => {
                     e.target.style.top = `0`;
                     if (e.target.classList.contains('right')) {
                         e.target.style.left = 'calc(100% + 16px)';
                     } else if (e.target.classList.contains('left')) {
                         e.target.style.right = 'calc(100% + 16px)';
                     }
                 });
            });
        });
    });
}
