import { gsap } from "gsap";

export function animateStoneFirst() {
    gsap.to(".stone__first", {
        rotation: "+=360",
        duration: 15,
        repeat: -1,
        ease: "linear",
        yoyo: false,
        immediateRender: false,
        onUpdate: () => {},
    });
}

export function animateStoneSecond() {
    gsap.to(".stone__second", {
        rotation: "+=360",
        duration: 20,
        repeat: -1,
        ease: "linear",
        yoyo: false,
        immediateRender: false,
        onUpdate: () => {},
    });
}

export function initAnimations() {
    document.addEventListener("DOMContentLoaded", function () {
        animateStoneFirst();
        animateStoneSecond();
    });
}

export const AnimationToggler = (() => {
    const animationArea = document.createElement("div");
    animationArea.classList.add("animation-controls");

    const toggleButton = document.createElement("button");
    toggleButton.classList.add(
        "toggle-animations",
        "button",
        "button--secondary"
    );
    toggleButton.setAttribute("aria-label", "Animationen stoppen");
    toggleButton.setAttribute("aria-pressed", "false");
    toggleButton.setAttribute("tabindex", "0");

    let animationsRunning = true;

    const storedStatus = localStorage.getItem("animationsRunning");
    if (storedStatus === "false") {
        animationsRunning = false;
        gsap.globalTimeline.pause();
    }

    const iconWrapper = document.createElement("span");
    iconWrapper.classList.add("animation-icon");

    const pauseSVG = `
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
        <rect x="6" y="4" width="4" height="16" />
        <rect x="14" y="4" width="4" height="16" />
      </svg>
    `;

    const playSVG = `
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
        <polygon points="5,3 19,12 5,21" />
      </svg>
    `;

    iconWrapper.innerHTML = pauseSVG;

    const labelText = document.createElement("span");
    labelText.classList.add("label-text");
    labelText.textContent = "Animation pausieren";

    toggleButton.appendChild(iconWrapper);
    toggleButton.appendChild(labelText);

    function updateButton() {
        if (animationsRunning) {
            iconWrapper.innerHTML = pauseSVG;
            labelText.textContent = "Animation pausieren";
            toggleButton.setAttribute("aria-label", "Animationen pausieren");
            toggleButton.setAttribute("aria-pressed", "false");
        } else {
            iconWrapper.innerHTML = playSVG;
            labelText.textContent = "Animation starten";
            toggleButton.setAttribute("aria-label", "Animationen starten");
            toggleButton.setAttribute("aria-pressed", "true");
        }
    }

    function handleToggle(event) {
        event.stopPropagation();
        animationsRunning = !animationsRunning;

        localStorage.setItem(
            "animationsRunning",
            animationsRunning ? "true" : "false"
        );

        animationsRunning
            ? gsap.globalTimeline.play()
            : gsap.globalTimeline.pause();
        updateButton();
    }

    toggleButton.addEventListener("click", handleToggle);
    toggleButton.addEventListener("focus", () =>
        toggleButton.classList.add("--expanded")
    );
    toggleButton.addEventListener("mouseover", () =>
        toggleButton.classList.add("--expanded")
    );
    toggleButton.addEventListener("blur", () =>
        toggleButton.classList.remove("--expanded")
    );
    toggleButton.addEventListener("mouseleave", () =>
        toggleButton.classList.remove("--expanded")
    );

    animationArea.appendChild(toggleButton);
    document.body.insertBefore(animationArea, document.body.firstChild);

    updateButton();
})();
