#!/bin/bash

TYPE=$1;

if [ -z "$(git status --porcelain)" ]; then 
  export GIT_MERGE_AUTOEDIT=no

  NODE_VERSION=$(node -p -e "require('./package.json').version");
  IFS='.' read -r -a VERSION <<< "$NODE_VERSION"

  case $TYPE in
    major)
      (( VERSION[0]+=1 ));
      (( VERSION[1]=0 ));
      (( VERSION[2]=0 ));
      ;;
    minor)
      (( VERSION[1]+=1 ));
      (( VERSION[2]=0 ));
      ;;
    patch | *)
      (( VERSION[2]+=1 ));
      ;;
  esac

  NEXT=${VERSION[0]}.${VERSION[1]}.${VERSION[2]}
  echo "Starting new release: $NEXT";

  git-flow release start $NEXT

  npm version $NEXT;

  git-flow release publish $NEXT

  git flow release finish "$NEXT" -m "$NEXT" -T "$NEXT"
  unset GIT_MERGE_AUTOEDIT

  echo "Release published: $NEXT";
else 
  echo "Git status unclean, please commit your changes!";
fi
