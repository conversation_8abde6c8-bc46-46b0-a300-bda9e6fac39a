{"name": "vnc-component-lib", "version": "0.2.25", "description": "", "files": ["dist"], "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "NODE_ENV=development BLUEBIRD_W_FORGOTTEN_RETURN=0 vite", "build": "NODE_ENV='production' vite build --emptyOutDir", "preview": "vite preview", "watch": "NODE_ENV='production' vite build --watch", "fractal:static": "NODE_ENV='production' fractal build && vite build", "dist": "NODE_ENV='production' vite build --config './vite.config.dist.js' ", "release": "bash ./scripts/release.sh", "postrelease": "bash ./scripts/publish.sh"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@frctl/fractal": "^1.5.14", "@frctl/mandelbrot": "^1.10.2", "sass": "^1.57.1", "svg-parser": "^2.0.4", "vite": "^4.0.4", "vite-plugin-css-export": "^2.0.0", "vite-plugin-sass-glob-import": "^2.0.0"}, "dependencies": {"@accessible360/accessible-slick": "^1.0.1", "@pluginjs/headroom": "^0.8.11", "@splidejs/splide": "^4.1.4", "@splidejs/splide-extension-auto-scroll": "^0.5.3", "bootstrap": "^5.3.2", "gsap": "^3.12.5"}, "main": "./dist/vnc-component-lib.es.js", "mobule": "./dist/vnc-component-lib.es.js", "exports": {".": {"import": "./dist/vnc-component-lib.es.js", "require": "./dist/vnc-component-lib.umd.js"}}, "browserslist": ["defaults", "not IE 11"]}