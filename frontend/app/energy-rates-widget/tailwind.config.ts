import type { Config } from "tailwindcss";

export default {
  prefix: 'vnc-',
  important: true,

  content: ["./src/**/*.{html,js,svelte,ts}"],
  safelist: ['vnc-opacity-50'],
  darkMode: ['selector', '[data-mode="dark"]'],

  theme: {
    fontFamily:{
      sans: ['inherit'],
    },
    extend: {
      colors: {
        primary: '#5e7a00',
        secondary: '#49083a',
        // input: '#eceeef',
        green: '#5e7a00',
        blue: '#0071ad',
        orange: '#e06500',
        red: '#c82314',
      }
    }
  },

  plugins: [
    require("@tailwindcss/typography"),
    require('@tailwindcss/forms')
  ]
} as Config;
