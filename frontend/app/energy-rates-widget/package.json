{"name": "nrg-rates-widget", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "check": "svelte-check --tsconfig ./tsconfig.json && tsc -p tsconfig.node.json"}, "devDependencies": {"@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.14", "@tsconfig/svelte": "^5.0.4", "@types/node": "^22.10.1", "autoprefixer": "^10.4.20", "sass-embedded": "^1.82.0", "svelte": "^5.2.7", "svelte-check": "^4.1.0", "tailwindcss": "^3.4.9", "tslib": "^2.8.1", "typescript": "~5.6.2", "vite": "^6.0.1", "vite-plugin-css-injected-by-js": "^3.5.2"}, "dependencies": {"svelte-i18n": "^4.0.1", "vite-svg-loader": "^5.1.0"}}