.vnc-input {
  position: relative;
  display: flex;
  gap: .3125rem;
  flex-direction: column;
  text-align: start;

  >input,
  >textarea,
  >p,
  >span {
    order: 1;
  }

  >label {
    order: 0;
  }

  &__label {
    font-weight: bold;
    min-height: 3rem;
    display: inline-flex;
    align-items: end;
    line-height: 1;
  }

  &__error {
    order: 2;
  }

  input,
  textarea {
    display: block;
    width: 100%;
    height: var(--vnc-widget-input-height);
    outline: none;
    border: thin solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--vnc-widget-input-color);
    padding: 0 1rem;
    transition: all 120ms ease;
  }

  ::placeholder {
    color: var(--vnc-widget-input-color);
    opacity: 1;
  }

  ::-ms-input-placeholder {
    color: var(--vnc-widget-input-color);
  }

  textarea {
    height: auto;
  }

  input[type=checkbox],
  input[type=radio] {
    position: relative;
    display: grid;
    place-content: center;
    width: 1.5em;
    height: 1.5em;
    margin: 0;
    padding: 0;
    appearance: none;
    border: 0.125em solid currentColor;
    // background-color: #fff;
    font: inherit;
    color: currentColor;
  }

  input[type=checkbox]::before,
  input[type=radio]::before {
    content: '';
    width: .5em;
    height: .5em;
    transform: scale(0);
    transition: 180ms transform ease-in-out;
    box-shadow: inset .5em .5em currentColor;
  }

  input[type=checkbox]::after,
  input[type=radio]::after {
    content: '';
    width: .5em;
    height: .5em;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: scale(0) translateX(-50%) translateY(-50%);
    transform-origin: 0 0;
    box-shadow: 0 0 0 .5em currentColor;
    opacity: .32;
    border-radius: 99999px;
    transition: all 320ms ease-in-out;
  }


  input[type=radio] {
    border-radius: 1em;
  }

  input[type=radio]::before {
    border-radius: 1em;
  }

  input:checked::before {
    transform: scale(1);
  }

  input:checked::after {
    opacity: 0;
    transform: scale(2) translateX(-50%) translateY(-50%);
  }

  input:disabled {
    ~label {
      opacity: .62;
    }
  }

  input[type="file"] {
    opacity: 0;
    z-index: -1;
    position: absolute;
    top: -1px;
    left: 0;
    width: 0;
    height: 0;
  }

  &:has(input:invalid) {
      + .input__error {
          display: block;

          &.hide {
              display: none;
          }
      }
  }
}

.input--light,
.button--light {
  .caption__count {
    border: .125rem solid var(--brand-color-white);
    background-color: transparent;
  }
}

.input__error {
  display: none;
  color: #fff;
}

form[data-validate] {
  .is-invalid,
  :invalid {
    border: .125em solid var(--vnc-widget-input-invalid-color);
    border-color: var(--vnc-widget-input-invalid-color);

    &::placeholder {
      color: var(--vnc-widget-input-invalid-color);
      opacity: 1;
    }

    &::-ms-input-placeholder {
      color: var(--vnc-widget-input-invalid-color);
    }

    ~label {
      color: var(--widget-white);

      &.vnc-btn {
        --button-text-color: var(--brand-color-white);
        --button-accent-color: var(--brand-color-error);
      }
    }

    ~.input__error {
      display: block;
    }
  }

  [data-append-text]:has(:invalid)::after {
    color: var(--vnc-widget-input-invalid-color);
  }

  &:has(:invalid) {
    .button[type=submit] {
      --button-text-color: var(--brand-color-white);
      --button-background-color: var(--brand-color-error);
      --button-border-color: var(--brand-color-error);
      --button-hover-background-color: var(--brand-color-error);
      pointer-events: none;
    }
  }
}

.vnc-btn {
  --vnc-widget-input-height: var(--vnc-widget-input-height);
  appearance: none;
  border: none;
  font-weight: bold;
  height: var(--vnc-widget-input-height);
  line-height: 1;
  display: inline-flex;
  align-items: center;
  gap: .3125rem;
  // padding: 1rem 3rem;
}

input[type=number] {
  -moz-appearance: textfield;
}

[data-append-text]:not([data-append-text=""]) {
  position: relative;

  input[type=text] {
    padding-inline-end: 6rem;
  }

  &::after {
    content: attr(data-append-text);
    position: absolute;
    bottom: 0;
    right: 0;
    width: 6rem;
    height: var(--vnc-widget-input-height);
    display: block;
    display: grid;
    place-content: center;
    color: var(--vnc-brand-color-secondary-dark);
}
}

.vnc-input {
  input[type=checkbox],
  input[type=radio] {
    background-color: transparent;
    position: relative;
    display: grid;
    place-content: center;
    width: 1.5em;
    height: 1.5em;
    margin: 0;
    padding: 0;
    appearance: none;
    border: 0.125em solid currentColor;
    // background-color: #fff;
    font: inherit;
    color: currentColor;
  }

  input[type=checkbox]::before,
  input[type=radio]::before {
    content: '';
    width: .5em;
    height: .5em;
    transform: scale(0);
    transition: 180ms transform ease-in-out;
    box-shadow: inset .5em .5em currentColor;
  }

  input[type=checkbox]::after,
  input[type=radio]::after {
    content: '';
    width: .5em;
    height: .5em;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: scale(0) translateX(-50%) translateY(-50%);
    transform-origin: 0 0;
    box-shadow: 0 0 0 .5em currentColor;
    opacity: .32;
    border-radius: 99999px;
    transition: all 320ms ease-in-out;
  }


  input[type=radio] {
    border-radius: 1em;
  }

  input[type=radio]::before {
    border-radius: 1em;
  }

  input:checked::before {
    transform: scale(1);
  }

  input:checked::after {
    opacity: 0;
    transform: scale(2) translateX(-50%) translateY(-50%);
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
  }

  input[type=number] {
      -moz-appearance:textfield;
  }
}

.vnc-input--radio,
.vnc-input--checkbox {
  display: inline-flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: .5rem;
  line-height: 1.1;
  display: flex;
  align-items: center;
  gap: 0.5em;
  margin-block-end: .5rem;
  min-height: 2rem;

  input,
  label {
    order: unset;
  }

  label {
    flex: 1 1 auto;
    cursor: pointer;
    align-items: center;
    // text-align: start;
  }

  .vnc-input__error {
    flex-basis: 100%;
  }
}

.vnc-container--grid {
    container: container-grid / inline-size;
}
@container container-grid (max-width: 992px) {
    .vnc-grid .vnc-g-col-lg-4 {
        grid-column: auto/span 4;
    }
}

.bullet-list {
    margin: 0 0 1rem 0!important
}
