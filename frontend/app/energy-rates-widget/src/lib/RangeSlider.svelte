<div class="vnc-input vnc-input--vnc-range" data-form-input>
    {#if label}
        <label for={`${id}__range`} class="vnc-input__label">{ label }</label>
    {/if}
    <input
        type="range"
        bind:value={value}
        id={`${id}__range`}
        name={name}
        min={min}
        max={max}
        step={step}
        required={required}
        disabled={disabled}
        readOnly={readOnly}
        data-input-range
    />
    {#if steps}
        <div class="vnc-input--vnc-range__steps">
            {#each [...Array(steps.length).keys()] as step, index}
                <input
                    type="radio"
                    id={`${id}__radio-${step}`}
                    value={step}
                    bind:group={value}
                    class="vnc-visually-hidden"
                />
                <label
                    for={`${id}__radio-${step}`}
                    class={`vnc-input__label ${index === value ? 'vnc-is-active' : 'vnc-opacity-50'}`}
                >{steps[step]}</label>
            {/each}
        </div>
    {/if}
    <!-- <span class="input__error">Element is invalid</span> -->
</div>

<script lang="ts">
    import {onMount} from 'svelte';
    import {_} from 'svelte-i18n';

    import avatar from '../assets/icons/avatar.svg?raw';
    import house from '../assets/icons/house.svg?raw';

    const icons = {
        avatar: avatar,
        house: house
    };

    export let id = '';
    export let name = '';
    export let label = '';
    export let required = false;
    export let disabled = false;
    export let readOnly = false;

    export let min = 0;
    export let max = 0;
    export let step: string | number = 'any';
    export let steps: number[] = [];

    export let value: number = 0;

    onMount(() => {
        document.documentElement.style.setProperty(
            '--vnc-thumb-background-avatar',
            `url("data:image/svg+xml;base64,${btoa(icons.avatar)}")`
        );
        document.documentElement.style.setProperty(
            '--vnc-thumb-background-house',
            `url("data:image/svg+xml;base64,${btoa(icons.house)}")`
        );
    })
</script>

<style lang="scss">
    :root,
    *::before,
    *::after {
        --vnc-range-track-height: 0.3125rem;
        --vnc-range-thumb-width: 2rem;
        --vnc-range-thumb-height: 2rem;
        --vnc-range-thumb-radius: 50%;

        --vnc-range-step-width: var(--vnc-range-thumb-width);

        --vnc-range-track-color: var(--vnc-widget-white);
        --vnc-range-thumb-color: var(--vnc-widget-white);
        --vnc-range-highlight-color: var(--vnc-widget-primary);
        --vnc-thumb-background-avatar: '';
        --vnc-thumb-background-house: '';
        --vnc-thumb-background: var(--vnc-thumb-background-house);
    }

    .vnc-input {
        position: relative;
        display: flex;
        gap: 0.3125rem;
        flex-direction: column;
        text-align: start;
    }

    .vnc-input--vnc-range {
        input[type='range'] {
            appearance: none;
            background: transparent;
            border: none;
            padding: 0;
            cursor: pointer;

            &::-webkit-slider-runnable-track {
                background: var(--vnc-range-track-color);
                height: var(--vnc-range-track-height);
            }

            &::-moz-range-track {
                background: var(--vnc-range-track-color);
                height: var(--vnc-range-track-height);
            }

            &::-webkit-slider-thumb {
                appearance: none;
                transform: translateY(
                        calc((var(--vnc-range-thumb-height) / 2 - var(--vnc-range-track-height) / 2) * -1)
                );
                background-color: var(--vnc-range-thumb-color);
                width: var(--vnc-range-thumb-width);
                height: var(--vnc-range-thumb-height);
                border-radius: var(--vnc-range-thumb-radius);
            }

            &::-moz-range-thumb {
                border: none;
                background-color: var(--vnc-range-thumb-color);
                width: var(--vnc-range-thumb-width);
                height: var(--vnc-range-thumb-height);
                border-radius: var(--vnc-range-thumb-radius);
            }
        }

        &__track {
            position: relative;
            // padding: 0 calc(var(--vnc-range-thumb-width) / 2);
            background: var(--vnc-range-track-color);
            height: var(--vnc-range-track-height);
            background-clip: content-box;
        }

        &__thumb {
            position: absolute;
            top: 50%;
            transform: translateY(-50%) translateX(-50%);
            background-color: var(--vnc-range-thumb-color);
            width: var(--vnc-range-thumb-width);
            height: var(--vnc-range-thumb-height);
            border-radius: var(--vnc-range-thumb-radius);
            transition: all 120ms ease;
        }

        &__steps {
            order: 2;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .vnc-input__label {
                width: var(--vnc-range-step-width);
                text-align: center;
                transition: opacity 120ms ease;
                display: block;
                min-height: 0;

                &:hover {
                    color: var(--brand-color-primary);
                }
            }
        }
    }

    .vnc-input--vnc-range__track {
        width: 100%;
        top: -24px;
    }

    .vnc-input--vnc-range__thumb {
        position: absolute;
        top: 1px;
        left: 15px;
        z-index: 1;
        width: 30px;
        height: 30px;
        display: grid;
        place-content: center;
    }

    .vnc-input--vnc-range input[type='range']::-webkit-slider-thumb {
        z-index: 2;
        background-image: var(--vnc-thumb-background);
        background-size: var(thumb-background-size);
        background-position: center center;
        background-repeat: no-repeat;

        // box-shadow: 0px 0px 0px 4px rgba(51, 153, 204, 0.49);
    }

    .vnc-input--vnc-range input[type='range']::-moz-range-thumb {
        z-index: 2;
        background-image: var(--vnc-thumb-background);
        background-size: var(thumb-background-size);
        background-position: center center;
        background-repeat: no-repeat;

        // box-shadow: 0px 0px 0px 4px rgba(51, 153, 204, 0.49);
    }
</style>
