<div class="vnc-tabs" data-selected-tab="{active + 1}">
  <div class="vnc-tab-nav vnc-flex middle vnc-gap-2 vnc-gap-md-4 vnc-gap-lg-5">
    {#each (items) as item, index}
      <div class="vnc-w-1/3">
        <button
          class="vnc-btn vnc-w-full"
          class:vnc-is-active={active === index}
          aria-label="{$_(`tab.${item}`)}"
          on:click={() => active = index}
        >
          <SvgIcon symbol={item}></SvgIcon>
          <span class="vnc-caption">{$_(`tab.${item}`)}</span>
        </button>
      </div>
    {/each}
  </div>
  <div class="vnc-tab-group vnc-overflow-hidden">
    {#each items as item, index}
      {#if active === index}
        <div
          id={`tab-group__item-${index}`}
          class="vnc-tab-group__item vnc-col-12"
          transition:fade
          >
          <div class="tab-group__item__inner">
            <slot name="item" :item="item" :index="index">id: { item }</slot>
          </div>
        </div>
      {/if}
  {/each}
  </div>
</div>

<script lang="ts">
import { _ } from 'svelte-i18n';
import {
	// blur,
	// crossfade,
	// draw,
	fade,
	// fly,
	// scale,
	// slide
} from 'svelte/transition';
import SvgIcon from "./SvgIcon.svelte";

export let active = 0;
export let items: string[] = [];
</script>

<style lang="scss">
.vnc-tabs {
  --vnc-tab-color: var(--vnc-widget-color);
  --vnc-tab-bg-color: var(--vnc-widget-bg-color);
  --vnc-tab-active-color: var(--vnc-widget-active-color);
  --vnc-tab-active-bg-color: var(--vnc-widget-active-bg-color);
}

.vnc-tab-nav {
  .vnc-btn {
    display: flex;
    align-items: center;
    color: var(--vnc-tab-color);
    background-color: var(--vnc-tab-bg-color);
    transition: all 320ms ease-in-out;
    border: none;
    justify-content: start;
    font-size: 1.25rem;
    gap: 0.625rem;
    height: 4rem;
    // padding: 0 2rem;
    font-weight: bold;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    &:active {
      color: var(--vnc-tab-color);
      background-color: var(--vnc-tab-bg-color);
    }
    &.vnc-is-active {
      color: var(--vnc-tab-active-color);
      background-color: var(--vnc-tab-active-bg-color);
    }
    // .vnc-caption {
    //   font-size: 1.125rem;
    // }
    // font-size: 1.125rem;
    @container container-grid (min-width: 576px) {
      font-size: 1.75rem;
      justify-content: start;
      flex-direction: row;
      .vnc-caption {
        display: inline;
      }
    }
  }
}
.vnc-tab-group {
  display: grid;
  &__item {
      grid-column-start: 1;
      grid-column-end: 2;
      grid-row-start: 1;
      grid-row-end: 2;
  }
}
</style>
