<div class="energy-rates-widget" data-mode="dark">
  <div class="vnc-container--grid vnc-mx-auto">
    <TabContainer bind:active={active} items={tabs}>
      <svelte:fragment slot="item">
        <EnergyRate
          bind:rate={rates[active]}
          config={configs[active]}
        ></EnergyRate>
      </svelte:fragment>
    </TabContainer>
  </div>
  <!-- <div class="vnc-text-black">
    {JSON.stringify(rates, null, 2)}
  </div> -->
</div>

<script lang="ts">
import { EnergyTypes, HeatingTypes, MeterTypes, type EnergyTypesKeys, type IEnergyRateConfig } from "../ambient";
import EnergyRate from "./EnergyRate.svelte";
import TabContainer from "./TabContainer.svelte";

export const baseRate = (type: string) => ({
  type: '',
  zip: '',
  consumption: undefined,
  ...(type === EnergyTypes.heat
    ? {
        heating: HeatingTypes.heatpump,
        meter: MeterTypes.single
      }
    : {})
});

export let configs: IEnergyRateConfig[] = [];
export let active = 0;

$: tabs = configs.map((config) => config.type);
$: rates = tabs.map((rate: EnergyTypesKeys) => ({ ...baseRate(rate), type: rate }));
</script>
