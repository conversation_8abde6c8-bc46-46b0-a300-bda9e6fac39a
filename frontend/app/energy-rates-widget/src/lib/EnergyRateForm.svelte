<div class={`vnc-energy-rate__form vnc-energy-rate--${config.type}__form`}>
  <form
    style="display: contents"
    action="javascript:void(0);"
    bind:this={form}
    on:submit={submit}
    >
    <slot></slot>
  </form>
  <!-- <div class="vnc-p-4">{JSON.stringify(submitData)}</div> -->
</div>

<script lang="ts">
import {
  EnergyTypes,
  HeatingTypes,
  MeterTypes,
  type IEnergyRate,
  type IEnergyRateConfig,
  type IObject
} from "../ambient";

const fieldMap: IObject = {
  type: {
    energyType: {
      [EnergyTypes.power]: 'electricity',
      [EnergyTypes.gas]: 'natural_gas',
      [EnergyTypes.heat]: 'electric_heating'
    }
  },
  zip: 'zipCode',
  heating: {
    category: {
      [HeatingTypes.heatpump]: 'heat_pump',
      [HeatingTypes.storage]: 'night_storage_heater'
    }
  },
  meter: {
    separateMeterForHeating: {
      heating: HeatingTypes.storage
    },
    filter_counterTypes: {
      [MeterTypes.single]: 'SINGLE',
      [MeterTypes.double]: 'DUAL',
      [MeterTypes.household]: 'SINGLE'
    }
  },
  consumptionAlt: 'consumptionNT'
};

const api = {
  endpoint: endpointUrl,
  method: 'get',
  target: endpointTarget,
  verbose: false
};

let form: HTMLFormElement;
let validate = false;

export let config: IEnergyRateConfig;
export let rate: IEnergyRate;

$: submitData = Object.keys(rate).reduce((current, next) => {
  let key: string, value: string | boolean, map: any;
  if (fieldMap[next] instanceof Object) {
    // console.log('NEXT', next);
    map = Object.keys(fieldMap[next]).reduce((current, key) => {
      let _key, _value;
      // console.log('keys', rate[next], Object.keys(fieldMap[next][key]));
      const _rate = rate[(next as keyof IEnergyRate)] as string;
      if (fieldMap[next][key][_rate]) {
        // console.log('found', next);

        _key = _rate;
        _value = fieldMap[next][key][_key];
      } else {
        // console.log('found', next);
        _key = Object.keys(fieldMap[next][key])[0];
        _value = rate[_key as keyof IEnergyRate] === fieldMap[next][key][_key];
      }
      // console.log('keys', key, _key, fieldMap[next][key][_rate]);
      // console.log('adfasdf', key, _key, _value, fieldMap[next][key][rate[next]]);

      return {
        ...current,
        [key]: _value
      };
    }, {});
  } else {
    key = fieldMap[next] || next;
    value = rate[next as keyof IEnergyRate] as string;
    map = { [key]: value };
  }
  // console.log('current', current, map);

  return {
    ...current,
    ...(map || {})
  };
}, {});

$: searchParams = new URLSearchParams(submitData);

function submit() {
  // console.log('submit', searchParams.toString());

  if (form.checkValidity()) {
    if (api.method === 'get') {
      if (api.target === '_blank') {
        window.open(`${api.endpoint}?${searchParams.toString()}`, '_blank').focus();
      } else {
        location.href = `${api.endpoint}?${searchParams.toString()}`;
      }
    }
  } else {
    validate = true;
    form.value.dataset.validate = true;
    if (api.verbose) {
      form.value.reportValidity();
    }
  }
}
</script>
