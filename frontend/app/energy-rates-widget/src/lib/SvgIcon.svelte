<i class="vnc-svg-icon vnc-svg-icon--{symbol}" title="{title}">
  <svelte:component this={icons[symbol]} class="vnc-icon"/>
</i>

<script lang="ts">
import powerIcon from '../assets/icons/power.svelte';
import gasIcon from '../assets/icons/gas.svelte';
import heatIcon from '../assets/icons/heat.svelte';
import type { IObject } from '../ambient';

const icons: IObject = {
  power: powerIcon,
  gas: gasIcon,
  heat: heatIcon
};

export let title = '';
export let symbol = '';
// export let size = 'sm';
</script>

<style lang="scss">
  .vnc-svg-icon {
    display: inline-grid;
    place-content: center;
    font-size: 1.4em;
    aspect-ratio: 1 / 1;
  }

  :global(.vnc-svg-icon svg) {
    display: inline-block;
    width: 1em;
    height: 1em;
    stroke-width: 0;
    stroke: currentColor;
    fill: currentColor;
  }
</style>