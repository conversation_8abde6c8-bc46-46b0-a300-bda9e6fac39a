<div class="vnc-input--radio-group">
  {#if label}
    <label for={`${id}-0`} class="vnc-input__label vnc-mb-1">
      <slot name="label">
        { label }
      </slot>
    </label>
  {/if}
  <div class="vnc-grid vnc-grid-cols-3 vnc-grid-rows-1 vnc-gap-4 vnc-gap-lg-5 vnc-row-gap-2">
    {#each _options as option, index}
      <div class="vnc-input vnc-input--radio vnc-mb-0 vnc-g-col-12 vnc-g-col-lg-4 vnc-d-inline-flex vnc-align-items-center vnc-gap-2">
        <input
          bind:group={value}
          type="radio"
          name={name}
          id={`${id}-${index}`}
          value={option.value}
          checked={option.value === value}
          disabled={disabled}
          readonly={readOnly}
          required={required}
        />
        <label class="vnc-input__label vnc-mb-0" for={`${id}-${index}`}>
          <slot name="item-label">
            { option.text }
          </slot>
        </label>
      </div>
    {/each}
  </div>
</div>

<script lang="ts">
import { _ } from 'svelte-i18n';

export let id = '';
export let name = '';
export let label = '';
export let required = false;
export let disabled = false;
export let readOnly = false;
export let options: { text: string, value: string }[] = [];

export let value = '';

$: _options = options.map((option: any | string) =>
  typeof option === 'string' || typeof option === 'number'
    ? { text: option, value: option }
    : option
);
</script>

<style lang="scss">
</style>