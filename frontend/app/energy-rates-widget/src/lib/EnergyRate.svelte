<div class={`vnc-energy-rate vnc-energy-rate--${config.type} vnc-bg-gradient-secondary`}>
    <div class="vnc-container vnc-mx-auto vnc-p-4 vnc-pb-5">
        <EnergyRateForm
            config={config}
            rate={rate}
        >
            <div class="vnc-mx-auto vnc-grid vnc-gap-4">
                {#if config.heating}
                    <div class="vnc-energy-rate__heating">
                        <div class="vnc-energy-rate__heating-type vnc-mb-3">
                            {#if rate.heating}
                                <RadioGroup
                                    id={`${config.type}-heating`}
                                    name="heatingType"
                                    bind:value={rate.heating}
                                    options={config.heating.types.map((value) => ({
                      text: $_(`rate.heating.${value}.label`),
                      value
                    }))}
                                    label={$_('rate.heating.label')}
                                ></RadioGroup>
                            {/if}
                        </div>
                        <div class="vnc-energy-rate__heating-type">
                            {#if rate.heating && meterOptions}
                                <RadioGroup
                                    id={`${config.type}-meter`}
                                    name="heatingMeter"
                                    bind:value={rate.meter}
                                    options={
                      meterOptions.map((value: string) => ({
                      text: $_(`rate.meter.${value}.label`),
                      value
                    }))}
                                    label={$_('rate.meter.label')}
                                ></RadioGroup>
                            {/if}
                        </div>
                    </div>
                {/if}

                <div class="vnc-energy-rate__main vnc-grid vnc-grid-cols-3 vnc-grid-rows-1 vnc-gap-12">
                    <div class="vnc-energy-rate__zip vnc-g-col-12 vnc-g-col-lg-4">
                        <div class="vnc-input">
                            <label
                                for={`${rate.type}-zip`}
                                class="vnc-input__label"
                            >{$_('rate.zip.label')}</label>
                            <input
                                id={`${rate.type}-zip`}
                                type="text"
                                bind:value={rate.zip}
                                required={config.zip?.required}
                                placeholder={$_('rate.zip.placeholder')}
                                minlength="5"
                                maxlength="5"
                                on:keydown={isNumeric}
                            />
                        </div>
                        {#if rate.zip !== ''}
                            <div class="input__error">
                                Bitte geben Sie eine gültige Postleitzahl ein.
                            </div>
                        {/if}
                    </div>

                    <div class="vnc-energy-rate__household vnc-g-col-12 vnc-g-col-lg-4">
                        {#if steps}
                            <RangeSlider
                                id={`${config.type}-consumption`}
                                bind:value={household}
                                min={0}
                                max={steps.length - 1}
                                step={1}
                                steps={steps}
                                label={$_(`rate.household.${config.type}.label`)}
                            ></RangeSlider>
                        {:else}
                            <div class="vnc-input" data-append-text={config.consumption.unit}>
                                <label
                                    for={`${rate.type}-consumptionDefault`}
                                    class="vnc-input__label"
                                >{$_('rate.consumptionDefault.label')}</label>
                                <input
                                    id={`${rate.type}-consumptionDefault`}
                                    type={config.consumption.type}
                                    bind:value={rate.consumption}
                                    placeholder={'Ihr Jahresverbrauch'}
                                    required={config.consumption.required}
                                    min={config.consumption.min}
                                    max={config.consumption.max}
                                    on:keydown={isNotZero}
                                    on:blur={empty}
                                />
                            </div>
                            {#if !rate.consumption}
                                <div class="input__error">
                                    Bitte geben Sie Ihren Jahresverbrauch im Hochtarif (HT) ein.
                                </div>
                            {/if}
                        {/if}
                    </div>

                    <div class="vnc-energy-rate__consumption vnc-g-col-12 vnc-g-col-lg-4">
                        <transition name="vnc-fade">
                            {#if config.household}
                                <div
                                    class="vnc-input"
                                    data-append-text={config.consumption.unit}
                                    transition:fade
                                >
                                    <label
                                        for={`${rate.type}-consumption`}
                                        class="vnc-input__label"
                                    >{$_('rate.consumption.label')}</label>
                                    <input
                                        bind:value={rate.consumption}
                                        id={`${rate.type}-consumption`}
                                        type={config.consumption.type}
                                        placeholder={$_('rate.consumption.placeholder')}
                                        required={config.consumption.required}
                                        min={config.consumption.min}
                                        max={config.consumption.max}
                                        on:keydown={isNotZero}
                                        on:blur={empty}
                                    />
                                </div>
                                {#if !rate.consumption}
                                    <div class="input__error">
                                        Bitte geben Sie Ihren Verbrauch ein.
                                    </div>
                                {/if}
                            {:else if displayAltConsumption}
                                <div
                                    class="vnc-input"
                                    data-append-text={config.consumption.unit}
                                    transition:fade
                                >
                                    <label
                                        for={`${rate.type}-consumptionAlt`}
                                        class="vnc-input__label"
                                    >{$_('rate.consumptionAlt.label')}</label>
                                    <input
                                        bind:value={rate.consumptionAlt}
                                        id={`${rate.type}-consumptionAlt`}
                                        type={config.consumptionAlt?.type}
                                        placeholder={$_('rate.consumptionAlt.placeholder')}
                                        required={config.consumptionAlt?.required}
                                        min={config.consumptionAlt?.min}
                                        max={config.consumptionAlt?.max}
                                        on:keydown={isNotZero}
                                        on:blur={empty}
                                    />
                                </div>
                                {#if !rate.consumptionAlt}
                                    <div class="input__error">
                                        Bitte geben Sie Ihren Jahresverbrauch im Niedertarif (NT) ein.
                                    </div>
                                {/if}
                            {/if}
                        </transition>
                    </div>
                </div>

                <div class="vnc-energy-rate__footer vnc-py-3">
                    <div class="vnc-energy-rate__submit">
                        <button
                            type="submit"
                            class="vnc-btn vnc-bg-light vnc-block vnc-mx-auto"
                        >Tarif berechnen
                        </button>
                    </div>

                    <!-- <div class="vnc-col-auto vnc-ms-auto">
                      <input-checkbox v-model="model.b2b"></input-checkbox>
                    </div> -->
                </div>
            </div>
        </EnergyRateForm>
    </div>
</div>

<script lang="ts">
    import {_} from 'svelte-i18n';
    import EnergyRateForm from "./EnergyRateForm.svelte";
    import RadioGroup from "./RadioGroup.svelte";
    import RangeSlider from "./RangeSlider.svelte";

    import {
        // blur,
        // crossfade,
        // draw,
        fade,
        // fly,
        // scale,
        // slide
    } from 'svelte/transition';

    import {
        HeatingTypes,
        MeterTypes,
        type IEnergyRate,
        type IEnergyRateConfig,
    } from "../ambient";

    export let config: IEnergyRateConfig;
    export let rate: IEnergyRate;

    export let household: number = 0;

    $: steps = config.household && Object.keys(config.household.steps).map(Number);
    $: displayHousehold = rate.heating === HeatingTypes.storage;
    $: displayAltConsumption = rate.meter !== MeterTypes.single;
    $: meterOptions = config.heating?.meters.filter((key) => key !== 'household' || displayHousehold);

    $: rate.consumption = (config.household && Object.values(config.household.steps).map(Number)[household]) || 0;

    $: zip = rate.zip;

    function isNumeric(event: KeyboardEvent) {
        return (
            event.key &&
            ((event.key.length === 1 && isNaN(Number(event.key))) || event.key === ' ') &&
            event.preventDefault()
        );
    }

    function isNotZero(event: KeyboardEvent) {
        return !(event.target as HTMLInputElement).value && event.key === '0' && event.preventDefault();
    }

    function empty(event: FocusEvent) {
        if (Number((event.target as HTMLInputElement).value) === 0) {
            (event.target as HTMLInputElement).value = '';
        }
    }
</script>

<style>
    .vnc-btn {
        color: var(--vnc-brand-color-secondary);
        background-color: var(--vnc-brand-color-light);
        border-radius: 5rem;
        font-weight: bold;
        min-height: 3rem;
        padding-inline: 1.5rem;
    }
</style>
