/* @import "tailwindcss/base";
@import "tailwindcss/components"; */
@import "tailwindcss/utilities";

:root,
*::before,
*::after {
  --vnc-brand-color-primary-dark: #5A7800;
  --vnc-brand-color-primary-light: #789314;
  --vnc-brand-color-secondary-dark: #49083a;
  --vnc-brand-color-secondary-light: #7D1464;
  --vnc-brand-color-tertiary-dark: #000000;
  --vnc-brand-color-tertiary-light: #575756;

  --vnc-brand-color-primary-variant-dark: #B0B108;
  --vnc-brand-color-primary-variant-light: #C8C920;

  --vnc-brand-color-primary: var(--vnc-brand-color-primary-dark);
  --vnc-brand-color-secondary: var(--vnc-brand-color-secondary-dark);
  --vnc-brand-color-tertiary: var(--vnc-brand-color-tertiary-dark);

  --vnc-brand-color-signal-success: #5A7800;
  --vnc-brand-color-signal-info: #0073AF;
  --vnc-brand-color-signal-warning: #E16400;
  --vnc-brand-color-signal-danger: #c82314;

  --vnc-brand-color-dark: #4e4e4e;
  --vnc-brand-color-light: #fafafa;
  --vnc-brand-color-black: #000;
  --vnc-brand-color-white: #fff;

  --vnc-body-color: #4e4e4e;
  --vnc-body-background-color: var(--vnc-brand-color-white);

  --vnc-link-color: var(--vnc-brand-color-secondary);

  --vnc-headline-font-family: 'Ropa-Sans';

  --vnc-border-color: var(--bs-border-color);
  --vnc-border-width: var(--bs-border-width);
  --vnc-border-radius: var(--bs-border-radius);

  --vnc-brand-gradient-primary-start: var(--vnc-brand-color-primary-light);
  --vnc-brand-gradient-primary-stop: var(--vnc-brand-color-primary-dark);
  --vnc-brand-gradient-primary: linear-gradient(180deg, var(--vnc-brand-gradient-primary-start) 0%, var(--vnc-brand-gradient-primary-stop) 100%);

  --vnc-brand-gradient-primary-variant-start: var(--vnc-brand-color-primary-variant-light);
  --vnc-brand-gradient-primary-variant-stop: var(--vnc-brand-color-primary-variant-dark);
  --vnc-brand-gradient-primary-variant: linear-gradient(180deg, var(--vnc-brand-gradient-primary-variant-start) 0%, var(--vnc-brand-gradient-primary-variant-stop) 100%);

  --vnc-brand-gradient-secondary-start: var(--vnc-brand-color-secondary-light);
  --vnc-brand-gradient-secondary-stop: var(--vnc-brand-color-secondary-dark);
  --vnc-brand-gradient-secondary: linear-gradient(180deg, var(--vnc-brand-gradient-secondary-start) 0%, var(--vnc-brand-gradient-secondary-stop) 100%);

  --vnc-widget-primary: var(--vnc-brand-color-primary);
  --vnc-widget-secondary: var(--vnc-brand-color-secondary);
  --vnc-widget-tertiary: var(--vnc-brand-color-teriary);
  --vnc-widget-white: var(--vnc-brand-color-white);
  --vnc-widget-red: var(--vnc-brand-color-red);
  --vnc-widget-orange: var(--vnc-brand-color-orange);

  --vnc-widget-color: var(--vnc-widget-white);
  --vnc-widget-bg-color: #49083A;
  --vnc-widget-active-color: var(--vnc-widget-color);
  --vnc-widget-active-bg-color: #7D1464;

  --vnc-widget-button-color: var(--vnc-widget-color);
  --vnc-widget-button-bg-color: var(--vnc-widget-bg-color);

  --vnc-widget-input-color: var(--vnc-brand-color-black);
  --vnc-widget-input-height: 4rem;
}

.theme--light {
  --vnc-body-color: var(--vnc-brand-color-primary);
  --vnc-body-background-color: var(--vnc-brand-color-white);
}

.theme--dark {
  --vnc-body-color: var(--vnc-brand-color-light);
  --vnc-body-background-color: var(--vnc-brand-color-primary);
}

:root {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  font-family: sans-serif;
}

.vnc-bg-gradient-primary {
  background: var(--vnc-brand-gradient-primary);
}

.vnc-bg-gradient-secondary {
  background: var(--vnc-brand-gradient-secondary);
}

/* .vnc-grid {
  display: grid;
  grid-template-rows: repeat(1, 1fr);
  grid-template-columns: repeat(12, 1fr);
} */
input[type=text],
input[type=number] {
  color: var(--vnc-widget-input-color);
}

.vnc-visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.vnc-energy-rate--power {
  --vnc-thumb-background: var(--vnc-thumb-background-avatar);
}
.vnc-energy-rate--gas {
  --vnc-thumb-background: var(--vnc-thumb-background-house);
}
