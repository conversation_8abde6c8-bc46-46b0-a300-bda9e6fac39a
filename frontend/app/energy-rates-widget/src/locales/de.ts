export const messages = {
  title: 'Energiepreiswidget',
  tab: {
    power: '<PERSON><PERSON><PERSON>',
    gas: 'Gas',
    heat: '<PERSON><PERSON>rm<PERSON>'
  },
  rate: {
    zip: {
      label: '<PERSON><PERSON>itz<PERSON>',
      placeholder: '<PERSON><PERSON><PERSON> Postleitzahl'
    },
    household: {
      // residents: {
      //   label: 'Personen im Haushalt'
      // },
      // area: {
      //   label: 'Wohnfläche in qm'
      // }
      power: {
        label: 'Personen im Haushalt'
      },
      gas: {
        label: 'Wohnfläche in qm'
      }
    },
    consumption: {
      label: 'Verbrauch',
      placeholder: 'Ihr Verbrauch'
    },
    consumptionDefault: {
      label: 'Ihr Jahresverbrauch im Hochtarif (HT)',
      placeholder: 'Ihr Jahresverbrauch'
    },
    consumptionAlt: {
      label: 'Ihr Jahresverbrauch im Niedertarif (NT)',
      placeholder: 'Ihr Jahresverbrauch'
    },
    heating: {
      label: 'Womit heizen Sie?',
      heatpump: {
        label: 'Wärmepumpe'
      },
      storage: {
        label: 'Nachspeicherheizung'
      }
    },
    meter: {
      label: 'Welchen Zählertyp nutzen Sie?',
      single: {
        label: '<PERSON><PERSON><PERSON><PERSON><PERSON>hler (nur Wärmestrom)'
      },
      double: {
        label: '<PERSON>wei<PERSON><PERSON><PERSON> (nur Wärmestrom)'
      },
      household: {
        label: 'Zweitarifzähler (inkl. Haushaltsstrom)'
      }
    }
    // area: {
    //   label: 'Wohnfläche in qm'
    // }
  },
  submit: {
    caption: 'Tarif berechnen'
  }
};

export default messages;
