<div class="vnc-svelte-app">
  {#if $isLoading}
    <p>Loading...</p>
  {:else}
    <EnergyRatesWidget configs={filteredRates}></EnergyRatesWidget>
  {/if}
</div>

<script lang="ts">
import { t, isLoading } from  "svelte-i18n";
import type { IEnergyRateConfig } from './ambient';
import { EnergyTypes } from './ambient';
import EnergyRatesWidget from './lib/EnergyRatesWidget.svelte';

export const rates: IEnergyRateConfig[] = [
  {
    id: EnergyTypes.power,
    type: EnergyTypes.power,
    zip: {
      minLength: 5,
      maxLength: 5,
      required: true
    },
    household: {
      steps: { 1: 1500, 2: 2500, 3: 3000, 4: 4000, 5: 5000, 6: 6000 }
    },
    consumption: {
      type: 'number',
      unit: 'kWh/Jahr',
      required: true,
      min: 0,
      max: 999999
    }
  },
  {
    id: EnergyTypes.gas,
    type: EnergyTypes.gas,
    household: {
      steps: { 25: 4300, 50: 6700, 75: 9800, 100: 12800, 125: 16400, 150: 22600 }
    },
    consumption: {
      type: 'number',
      unit: 'kWh/Jahr',
      required: true,
      min: 0,
      max: 999999
    }
  },
  {
    id: EnergyTypes.heat,
    type: EnergyTypes.heat,
    zip: {
      required: true
    },
    consumption: {
      type: 'number',
      unit: 'kWh/Jahr',
      required: true,
      min: 0,
      max: 999999
    },
    consumptionAlt: {
      type: 'number',
      unit: 'kWh/Jahr',
      required: true,
      min: 0,
      max: 999999
    },
    heating: {
      types: ['heatpump', 'storage'],
      meters: ['single', 'double', 'household']
    }
  }
];

//export const defaultRates = ['power', 'gas', 'heat'];

const filteredRates = rates.filter(rate => defaultRates.includes(rate.id));
</script>

<style>
  .vnc-svelte-app {
    color: #fff;
  }
</style>
