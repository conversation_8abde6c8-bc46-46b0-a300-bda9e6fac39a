/// <reference types="svelte" />
/// <reference types="vite/client" />

export interface IEnergyRateConfig {
  id: string;
  type: EnergyTypesKeys;
  zip?: {
    minLength?: number;
    maxLength?: number;
    required?: boolean;
  };
  household?: {
    steps: { [key: number]: number };
  };
  consumption: {
    type: string;
    required?: boolean;
    unit?: string;
    min?: number;
    max?: number;
  };
  consumptionAlt?: {
    type: string;
    required?: boolean;
    unit?: string;
    min?: number;
    max?: number;
  };
  heating?: IHeatingType;
  b2b?: boolean;
}

export interface IEnergyRate {
  id: string;
  type: string;
  zip: string;
  consumption: number;
  consumptionAlt: number;
  b2b?: boolean;
  heating?: HeatingTypesKeys;
  meter?: MeterTypesKeys;
}

export enum EnergyTypes {
  power = 'power',
  gas = 'gas',
  heat = 'heat'
}
export type EnergyTypesKeys = keyof typeof EnergyTypes;

export enum HeatingTypes {
  heatpump = 'heatpump',
  storage = 'storage'
}
export type HeatingTypesKeys = keyof typeof HeatingTypes;

export enum MeterTypes {
  single = 'single',
  double = 'double',
  household = 'household'
}
export type MeterTypesKeys = keyof typeof MeterTypes;

export interface IHeatingType {
  types: HeatingTypesKeys[];
  meters: MeterTypesKeys[];
}

export interface IObject {
  [key: string]: any;
}
