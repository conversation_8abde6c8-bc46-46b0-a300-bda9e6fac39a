plugin.tx_vncsitepackage {
  settings {
    # cat=plugin.tx_vncsitepackage/file; type=string; label=Pid of site Root
    startPagePid = 65
    # cat=plugin.tx_vncsitepackage/file; type=string; label=Path static files
    siteLogo =EXT:vnc_sitepackage/Resources/Public/Images/logo.svg
    # cat=plugin.tx_vncsitepackage/file; type=string; label=Path layout images
    imagePath = EXT:vnc_sitepackage/Resources/Public/Images
    # cat=plugin.tx_vncsitepackage/file; type=string; label=Path static files
    svgSpriteFile = Static/symbol-defs.svg
    # cat=plugin.tx_vncsitepackage/file; type=string; label=Path favicon set
    faviconPath = EXT:vnc_sitepackage/Resources/Public/Favicons/Default
    # cat=plugin.tx_vncsitepackage/Navigations; type=integer; label = Pid for meta navigation
    metaNavigationPid = 88
    # cat=plugin.tx_vncsitepackage/Navigations; type=integer; label = Pid for footer navigation
    footerNavigationPid = 75
  }
}

plugin.tx_indexedsearch {
  settings {
    # cat=plugin.tx_indexedsearch/settings; type=integer; label=Set the target page where search results are shown
    targetPid = 94
    # cat=plugin.tx_indexedsearch/settings; type=string; label=A list of integer which should be root-pages to search from
    rootPidList = 65
  }
}
